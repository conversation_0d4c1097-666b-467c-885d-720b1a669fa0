{"root": true, "env": {"node": true}, "extends": ["airbnb-typescript", "airbnb/hooks", "plugin:prettier/recommended", "prettier/@typescript-eslint", "prettier/react"], "plugins": ["@typescript-eslint"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "project": "./tsconfig.eslint.json", "extraFileExtensions": [".html"]}, "rules": {"import/no-extraneous-dependencies": ["error", {"devDependencies": true}], "no-console": "off", "react/jsx-props-no-spreading": "off", "react-hooks/exhaustive-deps": "off", "react/destructuring-assignment": "off", "@typescript-eslint/naming-convention": "off", "react/jsx-boolean-value": "off", "react/jsx-no-bind": "off", "@typescript-eslint/no-unused-vars": "off", "import/extensions": "off", "jsx-a11y/click-events-have-key-events": "off", "jsx-a11y/no-static-element-interactions": "off", "react/no-access-state-in-setstate": "off", "jsx-a11y/anchor-is-valid": "off", "react/no-this-in-sfc": "off", "react/no-did-update-set-state": "off", "jsx-a11y/media-has-caption": "off", "jsx-a11y/alt-text": "off"}}