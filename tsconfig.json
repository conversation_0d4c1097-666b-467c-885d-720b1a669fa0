{"compilerOptions": {"target": "esnext", "sourceMap": true, "allowJs": true, "moduleResolution": "node", "isolatedModules": true, "esModuleInterop": true, "strict": true, "noEmit": true, "jsx": "preserve", "experimentalDecorators": true, "baseUrl": "./", "module": "esnext", "paths": {"@app/*": ["src/*"], "@utils/*": ["src/utils/*"], "@views/*": ["src/views/*"], "@action/*": ["src/action/*"], "@components/*": ["src/components/*"]}, "plugins": [{"name": "typescript-tslint-plugin", "alwaysShowRuleFailuresAsWarnings": false, "ignoreDefinitionFiles": false, "configFile": "./tslint.json", "suppressWhileTypeErrorsPresent": false, "mockTypeScriptVersion": false}]}, "include": ["./src"], "exclude": ["./src/assets"]}