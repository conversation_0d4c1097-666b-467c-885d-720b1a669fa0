const webpack = require('webpack');
const path = require('path');
const merge = require('webpack-merge');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const FriendlyErrorsWebpackPlugin = require('friendly-errors-webpack-plugin');
const baseConfig = require('./webpack.base.config');

const ip = '0.0.0.0';
let proxyTarget = 'http://***********:8080'; // '**************:25500'; //
switch (process.env.NODE_ENV) {
  case 'test':
    proxyTarget = 'https://admintest.tidenews.com.cn';
    break;
  case 'testb':
    proxyTarget = 'https://admintest.tidenews.com.cn';
    break;
  case 'prod':
    proxyTarget = 'https://app-admin.zjol.com.cn/';
    break;
  case 'prev':
    proxyTarget = 'https://adminprev.zjol.com.cn';
    break;
  default:
    proxyTarget = 'https://admintest.tidenews.com.cn';
    break;
}

console.log('代理地址');
console.log(proxyTarget);
const distPath = `../dist_${process.env.NODE_ENV}`;
const devConfig = merge(baseConfig, {
  devServer: {
    // contentBase: path.join(__dirname, distPath),
    contentBase: path.join(__dirname, '../src'),
    publicPath: '/',
    compress: true,
    host: ip,
    port: 3000,
    hot: true,
    inline: true,
    open: false,
    clientLogLevel: 'warning',
    quiet: true,
    historyApiFallback: true,
    disableHostCheck: true,
    https: false,
    proxy: {
      '/endpoint': {
        target: proxyTarget,
        changeOrigin: true,
        secure: false,
      },
    },
  },
  plugins: [
    new webpack.HotModuleReplacementPlugin(),
    new webpack.NamedModulesPlugin(),
    new HtmlWebpackPlugin({
      filename: 'index.html',
      template: path.join(__dirname, '../src/index.html'),
      inject: true,
      hash: true,
      title: '浙江新闻管理后台',
    }),
    new FriendlyErrorsWebpackPlugin({
      compilationSuccessInfo: {
        messages: [`running on http://${ip}:3000, api proxy to ${proxyTarget}`],
      },
      onError: '/n',
    }),
  ],
});

module.exports = devConfig;
