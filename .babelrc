{"presets": [["@babel/env", {"useBuiltIns": "entry", "corejs": 3}], "@babel/react", ["@babel/typescript", {"isTSX": true, "allExtensions": true}]], "plugins": [["import", {"libraryName": "antd", "libraryDirectory": "es", "style": true}], ["@babel/plugin-proposal-decorators", {"legacy": true}], ["@babel/plugin-proposal-class-properties"], "@babel/plugin-proposal-object-rest-spread", "@babel/plugin-proposal-function-bind", "@babel/plugin-proposal-export-default-from", "@babel/plugin-proposal-logical-assignment-operators", "@babel/plugin-proposal-optional-chaining", "@babel/plugin-proposal-nullish-coalescing-operator", "@babel/plugin-proposal-do-expressions", "@babel/plugin-proposal-function-sent", "@babel/plugin-proposal-export-namespace-from", "@babel/plugin-proposal-numeric-separator", "@babel/plugin-proposal-throw-expressions", "@babel/plugin-syntax-dynamic-import", "@babel/plugin-syntax-import-meta", "@babel/plugin-proposal-json-strings"]}