CKEDITOR.plugins.add('wg', {
    requires: 'widget',
    init: function (editor) {
        editor.widgets.add('wgQuote',
            {
                template: '<blockquote class="wg wgQuote"></blockquote>',
                editables: {
                    content: {
                        selector: '.wgQuote',
                        allowedContent: 'strong em br'
                    }
                },
                allowedContent: 'blockquote(!wgQuote,wg)',
                requiredContent: 'blockquote(wgQuote)',
                upcast: function (element) {
                    return element.name == 'blockquote' && element.hasClass('wgQuote');
                }
            }
        );

        editor.widgets.add('wgImage',
            {
                template: '<figure class="wg wgImage">' +
                '<p class="wgImage-source"><img src="http://csmlf.8531.cn/mediacube/login/img/loadingPage-bg.jpg"/></p>' +
                '<p class="wgImage-content">Title</p>' +
                '</figure>',
                editables: {
                    content: {
                        selector: '.wgImage-content',
                        allowedContent: 'strong em br'
                    }
                },
                allowedContent: 'figure(!wgImage,wg); p(!wgImage-content); p(!wgImage-source)',
                requiredContent: 'figure(wgImage)',
                upcast: function (element) {
                    return element.name == 'figure' && element.hasClass('wgImage');
                }
            }
        );

        editor.widgets.add('wgVideo',
            {
                template: '<figure class="wg wgVideo">' +
                '<iframe src="http://player.youku.com/player.php/Type/Folder/Fid//Ob//sid/XMTgxOTUwNDE4OA==/v.swf"/>' +
                '</figure>',
                allowedContent: 'figure(!wgVideo,wg);iframe[!src]',
                requiredContent: 'figure(wgVideo)',
                upcast: function (element) {
                    return element.name == 'figure' && element.hasClass('wgVideo');
                }
            }
        );

        editor.widgets.add('wgVote',
            {
                template: '<div class="wg wgVote"><p data-id="voteid">新闻组件</p></div>',
                allowedContent: 'div(!wgVote,wg);p[data-id]',
                requiredContent: 'div(wgVote)',
                upcast: function (element) {
                    return element.name == 'div' && element.hasClass('wgVote');
                }
            }
        );

        editor.widgets.add('wgLink',
            {
                template: '<a class="wgLink" href="#">#奥巴马访华#</a>',
                allowedContent: 'a(!wgLink,wg)[!href]',
                requiredContent: 'a(wgLink)',
                editables: {
                    content: {
                        selector: '.wgLink',
                        allowedContent: 'strong em'
                    }
                },
                upcast: function (element) {
                    return element.name == 'a' && element.hasClass('wgLink');
                }
            }
        );

        editor.addCommand('wgLink', {
            exec: function (editor) {
                showInsertLink();
            }
        });
        editor.addCommand('image', {
            exec: function (editor) {
                showInsertImage();
            }
        });
        editor.addCommand('video', {
            exec: function (editor) {
                showInsertVideo();
            }
        });
        editor.addCommand('vote', {
            exec: function (editor) {
                showInsertVote();
            }
        });
        editor.addCommand('preview', {
            exec: function (editor) {
                showPreview();
            }
        });
        editor.addCommand('import', {
            exec: function (editor) {
                showImportURL();
            }
        });
        // editor.ui.addButton('wgLink', {icon: this.path + 'icons/link32.png', label: '插入官员／话题链接', command: 'wgLink'});
        // editor.ui.addButton('wgQuote', {icon: this.path + 'icons/quote32.png', label: '插入新闻导语', command: 'wgQuote'});
        editor.ui.addButton('wgImage', {icon: this.path + 'icons/image32.png', label: '插入图片', command: 'image'});
        // editor.ui.addButton('wgVideo', {icon: this.path + 'icons/video32.png', label: '插入视频', command: 'video'});
        // editor.ui.addButton('wgVote', {icon: this.path + 'icons/vote32.png', label: '插入组件', command: 'vote'});
        // editor.ui.addButton('wgPreview', {icon: this.path + 'icons/tablet32.png', label: '预览效果', command: 'preview'});
        // editor.ui.addButton('wgImport', {icon: this.path + 'icons/import32.png', label: '一键转载', command: 'import'});

    }
});