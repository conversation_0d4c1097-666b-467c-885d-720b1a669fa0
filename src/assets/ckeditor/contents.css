/*
Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/

body {
  /* Font */
  font-size: 12px;

  /* Text color */
  color: #333;

  /* Remove the background color to make it transparent */
  background-color: #fff;

  margin: 20px;
}

.cke_editable {
  font-size: 13px;
  line-height: 1.6;

  font-family: 'Helvetica Neue', 'Luxi Sans', 'DejaVu Sans', Tahoma, 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  /* Fix for missing scrollbars with RTL texts. (#10488) */
  word-wrap: break-word;
}

/*blockquote {*/
/*font-style: italic;*/
/*font-family: Georgia, Times, "Times New Roman", serif;*/
/*padding: 2px 0;*/
/*border-style: solid;*/
/*border-color: #ccc;*/
/*border-width: 0;*/
/*}*/

/*.cke_contents_ltr blockquote {*/
/*padding-left: 20px;*/
/*padding-right: 8px;*/
/*border-left-width: 5px;*/
/*}*/

/*.cke_contents_rtl blockquote {*/
/*padding-left: 8px;*/
/*padding-right: 20px;*/
/*border-right-width: 5px;*/
/*}*/

a {
  color: #0782c1;
}

ol,
ul,
dl {
  *margin-right: 0;
  margin: 0 0 20px 0;
  padding: 10px;
  line-height: 20px;
  background-color: #efefef;
  border: 1px solid #ccc;
  width: 700px;
}

ul {
  list-style: disc inside;
}

ol {
  list-style: decimal inside;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: normal;
  line-height: 1.2;
}

hr {
  border: 0px;
  border-top: 1px solid #ccc;
}

img.right {
  border: 1px solid #ccc;
  float: right;
  margin-left: 15px;
  padding: 5px;
}

img.left {
  border: 1px solid #ccc;
  float: left;
  margin-right: 15px;
  padding: 5px;
}

pre {
  white-space: pre-wrap; /* CSS 2.1 */
  word-wrap: break-word; /* IE7 */
  -moz-tab-size: 4;
  tab-size: 4;
}

.marker {
  background-color: Yellow;
}

span[lang] {
  font-style: italic;
}

/*figure {*/
/*text-align: center;*/
/*border: solid 1px #ccc;*/
/*border-radius: 2px;*/
/*background: rgba(0, 0, 0, 0.05);*/
/*padding: 10px;*/
/*margin: 10px 20px;*/
/*display: inline-block;*/
/*}*/

/*figure > figcaption {*/
/*text-align: center;*/
/*display: block; !* For IE8 *!*/
/*}*/

a > img {
  padding: 1px;
  margin: 1px;
  border: none;
  outline: 1px solid #0782c1;
}

body > * {
  margin-bottom: 20px;
}
.wg p {
  margin: 0;
  padding: 0;
}
.wg {
  padding: 8px;
  margin: 0 0 20px 0;
  border: 1px solid #ccc;
  width: 700px;
  position: relative;
  background-color: #efefef;
  line-height: 1;
}

.wg::before {
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  color: #999;
  font-size: 12px;
  width: 40px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  text-shadow: 1px 0 0 #fff;
}

p.wgImage-source img {
  width: 100%;
  height: auto;
}

.wg img {
  width: 100%;
  height: auto;
}

p.wgImage-content {
  line-height: 20px;
  margin-top: 5px;
}

.wgVideo {
  height: 296px;
}

.wgVideo iframe {
  border: none;
  width: 100%;
  height: 275px;
}

.wgQuote::before {
  content: '导语';
  text-shadow: none;
  font-style: normal;
}

.wgQuote {
  line-height: 25px;
  font-style: italic;
  color: #3c5567;
  text-shadow: 1px 0 0 #fff;
}

.wgVote::before {
  content: '组件';
}

.wgVote > p {
  line-height: 25px;
}

.wgVote,
.wgQuote {
  padding-left: 40px;
  width: 672px;
}

.wgLink {
  background: #fffde3;
  padding: 2px 3px 1px 3px;
  margin: 0 4px;
  border-bottom: 1px dashed #ccc;
  text-decoration: none;
}
