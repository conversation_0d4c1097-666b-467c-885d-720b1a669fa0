/**
 * @license Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see LICENSE.md or http://ckeditor.com/license
 */

CKEDITOR.editorConfig = function (config) {
    // Define changes to default configuration here. For example:
    // config.language = 'fr';
    config.toolbarGroups = [
        {name: 'tools', groups: ['tools']},
        {name: 'clipboard', groups: ['clipboard', 'undo']},
        {name: 'document', groups: ['mode', 'document', 'doctools']},
        {name: 'forms', groups: ['forms']},
        {name: 'basicstyles', groups: ['basicstyles', 'cleanup']},
        {name: 'styles', groups: ['styles']},
        {name: 'colors', groups: ['colors']},
        {name: 'paragraph', groups: ['list', 'indent', 'blocks', 'align', 'bidi', 'paragraph']},
        {name: 'links', groups: ['links']},
        {name: 'editing', groups: ['find', 'selection', 'spellchecker', 'editing']},
        {name: 'about', groups: ['about']},
        {name: 'insert', groups: ['insert']},
        {name: 'others', groups: ['others']},
    ];

    config.removeButtons = 'Form,Checkbox,Radio,TextField,Select,Textarea,Button,ImageButton,HiddenField,Print,Save,' +
        'NewPage,Preview,Templates,SelectAll,Scayt,Blockquote,CreateDiv,BidiLtr,BidiRtl,Language,Anchor,Image,Flash,' +
        'Table,HorizontalRule,Smiley,Iframe,About,PasteFromWord,Copy,Cut,Subscript,Superscript,Styles,SpecialChar,' +
        'PageBreak,Find,Replace,Format,Source';
    config.extraPlugins = 'widget,lineutils,wg';
    config.allowedContent = true;
    config.extraAllowedContent = 'figure(wg,wgVideo,wgImage);p(wgImage-source);iframe[!src];div(wg,wgVote);p(wgImage-content)[data-id]';
    config.forcePasteAsPlainText = true;
    config.width = '100%';
    config.height = '400px';
    config.magicline_color = '#C8C8C8';
    config.skin = 'office2013';
};
