/* eslint-disable import/prefer-default-export */
import { RequestBody, RequestInfo } from '@app/types';
import fetch from '@app/utils/fetch';
import {
  FAQAllData,
  OrgUsersAllData,
  VirtualUsersAllData,
  OrgUserArticleAllData,
} from '@app/views/users/users';

export const userApi = {
  // 用户协议
  getUserAgreementDetail: () => fetch.get('hcontent/detail', { type: 1 }),
  updateUserAgreement: (body: RequestBody) => fetch.post('hcontent/update', { ...body, type: 1 }),

  getHContent: (type: any) => fetch.get('hcontent/detail', { type }),
  updateHContent: (body: RequestBody) => fetch.post('hcontent/update', { ...body }),

  // 用户头像
  getAvatarList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('portrait/list', {}, request),
  sortAvatar: (body: RequestBody) => fetch.post('portrait/sort_update', body),
  updateAvatar: (body: RequestBody) => fetch.post('portrait/update', body),
  createAvatar: (body: RequestBody) => fetch.post('portrait/create', body),
  updateCustomAvatarSwitch: (body: RequestBody) =>
    fetch.post('portrait/custom_portrait_switch', body),

  // 用户列表
  getUserList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('account/list', body, request),
  updateUserStatus: (body: RequestBody) => fetch.post('account/update_status', body),
  getUserDetail: (body: RequestBody) => fetch.get('account/detail', body),
  batchUserCredits: (body: RequestBody) => fetch.blob('account/export', body),
  resetUserAvatar: (body: RequestBody) => fetch.post('account/reset_portrait', body),
  setUserDeleteStatus: (body: RequestBody) => fetch.post('account/log_off', body),
  updateUserCert: (body: RequestBody) => fetch.post('account/update_user_cert', body),
  updateOfficialCert: (body: RequestBody) => fetch.post('account/update_official_cert', body),
  updateUserTag: (body: RequestBody) => fetch.post('account/update_user_tag', body),
  // 荣誉记录
  getTaskLog: (body: RequestBody) => fetch.get('account/quality/task_log', body),
  // 荣誉详情
  qualityDetail: (body: RequestBody) => fetch.get('account/quality/detail', body),
  // 设置荣誉标识
  updateQualityReset: (body: RequestBody) => fetch.post('account/quality/reset', body),
  // 创作激励常见问题
  getContent: (body: RequestBody) => fetch.get('hcontent/detail', { type: 10 }),
  updateContent: (body: RequestBody) => fetch.post('hcontent/update', { ...body, type: 10 }),

  // 马甲用户
  getWaistcoatUserList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('account/shadow/list', body, request),
  getAllAreaList: () => fetch.get('area/list_area_tree'),
  createWaistcoatUser: (body: RequestBody) => fetch.post('account/shadow/edit', body),

  // 特权组
  getPrivilegeGroupList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('privilege_group/list_page', body, request),
  deletePrivilegeGroup: (body: RequestBody) => fetch.post('privilege_group/deleted', body),
  updatePrivilegeGroup: (body: RequestBody) => fetch.post('privilege_group/update', body),
  createPrivilegeGroup: (body: RequestBody) => fetch.post('privilege_group/create', body),
  getPrivilegeGroupDetail: (body: RequestBody) => fetch.get('privilege_group/detail', body),

  // 特权组用户
  getPGUserList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('privilege_user/list', body, request),
  addPGUser: (body: RequestBody) => fetch.post('privilege_user/create', body),
  deletePGUser: (body: RequestBody) => fetch.post('privilege_user/deleted', body),

  // 积分规则
  getCreditPolicyDetail: () => fetch.get('hcontent/detail', { type: 3 }),
  updateCreditPolicy: (body: RequestBody) => fetch.post('hcontent/update', { ...body, type: 3 }),
  updateMallSwitch: (body: RequestBody) =>
    fetch.post('web_feature/switch', { ...body, feature: 'score_mall_switch' }),
  getMallSwitch: () => fetch.get('web_feature/detail', { feature: 'score_mall_switch' }),

  // 用户消息
  getUserMessageList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('notice/list', body, request),
  getUserMessageDetail: (body: RequestBody) => fetch.get('notice/detail', body),
  deleteUserMessage: (body: RequestBody) => fetch.post('notice/deleted', body),
  createUserMessage: (body: RequestBody) => fetch.post('notice/create', body),

  // 推荐关注用户
  getRecommendUserList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('follow_recommend/list', body, request),
  createRecommendUsers: (body: RequestBody) => fetch.post('follow_recommend/create', body),
  updateRecommendUserStatus: (body: RequestBody) =>
    fetch.post('follow_recommend/update_status', body),
  deleteRecommendUser: (body: RequestBody) => fetch.post('follow_recommend/delete', body),
  updateRecommendUserSort: (body: RequestBody) => fetch.post('follow_recommend/update_sort', body),
  updateEmergencyPhone: (body: RequestBody) => fetch.post('account/update_emergency_phone', body),

  // 批量积分
  getBatchCreditList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('batch_score/list', body, request),
  getBatchCreditDetail: (body: RequestBody) => fetch.get('batch_score/detail', body),
  createBatchCredit: (body: RequestBody) => fetch.post('batch_score/create', body),

  // 用户反馈
  getFeedbackList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('account_feedback/list', body, request),
  batchReplyFeedback: (body: RequestBody) => fetch.post('account_feedback/batch_reply', body),
  getFeedbackDetail: (body: RequestBody) => fetch.get('account_feedback/detail', body),
  replyFeedback: (body: RequestBody) => fetch.post('account_feedback/save', body),

  // 帮助与反馈
  getFAQList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get<FAQAllData>('help/list', body, request),
  createFAQ: (body: RequestBody) => fetch.post('help/create', body),
  updateFAQ: (body: RequestBody) => fetch.post('help/update', body),
  updateFAQStatus: (body: RequestBody) => fetch.post('help/update_enabled', body),
  sortFAQ: (body: RequestBody) => fetch.post('help/update_sort', body),
  deleteFAQ: (body: RequestBody) => fetch.post('help/delete', body),
  getFAQDetail: (body: RequestBody) => fetch.get('help/detail', body),

  // 注销协议规则
  getDeletePolicyDetail: () => fetch.get('hcontent/detail', { type: 4 }),
  updateDeletePolicy: (body: RequestBody) => fetch.post('hcontent/update', { ...body, type: 4 }),

  // 机构认证用户
  getOrgUserList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get<OrgUsersAllData>('account/org_list', body, request),
  resetOrgUserPassword: (body: RequestBody) => fetch.post('account/update_account_pwd', body),
  getTMUserDetail: (body: RequestBody) => fetch.get('account/tm_detail', body),
  updateOrgUserCategory: (body: RequestBody) => fetch.post('account/set_tmh_class', body),

  // 机构认证用户稿件列表
  getOrgUserArticleList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get<OrgUserArticleAllData>('ugc_article/release_list', body, request),

  // 虚拟账号
  getVirtualUserList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get<VirtualUsersAllData>('account/virtual_list', body, request),
  createVirtualUser: (body: RequestBody) => fetch.post('account/virtual_create', body),

  // 机审白名单
  getWhiteList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get<VirtualUsersAllData>('media/review/white/list', body, request),
  createWhiteList: (body: RequestBody) => fetch.post('media/review/white/save', body),
  editWhiteList: (body: RequestBody) => fetch.post('media/review/white/edit', body),
  deleteWhiteList: (body: RequestBody) => fetch.post('/media/review/white/delete', body),

  // 黑名单
  setUserBlackList: (body: RequestBody) => fetch.post(`account_forbid_api/${body.api}`, body),
  batchUserBlackList: (body: RequestBody) => fetch.pblob('account_forbid_api/batch_forbid', body),

  shadowConvert: (body: RequestBody) => fetch.post(`account/shadow/convert`, body),

  // 注销协议规则
  getCashoutPolicyDetail: () => fetch.get('hcontent/detail', { type: 5 }),
  updateCashoutPolicy: (body: RequestBody) => fetch.post('hcontent/update', { ...body, type: 5 }),

  // 用户权益协议
  getEquityDetail: () => fetch.get('hcontent/detail', { type: 7 }),
  updateEquity: (body: RequestBody) => fetch.post('hcontent/update', { ...body, type: 7 }),

  // 潮鸣号审核认证服务协议
  getCmhAgreementDetail: () => fetch.get('hcontent/detail', { type: 8 }),
  updateCmhAgreement: (body: RequestBody) => fetch.post('hcontent/update', { ...body, type: 8 }),

  // 潮鸣号认证常见问题
  getCmhAuthQuestionDetail: () => fetch.get('hcontent/detail', { type: 9 }),
  updateCmhAuthQuestion: (body: RequestBody) => fetch.post('hcontent/update', { ...body, type: 9 }),

  // 潮鸣号分类管理
  getOrgCategoryList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('tmh_class/list', body, request),
  createOrgCategory: (body: RequestBody) => fetch.post('tmh_class/create', body),
  updateOrgCategory: (body: RequestBody) => fetch.post('tmh_class/update', body),
  deleteOrgCategory: (body: RequestBody) => fetch.post('tmh_class/delete', body),
  orderOrgCategory: (body: RequestBody) => fetch.post('tmh_class/order', body),
  updateOrgCategoryStatus: (body: RequestBody) => fetch.post('tmh_class/update_status', body),
  getAreaList: (body: RequestBody = {}) => fetch.get('area/list', body),
  getSimpleOrgCategoryList: (body: RequestBody = {}) => fetch.get('tmh_class/simple_list', body),
  getOrgClassAccountList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('tmh_class_account/list', body, request),
  addOrgClassAccount: (body: RequestBody) => fetch.post('tmh_class_account/add', body),
  deleteOrgClassAccount: (body: RequestBody) => fetch.post('tmh_class_account/remove', body),
  orderOrgClassAccount: (body: RequestBody) => fetch.post('tmh_class_account/order', body),
  getTmhAccountDetail: (body: RequestBody) => fetch.get('tmh_class_account/detail', body),

  // 潮客星榜
  getChaokeStarList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('rank_star/list', body, request),
  orderChaokeStar: (body: RequestBody) => fetch.post('rank_star/update_sort', body),
  deleteChaokeStar: (body: RequestBody) => fetch.post('rank_star/delete', body),
  createChaokeStar: (body: RequestBody) => fetch.post('rank_star/create', body),

  // 用户爆料
  getDisclosureList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('broke_news/list', body, request),
  getDisclosureLabelList: () => fetch.get('broke_news/label_list'),
  getDisclosureDetail: (body: RequestBody) => fetch.get('broke_news/detail', body),
  createDisclosureLabel: (body: RequestBody) => fetch.post('broke_news/label_create', body),
  updateDisclosureLabel: (body: RequestBody) => fetch.post('broke_news/label_update', body),
  deleteDisclosureLabel: (body: RequestBody) => fetch.post('broke_news/label_delete', body),

  // 潮鸣号榜单
  getOrgRankList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('tmh_rank/list', body, request),
  updateOrgRankSort: (body: RequestBody) => fetch.post('tmh_rank/exchange', body),
  updateOrgRankName: (body: RequestBody) => fetch.post('tmh_rank/update_rank_name', body),
  updateOrgRankRule: (body: RequestBody) => fetch.post('tmh_rank/update_rule', body),
  updateOrgRankStatus: (body: RequestBody) => fetch.post('tmh_rank/update_status', body),
  getRankDetailList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('tmh_rank/rank_list', body, request),
  updateRankValue: (body: RequestBody) => fetch.post('tmh_rank/update_value', body),
  insertRankItem: (body: RequestBody) => fetch.post('tmh_rank/insert_rank_item', body),
  deleteRankItem: (body: RequestBody) => fetch.post('tmh_rank/del_rank_item', body),
  searchRankItem: (body: RequestBody) => fetch.get('tmh_rank/rank_item_search', body),
  getRankLog: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('tmh_rank/rank_log_list', body, request),
  publishRank: (body: RequestBody) => fetch.post('tmh_rank/publish_rank', body),

  // 积分等级
  getCreditLevelList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('score_level/list', body, request),
  createCreditLevel: (body: RequestBody) => fetch.post('score_level/create', body),
  updateCreditLevel: (body: RequestBody) => fetch.post('score_level/update', body),
  deleteCreditLevel: (body: RequestBody) => fetch.post('score_level/delete', body),

  getRedpacketAgreementDetail: () => fetch.get('hcontent/detail', { type: 6 }),
  updateRedpacketAgreement: (body: RequestBody) =>
    fetch.post('hcontent/update', { ...body, type: 6 }),

  // 用户图像审核
  getUserImageAuditList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('account_image_audit/list', body, request),
  auditUserImage: (body: RequestBody) => fetch.post('account_image_audit/pass', body),
  // 用户图像开关
  getUserImageAuditSwitch: (body: RequestBody) => fetch.get('web_feature/detail', body),
  updateUserImageAuditSwitch: (body: RequestBody) => fetch.post('web_feature/switch', body),

  updateOrgUserInfo: (body: RequestBody) => fetch.post('account/set_info', body),

  // 评论预设
  // 新建
  createCommentPreset: (body: RequestBody) => fetch.post('comment_preset/create', body),
  // 编辑
  updateCommentPreset: (body: RequestBody) => fetch.post('comment_preset/update', body),
  // 删除
  deleteCommentPreset: (body: RequestBody) => fetch.post('comment_preset/delete', body),
  // 列表
  getListCommentPreset: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('comment_preset/preset_list', body, request),
  // 详情
  getInfoCommentPreset: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('comment_preset/get_comment_preset_info', body, request),
  // 上下架
  updateTypeCommentPreset: (body: RequestBody) =>
    fetch.post('comment_preset/update_comment_preset_type', body),
  // 根据Id查询稿件是否已经预设评论
  getChannelArticleIdExistsCommentPreset: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('comment_preset/get_channel_article_id_exists', body, request),
  // 删除单个评论预设
  deleteOneCommentPreset: (body: RequestBody) => fetch.post('comment_preset/delete_one', body),

  // 评论提示语
  // 新建
  createCommentTips: (body: RequestBody) => fetch.post('comment_tips/create', body),
  // 编辑
  updateCommentTips: (body: RequestBody) => fetch.post('comment_tips/update', body),
  // 删除
  deleteCommentTips: (body: RequestBody) => fetch.post('comment_tips/delete', body),
  // 列表
  getListCommentTips: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('comment_tips/tips_list', body, request),
  // 详情
  getInfoCommentTips: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('comment_tips/get_comment_tips_info', body, request),
  // 上下架
  updateTypeCommentTips: (body: RequestBody) =>
    fetch.post('comment_tips/update_comment_tips_type', body),
  // 根据Id查询稿件是否已经预设评论
  getChannelArticleIdExistsCommentTips: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('comment_tips/get_channel_article_id_exists', body, request),
  // 删除单个评论预设
  deleteOneCommentTips: (body: RequestBody) => fetch.post('comment_tips/delete_one', body),
  // 用户重置昵称
  resetNickName: (body: RequestBody) => fetch.post('account/reset_nick_name', body),
  // 取消/设置发布白名单
  toggleWhiteList: (body: RequestBody) => fetch.post('account/set_pub_white', body),
  // 取消/设置验证
  togglePhoneValid: (body: RequestBody) => fetch.post('account/update_old_phone_valid', body),
  // 潮鸣号审核列表查询接口
  getAuditList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('account_audit/audit_list', body, request),
  // 潮鸣号审核详情查询接口
  getAuditDetail: (body: RequestBody) => fetch.get('account_audit/audit_detail', body),
  // 潮鸣号审核状态更新接口
  submitAudit: (body: RequestBody) => fetch.post('account_audit/audit_submit', body),
  cancelFaceCert: (body: RequestBody) => fetch.post('account/cancel_face_cert', body),

  // 抓取账号
  getSpiderBindList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('spider_bind/list', body, request),
  getSpiderAccountList: (body: RequestBody) => fetch.get('account/search', body),
  createSpiderBind: (body: RequestBody) => fetch.json('spider_bind/create', body),
  updateSpiderBind: (body: RequestBody) => fetch.json('spider_bind/update', body),
  deleteSpiderBind: (body: RequestBody) => fetch.post('spider_bind/delete', body),
  importCheckSpiderBind: (body: RequestBody) => fetch.pblob('spider_bind/import_check', body),
  importSubmitSpiderBind: (body: RequestBody) => fetch.json('spider_bind/import_submit', body),

  // 潮鸣号新榜单
  // 榜单列表
  getCmhRankList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ranks/chaominghaorank/page', body, request),
  // 发榜设置 现在是两个接口
  changeCmhRankPublishSwitch: (body: RequestBody) =>
    fetch.post('ranks/chaominghaorank/publish_switch', body),
  changeCmhRankPublishTime: (body: RequestBody) =>
    fetch.post('ranks/chaominghaorank/publish_time', body),
  // 更新榜单数据
  updateCmhRankData: (body: RequestBody) =>
    fetch.post('ranks/chaominghaorank/update_rank_data', body),
  // 获取榜单月份
  getCmhRankMonthList: (body: RequestBody) => fetch.get('ranks/chaominghaorank/rank_months', body),
  getCmhRankWeekList: (body: RequestBody) => fetch.get('ranks/chaominghaorank/rank_weeks', body),
  // 榜单排序
  updateCmhRankSort: (body: RequestBody) => fetch.post('ranks/chaominghaorank/sort', body),
  // 上下架
  updateCmhRankStatus: (body: RequestBody) =>
    fetch.post('ranks/chaominghaorank/updatestatus', body),
  // 更新榜单 修改名字
  updateCmhRank: (body: RequestBody) => fetch.post('ranks/chaominghaorank/update', body),
  // 手动发榜
  cmhManualPublish: (body: RequestBody) => fetch.post('ranks/chaominghaorank/publish_all', body),
  cmhManualSinglePublish: (body: RequestBody) => fetch.post('ranks/chaominghaorank/publish', body),
  // 指数配置
  getCmhRankConfigInfo: (body: RequestBody) =>
    fetch.get('ranks/chaominghaorank/rank_config_info', body),
  getCmhRankConfigItems: (body: RequestBody) =>
    fetch.get('ranks/chaominghaorank/rank_config_items', body),
  // 指数配置修改
  updateCmhRankConfig: (body: RequestBody) =>
    fetch.post('ranks/chaominghaorank/rank_config_update', body),

  updateCmhRankTitle: (body: RequestBody) => fetch.post('ranks/chaominghaorank/update_title', body),

  getSingleCmhRankList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ranks/chaominghaorank/rank_data_page', body, request),
  getSingleCmhArticleList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ranks/chaominghaorank/article_page', body, request),
  getCmhRankCategorys: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ranks/chaominghaorank/rank_industries', body, request),

  // 更新榜单显示条数
  updateCmhRankShowNum: (body: RequestBody) =>
    fetch.post('ranks/chaominghaorank/update_show_num', body),
  cmhModifyRankData: (body: RequestBody) =>
    fetch.post('ranks/chaominghaorank/modify_rank_data_offset', body),

  updateCmhRankDesc: (body: RequestBody) => fetch.post('ranks/chaominghaorank/updatedesc', body),

  // configCmhAutoPublish: (body: RequestBody) =>
  //   fetch.post('ranks/chaominghaorank/publish_time', body),
  // configCmhAutoPublish: (body: RequestBody) =>
  //   fetch.post('ranks/chaominghaorank/publish_time', body),

  // 潮客榜单
  getCkRankList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ranks/chaokerank/page', body, request),
  // 获取榜单周期
  getCkRankWeekList: (body: RequestBody) => fetch.get('ranks/chaokerank/week_dates', body),
  // 手动发榜
  ckManualPublish: (body: RequestBody) => fetch.post('ranks/chaokerank/publish_all', body),
  // 更新榜单数据
  updateCkRankData: (body: RequestBody) => fetch.post('ranks/chaokerank/update_rank_data', body),
  // 发榜设置 现在是两个接口
  changeCkRankPublishSwitch: (body: RequestBody) =>
    fetch.post('ranks/chaokerank/publish_switch', body),
  changeCkRankPublishTime: (body: RequestBody) => fetch.post('ranks/chaokerank/publish_time', body),
  // 指数配置
  getCkRankConfigInfo: (body: RequestBody) => fetch.get('ranks/chaokerank/rank_config_info', body),
  getCkRankConfigItems: (body: RequestBody) =>
    fetch.get('ranks/chaokerank/rank_config_items', body),
  // 指数配置修改
  updateCkRankConfig: (body: RequestBody) =>
    fetch.post('ranks/chaokerank/rank_config_update', body),
  getSingleCkRankList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ranks/chaokerank/rank_data_page', body, request),
  // 更新榜单显示条数
  updateCkRankShowNum: (body: RequestBody) => fetch.post('ranks/chaokerank/update_show_num', body),
  ckManualSinglePublish: (body: RequestBody) => fetch.post('ranks/chaokerank/publish', body),
  ckModifyRankData: (body: RequestBody) =>
    fetch.post('ranks/chaokerank/modify_rank_data_offset', body),
  getCkRankBlacklist: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ranks/chaokerank/blacklist', body, request),
  // 更新榜单 修改名字
  updateCkRank: (body: RequestBody) => fetch.post('ranks/chaokerank/update', body),
  // 上下架
  updateCkRankStatus: (body: RequestBody) => fetch.post('ranks/chaokerank/updatestatus', body),

  updateCkRankDesc: (body: RequestBody) => fetch.post('ranks/chaokerank/updatedesc', body),

  // 发放奖励
  giveReward: (body: RequestBody) => fetch.post('creator/earnings/give', body),

  exportCmgRankData: (body: RequestBody) =>
    fetch.blob('ranks/chaominghaorank/export_rank_data', body),
  exportCmgRankArticleData: (body: RequestBody) =>
    fetch.blob('ranks/chaominghaorank/export_article_list', body),
  exportCmgRankClassData: (body: RequestBody) => fetch.blob('ranks/chaominghao/export_excel', body),

  exportCkRankData: (body: RequestBody) => fetch.blob('ranks/chaokerank/export_rank_data', body),

  deleteBlacklistRecord: (body: RequestBody) =>
    fetch.post('ranks/chaokerank/blacklist/delete', body),

  addBlacklistRecord: (body: RequestBody) => fetch.post('ranks/chaokerank/blacklist/add', body),
  // 榜单排序
  updateCkRankSort: (body: RequestBody) => fetch.post('ranks/chaokerank/sort', body),
  getCmhProvinceList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ranks/chaominghao/province_page', body, request),
  getCmhIndustryList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ranks/chaominghao/industry_page', body, request),

  provinceImport: (body: RequestBody) => fetch.post('ranks/chaominghao/province_import', body),
  industryImport: (body: RequestBody) => fetch.post('ranks/chaominghao/industry_import', body),

  editArticleRankHiddenStatus: (body: RequestBody) =>
    fetch.post('ranks/chaominghaorank/update_hidden', body),

  getCooperationAccountList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('cooperation/account/list', body, request),
  deleteCooperationAccount: (body: RequestBody) => fetch.post('cooperation/account/delete', body),
  editCooperationAccount: (body: RequestBody) => fetch.json('cooperation/account/edit', body),
  updateCooperationAccountOrder: (body: RequestBody) =>
    fetch.post('cooperation/account/order', body),

  getCooperationCaseList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('cooperation/case/list', body, request),
  getCooperationCommonProblem: (body: RequestBody) =>
    fetch.get('cooperation/question/detail', body),
  updateCooperationCommonProblem: (body: RequestBody) =>
    fetch.json('cooperation/question/edit', body),
  deleteCooperationCase: (body: RequestBody) => fetch.post('cooperation/case/delete', body),
  updateCooperationCaseOrder: (body: RequestBody) => fetch.post('cooperation/case/order', body),
  editCooperationCase: (body: RequestBody) => fetch.json('cooperation/case/edit', body),

  // ✅ 获取潮鸣号榜单上榜奖励列表
  getCmhEarningsList: (body: RequestBody) =>
    fetch.get('earnings_manuscript_income/cmh/earnings_list', body),
  // 潮鸣号榜单发放奖励
  cmhGiveReward: (body: RequestBody) =>
    fetch.json('earnings_manuscript_income/cmh/earnings_give', body),
  // ✅ 获取潮客号榜单上榜奖励列表
  getCkEarningsList: (body: RequestBody) =>
    fetch.get('earnings_manuscript_income/chaoke/earnings_list', body),
  // 潮客号榜单发放奖励
  ckGiveReward: (body: RequestBody) =>
    fetch.json('earnings_manuscript_income/chaoke/earnings_give', body),
  // 查询稿件是否发放稿费
  checkArticleEarnings: (body: RequestBody) =>
    fetch.get('earnings_manuscript_income/is_article_earnings', body),
};
