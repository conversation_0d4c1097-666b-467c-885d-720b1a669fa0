/* eslint-disable import/prefer-default-export */
import {
  RequestBody,
  RequestInfo,
  TSysPermissionGroupData,
  TSysPermissionElementRecord,
  TSysAdminData,
  TSysAdminRoleData,
  TSysChannelCategoryData,
  TSysChannelData,
} from '@app/types';
import fetch from '@app/utils/fetch';

export const sysApi = {
  // 管理员列表接口
  getAdminList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get<TSysAdminData>('admin_user/list', body, request),
  updateAdminState: (body: RequestBody) => fetch.post<{}>('admin_user/update_state', body),
  getPermissionGroupsForAdmin: (body: RequestBody) =>
    fetch.get<TSysAdminRoleData>('role/list_manage', body),
  updateAdminPermissionGroup: (body: RequestBody) => fetch.post('admin_user/update_roles', body),

  // 权限组列表接口
  getPermissionGroupList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('role/list', body, request),
  deletePermissionGroup: (body: RequestBody) => fetch.post('role/delete', body),
  copyPermissionGroup: (body: RequestBody) => fetch.post('role/create_by_role', body),
  createPermissionGroup: (body: RequestBody) => fetch.post('role/create', body),
  updatePermissionGroup: (body: RequestBody) => fetch.post('role/update', body),

  // 权限组用户接口
  getGroupUsersList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('admin_user/list_by_role', body, request),
  removeGroupUser: (body: RequestBody) => fetch.post('admin_user/delete', body),
  addGroupUser: (body: RequestBody) => fetch.post('role/add_user', body),

  // 权限管理接口
  getAllGroupList: (body: RequestBody = {}) =>
    fetch.get<TSysPermissionGroupData>('role/list_all', body),
  getPermissionList: (body: RequestBody) =>
    fetch.get<{ elements: TSysPermissionElementRecord[] }>('permission/list', body),
  updatePermissions: (body: RequestBody) => fetch.post<{}>('permission/update_permissions', body),

  // 导航管理接口
  getAppNavList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('app_nav/list', body, request),
  getCityNavList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('area/list', body, request),
  getAreaNavList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('area/list', body, request),
  updateAppNavState: (body: RequestBody) => fetch.post('app_nav/update_state', body),
  updateSecondNavState: (body: RequestBody) => fetch.post('app_nav/update_state', body),
  updateCityNavState: (body: RequestBody) => fetch.post('area/update_state', body),
  updateAreaNavState: (body: RequestBody) => fetch.post('area/update_state', body),
  updateCountyNavState: (body: RequestBody) => fetch.post('area/update_state', body),
  sortAppNav: (body: RequestBody) => fetch.post('app_nav/update_sort', body),
  sortSecondNav: (body: RequestBody) => fetch.post('app_nav/update_sort', body),
  sortCityNav: (body: RequestBody) => fetch.post('area/update_sort', body),
  sortAreaNav: (body: RequestBody) => fetch.post('area/update_sort', body),
  sortCountyNav: (body: RequestBody) => fetch.post('area/update_sort', body),
  deleteAppNav: (body: RequestBody) => fetch.post('app_nav/delete', body),
  deleteSecondNav: (body: RequestBody) => fetch.post('app_nav/delete', body),
  deleteCityNav: (body: RequestBody) => fetch.post('area/delete', body),
  deleteCountyNav: (body: RequestBody) => fetch.post('area/delete', body),
  deleteAreaNav: (body: RequestBody) => fetch.post('area/delete', body),
  updateAppNav: (body: RequestBody) => fetch.post('app_nav/update', body),
  updateSecondNav: (body: RequestBody) => fetch.post('app_nav/update', body),
  updateCityNav: (body: RequestBody) => fetch.post('area/update', body),
  updateCountyNav: (body: RequestBody) => fetch.post('area/update', body),
  updateAreaNav: (body: RequestBody) => fetch.post('area/update', body),
  createAppNav: (body: RequestBody) => fetch.post('app_nav/create', body),
  createSecondNav: (body: RequestBody) => fetch.post('app_nav/create', body),
  createCityNav: (body: RequestBody) => fetch.post('area/create', body),
  createCountyNav: (body: RequestBody) => fetch.post('area/create', body),
  createAreaNav: (body: RequestBody) => fetch.post('area/create', body),
  getPrivilegeGroupList: () => fetch.get('privilege_group/list'),
  getAppNavDetail: (body: RequestBody) => fetch.get('app_nav/detail', body),
  getCityNavDetail: (body: RequestBody) => fetch.get('area/detail', body),
  getCountyNavDetail: (body: RequestBody) => fetch.get('area/detail', body),
  getAreaNavDetail: (body: RequestBody) => fetch.get('area/detail', body),
  getSecondNavDetail: (body: RequestBody) => fetch.get('app_nav/detail', body),
  updateAreaNavLinkable: (body: RequestBody) => fetch.post('area/update_linkable', body),
  updateAppNavDefault: (body: RequestBody) => fetch.post('app_nav/set_default', body),

  getCenterNavList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('app_nav/list', { ...body, category: 1 }, request),
  updateCenterNavState: (body: RequestBody) =>
    fetch.post('app_nav/update_state', { ...body, category: 1 }),
  sortCenterNav: (body: RequestBody) => fetch.post('app_nav/update_sort', { ...body, category: 1 }),
  deleteCenterNav: (body: RequestBody) => fetch.post('app_nav/delete', { ...body, category: 1 }),
  updateCenterNav: (body: RequestBody) => fetch.post('app_nav/update', { ...body, category: 1 }),
  createCenterNav: (body: RequestBody) => fetch.post('app_nav/create', { ...body, category: 1 }),
  getCenterNavDetail: (body: RequestBody) => fetch.get('app_nav/detail', { ...body, category: 1 }),

  // 频道管理接口
  getChannelList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get<TSysChannelData>('channel/list', body, request),
  getChannelCategoryList: () => fetch.get<TSysChannelCategoryData>('channel_category/list'),

  // 版本管理接口
  getAppVersionList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('app_version/list', body, request),
  getAppVersionDetail: (body: RequestBody) => fetch.get('app_version/detail', body),
  updateAppVersion: (body: RequestBody) => fetch.post('app_version/update', body),
  createAppVersion: (body: RequestBody) => fetch.post('app_version/create', body),
  deleteAppVersion: (body: RequestBody) => fetch.post('app_version/delete', body),

  // 公告
  getNoticeDetail: () => fetch.get('admin_notice/detail'),
  updateNotice: (body: RequestBody) => fetch.post('admin_notice/save', body),

  // 接口开关管理
  getInterfaceList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('interface_switch/list', body, request),
  createInterface: (body: RequestBody) => fetch.post('interface_switch/save', body),
  updateInterface: (body: RequestBody) => fetch.post('interface_switch/update', body),

  // 通用开关管理
  getSwitchDetail: (body: RequestBody) => fetch.get('web_feature/detail', body),
  updateSwitch: (body: RequestBody) => fetch.post('web_feature/switch', body),
  updateSwitchWithVer: (body: RequestBody) => fetch.post('web_feature/switch_save', body),

  searchGlobalNews: (body: RequestBody) => fetch.get('channel_article/search_title_content', body),
  // 用户报错管理
  getAppCrashList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('client_error_log/list', body, request),
  processAppCrash: (body: RequestBody) => fetch.post('client_error_log/handle', body),
  deleteAppCrash: (body: RequestBody) => fetch.post('client_error_log/delete', body),
  getSimpleVersionList: () => fetch.get('client_error_log/list_version'),

  batchRevokeNews: (body: RequestBody) => fetch.post('channel_article/batch_cancel_release', body),
  batchUpdateAudio: (body: RequestBody) => fetch.post('channel_article/batch_update_audio', body),

  // 导航头部背景图
  getNavTopBg: () => fetch.get('app_info_config/detail'),
  saveNavTopBg: (body: RequestBody) => fetch.post('app_info_config/save', body),

  // 偏好
  getProposalWordList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('proposal_word/list', body, request),
  createProposalWord: (body: RequestBody) => fetch.post('proposal_word/create', body),
  updateProposalWord: (body: RequestBody) => fetch.post('proposal_word/update', body),
  deleteProposalWord: (body: RequestBody) => fetch.post('proposal_word/delete', body),
  updateStatusProposalWord: (body: RequestBody) => fetch.post('proposal_word/update_status', body),
  updateSortProposalWord: (body: RequestBody) => fetch.post('proposal_word/update_sort', body),
  setDefaultProposalWord: (body: RequestBody) => fetch.post('proposal_word/set_default', body),
  getProposalWordDetail: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('proposal_word/detail', body, request),

  // 潮客推荐/应用列表
  getRecommendsceneScenelist: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('recommend_scene/scene_list', body, request),
  // 创建
  createRecommendscene: (body: RequestBody) => fetch.post('recommend_scene/create', body),
  updateRecommendscene: (body: RequestBody) => fetch.post('recommend_scene/update', body),
  deleteRecommendscene: (body: RequestBody) => fetch.post('recommend_scene/delete', body),
  updatescenetypeRecommendscene: (body: RequestBody) =>
    fetch.post('recommend_scene/update_scene_type', body),
  getRecommendsceneDetails: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('/recommend_scene/get_recommend_scene', body, request),

  // 维度列表

  getRecommendsceneDimensionlistAll: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('/recommend_dimension/dimension_list_ht', body, request),
  getRecommendsceneDimensionlist: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('recommend_dimension/dimension_list', body, request),
  updateRecommenddimension: (body: RequestBody) => fetch.post('recommend_dimension/update', body),
  UpdatedimensiontypeRecommenddimension: (body: RequestBody) =>
    fetch.post('recommend_dimension/update_dimension_type', body),

  // 专题模板配置
  // 列表
  getSubjectTemplateConfig: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('subjectTemplateConfig/list', body, request),
  // // 上下架
  // updateSubjectTemplateConfig: (body: RequestBody) =>
  //   fetch.post('subjectTemplateConfig/updateStatus', body),
  // 编辑入口
  editSubjectTemplateConfig: (body: RequestBody) =>
    fetch.post('subjectTemplateConfig/updateEntry', body),
  // 新增/修改
  saveSubjectTemplateConfig: (body: RequestBody) => fetch.post('subjectTemplateConfig/save', body),
  // 删除
  delSubjectTemplateConfig: (body: RequestBody) => fetch.post('subjectTemplateConfig/delete', body),
  // 操作日志管理
  getOperationLogList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('admin_log/list', body, request),
  // 阅读偏好列表查询接口
  getHobbyList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('hobby/list', body, request),
  // 阅读偏好创建接口
  createHobby: (body: RequestBody) => fetch.post('hobby/create', body),
  // 阅读偏好内容更新接口
  updateHobby: (body: RequestBody) => fetch.post('hobby/update', body),
  // 阅读偏好内容上下架接口
  updateHobbyStatus: (body: RequestBody) => fetch.post('hobby/update_status', body),
  // 阅读偏好内容删除接口
  deleteHobby: (body: RequestBody) => fetch.post('hobby/delete', body),
  // 阅读偏好内容-排序接口
  updateHobbySort: (body: RequestBody) => fetch.post('hobby/update_sort', body),
  // UGC稿件状态变更
  manuallyUgcStatus: (body: RequestBody) => fetch.post('ugc_article/manually_change_status', body),
  // 助推卡列表
  getUGCCardList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ugc/encourage/card/list', body, request),
  // 发放助推卡
  grantCard: (body: RequestBody) => fetch.post('ugc/encourage/card/grant', body),
  // 设为失效
  setCardInvalid: (body: RequestBody) => fetch.post('ugc/encourage/card/invalid', body),
  // 助推内容列表
  getArticleList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ugc/encourage/card/article_list', body, request),
  // 助推详情
  getBoostDetail: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ugc/encourage/card/article', body, request),
  // 终止助推
  stopCard: (body: RequestBody) => fetch.post('ugc/encourage/card/stop', body),
  // 审核长视频列表
  getLongVideoDurationList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ugc/encourage/permissions/list', body, request),
  // 审核长视频
  permissionsReview: (body: RequestBody) => fetch.post('ugc/encourage/permissions/review', body),

  // App配置项配置列表信息
  getAppConfigList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('app_config/list', body, request),
  // App配置项创建App配置
  createAppConfig: (body: RequestBody) => fetch.post('app_config/create', body),
  // App配置项删除App配置
  deleteAppConfig: (body: RequestBody) => fetch.post('app_config/delete', body),
  // App配置项创建App配置
  updateAppConfig: (body: RequestBody) => fetch.post('app_config/update', body),
  // App配置项上下架App配置
  updateAppConfigStatus: (body: RequestBody) => fetch.post('app_config/update_status', body),

  // 导航头部样式设置接口
  updateNavHeadStyle: (body: RequestBody) => fetch.post('app_nav/head_style_set', body),
  // 导航头部样式查询接口
  queryNavHeadStyle: (body: RequestBody) => fetch.get('app_nav/query_head_style', body),

  // 短链接
  getShortURLList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('short_url/list', body, request),
  generateShortUrl: (body: RequestBody) => fetch.post('short_url/create', body),
  deleteShortUrl: (body: RequestBody) => fetch.post('short_url/delete', body),

  getAIChannelList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('channel/ai/list', body, request),
  updateAiChannelOrder: (body: RequestBody) => fetch.post('channel/ai/order', body),
  editAiChannel: (body: RequestBody) => fetch.post('channel/ai/edit', body),
  updateAiChannelStatus: (body: RequestBody) => fetch.post('channel/ai/online', body),
};
