/* eslint-disable import/prefer-default-export */
import { RequestBody, RequestInfo } from '@app/types';
import fetch from '@app/utils/fetch';

export const findApi = {
  // 影视管理
  // 获取影视列表
  getMediaList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('media/list', body, request),
  
  // 创建影视
  createMedia: (body: RequestBody) => fetch.post('media/create', body),
  
  // 更新影视
  updateMedia: (body: RequestBody) => fetch.post('media/update', body),
  
  // 删除影视
  deleteMedia: (body: RequestBody) => fetch.post('media/delete', body),
  
  // 影视上下架
  updateMediaOnline: (body: RequestBody) => fetch.post('media/online', body),
  
  // 影视排序
  updateMediaOrder: (body: RequestBody) => fetch.post('media/order', body),
  
  // 获取影视详情
  getMediaDetail: (body: RequestBody) => fetch.get('media/detail', body),
  
  // 编辑影视（创建或更新）
  editMedia: (body: RequestBody) => fetch.json('media/edit', body),

  // 影视资源管理
  // 获取影视资源列表
  getMediaItemList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('media/item/list', body, request),
  
  // 编辑影视资源（创建或更新）
  editMediaItem: (body: RequestBody) => fetch.json('media/item/edit', body),
  
  // 影视子集上下架
  updateMediaItemOnline: (body: RequestBody) => fetch.post('media/item/online', body),
  
  // 删除影视子集
  deleteMediaItem: (body: RequestBody) => fetch.post('media/item/delete', body),
  
  // 影视子集排序
  updateMediaItemOrder: (body: RequestBody) => fetch.post('media/item/order', body),

  // 批量添加影视资源
  batchAddMediaItems: (body: RequestBody) => fetch.post('media/item/batch_add', body),

  // 影视首页管理
  // 获取重点推荐列表
  getKeyRecommendList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('film_tv_key_recommend/list', body, request),
  
  // 创建重点推荐
  createKeyRecommend: (body: RequestBody) => fetch.post('film_tv_key_recommend/create', body),
  
  // 更新重点推荐
  updateKeyRecommend: (body: RequestBody) => fetch.post('film_tv_key_recommend/update', body),
  
  // 删除重点推荐
  deleteKeyRecommend: (body: RequestBody) => fetch.post('film_tv_key_recommend/delete', body),
  
  // 更新重点推荐状态
  updateKeyRecommendStatus: (body: RequestBody) => fetch.post('film_tv_key_recommend/update_status', body),
  
  // 重点推荐排序
  sortKeyRecommend: (body: RequestBody) => fetch.post('film_tv_key_recommend/sort', body),
  
  // 更新自动轮播状态
  updateAutoPlayStatus: (body: RequestBody) => fetch.post('film_tv_key_recommend/update_auto_play', body),

  // 获取推荐位列表
  getRecommendPositionList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('content_recommend/query_list', body, request),
  
  // 获取推荐位详情
  getRecommendPositionDetail: (body: RequestBody) => fetch.get('content_recommend/detail', body),
  // 创建推荐位
  createRecommendPosition: (body: RequestBody) => fetch.post('content_recommend/create', body),
  
  // 更新推荐位
  updateRecommendPosition: (body: RequestBody) => fetch.post('content_recommend/update', body),
  
  // 删除推荐位
  deleteRecommendPosition: (body: RequestBody) => fetch.post('content_recommend/delete', body),
  
  // 更新推荐位状态
  updateRecommendPositionStatus: (body: RequestBody) => fetch.post('content_recommend/update_status', body),
  
  // 推荐位排序
  sortRecommendPosition: (body: RequestBody) => fetch.post('content_recommend/move_position', body),

  // 获取热门影视列表
  getHotFilmList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('media/hot/list', body, request),
  
  // 更新热门影视信息
  updateHotFilm: (body: RequestBody) => fetch.post('film_tv_hot/update', body),
  
  // 更新热门影视状态
  updateHotFilmStatus: (body: RequestBody) => fetch.post('film_tv_hot/update_status', body),
  
  // 影视轮播图相关接口
  // 获取影视轮播图详情
  getMediaCarouselDetail: (body: RequestBody) => fetch.get('media/carousel/detail', body),
  
  // 编辑影视轮播图
  editMediaCarousel: (body: RequestBody) => fetch.json('media/carousel/edit', body),

  // 影视强国华数专区相关接口
  // 获取专区推荐详情
  getPromotionDetail: (body: RequestBody) => fetch.get('media/promotion/detail', body),
  
  // 编辑专区推荐
  editPromotion: (body: RequestBody) => fetch.json('media/promotion/edit', body),
}; 