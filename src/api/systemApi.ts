import { RequestBody, IResLoginData, IResNoticeViewData, IResUploadData } from '@app/types';
import fetch from '@app/utils/fetch';

export const systemApi = {
  debugLogin: (body: RequestBody) => fetch.post<IResLoginData>('login', body),
  login: (body: RequestBody) => fetch.post<IResLoginData>('sso/login', body),
  logout: () => fetch.get<{}>('logout'),
  rolling: () => fetch.get<{}>('system/rolling'),
  getNotice: () => fetch.get<IResNoticeViewData>('admin_notice/view'),
  uploadImg: (body: RequestBody) => fetch.post<IResUploadData>('file/upload', body),
  upload: (body: RequestBody) => fetch.post<IResUploadData>('file/upload', body),
  phoneUpload: (body: RequestBody) => fetch.post<IResUploadData>('push_notify/upload', body),
  gerVideoUploadUrl: (body: RequestBody) =>
    fetch.post<IResUploadData>('file/create_upload_url', body),
  getAllChannels: () => fetch.get('channel/list_all'),
  getAllChannelsTree: () => fetch.get('channel/list_tree'),
  getHotNewsList: (body: RequestBody) => fetch.get('article_metadata/hot_list', body),
};
