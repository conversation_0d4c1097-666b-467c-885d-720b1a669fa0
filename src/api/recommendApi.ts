/* eslint-disable import/prefer-default-export */
import {
    RequestBody,
    RequestInfo,
    IResReleaseListAllData,
    IResFocusListAllData,
    IResChannelRecommendAllData,
    IOperationLogRes,
    TChannelRecommendArticleListRes,
    TChannelRecommendDetailRes,
    IResTopNewsListAllData,
  } from '@app/types';
  import fetch from '@app/utils/fetch';
  import {
    TUserDeletedNewsResData,
    TUserDeletedListFilter,
    TUserComplaintListFilter,
    TUserComplaintNewsResData,
    TUserComplaintTag,
    TListArticleRecommendAllData,
  } from '@app/views/news/news';
  
  export const recommendApi = {
    // 保存推荐置顶接口
    saveTopList: (body: RequestBody) => fetch.post('recommend/update', body),
    // 活动项目列表数据展示
    getToppingManuscriptList: (body: RequestBody, request?: RequestInfo) =>
      fetch.get('channel_recommend/list_top_recommend', body, request),
      // 获取频道下稿件列表
    getTopicNewsList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('channel_article/search_of_channel', body, request),
    // 焦点图列表接口
    getChannelFocusList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('channel_focus/list', body, request),
    //搜索稿件列表
    searchManuscript: (body: RequestBody) => fetch.get('channel_article/search_channel_article', body),
  };
  