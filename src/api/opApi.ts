/* eslint-disable import/prefer-default-export */
import { IOperationLogRes, RequestBody, RequestInfo } from '@app/types';
import fetch from '@app/utils/fetch';
import { ProposalAllData, TopicAllData } from '@app/views/operates/operates';

export const opApi = {
  // 推送
  getPushNotifyList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('push_notify/list', body, request),
  rePushNotify: (body: RequestBody) => fetch.post('push_notify/push', body),
  getPushNotifyDetail: (body: RequestBody) => fetch.get('push_notify/detail', body),
  deletePushNotify: (body: RequestBody) => fetch.post('push_notify/delete', body),
  createPushNotify: (body: RequestBody) => fetch.post('push_notify/create', body),
  updatePushNotify: (body: RequestBody) => fetch.post('push_notify/update', body),
  getPushAreaList: (body: RequestBody = {}) => fetch.get('area/list_area_tree', body),
  getPushNotify: (body: RequestBody) => fetch.get('push_notify/stat', body),
  getOperateLog: (body: RequestBody) => fetch.get<IOperationLogRes>('admin_log/type_list', body),

  // 敏感词
  getSensitiveWords: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('block_word/list', body, request),

  getSensitiveWords2: (body: RequestBody, request?: RequestInfo) =>
    fetch.post('block_word/check', body),
  deleteSensitiveWord: (body: RequestBody) => fetch.post('block_word/delete', body),

  createSensitiveWord: (body: RequestBody) => fetch.post('block_word/create', body),

  // 启动页
  getStartPageList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('app_start_page/list', body, request),
  setAdvShow: (body: RequestBody) => fetch.post('app_start_page/set_adv_show', body),
  deleteStartPage: (body: RequestBody) => fetch.post('app_start_page/delete', { ...body, type: 1 }),
  offStartPage: (body: RequestBody) => fetch.post('app_start_page/off', { ...body, type: 1 }),
  updateStartPage: (body: RequestBody) => fetch.post('app_start_page/update', { ...body, type: 1 }),
  createStartPage: (body: RequestBody) => fetch.post('app_start_page/create', { ...body, type: 1 }),
  getStartPageDetail: (body: RequestBody) => fetch.post('app_start_page/detail', body),
  addStartPageDevice: (body: RequestBody) =>
    fetch.post('app_start_page/save_start_page_image', body),
  updateStartPageDevice: (body: RequestBody) =>
    fetch.post('app_start_page/update_start_page_image', body),
  deleteStartPageDevice: (body: RequestBody) =>
    fetch.post('app_start_page/delete_start_page_image', body),
  updateCarouselTime: (body: RequestBody) =>
    fetch.post('app_start_page/update_carousel_time', body),
  updateStartPageOrder: (body: RequestBody) => fetch.post('app_start_page/exchange_order', body),
  updateDisplayRule: (body: RequestBody) => fetch.post('app_start_page/update_rule', body),

  // 关于管理
  getAboutDetail: () => fetch.get('about/detail'),
  updateAboutDetail: (body: RequestBody) => fetch.post('about/create', body),

  // 隐私政策
  getPrivacyDetail: () => fetch.get('hcontent/detail', { type: 2 }),
  updatePrivacyDetail: (body: RequestBody) => fetch.post('hcontent/update', { ...body, type: 2 }),

  // 白名单
  getWhiteHostList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('white_list/list', body, request),
  updateWhiteHostStatus: (body: RequestBody) => fetch.post('white_list/update_status', body),
  updateWhiteHost: (body: RequestBody) => fetch.post('white_list/update_host', body),
  deleteWhiteHost: (body: RequestBody) => fetch.post('white_list/delete', body),
  createWhiteHost: (body: RequestBody) => fetch.post('white_list/create', body),

  // app特殊功能
  getFeatureList: () => fetch.get('app_feature_switch/list'),
  updateFeatureStatus: (body: RequestBody) => fetch.post('app_feature_switch/update', body),
  getFeatureLogs: () => fetch.get('app_feature_switch/log_list'),
  createFeature: (body: RequestBody) => fetch.post('app_feature_switch/save', body),
  deleteFeature: (body: RequestBody) => fetch.post('app_feature_switch/delete', body),
  editFeature: (body: RequestBody) => fetch.post('app_feature_switch/edit', body),

  // 推荐位管理
  getProposalList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get<ProposalAllData>('type_recommend/list', body, request),
  createProposal: (body: RequestBody) => fetch.post('type_recommend/create', body),
  updateProposal: (body: RequestBody) => fetch.post('type_recommend/update', body),
  sortProposal: (body: RequestBody) => fetch.post('type_recommend/update_sort', body),
  deleteProposal: (body: RequestBody) => fetch.post('type_recommend/delete', body),
  updateProposalStatus: (body: RequestBody) => fetch.post('type_recommend/update_status', body),
  getIndexProposalDetail: (body: RequestBody) => fetch.get('type_recommend/view_float_win', body),

  // 主题管理
  getThemeList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get<ProposalAllData>('app/theme/list', body, request),
  // 主题排序
  updateThemeSort: (body: RequestBody) => fetch.post('app/theme/update_sort', body),
  // 新增主题
  addTheme: (body: RequestBody, request?: RequestInfo) =>
    fetch.json<ProposalAllData>('app/theme/add', body, request),
  // 主题详情
  detailTheme: (body: RequestBody) => fetch.get('app/theme/detail', body),
  // 编辑主题
  editTheme: (body: RequestBody, request?: RequestInfo) =>
    fetch.json<ProposalAllData>('app/theme/edit', body, request),
  // 上下架
  updateThemeStatus: (body: RequestBody) => fetch.post('app/theme/change_status', body),
  // 删除主题
  deleteTheme: (body: RequestBody) => fetch.post('app/theme/delete', body),
  // 主题提醒列表
  getThemeRemindList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get<ProposalAllData>('app/theme/remind/list', body, request),
  // 主题名称查询
  getThemeSimpleList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get<ProposalAllData>('app/theme/simple_list', body, request),
  // 添加提醒
  addThemeRemind: (body: RequestBody, request?: RequestInfo) =>
    fetch.post<ProposalAllData>('app/theme/remind/add', body, request),
  // 编辑提醒
  editThemeRemind: (body: RequestBody, request?: RequestInfo) =>
    fetch.post<ProposalAllData>('app/theme/remind/edit', body, request),
  // 主题提醒上下架
  updateThemeRemindStatus: (body: RequestBody) =>
    fetch.post('app/theme/remind/change_status', body),
  // 删除主题提醒
  deleteThemeRemind: (body: RequestBody) => fetch.post('app/theme/remind/delete', body),

  // 频道浮窗-列表查询接口
  getChannelFloatWindowList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('channel_win_recommend/list', body, request),
  // 频道浮窗创建接口
  createChannelFloatWindow: (body: RequestBody) => fetch.post('channel_win_recommend/create', body),
  // 频道浮窗-更新接口
  updateChannelFloatWindow: (body: RequestBody) => fetch.post('channel_win_recommend/update', body),
  // 频道浮窗-上下架接口
  updateChannelFloatWindowStatus: (body: RequestBody) =>
    fetch.post('channel_win_recommend/update_status', body),
  // 频道浮窗-删除接口
  deleteChannelFloatWindow: (body: RequestBody) => fetch.post('channel_win_recommend/delete', body),
  // 首页浮窗详情查询接口
  getChannelFloatWindowDetail: (body: RequestBody) =>
    fetch.get('channel_win_recommend/view_float_win', body),
  // 首页浮窗详情查询接口
  sortChannelFloatWindow: (body: RequestBody) =>
    fetch.post('channel_win_recommend/update_sort', body),

  // 导航推荐位
  getNavRecommendlList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('nav_recommend/list', body, request),
  editNavRecommend: (body: RequestBody) => fetch.post('nav_recommend/update', body),
  updateNavRecommend: (body: RequestBody) => fetch.post('nav_recommend/update_displayed', body),
  // 导航图标自定义
  getNavIconList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('navIconConfig/list', body, request),
  editNavIcons: (body: RequestBody) => fetch.post('navIconConfig/update', body),
  updateNaveIcons: (body: RequestBody) => fetch.post('navIconConfig/updateDisplayed', body),
  // 话题管理
  getTopicList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get<TopicAllData>('topic_label/list', body, request),
  getTopicNameList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get<TopicAllData>('topic_label/name_list', body, request),
  updateTopicStatus: (body: RequestBody) => fetch.post('topic_label/change_enable', body),
  getTopicClassList: () => fetch.get('topic_class/list'),
  createTopic: (body: RequestBody) => fetch.post('topic_label/create', body),
  updateTopic: (body: RequestBody) => fetch.post('topic_label/update', body),
  sortTopic: (body: RequestBody) => fetch.post('topic_label/order', body),
  deleteTopic: (body: RequestBody) => fetch.post('topic_label/delete', body),

  // 栏目管理
  getColumnList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get<TopicAllData>('topic_label_column/list', body, request),
  createColumn: (body: RequestBody) => fetch.post('topic_label_column/create', body),
  updateColumn: (body: RequestBody) => fetch.post('topic_label_column/update', body),
  updateColumnStatus: (body: RequestBody) => fetch.post('topic_label_column/change_enable', body),
  deleteColumn: (body: RequestBody) => fetch.post('topic_label_column/delete', body),
  sortColumn: (body: RequestBody) => fetch.post('topic_label_column/order', body),
  moveColumnLabelColumn: (body: RequestBody) => fetch.post('topic_label_column/move_position', body),
  // 话题审核
  getTopicAuditList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ugc_topic_audit/list', body, request),
  deleteTopicAudit: (body: RequestBody) => fetch.post('ugc_topic_audit/delete', body),
  passTopicAudit: (body: RequestBody) => fetch.post('ugc_topic_audit/pass', body),

  // UGC话题推荐
  getRecommendTopicList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ugc_topic_recommend/list', body, request),
  sortRecommendTopic: (body: RequestBody) => fetch.post('type_recommend/update_sort', body),
  sortPositionRecommendTopic: (body: RequestBody) =>
    fetch.post('type_recommend/update_sort_position', body),
  updateRecommendTopicStatus: (body: RequestBody) =>
    fetch.post('ugc_topic_recommend/update_status', body),
  deleteRecommendTopic: (body: RequestBody) => fetch.post('type_recommend/delete', body),
  searchTopicByName: (body: RequestBody) => fetch.get('ugc_topic_recommend/search_name', body),
  createRecommendTopic: (body: RequestBody) => fetch.post('ugc_topic_recommend/create', body),
  getTopicRankList: (body: RequestBody) => fetch.get('ugc_topic_recommend/rank_list', body),

  // 热议话题
  getHotRecommendTopicList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('recommend_hot_topic/list', body, request),
  createHotRecommendTopic: (body: RequestBody) => fetch.post('recommend_hot_topic/create', body),
  updateHotRecommendTopic: (body: RequestBody) => fetch.post('recommend_hot_topic/update', body),
  deleteHotRecommendTopic: (body: RequestBody) => fetch.post('recommend_hot_topic/delete', body),
  updateHotRecommendTopicStatus: (body: RequestBody) =>
    fetch.post('recommend_hot_topic/update_status', body),
  sortHotRecommendTopic: (body: RequestBody) => fetch.post('recommend_hot_topic/sort', body),
  getHotRecommendTopicDetail: (body: RequestBody) =>
    fetch.get('recommend_hot_topic/detail', body),
  publishHotTopic: (body: RequestBody) => fetch.post('recommend_hot_topic/publish', body),

  // 话题下稿件管理
  getTopicArticleList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('topic_label/ugc_article_list', body, request),
  getTopicArticleRetrievedList: (body: RequestBody) =>
    fetch.get('ugc_article/retrieved_list', body),
  getNewsTopicArticleList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('topic_label/pgc_article_list', body, request),
  sortNewsTopicArticle: (body: RequestBody) => fetch.post('topic_label/exchange_order', body),
  removeArticleFromTopic: (body: RequestBody) => fetch.post('topic_label/remove_ugc_article', body),

  // 话题分类管理
  getTopicCategoryList: (body?: RequestBody, request?: RequestInfo) =>
    fetch.get('topic_class/list', body, request),
  createTopicCategory: (body: RequestBody) => fetch.post('topic_class/create', body),
  deleteTopicCategory: (body: RequestBody) => fetch.post('topic_class/delete', body),

  // 贴纸管理
  getStickerList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ugc_sticker/list', body, request),
  createSticker: (body: RequestBody) => fetch.post('ugc_sticker/create', body),
  deleteSticker: (body: RequestBody) => fetch.post('ugc_sticker/delete', body),
  updateSticker: (body: RequestBody) => fetch.post('ugc_sticker/update', body),
  sortSticker: (body: RequestBody) => fetch.post('ugc_sticker/exchange_order', body),

  // 红包管理
  getRedPacketList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('redPacketActivity/list', body, request),
  updateRedPacketSwitch: (body: RequestBody) =>
    fetch.post('web_feature/switch_save', { ...body, feature: 'red_packet_switch' }),
  getRedPacketSwitch: () => fetch.get('web_feature/detail', { feature: 'red_packet_switch' }),
  getRedPacketEntry: () => fetch.get('red_packet_recommend/detail'),
  updateRedPacketEntry: (body: RequestBody) => fetch.post('red_packet_recommend/save', body),
  updateRedPacketStatus: (body: RequestBody) => fetch.post('redPacketActivity/updateStatus', body),
  createRedPacket: (body: RequestBody) => fetch.post('redPacketActivity/save', body),
  updateRedPacket: (body: RequestBody) => fetch.post('redPacketActivity/save', body),
  getQYRedPacketList: (body: RequestBody) => fetch.get('redPacketActivity/qyRedPacketList', body),
  getRedPacketDetail: (body: RequestBody) => fetch.get('redPacketActivity/updateEntry', body),
  getQYRedPacketDetail: (body: RequestBody) =>
    fetch.get('redPacketActivity/oneQyRedPacketActivity', body),
  // 音乐管理
  getMusicList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('music/list', body, request),
  createMusic: (body: RequestBody) => fetch.post('music/create', body),
  updateMusic: (body: RequestBody) => fetch.post('music/update', body),
  deleteMusic: (body: RequestBody) => fetch.post('music/delete', body),
  exchangeMusicOrder: (body: RequestBody) => fetch.post('music/exchange_order', body),

  // 音乐分类管理
  getMusicCategoryList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('music/class_list', body, request),
  createMusicCategory: (body: RequestBody) => fetch.post('music/class_create', body),
  updateMusicCategory: (body: RequestBody) => fetch.post('music/class_update', body),
  deleteMusicCategory: (body: RequestBody) => fetch.post('music/class_delete', body),
  exchangeMusicCategoryOrder: (body: RequestBody) => fetch.post('music/class_exchange_order', body),

  // 1.5.3 红包
  getRepacketDataList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('data_balance/redpacket_data_detail_list', body, request),
  getUserMoneyList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('data_balance/account_money_detail_list', body, request),
  getUserMoneyDetail: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('data_balance/finance_data_detail_list', body, request),
  getPacketTotalList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('data_balance/finance_data_total_list', body, request),
  downloadUserMoneyDetail: (body: RequestBody, request?: RequestInfo) =>
    fetch.blob('data_balance/finance_data_detail_list', body, request),

  // 活动管理
  getActivityList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('activity/list', body, request),
  createActivity: (body: RequestBody) => fetch.post('activity/create', body),
  updateActivity: (body: RequestBody) => fetch.post('activity/update', body),
  deleteActivity: (body: RequestBody) => fetch.post('activity/delete', body),
  updateActivityStatus: (body: RequestBody) => fetch.post('activity/change_enabled', body),
  updateActivityTop: (body: RequestBody) => fetch.post('activity/change_top', body),

  // 热搜词
  getHotWordList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('hot_word/list', body, request),
  sortHotWord: (body: RequestBody) => fetch.post('hot_word/exchange', body),
  createHotWord: (body: RequestBody) => fetch.post('hot_word/create', body),
  updateHotWord: (body: RequestBody) => fetch.post('hot_word/update', body),
  deleteHotWord: (body: RequestBody) => fetch.post('hot_word/delete', body),
  updateHotWordStatus: (body: RequestBody) => fetch.post(`hot_word/${body.status}`, body),
  onekeyUpdateHotWord: (body: RequestBody) => fetch.post('hot_word/onekey_update', body),

  // 热点新闻 潮鸣号内容推荐
  getHotNewsList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('recommend_article/list', body, request),
  setHotNewsVisible: (body: RequestBody) => fetch.post('recommend_article/set_visible', body),
  exchangeHotNewsOrder: (body: RequestBody) => fetch.post('recommend_article/exchange_order', body),
  updateHotNewsSort: (body: RequestBody) => fetch.post('recommend_article/move_position', body),

  // 成就勋章管理
  getMedalList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('achievement_medal/list', body, request),
  createMedal: (body: RequestBody) => fetch.post('achievement_medal/create', body),
  updateMedal: (body: RequestBody) => fetch.post('achievement_medal/update', body),
  deleteMedal: (body: RequestBody) => fetch.post('achievement_medal/delete', body),
  updateMedalStatus: (body: RequestBody) => fetch.post('achievement_medal/update_status', body),
  grantMedal: (body: RequestBody) => fetch.post('achievement_medal/grant', body),

  // 2.0.1 每日签到
  getSignList: (body: RequestBody, request?: RequestInfo) => fetch.get('sign/list', body, request),
  createSign: (body: RequestBody) => fetch.post('sign/create', body),
  updateSign: (body: RequestBody) => fetch.post('sign/update', body),
  updateSignStatus: (body: RequestBody) => fetch.post('sign/update_status', body),
  deleteSign: (body: RequestBody) => fetch.post('sign/delete', body),

  // AR事件
  getArSceneList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ar_content/list', body, request),
  createArScene: (body: RequestBody) => fetch.post('ar_content/save', body),
  updateArScene: (body: RequestBody) => fetch.post('ar_content/edit', body),
  getArSceneDetail: (body: RequestBody) => fetch.get('ar_content/detail', body),
  deleteArScene: (body: RequestBody) => fetch.post('ar_content/delete', body),
  updateArSceneStatus: (body: RequestBody) => fetch.post('ar_content/update_status', body),
  // 电影消费券
  creatMovieRedeemCode: (body: RequestBody) => fetch.post('movie_redeem_code/create', body),
  updateMovieRedeemCode: (body: RequestBody) => fetch.post('movie_redeem_code/update', body),
  getMovieRedeemCodeList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('movie_redeem_code/code_list', body, request),
  updateTypeMovieRedeemCode: (body: RequestBody) =>
    fetch.post('movie_redeem_code/update_redeem_code_type', body),
  getMovieRedeemCodeInfo: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('movie_redeem_code/get_redeem_code_info', body, request),

  creatMovieTicketActivity: (body: RequestBody) => fetch.post('movie_ticket_activity/create', body),
  updateMovieTicketActivity: (body: RequestBody) =>
    fetch.post('movie_ticket_activity/update', body),
  getMovieTicketActivityList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('movie_ticket_activity/activity_list', body, request),
  updateTypeMovieTicketActivity: (body: RequestBody) =>
    fetch.post('movie_ticket_activity/update_activity_type', body),

  updateMovieActivityRule: (body: RequestBody) => fetch.post('movie_activity_rule/update', body),
  getMovieActivityRuleList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('movie_activity_rule/rule_list', body, request),

  getMovieCouponUserList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('movie_coupon_user/coupon_list', body, request),

  creatMovieActivityRule: (body: RequestBody) => fetch.post('movie_activity_rule/create', body),
  // 首页广告管理
  getAppadvertpageList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('app_advert_page/list', body, request),
  creatAppadvertpage: (body: RequestBody) => fetch.post('app_advert_page/create', body),
  updateAppadvertpage: (body: RequestBody) => fetch.post('app_advert_page/update', body),
  deleteAppadvertpage: (body: RequestBody) => fetch.post('app_advert_page/delete', body),
  updatedisplayedAppadvertpage: (body: RequestBody) =>
    fetch.post('app_advert_page/update_displayed', body),
  // 商业广告管理
  getCommercialAdList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('advertise/list', body, request),
  // 下架
  downCommercialAd: (body: RequestBody) => fetch.post('advertise/offline', body),
  // 商业广告编码列表
  getCommercialAdCodeList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('advertise/space/list', body, request),
  // 广告位新增接口
  addCommercialAdCode: (body: RequestBody) => fetch.post('advertise/space/add', body),
  // 广告位修改接口
  editCommercialAdCode: (body: RequestBody) => fetch.post('advertise/space/edit', body),
  // 删除广告
  deleteCommercialAdCode: (body: RequestBody) => fetch.post('advertise/space/delete', body),

  // 话题置顶稿件
  // 获取列表
  getTopManuscriptList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('topic_label_article_top/list', body, request),
  // 排序
  sortTopManuscript: (body: RequestBody) => fetch.post('topic_label_article_top/update_sort', body),
  // 新建
  creatTopManuscript: (body: RequestBody) => fetch.post('topic_label_article_top/save', body),
  // 取消置顶
  cancelTopManuscript: (body: RequestBody) => fetch.post('topic_label_article_top/cancel', body),
  // 获取话题下稿件列表
  getTopicNewsList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('topic_label/ugc_article_list', body, request),

  // 长视频管理
  // 删除
  deleteLongVideo: (body: RequestBody) => fetch.post('long_video_duration/delete', body),

  // 长视频列表
  getLongVideoList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('long_video_duration/list', body, request),
  // 添加
  addLongVideo: (body: RequestBody) => fetch.post('long_video_duration/save', body),
  // 长视用户列表
  getLongVideoUserList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('long_video_duration/account_list', body, request),
  // 长视频权限组列表
  getSimpleList: () => fetch.get('long_video_duration/simple_list'),
  // 删除用户
  deleteLongVideoUser: (body: RequestBody) =>
    fetch.post('long_video_duration/delete_account', body),
  // 添加用户
  addLongVideoUser: (body: RequestBody) => fetch.post('long_video_duration/save_account', body),
  // 下拉用户列表
  getUserList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('account/list', body, request),

  // UGC标签列表管理
  getUGCTagList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ugc_group/list', body, request),
  // UGC标签保存
  saveUGCTag: (body: RequestBody) => fetch.post('ugc_group/save', body),
  // UGC标签展示状态
  updateUGCTag: (body: RequestBody) => fetch.post('ugc_group/update_status', body),
  // UGC标签调整排序
  sortUGCTag: (body: RequestBody) => fetch.post('ugc_group/update_sort', body),
  // UGC标签稿件列表
  getUGCTagArticleList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ugc_group/article_list', body, request),

  // 话题下的稿件列表置顶
  topicArticleTop: (body: RequestBody) => fetch.post('topic_label/article_top', body),
  // 话题下的稿件列表取消置顶
  topicArticleUnTop: (body: RequestBody) => fetch.post('topic_label/article_un_top', body),
  // 活动排序
  activitySort: (body: RequestBody) => fetch.post('activity/update_sort', body),

  // 取签稿件查询接口
  getRetrievedList: (body: RequestBody) => fetch.get('ugc_article/retrieved_list', body),

  // 广告位选择列表接口
  getAdCodeList: (body: RequestBody) => fetch.get('advertise/space/code_list', body),
  // 指尖读报
  getFingerPaperList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('finger_paper/list', body, request),
  // 早晚报
  getReadPaperList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('read_paper/list', body, request),
  // 早晚报新增
  createReadPaper: (body: RequestBody) => fetch.json('read_paper/create', body),
  // 早晚报更新
  updateReadPaper: (body: RequestBody) => fetch.json('read_paper/update', body),
  // 早晚报详情
  detailReadPaper: (body: RequestBody) => fetch.get('read_paper/detail', body),
  // 早晚报删除
  deleteReadPaper: (body: RequestBody) => fetch.post('read_paper/delete', body),
  // 早晚报上架下架
  updateStatusReadPaper: (body: RequestBody) => fetch.post('read_paper/update_status', body),
  // 早晚报推送
  pushStatusReadPaper: (body: RequestBody) => fetch.post('read_paper/push', body),
  // 操作日志
  readPagerLogs: (body: RequestBody) => fetch.get('read_paper/log', body),

  createFingerPager: (body: RequestBody) => fetch.json('finger_paper/create', body),
  // 指尖读报更新
  updateFingerPager: (body: RequestBody) => fetch.json('finger_paper/update', body),
  // 指尖读报列表删除
  delFingerPager: (body: RequestBody) => fetch.post('finger_paper/delete', body),
  // 指尖读报详情
  detailFingerPager: (body: RequestBody) => fetch.get('finger_paper/detail', body),
  // 上架下架
  updateStatusFingerPager: (body: RequestBody) => fetch.post('finger_paper/update_status', body),
  // 通知
  pushFingerPager: (body: RequestBody) => fetch.post('finger_paper/push', body),
  // 操作日志
  fingerPagerLogs: (body: RequestBody) => fetch.get('finger_paper/log', body),

  // 榜单列表
  getPublishRankingList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ranking/publish_list', body, request),
  getSourceRankingList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ranking/source_list', body, request),

  // 榜单城市列表
  getRankingCitysList: (body: RequestBody) => fetch.get('ranking/city_list', body),

  // 排序
  rankingSort: (body: RequestBody) => fetch.post('ranking/sort', body),
  // 添加稿件
  addRankingArticle: (body: RequestBody) => fetch.post('ranking/add', body),
  // 一键发布
  rankingPublish: (body: RequestBody) => fetch.post('ranking/publish', body),
  deleteRankingArticle: (body: RequestBody) => fetch.post('ranking/delete', body),
  rankingPublishRemind: (body: RequestBody) => fetch.get('ranking/publish_remind', body),

  // 音频
  getAudioPlazaArticleList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('recommend_audio_home/list', body, request),
  deleteAudioPlazaArticle: (body: RequestBody) => fetch.post('recommend_audio_home/delete', body),
  addAudioPlazaArticle: (body: RequestBody) => fetch.post('recommend_audio_home/create', body),
  audioArticleSort: (body: RequestBody) => fetch.post('recommend_audio_home/exchange_order', body),
  audioArticleMovePosition: (body: RequestBody) =>
    fetch.post('recommend_audio_home/move_position', body),
  addAudioPlazaColumn: (body: RequestBody) =>
    fetch.post('recommend_audio_home/top_column_save', body),
  getAudioPlazaColumn: (body: RequestBody) =>
    fetch.get('recommend_audio_home/top_column_detail', body),
  addAudioPlazaRedpacket: (body: RequestBody) =>
    fetch.post('recommend_audio_home/redpacket_save', body),
  getAudioPlazaRedpacket: (body: RequestBody) =>
    fetch.get('recommend_audio_home/redpacket_detail', body),
  saveBroadcast: (body: RequestBody) => fetch.post('recommend_audio_home/broadcast_save', body),
  getBroadcast: (body: RequestBody) => fetch.get('recommend_audio_home/broadcast_detail', body),

  // 智能助手 问答查询列表接口
  getGPTQuestionList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('gpt_question/list', body, request),
  deleteGPTQuestion: (body: RequestBody) => fetch.post('gpt_question/delete', body),
  updateGPTQuestionStatus: (body: RequestBody) => fetch.post('gpt_question/update_status', body),
  gptQuestionWelcomeQuery: (body: RequestBody) => fetch.get('gpt_question/welcome_query', body),
  gptQuestionWelcomeSave: (body: RequestBody) => fetch.post('gpt_question/welcome_save', body),

  questionSort: (body: RequestBody) => fetch.post('gpt_question/update_sort', body),
  gptQuestionDetail: (body: RequestBody) => fetch.get('gpt_question/detail', body),

  questionCreate: (body: RequestBody) => fetch.post('gpt_question/create', body),
  questionUpdate: (body: RequestBody) => fetch.post('gpt_question/update', body),
  questionDelete: (body: RequestBody) => fetch.post('gpt_question/delete', body),

  // 24H 客厅
  updateGpt24hmeetSwitch: (body: RequestBody) =>
    fetch.post('web_feature/switch_save', { ...body, feature: 'gpt_24hmeet_switch' }),
  getGpt24hmeetSwitch: () => fetch.get('web_feature/detail', { feature: 'gpt_24hmeet_switch' }),

  // 底部推荐位
  getBottomRecommendList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('bottom_recommend/list', body, request),
  bottomRecommendCreate: (body: RequestBody) => fetch.post('bottom_recommend/create', body),
  bottomRecommendUpdate: (body: RequestBody) => fetch.post('bottom_recommend/update', body),
  bottomRecommendDelete: (body: RequestBody) => fetch.post('bottom_recommend/delete', body),

  getSubjectRecommendList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('subject_recommend/list', body, request),
  updateSubjectRecommendStatus: (body: RequestBody) =>
    fetch.post('subject_recommend/update_status', body),
  deleteSubjectRecommend: (body: RequestBody) => fetch.post('subject_recommend/delete', body),

  // 添加稿件
  addPartyNews: (body: RequestBody) => fetch.post('subject_recommend/create', body),
  updatePartyNews: (body: RequestBody) => fetch.post('subject_recommend/update', body),
  partyNewsDetail: (body: RequestBody) => fetch.get('subject_recommend/detail', body),

  // 阅读榜详情
  getBookRankList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('recommend_books/list', body, request),
  addBookRank: (body: RequestBody) => fetch.post('recommend_books/create', body),
  updateBookRank: (body: RequestBody) => fetch.post('recommend_books/update', body),
  bookRankDetail: (body: RequestBody) => fetch.get('recommend_books/detail', body),
  updateBookRankStatus: (body: RequestBody) => fetch.post('recommend_books/update_status', body),
  deleteBookRank: (body: RequestBody) => fetch.post('recommend_books/delete', body),

  getGptStyleDetail: (body: RequestBody) => fetch.get('gpt_question/style_detail', body),
  saveGptStyle: (body: RequestBody) => fetch.post('gpt_question/style_save', body),

  getHudongAvatarList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('recommend_hudong/list', body, request),
  delHudongAvatar: (body: RequestBody) => fetch.post('recommend_hudong/delete', body),
  updateHudongAvatarStatus: (body: RequestBody) =>
    fetch.post('recommend_hudong/update_status', body),
  gptHudongConfig: (body: RequestBody) => fetch.get('recommend_hudong/config_detail', body),

  saveHudongAvatarConfig: (body: RequestBody) => fetch.post('recommend_hudong/config_save', body),

  getHudongAvatarDetail: (body: RequestBody) => fetch.get('recommend_hudong/detail', body),
  saveHudongAvatar: (body: RequestBody) => fetch.post('recommend_hudong/create', body),
  editHudongAvatar: (body: RequestBody) => fetch.post('recommend_hudong/update', body),

  getPushCount: (body: RequestBody) => fetch.get('push_notify/circle_label_count', body),
  getDurationUserList: (body: RequestBody) => fetch.get('long_video_duration/account_list', body),

  getAiFeedbackList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ai/create/feedback/list', body, request),

  getAudioZwbSwitch: () => fetch.get('web_feature/detail', { feature: 'audio_zwb_switch' }),
  updateAudioZwbSwitch: (body: RequestBody) => fetch.post('web_feature/switch', body),

  mergeAudio: (body: RequestBody) => fetch.post('read_paper/merge_audios', body),

  // 创作中心轮播图
  getCreativeCenterBannerList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('create/recommend/list', body, request),
  deleteCreativeCenterBanner: (body: RequestBody) => fetch.post('create/recommend/delete', body),
  editCreativeCenterBanner: (body: RequestBody) => fetch.post('create/recommend/edit', body),
  sortCreativeCenterBanner: (body: RequestBody) => fetch.post('create/recommend/order', body),
  updateCreativeCenterBannerStatus: (body: RequestBody) =>
    fetch.post('create/recommend/online', body),
  // 创作中心创作指南
  getCreativeCenterGuideList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('create/guide/list', body, request),
  deleteCreativeCenterGuide: (body: RequestBody) => fetch.post('create/guide/delete', body),
  createCreativeCenterGuide: (body: RequestBody) => fetch.post('create/guide/create', body),
  sortCreativeCenterGuide: (body: RequestBody) => fetch.post('create/guide/order', body),

  // 数字主理人气泡详情
  gptQuestionBubbleDetail: (body: RequestBody) => fetch.get('gpt_question/dp_bubble_detail', body),
  gptQuestionBubbleSave: (body: RequestBody) => fetch.post('gpt_question/dp_bubble_save', body),
  //数字主理人功能详情
  gptQuestionConfigDetail: (body: RequestBody) => fetch.get('gpt_question/dp_config_detail', body),
  gptQuestionConfigSave: (body: RequestBody) => fetch.post('gpt_question/dp_config_save', body),

  gptHospitalAdvertiseDetail: (body: RequestBody) => fetch.get('type_recommend/chao_health/advertise_hospital_query', body),
  gptHospitalAdvertiseSave: (body: RequestBody) => fetch.json('type_recommend/chao_health/advertise_hospital_save', body),

  gptHospitalDetail: (body: RequestBody) => fetch.get('type_recommend/chao_health/recommend_hospital_query', body),
  gptHospitalSave: (body: RequestBody) => fetch.json('type_recommend/chao_health/recommend_hospital_save', body),

  //抽屉列表
  getDrawerList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('drawer/list', body, request),
  createDrawerEdit: (body: RequestBody) => fetch.post('drawer/edit', body),

  createDrawerSetConfig: (body: RequestBody) => fetch.post('drawer/set_config', body),
  getDrawerConfig: (body: RequestBody) => fetch.get('drawer/get_config', body),
  deleteDrawer: (body: RequestBody) => fetch.post('drawer/delete', body),
  onlineDrawer: (body: RequestBody) => fetch.post('drawer/online', body),
  drawerDetail: (body: RequestBody) => fetch.get('drawer/detail', body),

  // 栏目分类列表
  getColumnClassList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('topic_class/list', body, request),
  getColumnClassPGList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('topic_class/pg_list', body, request),
  sortColumnClass: (body: RequestBody) => fetch.post('topic_class/exchange_order', body),
  moveColumnClass: (body: RequestBody) => fetch.post('topic_class/move_position', body),
  deleteColumnClass: (body: RequestBody) => fetch.post('topic_class/delete', body),
  createColumnClass: (body: RequestBody) => fetch.post('topic_class/create', body),
  updateColumnClass: (body: RequestBody) => fetch.post('topic_class/update', body),

  // 私信列表
  getIMList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('im/list', body, request),

  getIMChatList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('im/chat_list', body, request),

  // 私信白名单列表
  getIMWhiteList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('im/whitelist/list', body, request),
  deleteIMWhiteList: (body: RequestBody) => fetch.post('im/whitelist/delete', body),
  addIMWhiteList: (body: RequestBody) => fetch.post('im/whitelist/create', body),


  // 私信黑名单列表
  getIMBlackList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('im/blacklist/list', body, request),
  cancelBlackUser: (body: RequestBody) => fetch.post('im/blacklist/cancel', body),
  addIMBlackUser: (body: RequestBody) => fetch.post('/im/blacklist/create', body),

  // 私信举报列表
  getIMComplainList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('im/complain/list', body, request),
  updateIMComplainStatus: (body: RequestBody) => fetch.post('im/complain/modify', body),

  secondFloorList: (body: RequestBody, request?: RequestInfo) => fetch.get('activity/second_floor_list', body, request),
  sortSecondFloor: (body: RequestBody) => fetch.post('activity/update_second_sort_number', body),

  // 潮圈首页
  // 获取潮圈首页轮播图
  getCircleBannerList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('recommend_circle_carousel/list', body, request),
  createCircleBanner: (body: RequestBody) => fetch.post('recommend_circle_carousel/create', body),
  updateCircleBanner: (body: RequestBody) => fetch.post('recommend_circle_carousel/update', body),
  deleteCircleBanner: (body: RequestBody) => fetch.post('recommend_circle_carousel/delete', body),
  sortCircleBanner: (body: RequestBody) => fetch.post('recommend_circle_carousel/update_sort', body),
  updateCircleBannerStatus: (body: RequestBody) => fetch.post('recommend_circle_carousel/update_status', body),
  getCircleBannerDetail: (body: RequestBody) => fetch.get('recommend_circle_carousel/detail', body),

  // 获取潮圈首页推荐位
  getCircleHomeRecommendList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('content_recommend/query_list', body, request),
  deleteCircleRecommend: (body: RequestBody) => fetch.post('content_recommend/delete', body),
  sortCircleRecommend: (body: RequestBody) => fetch.post('content_recommend/exchange_order', body),
  updateCircleRecommendStatus: (body: RequestBody) => fetch.post('content_recommend/update_status', body),
  moveCircleRecommend: (body: RequestBody) => fetch.post('content_recommend/move_position', body),

  // 获取潮圈首页热门内容
  getCircleHotList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('circle/hot_list', body, request),
  // 获取群聊列表
  getGroupChatList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('im/group/list', body, request),
  // 群聊添加
  createGroupChat: (body: RequestBody) => fetch.post('im/group/create', body),
  // 群聊编辑
  editGroupChat: (body: RequestBody) => fetch.post('im/group/update', body),
  // 群聊删除
  deleteGroupChat: (body: RequestBody) => fetch.post('im/group/delete', body),
  // 群聊排序
  sortGroupChat: (body: RequestBody) => fetch.post('im/group/order', body),
};
