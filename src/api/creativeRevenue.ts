/* eslint-disable import/prefer-default-export */
import { RequestBody, CommonResponse, RequestInfo } from '@app/types';
import fetch from '@app/utils/fetch';

export const creativeRevenueApi = {
  // 导出
  export: (body: RequestBody) => fetch.blob('earnings_manuscript_income/export_income', body),
  // 获取稿费评级列表
  getRatedList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('earnings_manuscript_income/list', body, request),

  // 新增指定稿费
  addFee: (body: RequestBody) => fetch.json('earnings_manuscript_income/add_manuscript', body),
  // 批量送审
  batchAudit: (body: RequestBody) => fetch.get('earnings_manuscript_income/send_for_review', body),
  // 批量评级
  batchUpdateFeeGrade: (body: RequestBody) =>
    fetch.get('earnings_manuscript_income/batch_update_fee_grade', body),
  // 删除指定稿费
  deleteFee: (body: RequestBody) => fetch.get('earnings_manuscript_income/delete', body),
  // 一审审核
  firstAudit: (body: RequestBody) => fetch.get('earnings_manuscript_income/first_review', body),
  // 二审审核
  secondAudit: (body: RequestBody) => fetch.get('earnings_manuscript_income/second_review', body),
  // 一审批量审核
  firstBatchAudit: (body: RequestBody) =>
    fetch.get('earnings_manuscript_income/batch_first_review', body),
  // 二审批量审核
  secondBatchAudit: (body: RequestBody) =>
    fetch.get('earnings_manuscript_income/batch_second_review', body),

  // 获取上榜详情
  getRankDetail: (body: RequestBody) => fetch.get('earnings_manuscript_income/rank/detail', body),
  // 操作日志
  getOperateLog: (body: RequestBody) => fetch.get('earnings_operation_log/income_list', body),


  // 待审核（一审） 提现申请列表
  withdrawPendingList: (body: RequestBody, request?: RequestInfo) => fetch.get('earnings_withdraw_request/list', body, request),
  // 待审核（二审） 提现申请列表
  withdrawPendingFirstPassedList: (body: RequestBody, request?: RequestInfo) => fetch.get('earnings_withdraw_request/first_passed_list', body, request),
  // 审核通过 提现申请列表
  withdrawPassedList: (body: RequestBody, request?: RequestInfo) => fetch.get('earnings_withdraw_request/already_passed_list', body, request),
  // 审核未通过 提现申请列表
  withdrawNotPassedList: (body: RequestBody, request?: RequestInfo) => fetch.get('earnings_withdraw_request/not_passed_list', body, request),
  // 提现收益明细
  withdrawDetail: (body: RequestBody) => fetch.get('earnings_withdraw_request/detail', body),
  // 导出 提现申请列表
  withdrawExport: (body: RequestBody) => fetch.blob('earnings_withdraw_request/request_export', body),
  // 批量标记失败
  withdrawBatchMarkFailed: (body: RequestBody) => fetch.post('earnings_withdraw_request/batch_mark_failed', body),
  // 批量重新打款
  withdrawBatchRetry: (body: RequestBody) => fetch.post('earnings_withdraw_request/batch_re_withdraw', body),
  // 批量通过/不通过（一审）
  withdrawBatchFirstReview: (body: RequestBody) => fetch.get('earnings_withdraw_request/first_review', body),
  // 批量通过/不通过（二审）
  withdrawBatchReview: (body: RequestBody) => fetch.get('earnings_withdraw_request/review', body),
  // 提现申请操作日志列表
  withdrawRequestList: (body: RequestBody) => fetch.get('earnings_operation_log/request_list', body),
  // 收益查询 资金明细
  earningsFundFlow: (body: RequestBody) => fetch.get('earnings_fund_flow/list', body),
};
