/* eslint-disable import/prefer-default-export */
import { RequestBody } from '@app/types';
import fetch from '@utils/fetch';

export const searchApi = {
  getToppingManuscriptList: (body: RequestBody) =>
    fetch.get('channel_recommend/list_top_recommend', body),
  recommendSearch: (body: RequestBody) => fetch.get('channel_article/search_of_channel', body),
  newsCardSearch: (body: RequestBody) =>
    fetch.get('channel_article/search_of_channel', { ...body, type: 1 }),
  newsCardTop30: (body: RequestBody) => fetch.get('channel_article/top_articles', body),
  articleTopSearch: (body: RequestBody) => fetch.get('channel_article/search', body),
  pushSearch: (body: RequestBody) => fetch.get('channel_article/search', body),
  pushSearch1: (body: RequestBody) => fetch.get('channel_article/search_article', body),
  redBoatTopSearch: (body: RequestBody) => fetch.get('channel_article/search_of_channel', body),
  MCNArticleTopSearch: (body: RequestBody) => fetch.get('channel_article/search_of_channel', body),
  listArticleRecommendSearch: (body: RequestBody) =>
    fetch.get('channel_article/search_channel_article', body),
  searchUGCTopic: (body: RequestBody) => fetch.get('ugc_topic_recommend/search_name', body),
  channelTopSearch: (body: RequestBody) =>
    fetch.get('channel_article/search_channel_article', body),
  searchRankItem: (body: RequestBody) => fetch.get('tmh_rank/rank_item_search', body),
  conmentSearch: (body: RequestBody) => fetch.get('channel_article/search_channel_article', body),
  topListSearch: (body: RequestBody) => fetch.get('channel_article/search_channel_article', body),
  recommendTopicSearchList: (body: RequestBody) =>
    fetch.get('ugc_topic_recommend/search_name', body),
  // 圈子内容搜索
  searchCircleArticle: (body: RequestBody) => fetch.get('circle/article/search', body),

  // 报料搜索
  searchRecommendReport: (body: RequestBody) => fetch.get('report/search', body),

  searchArticleVote: (body: RequestBody) => fetch.get('comment/vote_info', body),

  getOrgUserArticleList: (body: RequestBody) => fetch.get('ugc_article/release_list', body),
  // 影视搜索
  searchFilmTv: (body: RequestBody) => fetch.get('media/search', body),

  // 圈子列表
  getCircleList: (body: RequestBody) => fetch.get('circle/list', body),
  // 用户过审稿件搜索
  searchUserArticle: (body: RequestBody) => fetch.get('earnings_manuscript_income/search_article_earnings', body),
};
