/* eslint-disable import/prefer-default-export */
import { RequestBody, RequestInfo } from '@app/types';
import fetch from '@app/utils/fetch';

export const creatorApi = {
  // 创作者公告管理列表
  creatorNoticeList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get<{}>('creator_notice/list', body, request),
  // 创作者公告上下线
  creatorNoticeUpdateEnabled: (body: RequestBody) =>
  fetch.post<{}>('creator_notice/update_enabled', body),
  // 创作者公告删除
  creatorNoticeDelete: (body: RequestBody) =>
  fetch.post<{}>('creator_notice/delete', body),
  // 创作者公告排序
  creatorNoticeUpdateSort: (body: RequestBody) =>
  fetch.post<{}>('creator_notice/update_sort', body),
  // 编辑公告
  creatorNoticeUpdate: (body: RequestBody) =>
  fetch.post<{}>('/creator_notice/update', body),
  // 创建公告
  creatorNoticeCreate: (body: RequestBody) =>
  fetch.post<{}>('creator_notice/create', body),
  // 公告详情接口
  creatorNoticeDetail: (body: RequestBody) =>
  fetch.get<{}>('creator_notice/detail', body),

  // 图片配置接口
  reviewImageConfigList: (body: RequestBody, request?: RequestInfo) =>
  fetch.get<{}>('media/review/image/config/list', body, request),
  reviewImageConfigCreate: (body: RequestBody) =>
  fetch.post<{}>('media/review/image/config/save', body),
  reviewImageConfigDelete: (body: RequestBody) =>
  fetch.post<{}>('media/review/image/config/delete', body),

};
