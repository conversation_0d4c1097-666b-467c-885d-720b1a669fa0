import Router from '@app/router';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/lib/locale-provider/zh_CN';
import moment from 'moment';
import 'moment/locale/zh-cn';
import React from 'react';

import '@app/assets/index.scss';
import 'react-photo-view/dist/react-photo-view.css';

moment.locale('zh-cn');

class App extends React.PureComponent {
  render() {
    return (
      <ConfigProvider locale={zhCN}>
        <Router />
      </ConfigProvider>
    );
  }
}

export default App;
