import { listApi as api } from '@app/api';
import { RequestBody, TableFetchStatus, TableList, CommonObject } from '@app/types';
import { message } from 'antd';
import uuid from 'uuid/v4';
import { Dispatch } from 'redux';
import { AppState } from '@app/utils/configureStore';

export const SET_TABLE = 'SET_TABLE';
export const SET_FETCH_STATUS = 'SET_TABLE_FETCH_STATUS';
export const CLEAR_TABLE = 'CLEAR_TABLE';

export function setTableList(data: TableList) {
  return { data, type: SET_TABLE };
}

export function setFetchStatus(data: TableFetchStatus) {
  return { data, type: SET_FETCH_STATUS };
}

export function clearTableList() {
  return { type: CLEAR_TABLE };
}

// 获取列表数据函数 接收参数 请求方法  返回list主键名 请求体 回调函数（可选）
export function getTableList(apiFunc: keyof typeof api, dataIndex: string, filter: RequestBody,callBack: Function = ()=>{}) {
  return (dispatch: Dispatch, getState: () => AppState) => {
    const requestId = uuid();
    const timestamp = Date.now().toString();
    dispatch(
      setFetchStatus({
        requestId,
        loading: true,
      })
    );

    // TODO 兼容调用签名问题
    (
      api[apiFunc](filter, {
        requestId,
        timestamp,
      }) as any
    )
      .then((r: any) => {
        const { tableList } = getState();
        console.log(getState());
        if (tableList.requestId === r.requestId) {
          message.success('列表获取成功');
          if (r.data[dataIndex].records) {
            dispatch(
              setTableList({
                total: r.data[dataIndex].total,
                size: r.data[dataIndex].size,
                current: r.data[dataIndex].current,
                records: r.data[dataIndex].records,
                allData: r.data,
              })
            );
          } else {
            dispatch(
              setTableList({
                total: r.data[dataIndex].length,
                size: r.data[dataIndex].length,
                current: 1,
                records: r.data[dataIndex],
                allData: r.data,
              })
            );
          }
          dispatch(
            setFetchStatus({
              loading: false,
            })
          );
        } else {
          dispatch(
            setFetchStatus({
              loading: false,
            })
          );
        }
        if(callBack){
          callBack()
        }
      })
      .catch((e: any) => {
        console.error(e);
        dispatch(
          setFetchStatus({
            loading: false,
          })
        );
      });
  };
}
