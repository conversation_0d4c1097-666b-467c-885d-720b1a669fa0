/* eslint-disable prefer-promise-reject-errors */
import { RequestBody, RequestInfo, CommonResponse } from '@app/types';
import { message } from 'antd';
import fetch from 'isomorphic-fetch';
import { sha256 } from 'js-sha256';
import uuid from 'uuid/v4';
import { API_LOCATION, SALT } from './constants';

function roxFetch<T>(
  method: string,
  url: string,
  body: FormData | undefined | string,
  request: RequestInfo
): Promise<CommonResponse<T>> {
  const signature = sha256(`${request.requestId}&&${request.timestamp}&&${SALT}`);
  const signatureHeaders = new Headers({
    'X-REQUEST-ID': request.requestId,
    'X-TIMESTAMP': request.timestamp,
    'X-SIGNATURE': signature,
  });
  if (method === 'post' && typeof body === 'string') {
    signatureHeaders.append('Content-Type', 'application/json;charset=UTF-8');
  }
  return new Promise<CommonResponse<T>>((resolve, reject) => {
    const settings: { [key: string]: any } = {
      method,
      headers: signatureHeaders,
      credentials: 'include',
    };
    if (body) {
      settings.body = body;
    }
    fetch(`${API_LOCATION}/${url}`, settings as RequestInit)
      .then((response: any) => {
        if (request.blob) {
          const res2 = response.clone();
          response.blob().then((blob: any) => {
            if (blob.type === 'application/octet-stream') {
              resolve({ data: blob });
            } else {
              res2.json().then((json: any) => {
                if (json.code === 0) {
                  resolve({ data: json.data, requestId: request.requestId });
                } else {
                  message.error(json.message);
                  reject({ code: json.code, message: json.message });
                }
              });
            }
          });
        } else {
          response.json().then((json: any) => {
            console.log('xxxxxx', json, url)
            if (json.code === 0 || (url.indexOf('redpacket/yun_list') > -1 && json.code === 3001)) {
              resolve({ data: json.data, requestId: request.requestId });
            } else {
              if (url.includes('channel_article/mlf_edit_url') && json.code == 35023) {
                // 稿件锁定不进行错误提示
              } else {
                message.error(json.message || json.msg);
              }
              reject({ code: json.code, message: json.message || json.msg });
            }
          });
        }
      })
      .catch((error) => {
        // TODO: custom by platform
        message.error('请求异常，详情请查看Console');
        console.error(error);
        reject({ message: error });
      });
  });
}

const f = {
  get: <T>(
    url: string,
    body: RequestBody = {},
    request: RequestInfo = {
      requestId: uuid(),
      timestamp: Date.now().toString(),
    }
  ) => {
    let str = '';
    Object.keys(body).forEach((v) => {
      str = `${str}&${v}=${encodeURIComponent(body[v])}`;
    });
    str = str.substr(1);
    return roxFetch<T>('get', `${url}${str.length > 0 ? '?' : ''}${str}`, undefined, request);
  },
  post: <T>(
    url: string,
    body: RequestBody = {},
    request: RequestInfo = {
      requestId: uuid(),
      timestamp: Date.now().toString(),
    }
  ) => {
    const formData = Object.keys(body).length === 0 ? undefined : new FormData();
    Object.keys(body).forEach((v) => {
      if (formData) {
        formData.append(v, body[v]);
      }
    });
    return roxFetch<T>('post', `${url}`, formData, request);
  },
  json: <T>(
    url: string,
    body: RequestBody = {},
    request: RequestInfo = {
      requestId: uuid(),
      timestamp: Date.now().toString(),
    }
  ) => {
    return roxFetch<T>('post', `${url}`, JSON.stringify(body), request);
  },
  blob: <T>(
    url: string,
    body: RequestBody = {},
    request: RequestInfo = {
      requestId: uuid(),
      timestamp: Date.now().toString(),
      blob: true,
    }
  ) => {
    let str = '';
    Object.keys(body).forEach((v) => {
      str = `${str}&${v}=${body[v]}`;
    });
    str = str.substr(1);
    return roxFetch<T>('get', `${url}${str.length > 0 ? '?' : ''}${str}`, undefined, request);
    // return roxFetch('get', `${url}`, formData, request);
    // const formData = Object.keys(body).length === 0 ? undefined : new FormData();
    // Object.keys(body).forEach(v => {
    //   if(formData) {
    //     formData.append(v, body[v]);
    //   }
    // });
  },
  pblob: <T>(
    url: string,
    body: RequestBody = {},
    request: RequestInfo = {
      requestId: uuid(),
      timestamp: Date.now().toString(),
      blob: true,
    }
  ) => {
    const formData = Object.keys(body).length === 0 ? undefined : new FormData();
    Object.keys(body).forEach((v) => {
      if (formData) {
        formData.append(v, body[v]);
      }
    });
    return roxFetch<T>('post', `${url}`, formData, request);
  },
  fetch: roxFetch,
};

window.fet = f;

export default f;
