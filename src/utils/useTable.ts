import { CommonObject, TableList } from '@app/types';
import { message } from 'antd';
import { useState } from 'react';
import uuid from 'uuid';

interface UseTableProps {
  filter?: any;
  api: any;
  index: string;
}

export default function useTable(props: UseTableProps) {
  const [tableList, setTableList] = useState<TableList | undefined>(undefined);
  const [loading, setLoading] = useState<boolean>(false);

  const getTableList = (current: number = 1, size?: number) => {
    const requestId = uuid();
    const timestamp = Date.now().toString();
    const realSize = size || tableList?.size || 10;

    setLoading(true);
    const filterObj = props.filter?.() || {};
    props
      .api(
        { size: realSize, ...filterObj, current },
        {
          requestId,
          timestamp,
        }
      )
      .then((r: any) => {
        setLoading(false);
        message.success('列表获取成功');

        let tableData: undefined | TableList = undefined;
        if (r.data[props.index].records) {
          tableData = {
            total: r.data[props.index].total,
            size: r.data[props.index].size,
            current: r.data[props.index].current,
            records: r.data[props.index].records,
            allData: r.data,
          };
        } else if (r.data[props.index]) {
          tableData = {
            total: r.data[props.index].length,
            size: r.data[props.index].length,
            current: 1,
            records: r.data[props.index],
            allData: r.data,
          };
        } else {
          tableData = {
            total: r.data?.records?.length,
            size: r.data?.records?.length,
            current: 1,
            records: r.data?.records,
            allData: r.data,
          };
        }
        console.log('列表数据', tableData);
        setTableList(tableData);
        setLoading(false);
      })
      .catch((e: any) => {
        setLoading(false);
      });
  };

  return {
    tableList,
    loading,
    setLoading,
    getTableList,
  };
}
