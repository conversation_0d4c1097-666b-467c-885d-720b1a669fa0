/* eslint-disable prefer-promise-reject-errors */
/* eslint-disable react-hooks/exhaustive-deps */
import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import usePersistFn from './usePersistFn';

function useXHR() {
  const [state, setState] = useState(() => {
    return {
      loading: false,
    };
  });
  const dispatch = useDispatch();

  const run = usePersistFn((service: any, body: any, setStoreLoading: boolean = false) => {
    if (state.loading) {
      return Promise.reject({ code: -1, message: '请求进行中' });
    }
    return new Promise((resolve, reject) => {
      setState({
        loading: true,
      });
      if (setStoreLoading) {
        dispatch(setConfig({ loading: true, confirmLoading: true }));
      }
      service(body)
        .then((res: any) => {
          setState({
            loading: false,
          });
          if (setStoreLoading) {
            dispatch(setConfig({ loading: false, confirmLoading: false }));
          }
          resolve(res);
        })
        .catch((err: any) => {
          setState({
            loading: false,
          });
          if (setStoreLoading) {
            dispatch(setConfig({ loading: false, confirmLoading: false }));
          }
          reject(err);
        });
    });
  });

  return {
    ...state,
    run,
  };
}

export default useXHR;
