/* eslint-disable consistent-return */
import { useCallback, useRef } from 'react';

function usePersistFn(fn: any) {
  const ref = useRef<any>(() => {
    throw new Error('Cannot call an event handler while rendering.');
  });

  ref.current = fn;

  const persist = useCallback(
    (...args) => {
      const refFn = ref.current;
      if (refFn) {
        return refFn(...args);
      }
    },
    [ref]
  );

  return persist;
}

export default usePersistFn;
