/* eslint-disable prefer-promise-reject-errors */
import { RequestBody, RequestInfo, CommonResponse } from '@app/types';
import { message } from 'antd';
import fetch from 'isomorphic-fetch';
import { sha256 } from 'js-sha256';
import uuid from 'uuid/v4';
import { SALT } from './constants';

function newRoxFetch<T>(
  url: string,
  body: FormData | string,
  request: RequestInfo,
  method: string = 'post',
  headers: any = {}
): Promise<CommonResponse<T>> {
  const signature = sha256(`${request.requestId}&&${request.timestamp}&&${SALT}`);
  const signatureHeaders = new Headers({
    'X-REQUEST-ID': request.requestId,
    'X-TIMESTAMP': request.timestamp,
    'X-SIGNATURE': signature,
    'Content-Type': 'video/mp4',
  });
  
  if (typeof body === 'string') {
    signatureHeaders.append('Content-Type', 'application/json;charset=UTF-8');
  }
  
  return new Promise<CommonResponse<T>>((resolve, reject) => {
    const settings: RequestInit = {
      method,
      headers: signatureHeaders,
      credentials: 'include',
      body,
    };
    
    fetch(url, settings)
      .then((response: any) => {
        response.json().then((json: any) => {
          console.log('xxxxxx', json, url);
          if (json.code === 0) {
            resolve({ data: json.data, requestId: request.requestId });
          } else {
            message.error(json.message || json.msg);
            reject({ code: json.code, message: json.message || json.msg });
          }
        });
      })
      .catch((error) => {
        message.error('请求异常，详情请查看Console');
        console.error(error);
        reject({ message: error });
      });
  });
}

const newFetch = {
  post: <T>(
    url: string,
    body: RequestBody = {},
    request: RequestInfo = {
      requestId: uuid(),
      timestamp: Date.now().toString(),
    }
  ) => {
    const formData = Object.keys(body).length === 0 ? new FormData() : new FormData();
    Object.keys(body).forEach((v) => {
      formData.append(v, body[v]);
    });
    return newRoxFetch<T>(url, formData, request, 'post');
  },
  json: <T>(
    url: string,
    body: RequestBody = {},
    request: RequestInfo = {
      requestId: uuid(),
      timestamp: Date.now().toString(),
    }
  ) => {
    return newRoxFetch<T>(url, JSON.stringify(body), request, 'post');
  },
  put: <T>(
    url: string,
    body: RequestBody = {},
    request: RequestInfo = {
      requestId: uuid(),
      timestamp: Date.now().toString(),
    }
  ) => {
    const formData = Object.keys(body).length === 0 ? new FormData() : new FormData();
    Object.keys(body).forEach((v) => {
      formData.append(v, body[v]);
    });
    return newRoxFetch<T>(url, formData, request, 'put');
  },
  putWithHeaders: <T>(
    url: string,
    body: RequestBody = {},
    request: RequestInfo = {
      requestId: uuid(),
      timestamp: Date.now().toString(),
    },
    headers: any = {}
  ) => {
    const formData = Object.keys(body).length === 0 ? new FormData() : new FormData();
    Object.keys(body).forEach((v) => {
      formData.append(v, body[v]);
    });
    return newRoxFetch<T>(url, formData, request, 'put', headers);
  },
  putJson: <T>(
    url: string,
    body: RequestBody = {},
    request: RequestInfo = {
      requestId: uuid(),
      timestamp: Date.now().toString(),
    }
  ) => {
    return newRoxFetch<T>(url, JSON.stringify(body), request, 'put');
  },
};

export default newFetch; 