import {
  CommonObject,
  TableFetchStatus,
  TableList,
  IComponentProps,
  ITableProps,
} from '@app/types';
import { connectTable as connect } from '@utils/connect';
import { Col, Pagination, Row, Table, Icon, message, Spin } from 'antd';
import { size } from 'lodash';
import React from 'react';
import { SortableContainer, SortableElement, SortableHandle, SortEnd } from 'react-sortable-hoc';
import uuid from 'uuid';

interface NewTableProps extends React.Props<React.Component> {
  pagination?: boolean;
  tableProps?: CommonObject;
  total?: string | number;
  tableList?: TableList;
  columns: CommonObject[];
  // updateFilter?: (...arg: any) => void;
  rowKey: string | ((record: any) => string);
  multi?: boolean;
  selectedRowKeys?: string[];
  onSelectChange?: (keys: any, rows: any) => void;
  draggable?: boolean;
  getRecordDraggable?: (record: any, index: number) => boolean;
  onDragEnd?: (oldIndex: number, newIndex: number) => void;
  sizeChange?: (size: number) => void;

  getTableList?: (current?: number, size?: number) => void;

  loading?: boolean
}

const DragHandle = SortableHandle(() => (
  <Icon type="menu" style={{ cursor: 'grab', color: '#999' }} />
));
const SortableItem = SortableElement((props: any) => <tr {...props} />);
const CustomSortableContainer = SortableContainer((props: any) => <tbody {...props} />);

class NewTableComponent extends React.Component<NewTableProps, any> {
  constructor(props: any) {
    super(props);
  }

  reloadData = () => {
    this.props.getTableList?.()
  }

  onPageChange = (page: number) => {
    this.props.getTableList?.(page)
  };

  onSizeChange = (page: number, pageSize: number) => {
    const { sizeChange } = this.props;
    this.props.getTableList?.(1, pageSize)

    if (sizeChange) {
      sizeChange(pageSize);
    }
  };

  dragEnd = ({ newIndex, oldIndex }: SortEnd) => {
    const { onDragEnd } = this.props;
    if (onDragEnd && typeof onDragEnd === 'function') {
      onDragEnd(oldIndex, newIndex);
    }
  };

  render() {
    const { records, current = 1, size = 10, total = 1 } = this.props.tableList || {};
    const {
      tableProps = {},
      pagination,
      columns,
      rowKey,
      draggable = false,
      getRecordDraggable = () => true,
    } = this.props;
    const { multi, selectedRowKeys, onSelectChange } = this.props;
    const showTotal = this.props.total ? this.props.total : total;
    const paginationSpan = 18;

    const rowSelection = multi
      ? {
        selectedRowKeys: selectedRowKeys || [],
        onChange: onSelectChange || (() => { }),
      }
      : undefined;

    const selfColumns: any = draggable
      ? [
        {
          title: ' ',
          key: 'drag-sort',
          render: (_: any, record: any, index: number) =>
            getRecordDraggable(record, index) ? <DragHandle /> : null,
          width: 30,
        } as CommonObject,
      ].concat(columns)
      : columns;

    const DraggableContainer = (props: any) => {
      return (
        <CustomSortableContainer
          useDragHandle
          disableAutoscroll
          helperClass="row-dragging"
          onSortEnd={this.dragEnd}
          {...props}
        />
      );
    };

    const DraggableBodyRow = ({ className, style, ...restProps }: any) => {
      // function findIndex base on Table rowKey props and should always be a right array index
      const index = records?.findIndex(
        (x) => (typeof rowKey === 'function' ? rowKey(x) : x[rowKey]) === restProps['data-row-key']
      );
      return <SortableItem index={index} {...restProps} className={className} style={style} />;
    };

    return (
      <div>
        <Row>
          <Table
            loading={
              {
                tip: "正在加载...",
                spinning: this.props.loading ?? false,
                wrapperClassName: "spin",
                className: "spin"
              }
            }
            pagination={false}
            dataSource={records}
            columns={selfColumns}
            {...tableProps}
            rowKey={rowKey}
            rowSelection={rowSelection}
            components={
              draggable
                ? {
                  body: {
                    wrapper: DraggableContainer,
                    row: DraggableBodyRow,
                  },
                }
                : {}
            }
          />
        </Row>
        {Boolean(pagination) && (
          <Row style={{ marginTop: 16 }}>
            <Col span={6} style={{ verticalAlign: 'bottom' }}>
              共{showTotal}条数据
            </Col>
            <Col span={paginationSpan} className="pagination-pages">
              <Pagination
                showSizeChanger={true}
                showQuickJumper={true}
                pageSize={size}
                current={current}
                pageSizeOptions={['10', '20', '50', '100']}
                onChange={this.onPageChange}
                onShowSizeChange={this.onSizeChange}
                total={total}
              />
            </Col>
          </Row>
        )}
      </div>
    );
  }
}

export default NewTableComponent;
