import { applyMiddleware, combineReducers, createStore, Reducer, AnyAction } from 'redux';
import reduxLogger from 'redux-logger';
import reduxThunk from 'redux-thunk';

import * as reducers from '@app/reducer';
import { Session, TableList, CommonObject ,TableCache} from '@app/types';

const rootReducer = combineReducers({ ...reducers });

// export type AppState = ReturnType<typeof rootReducer>;
export interface AppState<T = CommonObject, S = CommonObject> {
  session: Session;
  tableList: TableList<T, S>;
  config: CommonObject;
  tableCache: TableCache<T, S>;
}

export default function configureStore() {
  const createStoreWithMiddleware = applyMiddleware(reduxThunk, reduxLogger)(createStore);

  const store = createStoreWithMiddleware(rootReducer);

  return store;
}
