import { connect, InferableComponentEnhancerWithProps, DispatchProp } from 'react-redux';

import {Session, TableList, CommonObject, TableCache} from '@app/types';
import { AppState } from './configureStore';

interface ISessionState {
  session: Session;
}

interface ITableState<T, S> {
  session: Session;
  tableList: TableList<T, S>;
  tableCache:TableCache<T, S>
}

export const connectSession: InferableComponentEnhancerWithProps<ISessionState, {}> = connect<
  ISessionState,
  {},
  {},
  AppState
>((state: AppState): ISessionState => {
  return {
    session: state.session,
  };
});

export const connectAll = <
  T = CommonObject,
  S = CommonObject
>(): InferableComponentEnhancerWithProps<AppState<T, S> & DispatchProp, {}> =>
  connect<AppState<T, S>, {}, {}, AppState<T, S>>((state: AppState<T, S>): AppState<T, S> => {
    return {
      session: state.session,
      tableList: state.tableList,
      config: state.config,
      tableCache:state.tableCache,
    };
  });

export const connectTable = <
  T = CommonObject,
  S = CommonObject
>(): InferableComponentEnhancerWithProps<AppState<T, S> & DispatchProp, {}> =>
  connect<ITableState<T, S>, {}, {}, AppState<T, S>>((state: AppState<T, S>): ITableState<T, S> => {
    return {
      session: state.session,
      tableList: state.tableList,
      tableCache: state.tableCache,
    };
  });
