import { sysApi as api } from '@app/api';
import { Form, Input, message, Radio, Slider, Tooltip, Icon, Row } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader } from '../common';

@connectSession
@(Form.create({ name: 'NavTopBgForm' }) as any)
class NavTopBgForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      ...props.formContent,
    };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  sliderOnChange = (value: any) => {
    console.log('onChange: ', value);
  };

  onAfterChange = (value: any) => {
    console.log('onAfterChange: ', value);
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        const body = {
          ...values,
          id: this.props.formContent.id,
        };
        api
          .saveNavTopBg(body)
          .then(() => {
            setMLoading(this, false);
            message.success('操作成功');
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  render() {
    const { getFieldDecorator, getFieldsValue } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const values = { ...this.props.formContent, ...getFieldsValue() };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <span style={{color:'rgb(0 0 0 / 45%)'}}>此处配置仅针对客户端V6.0.0以下版本生效，如需修改新版本样式请前往【主题管理】添加全局生效主题</span>
        <div style={{ fontSize: '18px', color: '#000000' }}>背景风格配置</div>
        <Form.Item label="选项">
          {getFieldDecorator('config_type', {
            initialValue: this.state.config_type || 0,
          })(
            <Radio.Group>
              <Radio value={0}>默认样式</Radio>
              <Radio value={1}>显示背景图</Radio>
              {/* <Radio value={2}>更改色值</Radio> */}
            </Radio.Group>
          )}
        </Form.Item>
        {values.config_type === 1 && (
          <>
            <Form.Item label={
              <span style={{ position: 'relative' }}>
                <Tooltip title='客户端V5.2.5以上版本生效'>
                  <Icon type="question-circle" style={{ position: 'absolute', left: -30, top: 0 }} />
                </Tooltip>
                背景图-上图
              </span>
            } extra="支持jpg, png, gif等格式, 比例 5:6,建议尺寸1125px * 1350px">
              {getFieldDecorator('config_url', {
                initialValue: this.state.config_url || 0,
                rules: [{ required: true, message: '请上传图片' }],
              })(<ImageUploader ratio={5/6} />)}
            </Form.Item>
            <Form.Item label={
              <span style={{ position: 'relative' }}>
                <Tooltip title='客户端V5.2.5以上版本生效'>
                  <Icon type="question-circle" style={{ position: 'absolute', left: -30, top: 0 }} />
                </Tooltip>
                背景图-下图
              </span>
            } extra="支持jpg, png, gif等格式, 比例 75:68, 建议尺寸1125px * 1020px">
              {getFieldDecorator('config_down_url', {
                initialValue: this.state.config_down_url || 0,
                rules: [{ required: true, message: '请上传图片' }],
              })(<ImageUploader ratio={1.1029} />)}
            </Form.Item>
            {/* <Form.Item label="背景填充色值" extra="输入背景填充色值 例: #888888">
              {getFieldDecorator('config_bg_fill_value', {
                initialValue: this.state.config_bg_fill_value || '',
                rules: [
                  { required: true, message: '请输入色值' },
                  { pattern: /^#[0-9ABCDEFGabcdefg]{6}$/, message: '输入格式不正确' },
                ],
              })(<Input placeholder="输入色值" />)}
            </Form.Item> */}
            {/* <Form.Item label="LOGO颜色">
              {getFieldDecorator('config_font', {
                initialValue: this.state.config_font ?? 0,
              })(
                <Radio.Group>
                  <Radio value={0}>白色</Radio>
                  <Radio value={1}>黑色</Radio>
                </Radio.Group>
              )}
            </Form.Item> */}
            <Form.Item label="LOGO(SVG)颜色">
              {getFieldDecorator('logo_color_value', {
                initialValue: this.state.logo_color_value ?? 0,
              })(<Input placeholder="输入色值" />)}
            </Form.Item>
          </>
        )}
        {/* {values.config_type === 2 && (
          <>
            <Form.Item label="色值" extra="输入色值 例: #888888">
              {getFieldDecorator('config_color_value', {
                initialValue: this.state.config_color_value || '',
                rules: [
                  { required: true, message: '请输入色值' },
                  { pattern: /^#[0-9ABCDEFGabcdefg]{6}$/, message: '输入格式不正确' },
                ],
              })(<Input placeholder="输入色值" />)}
            </Form.Item>
            <Form.Item label="LOGO颜色">
              {getFieldDecorator('config_font', {
                initialValue: this.state.config_font ?? 0,
              })(
                <Radio.Group>
                  <Radio value={0}>白色</Radio>
                  <Radio value={1}>黑色</Radio>
                </Radio.Group>
              )}
            </Form.Item>
            <Form.Item label="LOGO(SVG)颜色">
              {getFieldDecorator('logo_color_value', {
                initialValue: this.state.logo_color_value ?? 0,
              })(<Input placeholder="输入色值" />)}
            </Form.Item>
          </>
        )} */}
        {values.config_type !== 0 && (
          <>
            <div style={{ fontSize: '18px', color: '#000000' }}>搜索栏配置</div>
            <Form.Item label="背景色透明度">
              {getFieldDecorator('search_bar_bg_openness', {
                initialValue: this.state.search_bar_bg_openness ?? 100,
              })(
                <Slider
                  style={{ width: '200px' }}
                  onChange={this.sliderOnChange}
                  onAfterChange={this.onAfterChange}
                />
              )}
            </Form.Item>
            {/* <Form.Item label="背景颜色" extra="输入背景填充色值 例: #888888">
          {getFieldDecorator('config_bg_fill_value', {
            initialValue: this.state.config_bg_fill_value || '',
            rules: [
              { required: true, message: '请输入色值' },
              { pattern: /^#[0-9ABCDEFGabcdefg]{6}$/, message: '输入格式不正确' },
            ],
          })(<Input placeholder="输入色值" />)}
        </Form.Item> */}
            <Form.Item label="文字颜色" extra="输入背景填充色值 例: #888888">
              {getFieldDecorator('search_bar_font_color', {
                initialValue: this.state.search_bar_font_color || '',
                rules: [
                  { required: true, message: '请输入色值' },
                  { pattern: /^#[0-9ABCDEFGabcdefg]{6}$/, message: '输入格式不正确' },
                ],
              })(<Input placeholder="输入色值" />)}
            </Form.Item>
            <Form.Item label="是否有边框">
              {getFieldDecorator('search_bar_with_border', {
                initialValue: this.state.search_bar_with_border ?? false,
              })(
                <Radio.Group>
                  <Radio value={false}>否</Radio>
                  <Radio value={true}>是</Radio>
                </Radio.Group>
              )}
            </Form.Item>
            {/* <div style={{ fontSize: '18px', color: '#000000' }}>天气配置</div>
            <Form.Item label="温度文字颜色" extra="输入背景填充色值 例: #888888">
              {getFieldDecorator('weather_temperature_color', {
                initialValue: this.state.weather_temperature_color || '',
                rules: [
                  { required: true, message: '请输入色值' },
                  { pattern: /^#[0-9ABCDEFGabcdefg]{6}$/, message: '输入格式不正确' },
                ],
              })(<Input placeholder="输入色值" />)}
            </Form.Item> */}
            <div style={{ fontSize: '18px', color: '#000000' }}>顶部导航配色配置</div>
            {/* <Form.Item
              label="背景配色"
              extra="输入背景填充色值 例: #FFFFFF，透明色值例如：#00000000（前面两个0代表全透明）"
            >
              {getFieldDecorator('top_nav_background_color_value', {
                initialValue: this.state.top_nav_background_color_value || '#FFFFFF',
                rules: [
                  { required: true, message: '请输入色值' },
                  { pattern: /^#[0-9ABCDEFGabcdefg]{8}$/, message: '输入格式不正确' },
                ],
              })(<Input placeholder="输入色值" />)}
            </Form.Item> */}
            <Form.Item label="选中状态配色" extra="输入背景填充色值 例: #888888">
              {getFieldDecorator('top_nav_selected_color_value', {
                initialValue: this.state.top_nav_selected_color_value || '',
                rules: [
                  { required: true, message: '请输入色值' },
                  { pattern: /^#[0-9ABCDEFGabcdefg]{6}$/, message: '输入格式不正确' },
                ],
              })(<Input placeholder="输入色值" />)}
            </Form.Item>
            <Form.Item label="未选中状态配色" extra="输入背景填充色值 例: #888888">
              {getFieldDecorator('top_nav_unselected_color_value', {
                initialValue: this.state.top_nav_unselected_color_value || '',
                rules: [
                  { required: true, message: '请输入色值' },
                  { pattern: /^#[0-9ABCDEFGabcdefg]{6}$/, message: '输入格式不正确' },
                ],
              })(<Input placeholder="输入色值" />)}
            </Form.Item>
          </>
        )}
      </Form>
    );
  }
}

export default NavTopBgForm;
