import { sysApi as api, opApi as api2 } from '@app/api';
import { Form, Input, message, Select } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader } from '../common';

@connectSession
@(Form.create({ name: 'ColdStartForm' }) as any)
class ColdStartForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const s = {
      title: '',
      ...this.props.formContent,
    };
    this.state = {
      ...this.props.formContent,
    };
  }

  handleSubmit = (e: any) => {
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        const body = { ...values };
        const service = api.saveSubjectTemplateConfig;
        if (this.state.isEdit) {
          body.id = this.state.id;
        }

        service(body)
          .then(() => {
            setMLoading(this, false);
            message.success('添加成功');
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  render() {
    const { getFieldDecorator, getFieldsValue } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="模板名称">
          {getFieldDecorator('templateName', {
            initialValue: this.state.templateName,
            rules: [
              {
                required: true,
                message: '请输入模板名称',
              },
              {
                max: 10,
                message: '最多10个字',
              },
            ],
          })(<Input placeholder="请输入模板名称" />)}
        </Form.Item>
        <Form.Item label="模板预览图片" extra="支持jpg,jpeg,png,gif图片大小为100*100">
          {getFieldDecorator('imgUrl', {
            initialValue: this.state.imgUrl,
            rules: [
              {
                required: true,
                message: '请上传模板预览图片',
              },
            ],
          })(
            <ImageUploader
              ratio={1 / 1}
              accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
            />
          )}
        </Form.Item>
        <Form.Item label="关联媒立方模板ID">
          {getFieldDecorator('mlfTemplateId', {
            initialValue: this.state.mlfTemplateId,
            rules: [
              {
                required: true,
                message: '请输入关联媒立方模板ID',
              },
            ],
          })(<Input placeholder="请输入关联媒立方模板ID" />)}
        </Form.Item>
      </Form>
    );
  }
}

export default ColdStartForm;
