import React from "react";
import { Drawer } from '@components/common';
import { Form, Input, message } from "antd";
import connect from '@utils/connectSession';
import { sysApi as api } from '@app/api';
import { setMLoading } from "@app/utils/utils";

@connect
@(Form.create({ name: 'AddConfigOptionDrawer' }) as any)
export default class AddConfigOptionDrawer extends React.Component<any, any> {
  handleOkClick = () => {
    const { form: { validateFields }, onEnd, record } = this.props
    validateFields((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        if (record && record.id) {
          api.updateAppConfig({ ...values, code: record.item_code }).then(() => {
            setMLoading(this, false);
            message.success('修改成功');
            onEnd(false)
          }).catch(() => {
            setMLoading(this, false);
          })
        } else {
          api.createAppConfig({ ...values }).then(() => {
            setMLoading(this, false);
            message.success('添加成功');
            onEnd(true)
          }).catch(() => {
            setMLoading(this, false);
          })
        }
      }
    })
  }
  render() {
    const { record, visible, onClose, form: { getFieldDecorator } } = this.props
    const formLayout = {
      labelCol: { span: 3 },
      wrapperCol: { span: 19 },
    };
    return <Drawer
      title={`${record ? '编辑' : '新增'}配置项`}
      onClose={onClose}
      visible={visible}
      onOk={this.handleOkClick}
      skey={'AddConfigOptionDrawer'}
    >
      <Form {...formLayout}>
        <Form.Item label="名称">
          {getFieldDecorator('code', {
            rules: [
              {
                required: true,
                message: '名称不能为空',
              },
            ],
            initialValue: record ? record.item_code : ''
          })(<Input placeholder="请输入配置项名称" allowClear disabled={!!record} />)}
        </Form.Item>
        <Form.Item label="配置信息">
          {getFieldDecorator('value', {
            rules: [
              {
                required: true,
                message: '配置信息不能为空',
              },
            ],
            initialValue: record ? record.item_value : ''
          })(<Input.TextArea placeholder="请输入配置信息" rows={8} />)}
        </Form.Item>
        <Form.Item label="备注说明">
          {getFieldDecorator('remark', {
            initialValue: record ? record.remark : ''
          })(<Input.TextArea placeholder="请输入备注说明" rows={8} />)}
        </Form.Item>
      </Form>
    </Drawer>
  }
}