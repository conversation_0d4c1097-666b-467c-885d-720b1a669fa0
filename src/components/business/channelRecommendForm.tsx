import { releaseListApi } from '@app/api';
import ImageUploader from '@components/common/imageUploader';
import SearchAndInput from '@components/common/newsSearchAndInput';
import { Form, Icon, Input, message, Radio, Row, Tooltip } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';

@connectSession
@(Form.create({ name: 'channelRecommendForm' }) as any)
class ChannelRecommendForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = { ...props.formContent, channelMode: props.channelMode };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  }

  doSubmit = () => {
    const fields = ['title', 'pic_url', this.state.link ? 'url' : 'channel_article_ids'];
    this.props.form.validateFieldsAndScroll(fields, (err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        releaseListApi
          .submitChannelRecommendDetail(this.state.id ? 'update' : 'create', {
            ...values,
            [this.state.id ? 'id' : 'channel_id']: this.state.id || this.props.channelId,
            url: this.state.link ? values.url : '',
            channel_article_ids: this.state.link ? '' : values.channel_article_ids.join(','),
          })
          .then(() => {
            message.success('成功');
            setMLoading(this, false);
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  }

  getColumn = () => {
    return [
      {
        title: '新闻类型',
        key: 'type',
        dataIndex: 'doc_type_name',
        width: 90,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
      },
    ];
  }

  articlesChange = (data: any) => {
    this.setState({ ...data });
  }

  toggleLink = (link: boolean) => {
    this.setState({
      link,
    });
  }

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="名称">
          {getFieldDecorator('title', {
            initialValue: this.state.title,
            rules: [
              {
                required: true,
                message: '请填写名称',
              },
              {
                max: 6,
                message: '名称最长不能超过6个字',
              },
              {
                pattern: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/,
                message: '名称不能包含特殊字符',
              },
            ],
          })(<Input placeholder="请输入名称" />)}
        </Form.Item>
        <Form.Item label="图标" extra="支持jpg、jpeg、png图片格式">
          {getFieldDecorator('pic_url', {
            initialValue: this.state.pic_url,
            rules: [
              {
                required: this.state.channelMode ? true : false,
                message: '有图模式下必须上传图标',
              },
            ],
          })(<ImageUploader ratio={3 / 2} />)}
        </Form.Item>
        <Form.Item label="关联类型">
          <Row>
            <Radio checked={this.state.link} onClick={this.toggleLink.bind(this, true)}>
              关联链接
            </Radio>
            <Radio checked={!this.state.link} onClick={this.toggleLink.bind(this, false)}>
              关联新闻
            </Radio>
          </Row>
        </Form.Item>
        {this.state.link ? (
          <Form.Item label="关联信息">
            {getFieldDecorator('url', {
              initialValue: this.state.url,
              preserve: true,
              rules: [
                {
                  required: true,
                  message: '请填写链接',
                },
                {
                  pattern: /^https?:\/\/[a-zA-Z0-9]/,
                  message: '请正确填写链接',
                },
              ],
            })(<Input placeholder="请输入链接" />)}
          </Form.Item>
        ) : (
          <Form.Item label="关联信息">
            {getFieldDecorator('channel_article_ids', {
              initialValue: this.state.channel_article_ids,
              preserve: true,
              rules: [
                {
                  required: true,
                  message: '请关联新闻',
                  type: 'array',
                },
                {
                  max: 30,
                  message: '最多关联30条新闻',
                  type: 'array',
                },
              ],
            })(
              <SearchAndInput
                max={30}
                func="recommendSearch"
                columns={this.getColumn()}
                initialValues={{ list: this.state.channelArticles }}
                triggerInitialValueChange={this.articlesChange}
                body={{ channel_id: this.props.channelId }}
                order={true}
                afix={
                  <Tooltip title={<p>只能关联该频道稿件</p>}>
                    <Icon type="question-circle" />
                  </Tooltip>
                }
              />
            )}
          </Form.Item>
        )}
      </Form>
    );
  }
}

export default ChannelRecommendForm;
