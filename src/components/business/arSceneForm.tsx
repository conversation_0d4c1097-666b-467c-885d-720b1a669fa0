import { opApi as api } from '@app/api';
import {
  Checkbox,
  Form,
  Input,
  message,
  Select,
  DatePicker,
  InputNumber,
  Radio,
  Row,
  Button,
  Divider,
  Col,
} from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import scrollIntoView from 'dom-scroll-into-view';
import { ImageUploader } from '../common';

const events = [
  {
    id: '1000',
    name: '点击模型',
  },
  {
    id: '1001',
    name: '双击模型',
  },
  {
    id: '1100',
    name: '上滑模型',
  },
  {
    id: '1101',
    name: '下滑模型',
  },
  {
    id: '1102',
    name: '左滑模型',
  },
  {
    id: '1103',
    name: '右滑模型',
  },
  {
    id: '2000',
    name: '点击UI',
  },
  {
    id: '2001',
    name: '双击UI',
  },
  {
    id: '3000',
    name: '开始播放',
  },
  {
    id: '3001',
    name: '播放结束',
  },
  {
    id: '3002',
    name: '动画播放次数',
  },
];

@connectSession
@(Form.create({ name: 'activityForm' }) as any)
class AddForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const s = {
      title: '',
      ar_id: '',
      identify_method: 1,
      json: [
        {
          event_id: '',
          name: '',
          jump_url: '',
          ar_content_type: 0,
          share_title: '',
          share_info: '',
          share_image_url: '',
          share_url: '',
        },
      ],
      ...this.props.formContent,
    };
    const objectedJson: any = {};
    const keys = s.json.map((v: any, i: number) => {
      objectedJson[i] = v;
      return i;
    });
    this.state = {
      ...s,
      json: objectedJson,
      keys,
      maxKey: keys[keys.length - 1],
    };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  add = () => {
    const id = this.state.maxKey + 1;
    const newKeys = [...this.state.keys, id];
    const json = {
      ...this.state.json,
      [id]: {
        event_id: '',
        name: '',
        jump_url: '',
        ar_content_type: 0,
        share_title: '',
        share_info: '',
        share_image_url: '',
        share_url: '',
      },
    };
    this.setState(
      {
        keys: newKeys,
        maxKey: id,
        json,
      },
      () => {
        scrollIntoView(
          document.getElementById('add-btn-line'),
          document.getElementsByClassName('rox-drawer-content')[0]
        );
      }
    );
  };

  remove = (key: any) => {
    const newKeys = this.state.keys.filter((v: any) => v !== key);
    this.setState({
      keys: newKeys,
    });
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        if (!this.state.isEdit && this.props.allIds.indexOf(values.ar_id) > -1) {
          message.error('ARID已存在于列表中');
          return;
        }
        setMLoading(this, true);
        
        const body = { ...values };
        body.json = JSON.stringify(
          this.state.keys.map((v: any) => {
            return values.json[v];
          })
        );
        const service = this.state.isEdit ? api.updateArScene : api.createArScene;
        service(body)
          .then(() => {
            setMLoading(this, false);
            message.success('添加成功');
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  validator = (rule: any, value: any, callback: Function) => {
    const values = this.props.form.getFieldsValue();
    console.log(values, rule, value);
    const key = parseInt(rule.field.replace(/json\[/g, '').replace(/\]\.name/g, ''), 10);
    const count = Object.keys(values.json).filter((k: any) => {
      const v = parseInt(k, 10);
      if (key !== v && value === values.json[v].name) {
        console.log('---true---', key, v, value, values.json[v].name);
        return true;
      }
      console.log('---false---', key, v, value, values.json[v].name);
      return false;
    }).length;
    if (count > 0) {
      callback('NAME不能重复');
    } else {
      callback();
    }
  };

  render() {
    const { getFieldDecorator, getFieldsValue } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const values = getFieldsValue();
    const json = { ...this.state.json, ...(values.json ? values.json : {}) };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="AR标题">
          {getFieldDecorator('title', {
            initialValue: this.state.title,
            rules: [
              {
                required: true,
                message: '请输入标题',
              },
              {
                max: 50,
                message: '最多50个字',
              },
            ],
          })(<Input placeholder="仅用于后台显示，便于管理员区分" />)}
        </Form.Item>
        <Form.Item label="识别方式" required>
          {getFieldDecorator('identify_method', {
            initialValue: this.state.identify_method,
          })(
            <Radio.Group>
              <Radio value={1}>静态图片</Radio>
              <Radio value={2}>手势</Radio>
              <Radio value={3}>扫平</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item label="AR内容ID">
          {getFieldDecorator('ar_id', {
            initialValue: this.state.ar_id,
            rules: [
              {
                required: true,
                message: '请输入AR内容ID',
              },
            ],
          })(<Input placeholder="填写eazyAR后台对应的素材ID" disabled={this.state.isEdit} />)}
        </Form.Item>
        {this.state.keys.map((v: any, i: number) => (
          <Row key={v}>
            <Row>
              <Divider type="horizontal" />
              <Col span={4} style={{ textAlign: 'right' }}>
                <h3>行为{i + 1}</h3>
              </Col>
              <Col span={18} style={{ textAlign: 'right' }}>
                <Button
                  type="danger"
                  onClick={() => this.remove(v)}
                  disabled={this.state.keys.length <= 1}
                >
                  删除行为{i + 1}
                </Button>
              </Col>
            </Row>
            <Row>
              <Form.Item label="Name">
                {getFieldDecorator(`json[${v}].name`, {
                  initialValue: this.state.json[v].name,
                  rules: [
                    {
                      required: true,
                      message: '请填写Name, 如不需要请删除本条目',
                    },
                    {
                      validator: this.validator,
                    },
                  ],
                })(<Input placeholder="填写AR素材中自定义的Name" />)}
              </Form.Item>
              <Form.Item label="交互行为">
                {getFieldDecorator(`json[${v}].event_id`, {
                  initialValue: this.state.json[v].event_id,
                  rules: [
                    {
                      required: true,
                      message: '请选择交互行为, 如不需要请删除本条目',
                    },
                  ],
                })(
                  <Select placeholder="选择交互行为">
                    <Select.Option value="" disabled>
                      选择交互行为
                    </Select.Option>
                    {events.map((vv: any) => (
                      <Select.Option value={vv.id} key={vv.id}>
                        {vv.name}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
              <Form.Item label="内容类型">
                {getFieldDecorator(`json[${v}].ar_content_type`, {
                  initialValue: this.state.json[v].ar_content_type,
                })(
                  <Radio.Group>
                    <Radio value={0}>跳转链接</Radio>
                    <Radio value={1}>分享</Radio>
                  </Radio.Group>
                )}
              </Form.Item>
              {json[v].ar_content_type === 0 && (
                <Form.Item label="跳转链接">
                  {getFieldDecorator(`json[${v}].jump_url`, {
                    initialValue: this.state.json[v].jump_url,
                    rules: [
                      {
                        required: true,
                        message: '请填写跳转链接, 如不需要请删除本条目',
                      },
                      {
                        pattern: /^https?:\/\//,
                        message: '请正确填写跳转链接',
                      },
                    ],
                  })(<Input placeholder="填写跳转链接" />)}
                </Form.Item>
              )}
              {json[v].ar_content_type === 1 && (
                <>
                  <Form.Item label="分享标题">
                    {getFieldDecorator(`json[${v}].share_title`, {
                      initialValue: this.state.json[v].share_title,
                      rules: [
                        {
                          required: true,
                          message: '请填写分享标题',
                        },
                        {
                          max: 24,
                          message: '最多输入24个字',
                        },
                      ],
                    })(<Input placeholder="最多输入24个字" />)}
                  </Form.Item>
                  <Form.Item label="分享简介">
                    {getFieldDecorator(`json[${v}].share_info`, {
                      initialValue: this.state.json[v].share_info,
                      rules: [
                        {
                          max: 35,
                          message: '最多输入35个字',
                        },
                      ],
                    })(
                      <Input placeholder="最多输入35个字，不填写的话，默认显示“来自潮新闻客户端”" />
                    )}
                  </Form.Item>
                  <Form.Item label="分享缩略图" extra="分享缩略图, 支持jpg png jpeg等格式, 比例1:1">
                    {getFieldDecorator(`json[${v}].share_image_url`, {
                      initialValue: this.state.json[v].share_image_url,
                      rules: [
                        {
                          required: true,
                          message: '请上传分享缩略图',
                        },
                      ],
                    })(
                      <ImageUploader ratio={1} accept={['image/png', 'image/jpeg', 'image/jpg']} />
                    )}
                  </Form.Item>
                  <Form.Item label="分享地址">
                    {getFieldDecorator(`json[${v}].share_url`, {
                      initialValue: this.state.json[v].share_url,
                      rules: [
                        {
                          required: true,
                          message: '请填写分享地址, 如不需要此行为请删除本条目',
                        },
                        {
                          pattern: /^https?:\/\//,
                          message: '请正确填写分享地址',
                        },
                      ],
                    })(<Input placeholder="填写分享地址" />)}
                  </Form.Item>
                </>
              )}
            </Row>
          </Row>
        ))}
        <Row style={{ textAlign: 'center', marginBottom: 16 }} id="add-btn-line">
          <Button onClick={this.add}>新增行为</Button>
        </Row>
      </Form>
    );
  }
}

export default AddForm;
