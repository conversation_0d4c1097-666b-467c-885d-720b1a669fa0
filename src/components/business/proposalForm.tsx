import { opApi as api } from '@app/api';
import { Checkbox, Form, Input, message, Radio, Select } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader } from '../common';
import ArLinkInput, { arLinkValidator, isArLink } from '../common/arLinkInput';

@connectSession
@(Form.create({ name: 'proposalForm' }) as any)
class ProposalForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const s = {
      ...props.formContent,
    };
    this.state = {
      ...s,
      url: isArLink(s.url) ? '' : s.url,
      arUrl: isArLink(s.url) ? s.url : '',
      linkType: isArLink(s.url) ? 1 : 0,
    };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    const fields = ['title', 'pic_url'];
    if (this.state.linkType === 0) {
      fields.push('url');
    } else {
      fields.push('arUrl');
    }
    this.props.form.validateFieldsAndScroll(fields, (err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        let func = 'createProposal';
        const body = {
          ...values,
          type: this.props.type,
        };
        if (this.state.id) {
          func = 'updateProposal';
          body.id = this.state.id;
        }
        if (this.state.linkType === 1) {
          body.url = values.arUrl;
          delete body.arUrl;
        }
        api[func as keyof typeof api](body)
          .then(() => {
            setMLoading(this, false);
            message.success('操作成功');
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="名称">
          {getFieldDecorator('title', {
            initialValue: this.state.title,
            rules: [
              {
                required: true,
                message: '请填写推荐位名称',
              },
              {
                max: 20,
                message: '推荐位名称最长不能超过20个字',
              },
            ],
          })(<Input placeholder="请输入推荐位名称" />)}
        </Form.Item>
        <Form.Item label="类型">
          <Radio.Group
            value={this.state.linkType}
            onChange={(e: any) => this.setState({ linkType: e.target.value })}
          >
            <Radio value={0}>链接</Radio>
            <Radio value={1}>AR</Radio>
          </Radio.Group>
        </Form.Item>
        {this.state.linkType === 0 && (
          <Form.Item label="链接">
            {getFieldDecorator('url', {
              initialValue: this.state.url,
              preserve: true,
              rules: [
                {
                  required: true,
                  message: '请输入链接',
                },
                {
                  pattern: /^https?:\/\//,
                  message: '请输入正确的链接格式',
                },
              ],
            })(<Input placeholder="请输入链接" />)}
          </Form.Item>
        )}
        {this.state.linkType === 1 && (
          <Form.Item label="AR信息" required>
            {getFieldDecorator('arUrl', {
              initialValue: this.state.arUrl,
              preserve: true,
              rules: [
                {
                  validator: arLinkValidator,
                },
              ],
            })(<ArLinkInput />)}
          </Form.Item>
        )}
        <Form.Item label="图片" extra="支持jpg,jpeg,png图片格式">
          {getFieldDecorator('pic_url', {
            initialValue: this.state.pic_url,
            rules: [
              {
                required: true,
                message: '请上传图片',
              },
            ],
          })(<ImageUploader ratio={345 / 80} />)}
        </Form.Item>
      </Form>
    );
  }
}

export default ProposalForm;
