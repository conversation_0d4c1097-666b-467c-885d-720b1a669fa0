import React from "react";
import { Drawer } from '@components/common';
import { Form, Icon, Spin, Switch, Tabs, Tooltip, message } from 'antd';
import connect from '@utils/connectSession';
import SearchAndInput from "../common/newNewsSearchAndInput";
import { searchToObject, setMLoading } from "@app/utils/utils";
import { releaseListApi as api } from "@app/api";

@connect
@(Form.create({ name: 'LoopArticleDrawer' }) as any)
export default class LoopArticleDrawer extends React.Component<any, any> {

  state = {
    loading: false,
    detail: {},
    tab: 'first'
  }

  onOk = () => {
    const { form, onEnd } = this.props
    form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const { status, channelArticles } = values
        const totalArticlesCount = (channelArticles?.first?.length ?? 0) + (channelArticles?.second?.length ?? 0) + (channelArticles?.third?.length ?? 0)
        if (totalArticlesCount <= 0) {
          message.error('请至少关联一条稿件');
          return
        }

        setMLoading(this, true);
        api.contentRecommendFocusSave({
          type: 37,
          channel_id: searchToObject().channel_id,
          status: status ? 1 : 0,
          ref_ids: channelArticles?.first?.map((item: any) => item.id)?.join(','),
          ref_ids2: channelArticles?.second?.map((item: any) => item.id)?.join(','),
          ref_ids3: channelArticles?.third?.map((item: any) => item.id)?.join(','),
        }).then(() => {
          setMLoading(this, false);
          message.success('操作成功');
          onEnd();
        }).catch(() => {
          setMLoading(this, false);
        })
      } else {
        message.error('请检查表单内容');
      }
    })
  }

  componentDidUpdate(prevProps: Readonly<any>, prevState: Readonly<any>, snapshot?: any): void {
    const { visible, form: { resetFields } } = this.props
    if (!prevProps.visible && visible) {
      this.setState({ loading: true, detail: {} })
      resetFields()
      api.contentRecommendFocusDetail({ type: 37, channel_id: searchToObject().channel_id, })
        .then((res) => {
          const data: any = res.data
          const { recommend, article_list, article_list2, article_list3 } = data

          this.setState({
            loading: false, detail: {
              status: recommend.status === 1,
              channelArticles: {
                first: article_list?.map((v: any) => ({
                  channel_name: v.channel_name,
                  list_title: v.list_title,
                  id: v.id,
                })) ?? [],
                second: article_list2?.map((v: any) => ({
                  channel_name: v.channel_name,
                  list_title: v.list_title,
                  id: v.id,
                })) ?? [],
                third: article_list3?.map((v: any) => ({
                  channel_name: v.channel_name,
                  list_title: v.list_title,
                  id: v.id,
                })) ?? [],
              }
            }
          })
        })
        .catch(() => {
          this.setState({ loading: false })
        })
    }
  }

  changeTab(key: any) {
    this.setState({ ...this.state, tab: key })
  }

  validator(value: any) {
    const { form: { getFieldsValue } } = this.props
    const { channelArticles } = getFieldsValue()
    const result = Object.values(channelArticles).flatMap((v: any) => v).filter((v: any) => {
      return v != null && v.id == value.id
    })
    if (result.length > 0) {
      return '不能添加重复稿件'
    }

    return null
  }

  render() {
    const { visible, onClose, form: { getFieldDecorator, getFieldsValue } } = this.props
    const detail: any = this.state.detail
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const columns: any = [
      {
        title: '潮新闻ID',
        dataIndex: 'id',
        width: 80,
      },
      {
        title: '新闻频道',
        key: 'type',
        dataIndex: 'channel_name',
        width: 90,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
      },
    ]
    return (
      <Drawer
        skey="LoopArticleDrawer"
        title="走马灯管理"
        visible={visible}
        onClose={onClose}
        onOk={this.onOk}
        okPerm={`content_recommend:${searchToObject().channel_id}:focus_save:37`}>
        <Spin
          tip="正在加载..."
          spinning={this.state.loading}>
          <Form {...formLayout}>
            <Form.Item label="显示位置">走马灯稿件显示在频道顶部，详见示意图。
              <Tooltip title={<img src='/assets/banner_tip.png' width={150} height={135} />}>
                <Icon type="question-circle" />
              </Tooltip>
            </Form.Item>
            <Form.Item label="显示开关">
              {getFieldDecorator('status', {
                initialValue: detail.status,
                valuePropName: 'checked',
              })(<Switch />)}
            </Form.Item>
            {/* <Tabs type="card" style={{ marginLeft: 60 }} activeKey={this.state.tab} onChange={this.changeTab.bind(this)}>
              <Tabs.TabPane tab="位置一" key="first">
              </Tabs.TabPane>
              <Tabs.TabPane tab="位置二" key="second">
              </Tabs.TabPane>
              <Tabs.TabPane tab="位置三" key="third">
              </Tabs.TabPane>
            </Tabs> */}
            <h3 style={{ marginLeft: 60 }}>位置一</h3>
            <Form.Item key='first' label="关联稿件">
              {getFieldDecorator('channelArticles.first', {
                initialValue: detail.channelArticles?.first,
                preserve: true,
                rules: [
                  {
                    required: true,
                    message: '请关联新闻',
                    type: 'array',
                    validator: (rule: any, value: any, callback: any, source?: any, options?: any) => {
                      callback()
                    }
                  },
                  {
                    max: 10,
                    message: '最多关联10条新闻',
                    type: 'array',
                  },
                  // {
                  //   min: 2,
                  //   message: '为保证客户端显示效果，关联新闻数不能少于2条！',
                  //   type: 'array',
                  // },
                ],
              })(
                <SearchAndInput
                  max={10}
                  func="listArticleRecommendSearch"
                  columns={columns}
                  placeholder="输入ID或标题关联稿件"
                  body={{ doc_types: '2,3' }}
                  order={true}
                  addOnTop={true}
                  tips="最多关联10条新闻"
                  validator={this.validator.bind(this)}

                />
              )}
            </Form.Item>
            <h3 style={{ marginLeft: 60 }}>位置二</h3>
            <Form.Item key='second' label="关联稿件">
              {getFieldDecorator('channelArticles.second', {
                initialValue: detail.channelArticles?.second,
                preserve: true,
                rules: [
                  {
                    required: true,
                    message: '请关联新闻',
                    type: 'array',
                    validator: (rule: any, value: any, callback: any, source?: any, options?: any) => {
                      callback()
                    }
                  },
                  {
                    max: 10,
                    message: '最多关联10条新闻',
                    type: 'array',
                  },
                  // {
                  //   min: 2,
                  //   message: '为保证客户端显示效果，关联新闻数不能少于2条！',
                  //   type: 'array',
                  // },
                ],
              })(
                <SearchAndInput
                  max={10}
                  func="listArticleRecommendSearch"
                  columns={columns}
                  placeholder="输入ID或标题关联稿件"
                  body={{ doc_types: '2,3' }}
                  order={true}
                  addOnTop={true}
                  tips="最多关联10条新闻"
                  validator={this.validator.bind(this)}

                />
              )}
            </Form.Item>
            <h3 style={{ marginLeft: 60 }}>位置三</h3>
            <Form.Item key='third' label="关联稿件">
              {getFieldDecorator('channelArticles.third', {
                initialValue: detail.channelArticles?.third,
                preserve: true,
                rules: [
                  {
                    required: true,
                    message: '请关联新闻',
                    type: 'array',
                    validator: (rule: any, value: any, callback: any, source?: any, options?: any) => {
                      callback()
                    }
                  },
                  {
                    max: 10,
                    message: '最多关联10条新闻',
                    type: 'array',
                  },
                  // {
                  //   min: 2,
                  //   message: '为保证客户端显示效果，关联新闻数不能少于2条！',
                  //   type: 'array',
                  // },
                ],
              })(
                <SearchAndInput
                  max={10}
                  func="listArticleRecommendSearch"
                  columns={columns}
                  placeholder="输入ID或标题关联稿件"
                  body={{ doc_types: '2,3' }}
                  order={true}
                  addOnTop={true}
                  tips="最多关联10条新闻"
                  validator={this.validator.bind(this)}
                />
              )}
            </Form.Item>
          </Form>
        </Spin>
      </Drawer>
    )
  }
}