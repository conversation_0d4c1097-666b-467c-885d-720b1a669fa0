import { releaseListApi as api } from '@app/api';
import { Form, Input, message, Switch, } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';

@connectSession
@(Form.create({ name: 'LiveForm' }) as any)
class LiveForm extends React.Component<any, any> {
    constructor(props: any) {
        super(props);
        this.state = {
            ...props.formContent,
        };
    }

    handleSubmit = (e: any) => {
        e.preventDefault();
        this.doSubmit();
    };
    doSubmit = () => {
        this.props.form.validateFieldsAndScroll((err: any, values: any) => {
            if (!err) {
                setMLoading(this, true);
                const body = {
                    ...values,
                    article_id: this.state.article_id,
                };
                if (this.state.id) {
                    body.id = this.state.id;
                }
                if (this.state.status == true) {
                    body.status = 1
                }
                if (this.state.status == false) {
                    body.status = 0
                }
                api.saveBroadcast({
                    ...body
                }).then((res) => {
                    setMLoading(this, false);
                    message.success('操作成功');
                    this.props.onEnd();
                }).catch(() => {
                    setMLoading(this, false);
                })
            } else {
                message.error('请完善必填项信息后再添加');
            }
        });
    };

    render() {
        const { getFieldDecorator } = this.props.form;
        const { status } = this.state
        const formLayout = {
            labelCol: { span: 4 },
            wrapperCol: { span: 18 },
        };
        return (
            <Form {...formLayout} onSubmit={this.handleSubmit}>
                <Form.Item label="开关" extra="开启状态客户端展示广播内容">
                    {getFieldDecorator('status', {
                        initialValue: this.state.status,
                        rules: [
                            {
                                required: true,
                                message: '请填写分类名称',
                            },

                        ],
                    })(
                        <Switch checked={status === 1}
                            onChange={(status) => {
                                this.setState({
                                    status: status ? 1 : 0
                                })
                            }}
                            checkedChildren="开" unCheckedChildren="关" defaultChecked />
                    )}
                </Form.Item>
                <Form.Item label="广播内容">
                    {getFieldDecorator('content', {
                        initialValue: this.state.content,
                        rules: [
                            {
                                required: true,
                                message: '请填写广播内容',
                            },
                        ],
                    })(
                        <Input.TextArea placeholder='请输入广播内容，最多60个字符' autoSize={{ minRows: 5, maxRows: 7 }} maxLength={60} />
                    )}
                </Form.Item>
            </Form>
        );
    }
}

export default LiveForm;

