import { opApi as api } from '@app/api';
import { Checkbox, Form, Input, message, Select, Switch } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader } from '../common';

@connectSession
@(Form.create({ name: 'redpacketEntryForm' }) as any)
class RedPacketEntryForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      ...props.formContent,
    };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        api
          .updateRedPacketEntry({
            ...values,
          })
          .then(() => {
            setMLoading(this, false);
            message.success('操作成功');
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 6 },
      wrapperCol: { span: 16 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit} ref="form">
        <Form.Item label="悬浮窗图标" extra="支持jpg,jpeg,png,gif图片格式">
          {getFieldDecorator('pic_url', {
            initialValue: this.state.pic_url,
            rules: [
              {
                required: true,
                message: '请上传图片',
              },
            ],
          })(
            <ImageUploader
              ratio={2 / 3}
              accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
            />
          )}
        </Form.Item>
        <Form.Item label="红包首页浮窗显示">
          {getFieldDecorator('index_packet_switch', {
            initialValue: this.state.index_packet_switch,
            valuePropName: 'checked',
          })(<Switch checkedChildren="开启" unCheckedChildren="关闭" />)}
        </Form.Item>
        <Form.Item label="个人中心-我的红包入口">
          {getFieldDecorator('personal_my_packet_switch', {
            initialValue: this.state.personal_my_packet_switch,
            valuePropName: 'checked',
          })(<Switch checkedChildren="开启" unCheckedChildren="关闭" />)}
        </Form.Item>
        <Form.Item label="个人中心-红包累计金额">
          {getFieldDecorator('personal_packet_account_switch', {
            initialValue: this.state.personal_packet_account_switch,
            valuePropName: 'checked',
          })(<Switch checkedChildren="开启" unCheckedChildren="关闭" />)}
        </Form.Item>
      </Form>
    );
  }
}

export default RedPacketEntryForm;
