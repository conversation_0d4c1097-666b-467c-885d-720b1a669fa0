import { releaseListApi as api } from '@app/api';
import { Form, Input, message, Switch, } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader } from '../common';


@connectSession
@(Form.create({ name: 'FloatingAdvertisementForm' }) as any)
class FloatingAdvertisementForm extends React.Component<any, any> {
    constructor(props: any) {
        super(props);
        this.state = {
            ...props.formContent,
        };
    }

    handleSubmit = (e: any) => {
        e.preventDefault();
        this.doSubmit();
    };

    doSubmit = () => {
        this.props.form.validateFieldsAndScroll((err: any, values: any) => {
            if (!err) {
                setMLoading(this, true);
                const body = {
                    ...values,
                };
                if (this.state.id) {
                    body.id = this.state.id;
                }
                body.channel_id = this.state.channel_id
                body.article_id = this.state.article_id
                if(body.status == false) {
                    body.status = 0
                }
                if(body.status == true) {
                    body.status = 1
                }
                api.saveSuspendedAd({
                    ...body
                }).then(() => {
                    setMLoading(this, false);
                    message.success('操作成功');
                    this.props.onEnd();
                }).catch(() => {
                    setMLoading(this, false);
                });

            } else {
                message.error('请完善必填项信息后再添加');
            }
        });
    };

    render() {
        const { getFieldDecorator } = this.props.form;
        const { status } = this.state
        const formLayout = {
            labelCol: { span: 4 },
            wrapperCol: { span: 18 },
        };
        return (
            <Form {...formLayout} onSubmit={this.handleSubmit}>
                <Form.Item label="开关" extra="开启状态客户端展示浮窗广告">
                    {getFieldDecorator('status', {
                        initialValue: this.state.status,
                        rules: [
                            {
                                required: true,
                                message: '请选择是否开启',
                            },

                        ],
                    })(
                        <Switch checked={status === 1}
                            onChange={(status) => {
                                this.setState({
                                    status: status ? 1 : 0
                                })
                            }}
                            checkedChildren="开" unCheckedChildren="关" />
                    )}
                </Form.Item>
                <Form.Item label="链接地址">
                    {getFieldDecorator('url', {
                        initialValue: this.state.url,
                        rules: [
                            {
                                required: true,
                                message: '请填写链接地址',
                            },
                        ],
                    })(
                        <Input placeholder='请输入链接地址,最多可输入255个字符' maxLength={255} />
                    )}
                </Form.Item>
                <Form.Item label="图片" extra="图片尺寸：360*120像素；支持jpg、jpeg、png、gif图片格式；最大不能超过0.5M">
                    {getFieldDecorator('expand_img_url', {
                        initialValue: this.state.expand_img_url,
                        rules: [
                            {
                                required: true,
                                message: '请上传图片',
                            },
                        ],
                    })(<ImageUploader ratio={3/1} imgsize={512} accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}/>)}
                </Form.Item>

            </Form>
        );
    }
}

export default FloatingAdvertisementForm;

