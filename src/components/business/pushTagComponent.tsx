import { Button, Checkbox, Col, message, Radio, Row, Select, TreeSelect } from 'antd';
import React, { useEffect, useState } from 'react';
import { opApi as api, opApi } from '@app/api';

const readTags = [
  '高层动态',
  '地方政务',
  '国际时事',
  '部委信息',
  '体育赛事',
  '文化产业',
  '正能量',
  '八八战略',
  '社会治安',
  '人工智能',
  '经济建设',
  '数字化改革',
  '美食',
  '互联网公司',
  '法治建设',
  '反腐倡廉',
  '房产行情',
  '国际军事关系',
  '高质量发展',
  '社会治安防控',
  '金融业',
  '生态文明建设',
  '社区建设',
  '就业创业',
  '民生',
];

const timeTags = [
  {
    value: '00-05',
    label: '0点-5点',
  },
  {
    value: '05-08',
    label: '5点-8点',
  },
  {
    value: '08-11',
    label: '8点-11点',
  },
  {
    value: '11-18',
    label: '11点-18点',
  },
  {
    value: '18-21',
    label: '18点-21点',
  },
  {
    value: '21-24',
    label: '21点-24点',
  },
];

export const PushTagComponent = (props: any) => {
  const {
    search_relation = 'and',
    location = [],
    gov_emp_type = '',
    article_read_preference = '',
    time_preference = '',
  } = props.value;
  const channelPreferenceValue = !!article_read_preference
    ? article_read_preference.split(';')
    : [];
  const channelPreferenceCheckAll = channelPreferenceValue.length == readTags.length;
  const channelPreferenceIndeterminate =
    channelPreferenceValue.length > 0 && channelPreferenceValue.length < readTags.length;

  const govValue = !!gov_emp_type ? gov_emp_type.split(';') : [];
  const govCheckAll = govValue.length == 2;
  const govIndeterminate = govValue.length > 0 && govValue.length < 2;

  const timePreferenceValue = !!time_preference ? time_preference.split(';') : [];
  const timePreferenceCheckAll = timePreferenceValue.length == timeTags.length;
  const timePreferenceIndeterminate =
    timePreferenceValue.length > 0 && timePreferenceValue.length < timeTags.length;

  const triggerChange = (changedValue: any) => {
    const { onChange, value } = props;
    if (onChange) {
      onChange({
        ...value,
        ...changedValue,
      });
    }
  };
  const [loading, setLoading] = useState(false);
  const [count, setCount] = useState(-1);

  const computeCount = () => {
    if (!article_read_preference && !gov_emp_type && !time_preference && location.length == 0) {
      message.error('至少选择一个标签维度');
      return
    }
    const body: any = {
      search_relation: search_relation,
      location: location,
      article_read_preference: article_read_preference,
      time_preference: time_preference,
      gov_emp_type: gov_emp_type,
    };

    setLoading(true);
    setCount(-1);
    opApi
      .getPushCount(body)
      .then((res: any) => {
        setLoading(false);
        setCount(res.data.total);
      })
      .catch(() => {
        setLoading(false);
        setCount(-1);
      });
  };

  useEffect(() => {
    props.onChange({
      search_relation,
      location,
      gov_emp_type,
      article_read_preference,
      time_preference,
    });
  }, []);

  return (
    <div>
      <Radio.Group
        value={search_relation}
        onChange={(e) => triggerChange({ search_relation: e.target.value })}
      >
        <Radio value={'and'}>且</Radio>
        <Radio value={'or'}>或</Radio>
      </Radio.Group>
      <div style={{ color: 'rgba(0, 0, 0, 0.45)' }}>仅选择一个维度的标签时，且/或 不生效</div>
      <div key="city">
        <span>所在地区:</span>
        <TreeSelect
          value={location}
          treeData={props.areaList}
          placeholder="请选择地区，支持多选"
          treeDefaultExpandAll={false}
          treeCheckable={true}
          // style={{ marginLeft: '30px' }}
          onChange={(e) => triggerChange({ location: e })}
        />
      </div>
      <div key={'read'} style={{ padding: '10px 0' }}>
        <Checkbox
          checked={channelPreferenceCheckAll}
          indeterminate={channelPreferenceIndeterminate}
          onChange={(e) => {
            if (e.target.checked) {
              triggerChange({
                article_read_preference: readTags.join(';'),
              });
            } else {
              triggerChange({
                article_read_preference: '',
              });
            }
          }}
        >
          阅读偏好
        </Checkbox>
        <div style={{ marginLeft: '30px' }}>
          <Checkbox.Group
            value={channelPreferenceValue}
            onChange={(e) => {
              triggerChange({
                article_read_preference: e.join(';'),
              });
            }}
          >
            {readTags.map((v, i) => {
              return <Checkbox value={v}>{v}</Checkbox>;
            })}
          </Checkbox.Group>
        </div>
      </div>
      <div key={'zzd'} style={{ padding: '10px 0' }}>
        <Checkbox
          checked={govCheckAll}
          indeterminate={govIndeterminate}
          onChange={(e) => {
            if (e.target.checked) {
              triggerChange({
                gov_emp_type: '0;1',
              });
            } else {
              triggerChange({
                gov_emp_type: '',
              });
            }
          }}
        >
          身份
        </Checkbox>
        <div style={{ marginLeft: '30px' }}>
          <Checkbox.Group
            value={govValue}
            onChange={(e) => {
              triggerChange({
                gov_emp_type: e.join(';'),
              });
            }}
          >
            <Checkbox value="1">浙政钉人员</Checkbox>
            <Checkbox value="0">非浙政钉人员</Checkbox>
          </Checkbox.Group>
        </div>
      </div>
      <div key={'time'} style={{ padding: '10px 0' }}>
        <Checkbox
          checked={timePreferenceCheckAll}
          indeterminate={timePreferenceIndeterminate}
          onChange={(e) => {
            if (e.target.checked) {
              triggerChange({
                time_preference: timeTags.map((v) => v.value).join(';'),
              });
            } else {
              triggerChange({
                time_preference: '',
              });
            }
          }}
        >
          使用时段
        </Checkbox>
        <div style={{ marginLeft: '30px' }}>
          <Checkbox.Group
            value={timePreferenceValue}
            onChange={(e) => {
              triggerChange({
                time_preference: e.join(';'),
              });
            }}
          >
            {timeTags.map((v) => {
              return <Checkbox value={v.value}>{v.label}</Checkbox>;
            })}
          </Checkbox.Group>
        </div>
      </div>
      <Button disabled={loading} onClick={computeCount}>
        {loading ? '计算中...' : '计算人数'}
      </Button>
      {count > -1 && (
        <span>
          &nbsp;&nbsp;约选择 <span style={{ color: 'red' }}>{count}</span> 人
        </span>
      )}
    </div>
  );
};
export default PushTagComponent;
