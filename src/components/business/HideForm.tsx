import { userApi as api } from '@app/api';
import { Form, Input, message, Radio, DatePicker } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import moment from 'moment';
import { releaseListApi } from '@app/api';
const { RangePicker } = DatePicker;

@connectSession
@(Form.create({ name: 'HideForm' }) as any)
class HideForm extends React.Component<any, any> {
    constructor(props: any) {
        super(props);
        this.state = {
            ...props.HideForm,
        };
    }
    componentDidMount() {
        console.log(this.props,'state==========');
        this.setState({
            ...this.state.first,
            start_show_time: moment(this.state.start_show_time),
            end_show_time: moment(this.state.end_show_time),
        })
    }
    handleSubmit = (e: any) => {
        e.preventDefault();
        this.doSubmit();
    };

    doSubmit = () => {
        this.props.form.validateFieldsAndScroll((err: any, values: any) => {
            console.log(values, 'values===========');
            if (!err) {
                const data = !values.is_timing ? 
                 {
                    id: this.state.id,
                    is_timing: values.is_timing,
                    visible: values.visible,
                } : {
                    id: this.state.id,
                    visible: false,
                    is_timing: values.is_timing ,
                    start_show_time: values.date[0].format("YYYY-MM-DD HH:mm:ss") ,
                    end_show_time: values.date[1].format("YYYY-MM-DD HH:mm:ss")
                }
                setMLoading(this, true);
                releaseListApi.releaseChangeVisible(data).then(res => {
                    setMLoading(this, false);
                    this.props.onEnd();
                    message.success('操作成功');
                }).catch(() => {
                    setMLoading(this, false);
                })
            } else {
                message.error('请检查表单内容');
            }
        });
    };
    changeRadio = (e: any) => {
        this.setState({
            ...this.state,
            is_timing:e.target.value
        })
    }
    render() {
        const { getFieldDecorator } = this.props.form;
        const formLayout = {
            labelCol: { span: 4 },
            wrapperCol: { span: 18 },
        };
        return (
            <Form {...formLayout} onSubmit={this.handleSubmit} ref="form">
                <Form.Item label="类型">
                    {getFieldDecorator('is_timing', {
                        initialValue: this.state.is_timing ,
                        rules: [
                            {
                                required: true,
                                message: '请填写问题描述',
                            },
                        ],
                    })(<Radio.Group onChange={this.changeRadio}>
                        <Radio value={false}>直接设置</Radio>
                        <Radio value={true}>定时设置</Radio>
                    </Radio.Group>)}
                </Form.Item>
                {
                !this.state.is_timing && <Form.Item label="隐藏">
                        {getFieldDecorator('visible', {
                            initialValue: this.state.visible,
                            rules: [
                                {
                                    required: true,
                                    message: '请填写问题答复',
                                },
                            ],
                        })(<Radio.Group >
                            <Radio value={false}>是</Radio>
                            <Radio value={true}>否</Radio>
                        </Radio.Group>)}
                    </Form.Item>
                }
                {
                !!this.state.is_timing && <Form.Item label={`显示时间`}>
                        {getFieldDecorator('date', {
                            initialValue: !!this.props.HideForm.is_timing ? [moment(this.state.start_show_time), moment(this.state.end_show_time)] : [] ,
                            rules: [
                                {
                                    required: true,
                                    message: '结束时间必须大于开始时间',
                                },
                            ],
                        })(<RangePicker showTime format="YYYY-MM-DD HH:mm:ss" />,)}
                    </Form.Item>
                }
            </Form>
        );
    }
}

export default HideForm;
