import { opApi as api } from '@app/api';
import { Checkbox, Form, Input, message, Select } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';

@connectSession
@(Form.create({ name: 'appFeatureForm' }) as any)
class AppFeatureForm extends React.Component<any, any> {
  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
          setMLoading(this, true);
          const { record } = this.props
          if (record){
              api
                  .editFeature({
                      ...values,
                      id:record.id
                  })
                  .then(() => {
                      setMLoading(this, false);
                      message.success('编辑成功');
                      this.props.onEnd();
                  })
                  .catch(() => {
                      setMLoading(this, false);
                  });
          }else {
              api
                  .createFeature({
                      ...values,
                  })
                  .then(() => {
                      setMLoading(this, false);
                      message.success('添加成功');
                      this.props.onEnd();
                  })
                  .catch(() => {
                      setMLoading(this, false);
                  });
          }
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  render() {
      const { record, form: { getFieldDecorator } } = this.props
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="功能名称">
          {getFieldDecorator('name', {
            initialValue: record ? record.name : '',
            rules: [
              {
                required: true,
                message: '请填写功能名称',
              },
              {
                max: 15,
                message: '功能名称最长不能超过15个字',
              },
              {
                pattern: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/,
                message: '功能名称不能包含特殊字符',
              },
            ],
          })(<Input placeholder="请输入功能名称" />)}
        </Form.Item>
        <Form.Item label="功能编码">
          {getFieldDecorator('feature', {
            initialValue: record ? record.feature : '',
            rules: [
              {
                required: true,
                message: '请输入功能编码',
              },
              {
                pattern: /^[a-zA-Z_]+$/,
                message: '功能编码仅支持输入英文字符及下划线',
              },
            ],
          })(<Input placeholder="仅支持输入英文字符" />)}
        </Form.Item>
        <Form.Item label="系统类型">
          {getFieldDecorator('device_type', {
            initialValue:  record ? record.device_type :  1,
          })(
            <Select disabled={record ? true : false}>
              <Select.Option value={1}>iOS</Select.Option>
              <Select.Option value={2}>Android</Select.Option>
              <Select.Option value={3}>Web</Select.Option>
              <Select.Option value={4}>鸿蒙</Select.Option>
            </Select>
          )}
        </Form.Item>
        <Form.Item label="是否开启">
          {getFieldDecorator('enabled', {
            initialValue: record ? record.enabled : false,
            valuePropName: 'checked',
          })(<Checkbox />)}
        </Form.Item>
      </Form>
    );
  }
}

export default AppFeatureForm;
