import { opApi as api } from '@app/api';
import { Checkbox, Form, Input, message, Radio } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader, FileUploader } from '../common';

@connectSession
@(Form.create({ name: 'musicCategoryForm' }) as any)
class MusicCategoryForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      ...props.formContent,
    };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  }

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        let func = 'createMusicCategory';
        const body = {
          ...values,
        };
        if (this.state.id) {
          func = 'updateMusicCategory';
          body.id = this.state.id;
        }
        api[func as keyof typeof api](body)
          .then(() => {
            setMLoading(this, false);
            message.success('操作成功');
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  }

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit} ref="form">
        <Form.Item label="分类名">
          {getFieldDecorator('class_name', {
            initialValue: this.state.class_name,
            rules: [
              {
                required: true,
                message: '请填写分类名',
              },
              {
                max: 10,
                message: '分类名称不能多于10个字',
              },
            ],
          })(<Input placeholder="请输入分类名" />)}
        </Form.Item>
      </Form>
    );
  }
}

export default MusicCategoryForm;
