import React, { useState, useEffect } from 'react';
import { Form, Input, InputNumber, Select, Radio, Row, Col, message } from 'antd';
import connectSession from '@utils/connectSession';
import { opApi as api } from '@app/api';
import { cloneDeep } from 'lodash';
import { setMLoading } from '@utils/utils';

// const { Title } = Typography;

@connectSession
@(Form.create({ name: 'CreateFilmActivitiesForm' }) as any)
class CreateFilmActivitiesForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const { formContent } = props;
    this.state = {
      ...props,
      restrictState: 1,
      selectData: [],
      idMessage: '请至少输入1个新用户券池ID',
      inputCheckState: true,
      inputArray: {
        taoppNewState: false,
        taoppOldState: false,
        maoyNewState: false,
        maoyOldState: false,
      },
      form: {
        id: undefined,
        activity_name: '',
        user_type: 1,
        redeem_code: 0,
        taopp_old_id: undefined,
        taopp_new_id: undefined,
        maoy_old_id: undefined,
        maoy_new_id: undefined,
        activity_restrict_date: 7,
        activity_restrict_count: 1,
      },
    };
    if (formContent.id) {
      Object.assign(this.state, {
        restrictState: formContent.activity_restrict_date ? 1 : 2,
        inputArray: {
          taoppNewState: !!formContent.taopp_new_id,
          taoppOldState: !!formContent.taopp_old_id,
          maoyNewState: !!formContent.maoy_new_id,
          maoyOldState: !!formContent.maoy_old_id,
        },
        form: {
          id: formContent.id,
          activity_name: formContent.activity_name,
          user_type: formContent.user_type,
          redeem_code: formContent.redeem_code,
          redeem_code_name: formContent.redeem_code_name,
          taopp_old_id: formContent.taopp_old_id,
          taopp_new_id: formContent.taopp_new_id,
          maoy_old_id: formContent.maoy_old_id,
          maoy_new_id: formContent.maoy_new_id,
          activity_restrict_date: formContent.activity_restrict_date,
          activity_restrict_count: formContent.activity_restrict_count,
        },
      });
    }
    if (this.state.form.user_type === 3) {
      Object.assign(this.state, {
        idMessage: '请至少输入1个新用户券池ID和1个老用户券池ID',
      });
    }
    if (this.state.form.user_type === 2) {
      Object.assign(this.state, {
        idMessage: '请至少输入1个老用户券池ID',
      });
    }
    this.getSelectList();
  }

  // eslint-disable-next-line react/sort-comp
  inputCheck() {
    const { user_type } = this.state.form;
    const { inputArray } = this.state;
    let inputCheckState = false;
    if (user_type === 1) {
      if (inputArray.taoppNewState || inputArray.maoyNewState) {
        inputCheckState = true;
      }
    } else if (user_type === 2) {
      if (inputArray.taoppOldState || inputArray.maoyOldState) {
        console.log('旧用户必填成功');
        inputCheckState = true;
      }
    } else if (user_type === 3) {
      let newState = false;
      let oldState = false;
      if (inputArray.taoppNewState || inputArray.maoyNewState) {
        newState = true;
      }
      if (inputArray.taoppOldState || inputArray.maoyOldState) {
        oldState = true;
      }
      if (newState && oldState) {
        inputCheckState = true;
      }
    }
    this.setState({
      inputCheckState,
    });
    return inputCheckState;
  }

  getSelectList() {
    const redeem_code_type = {
      redeem_code_type: 1,
    };
    api.getMovieRedeemCodeList(redeem_code_type).then((r: any) => {
      const { records } = r.data.code_list;
      this.setState({ selectData: records });
    });
  }

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      // 两个效验都成功
      if (this.inputCheck() && !err) {
        setMLoading(this, true);
        const { form } = this.state;
        this.setState({
          form: { ...form, activity_name: values.activity_name },
        });
        const data = cloneDeep(form);
        data.activity_name = values.activity_name;
        Object.keys(data).forEach((x) => {
          if (!data[x]) {
            if (x !== 'redeem_code') {
              delete data[x];
            }
          }
        });

        if (form.user_type === 1) {
          delete data.taopp_old_id;
          delete data.maoy_old_id;
        }

        if (form.user_type === 2) {
          delete data.taopp_new_id;
          delete data.maoy_new_id;
        }

        if (this.state.restrictState === 2) {
          data.activity_restrict_count = 0;
          data.activity_restrict_date = 0;
        }

        if (data.id) {
          api
            .updateMovieTicketActivity(data)
            .then((r) => {
              message.success('操作成功');
              setMLoading(this, false);
              this.props.onEnd();
            })
            .catch(() => {
              setMLoading(this, false);
            });
        } else {
          api
            .creatMovieTicketActivity(data)
            .then((r) => {
              message.success('操作成功');
              setMLoading(this, false);
              this.props.onEnd();
            })
            .catch(() => {
              setMLoading(this, false);
            });
        }
      } else {
        // 两个必有一个失败
        console.log(err);
      }
    });
  };

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  handleChange = (vl: any) => {
    this.setState({
      form: { ...this.state.form, redeem_code: vl },
    });
  };

  inpurNameChange = (e: any) => {
    this.setState({
      form: { ...this.state.form, activity_name: e.target.value },
    });
  };

  inptTaoNewChange = (e: any) => {
    if (e.target.value.length > 0) {
      this.setState({
        inputArray: { ...this.state.inputArray, taoppNewState: true },
        form: { ...this.state.form, taopp_new_id: e.target.value },
      });
    } else {
      this.setState({
        inputArray: { ...this.state.inputArray, taoppNewState: false },
        form: { ...this.state.form, taopp_new_id: e.target.value },
      });
    }
    this.inputCheck();
  };

  inptTaoOldChange = (e: any) => {
    if (e.target.value.length > 0) {
      this.setState({
        inputArray: { ...this.state.inputArray, taoppOldState: true },
        form: { ...this.state.form, taopp_old_id: e.target.value },
      });
    } else {
      this.setState({
        inputArray: { ...this.state.inputArray, taoppOldState: false },
        form: { ...this.state.form, taopp_old_id: e.target.value },
      });
    }
    this.inputCheck();
  };

  inptMaoNewChange = (e: any) => {
    if (e.target.value.length > 0) {
      this.setState({
        inputArray: { ...this.state.inputArray, maoyNewState: true },
        form: { ...this.state.form, maoy_new_id: e.target.value },
      });
    } else {
      this.setState({
        inputArray: { ...this.state.inputArray, maoyNewState: false },
        form: { ...this.state.form, maoy_new_id: e.target.value },
      });
    }
    this.inputCheck();
  };

  inptMaoOldChange = (e: any) => {
    if (e.target.value.length > 0) {
      this.setState({
        inputArray: { ...this.state.inputArray, maoyOldState: true },
        form: { ...this.state.form, maoy_old_id: e.target.value },
      });
    } else {
      this.setState({
        inputArray: { ...this.state.inputArray, maoyOldState: false },
        form: { ...this.state.form, maoy_old_id: e.target.value },
      });
    }
    this.inputCheck();
  };

  radioUserChange = (e: any) => {
    let Message = '请至少输入1个新用户券池ID';
    if (e.target.value === 2) {
      Message = '请至少输入1个老用户券池ID';
    } else if (e.target.value === 3) {
      Message = '请至少输入1个新用户券池ID和1个老用户券池ID';
    }
    this.setState({
      idMessage: Message,
      form: { ...this.state.form, user_type: e.target.value },
    });
  };

  radiogroupChange = (e: any) => {
    if (e.target.value === 2) {
      this.setState({
        restrictState: e.target.value,
        form: {
          ...this.state.form,
          activity_restrict_date: 0,
          activity_restrict_count: 0,
        },
      });
    } else {
      this.setState({
        restrictState: e.target.value,
        form: {
          ...this.state.form,
          activity_restrict_date: 7,
          activity_restrict_count: 1,
        },
      });
    }
  };

  inputNumberCountChange = (e: any) => {
    this.setState({
      form: { ...this.state.form, activity_restrict_count: e },
    });
  };

  inputNumberDatetChange = (e: any) => {
    this.setState({
      form: { ...this.state.form, activity_restrict_date: e },
    });
  };

  render() {
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const { getFieldDecorator } = this.props.form;
    const { form, selectData, restrictState } = this.state;
    const validateMessages = {
      required: '请输入活动名称',
    };

    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="名称">
          <Col span={16}>
            {getFieldDecorator('activity_name', {
              initialValue: form.activity_name,
              rules: [
                {
                  required: true,
                  message: '名称不能为空',
                },
                {
                  max: 20,
                  message: '最多20个字',
                },
              ],
            })(<Input placeholder="请输入活动名称，不超过20个汉字" />)}
            {/* <Input
              placeholder="请输入活动名称，不超过20个汉字"
              value={form.activity_name}
              onChange={this.inpurNameChange}
            /> */}
          </Col>
        </Form.Item>
        <Form.Item label="面向用户" required>
          <Radio.Group name="user" value={form.user_type} onChange={this.radioUserChange}>
            <Radio value={1}>新用户</Radio>
            <Radio value={2}>老用户</Radio>
            <Radio value={3}>全部用户</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="兑换码">
          <Col span={12}>
            <Select
              placeholder="请选择兑换码"
              value={form.redeem_code}
              onChange={this.handleChange}
            >
              <Select.Option key={0} value={0}>
                无兑换码
              </Select.Option>
              {selectData.map((t: any) => {
                return (
                  <Select.Option key={t.id} value={t.redeem_code}>
                    {t.redeem_code_name}
                  </Select.Option>
                );
              })}
            </Select>
          </Col>
        </Form.Item>
        <Form.Item
          label="消费券池"
          className={this.state.inputCheckState ? '' : 'has-error'}
          required
        >
          {form.user_type !== 2 && (
            <Row align="middle" justify="start">
              <Col span={12}>
                <Input
                  maxLength={50}
                  placeholder="请输入新用户券池ID"
                  value={form.taopp_new_id}
                  onChange={this.inptTaoNewChange}
                />
              </Col>
              <Col span={10} offset={2}>
                淘票票渠道
              </Col>
            </Row>
          )}

          {form.user_type !== 1 && (
            <Row align="middle" justify="start">
              <Col span={12}>
                <Input
                  maxLength={50}
                  placeholder="请输入老用户券池ID"
                  value={form.taopp_old_id}
                  onChange={this.inptTaoOldChange}
                />
              </Col>
              <Col span={10} offset={2}>
                淘票票渠道
              </Col>
            </Row>
          )}
          {form.user_type !== 2 && (
            <Row justify="space-between" align="bottom">
              <Col span={12}>
                <Input
                  maxLength={50}
                  placeholder="请输入新用户券池ID"
                  value={form.maoy_new_id}
                  onChange={this.inptMaoNewChange}
                />
              </Col>
              <Col span={10} offset={2}>
                猫眼渠道
              </Col>
            </Row>
          )}

          {form.user_type !== 1 && (
            <Row justify="space-between" align="bottom">
              <Col span={12}>
                <Input
                  maxLength={50}
                  placeholder="请输入老用户券池ID"
                  value={form.maoy_old_id}
                  onChange={this.inptMaoOldChange}
                />
              </Col>
              <Col span={10} offset={2}>
                猫眼渠道
              </Col>
            </Row>
          )}
          <Row justify="space-between" align="bottom">
            <Col span={12}>
              <p style={{ color: 'red' }}>{this.state.idMessage}</p>
            </Col>
          </Row>
        </Form.Item>

        <Form.Item label="领取限制" required>
          <Radio.Group name="radiogroup" value={restrictState} onChange={this.radiogroupChange}>
            <Radio value={1}>
              <InputNumber
                style={{ width: '60px' }}
                size="small"
                min={0}
                max={99}
                disabled={restrictState !== 1}
                value={form.activity_restrict_date}
                onChange={this.inputNumberDatetChange}
              />
              &nbsp;&nbsp;日内限领&nbsp;&nbsp;
              <InputNumber
                style={{ width: '60px' }}
                size="small"
                min={0}
                max={99}
                disabled={restrictState !== 1}
                value={form.activity_restrict_count}
                onChange={this.inputNumberCountChange}
              />
              &nbsp;&nbsp;次
            </Radio>
            <Radio value={2}>无限制</Radio>
          </Radio.Group>
        </Form.Item>
      </Form>
    );
  }
}

export default CreateFilmActivitiesForm;
