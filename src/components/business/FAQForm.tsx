import { userApi as api } from '@app/api';
import { Form, Input, message } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { CKEditor } from '../common';

@connectSession
@(Form.create({ name: 'FAQForm' }) as any)
class FAQForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      ...props.formContent,
    };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        let func = 'createFAQ';
        const body = {
          ...values,
        };
        if (this.state.id) {
          func = 'updateFAQ';
          body.id = this.state.id;
        }
        api[func as keyof typeof api](body)
          .then(() => {
            setMLoading(this, false);
            message.success('操作成功');
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit} ref="form">
        <Form.Item label="问题描述">
          {getFieldDecorator('title', {
            initialValue: this.state.title,
            rules: [
              {
                required: true,
                message: '请填写问题描述',
              },
              {
                max: 50,
                message: '问题描述最长不能超过20个字',
              },
            ],
          })(<Input placeholder="请填写问题描述" />)}
        </Form.Item>
        <Form.Item label="问题答复">
          {getFieldDecorator('content', {
            initialValue: this.state.content,
            rules: [
              {
                required: true,
                message: '请填写问题答复',
              },
            ],
          })(<CKEditor />)}
        </Form.Item>
      </Form>
    );
  }
}

export default FAQForm;
