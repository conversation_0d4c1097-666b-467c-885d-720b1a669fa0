import React from "react";
import { Drawer } from '@components/common';
import { Tree, Spin, message } from 'antd';
import { systemApi, listApi } from '@app/api';
import { searchToObject, setMLoading } from '@utils/utils';
import connect from '@utils/connectSession';

@connect
class PushChannelDrawer extends React.Component<any, any> {

  constructor(props: any) {
    super(props)
    this.state = {
      loading: false,
      channelList: [],
      checkedKeys: [],
      disabledCheckedKeys: [],
    }
  }

  componentDidUpdate(prevProps: Readonly<any>, prevState: Readonly<any>, snapshot?: any): void {
    if (!prevProps.visible && this.props.visible) {
      this.setState({ checkedKeys: [] })
      if (!this.state.channelList.length) {
        this.getChannelList()
      }
    }
    if (prevProps.record !== this.props.record && this.props.record) {
      // 切换了稿件，请求稿件推频道信息
      const { id: article_id } = this.props.record
      if (!article_id) {
        return
      }
      listApi.getToChannelList({ article_id }).then((r: any) => {
        // 这里返回的频道列表都是推过的，不能二次推
        const { data: {article, list = []} } = r
        if (article.id === article_id) {
          const keys = list.map((item: any) => item.channel_id)
          this.setState({ disabledCheckedKeys: keys, checkedKeys: keys })
        }
      }).catch(() => { })
    }
  }

  getChannelList = () => {
    this.setState({ loading: true });
    systemApi
      .getAllChannelsTree()
      .then((r: any) => {
        this.setState({
          channelList: r.data.channel_list,
          loading: false,
        });
      })
      .catch(() => {
        this.setState({ loading: false });
      });
  }

  renderTreeNodeDisabled = (node: any) => {
    // 自己不能选，潮客，潮鸣号不能选
    const channel_id = searchToObject().channel_id
    return channel_id === node.id || node.name === '潮客' || node.name === '潮鸣号' || this.state.disabledCheckedKeys.includes(node.id) || (this.state.checkedKeys.length >= 3 && !this.state.checkedKeys.includes(node.id))
  }

  renderTreeNode = (node: any) => {
    return node.children && node.children.length > 0 ? (
      <Tree.TreeNode title={node.name} key={node.id} disabled={true}>
        {node.children.map((snode: any) => this.renderTreeNode(snode))}
      </Tree.TreeNode>
    ) : (
      <Tree.TreeNode title={node.name} key={node.id} disabled={this.renderTreeNodeDisabled(node)} />
    );
  }

  handleCheckChannel = (checkedKeys: any) => {
    this.setState({ checkedKeys })
  }

  handleTreeClick = () => {
    const beforeCheckedKeysCount = this.state.checkedKeys.length
    setTimeout(() => {
      const currentCheckedKeysCount = this.state.checkedKeys.length
      if (beforeCheckedKeysCount >= 3 && currentCheckedKeysCount >= 3) {
        message.error('最多支持选择三个频道');
      }
    }, 0);
  }

  handleOkClick = () => {
    if (this.state.checkedKeys.length < 1) {
      message.error('请至少选择一个频道')
      return
    }
    const { onOk, record } = this.props
    if (!record.id) {
      return
    }
    const canSaveKeys = this.state.checkedKeys.filter((key: any) => !this.state.disabledCheckedKeys.includes(key))
    setMLoading(this, true);
    onOk(record.id, canSaveKeys.join(','), () => {
      setMLoading(this, false);
    })
  }

  render() {
    const { visible, onClose, skey, record } = this.props
    return <Drawer
      title="推至多频道"
      onClose={onClose}
      visible={visible}
      skey={skey}
      onOk={this.handleOkClick}
    >
      <Spin
        tip="正在加载..."
        spinning={this.state.loading}
      >
        <div style={{ display: 'flex', marginBottom: '8px' }}><span style={{ width: '80px' }}>潮新闻ID</span><span>{record.id}</span></div>
        <div style={{ display: 'flex', marginBottom: '8px' }}><span style={{ width: '80px' }}>新闻标题</span><span>{record.list_title}</span></div>
        <div style={{ display: 'flex', marginBottom: '8px' }}>
          <span style={{ width: '70px' }}>推至频道</span>
          <div onClick={this.handleTreeClick} >
            <Tree selectable={false} checkedKeys={this.state.checkedKeys} onCheck={this.handleCheckChannel} checkable>
              {this.state.channelList.map((node: any) => this.renderTreeNode(node))}
            </Tree>
          </div>
        </div>
      </Spin>
    </Drawer>
  }
}

export default PushChannelDrawer