import { opApi as api } from '@app/api';
import { Checkbox, Form, Input, message, Select, DatePicker, InputNumber } from 'antd';
import React from 'react';
import { setMLoading, setMenu } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader } from '../common';

@connectSession
@(Form.create({ name: 'medalForm' }) as any)
class MedalForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      achievement_desc: '',
      achievement_name: '',
      medal_pic_url: '',
      ...props.formContent,
    };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        const body: any = { ...values };
        let func = api.createMedal;
        if (this.state.id) {
          body.id = this.state.id;
          func = api.updateMedal;
        }
        func(body)
          .then(() => {
            setMLoading(this, false);
            message.success('添加成功');
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="勋章名称">
          {getFieldDecorator('achievement_name', {
            initialValue: this.state.achievement_name,
            rules: [
              {
                required: true,
                message: '请输入勋章名称',
              },
              {
                max: 15,
                message: '勋章名称不能超过15个字',
              },
            ],
          })(<Input placeholder="请输入勋章名称" />)}
        </Form.Item>
        <Form.Item label="获得条件">
          {getFieldDecorator('achievement_desc', {
            initialValue: this.state.achievement_desc,
            rules: [
              {
                required: true,
                message: '请输入获得条件',
              }
            ],
          })(<Input placeholder="请输入获得条件" />)}
        </Form.Item>
        <Form.Item label="勋章ICON" extra="支持jpg,jpeg,png格式, 比例1:1">
          {getFieldDecorator('medal_pic_url', {
            initialValue: this.state.medal_pic_url,
            rules: [
              {
                required: true,
                message: '请上传勋章ICON',
              },
            ],
          })(<ImageUploader ratio={1} />)}
        </Form.Item>
      </Form>
    );
  }
}

export default MedalForm;
