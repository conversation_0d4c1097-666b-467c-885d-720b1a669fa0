import { opApi as api } from '@app/api';
import {
  Checkbox,
  Form,
  Input,
  message,
  Select,
  Radio,
  Row,
  Col,
  Tooltip,
  Icon,
  Modal,
} from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { CommonObject } from '@app/types';
import { ImageUploader, FileUploader, MultiFileUploader } from '../common';
import ArLinkInput, { arLinkValidator, isArLink } from '../common/arLinkInput';
const rates = [
  {
    id:1,
    name:'1天1次'
  },
  {
    id:2,
    name:'2天1次'
  },
  {
    id:3,
    name:'3天1次'
  },
  {
    id:4,
    name:'4天1次'
  },
  {
    id:5,
    name:'5天1次'
  },
  {
    id:6,
    name:'6天1次'
  },
  {
    id:7,
    name:'7天1次'
  },
  {
    id:8,
    name:'8天1次'
  },
  {
    id:9,
    name:'9天1次'
  },
  {
    id:10,
    name:'10天1次'
  },
]
@connectSession
@(Form.create({ name: 'navRecommend' }) as any)
class IndexProposalForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const s = {
      ...props.formContent,
    };

    this.state = {
      ...s,

    };
  }



  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    const fields = ['bubble_content','pop_rule','disappear_rule'];
    this.props.form.validateFieldsAndScroll(fields, (err: any, values: any) => {
      if (!err) {
        // setMLoading(this, true);
        let body = {
          ...values,
        };
        body.id = this.state.id
        api.editNavRecommend(body)
          .then((r: any) => {
            setMLoading(this, false);
            message.success('操作成功');
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="导航位置">
         {this.state.nav_name}
        </Form.Item>
        <Form.Item label="气泡类型">
         {this.state.bubble_type === 1 ? '图片':'文本'}
        </Form.Item>
       {this.state.bubble_type != 1 ?  <Form.Item label="气泡内容">
        {getFieldDecorator('bubble_content', {
              initialValue: this.state.bubble_content,
              preserve: true,
              rules: [
                {
                  required: true,
                  message: '请输入气泡内容',
                },
              ],
            })(<Input placeholder="气泡内容 最多10个字" maxLength={10}/>)}
        </Form.Item> :
        <Form.Item label="气泡内容" extra="支持jpg,jpeg,png,gif图片格式,建议上传大小150x50">
          {getFieldDecorator('bubble_content', {
            initialValue: this.state.bubble_content,
            rules: [
              {
                required: true,
                message: '请上传图片',
              },
            ],
          })(
            <ImageUploader
              ratio={3 / 1}
              imgSize={200}
              accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
            />
          )}
        </Form.Item>}
        {this.state.nav_type != 'chao_benben' && <Form.Item label="推荐频次">
          {getFieldDecorator(`pop_rule`, {
            initialValue:  this.state.pop_rule,
            rules: [
              {
                required: true,
                message: '推荐频次不能为空',
              },
            ],
          })(
            <Select placeholder="请选择推荐频次" >
              {rates.map((vv: any) => (
                <Select.Option value={vv.id} key={vv.id}>
                  {vv.name}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>}
        {this.state.nav_type == 'chao_benben' && <Form.Item label="推荐频次" required>
          <span>仅面向参与活动的用户，在结果出来后，1天1次。</span>
          {/* {getFieldDecorator(`pop_rule`, {
            initialValue:  this.state.pop_rule,
            rules: [
              {
                required: true,
                message: '推荐频次不能为空',
              },
            ],
          })(
            <Select placeholder="请选择推荐频次" >
              {rates.map((vv: any) => (
                <Select.Option value={vv.id} key={vv.id}>
                  {vv.name}
                </Select.Option>
              ))}
            </Select>
          )} */}
        </Form.Item>}
        <Form.Item label="消失规则">
          {getFieldDecorator(`disappear_rule`, {
            initialValue:  this.state.disappear_rule,
            rules: [
              {
                required: true,
                message: '请选择消失规则',
              },
            ],
          })(
            <Radio.Group
              onChange={(e: any) => this.setState({ disappear_rule: e.target.value })}
            >
              <Radio value={1}>用户点击后消失</Radio>
              <Radio value={2}>展示5秒后自动消失</Radio>
            </Radio.Group>
          )}
        </Form.Item>
      </Form>
    );
  }
}

export default IndexProposalForm;
