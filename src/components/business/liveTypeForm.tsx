import { releaseListApi } from '@app/api';
import { Checkbox, Form, Input, message, Radio } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader, FileUploader } from '../common';

@connectSession
@(Form.create({ name: 'LiveType' }) as any)
class StickerForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      type: props.type,
      article_id: props.id,
    };
  }
  onChange = (e: any) => {
    this.setState({
      type: e.target.value,
    });
  };
  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        let func = 'getLiveTypeSave';
        let body = {
          article_id: this.state.article_id,
          live_room_type: this.state.type,
        };
        releaseListApi
          .getLiveTypeSave(body)
          .then(() => {
            setMLoading(this, false);
            message.success('操作成功');
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="直播类型">
          {getFieldDecorator('type', {
            initialValue: this.state.type,
          })(
            <Radio.Group onChange={this.onChange}>
              <Radio value={0} style={{ marginRight: '250px' }}>
                普通直播间
              </Radio>
              <Radio value={1}>互动直播间</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <div style={{ display: 'flex' }}>
          <img
            src="https://model-tianmunews.oss-cn-hangzhou.aliyuncs.com/model/tmAdmin/live.jpg"
            alt=""
            style={{
              width: '300px',
              marginRight: '50px',
              marginLeft: '100px',
            }}
          />
          <img
            src="https://model-tianmunews.oss-cn-hangzhou.aliyuncs.com/model/tmAdmin/live-2.jpg"
            alt=""
            style={{
              width: '300px',
            }}
          />
        </div>
      </Form>
    );
  }
}

export default StickerForm;
