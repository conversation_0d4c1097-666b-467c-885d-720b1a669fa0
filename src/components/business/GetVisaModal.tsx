import { Modal, Table } from "antd";
import React, { useEffect, useState } from "react";
import { opApi as api } from '@app/api';
import moment from "moment";

export default function GetVisaModal(props: any) {
  const { visible, onCancel, record } = props
  const [loading, setLoading] = useState(false)
  const [dataList, setDataList] = useState([])
  const columns: any = [
    {
      title: '潮新闻id',
      dataIndex: 'id',
      width: 100
    },
    {
      title: '媒立方id',
      dataIndex: 'metadata_id',
      width: 100
    },
    {
      title: '所属频道',
      dataIndex: 'channel_name',
      width: 100
    },
    {
      title: '稿件标题',
      dataIndex: 'list_title',
    },
    {
      title: '签发状态',
      dataIndex: 'status',
      width: 100,
      render: (status: number) => status === 4 ? '已签发' : '未签发'
    },
    {
      title: '签发时间',
      dataIndex: 'published_at',
      width: 160,
      render: (time: string) => time ? moment(time).format('YYYY-MM-DD HH:mm:ss') : ''
    }
  ]

  useEffect(() => {
    if (visible) {
      setDataList([])
      setLoading(true)
      api.getRetrievedList({ id: record.id || record.article_id })
        .then(({ data }) => {
          setLoading(false)
          const { article_list } = data as any
          setDataList(article_list)
        })
        .catch(() => {
          setLoading(false)
        })
    }
  }, [visible])

  return (
    <Modal
      visible={visible}
      key="GetVisaModal"
      title="取签详情"
      width="800px"
      maskClosable={false}
      onCancel={onCancel}
      footer={null}
      destroyOnClose
    >
      <Table
        loading={loading}
        columns={columns}
        rowKey="id"
        dataSource={dataList}
        pagination={false}
      />
    </Modal>
  )
}