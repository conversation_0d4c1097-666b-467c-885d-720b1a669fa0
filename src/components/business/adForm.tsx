/* eslint-disable no-restricted-syntax */
/* eslint-disable guard-for-in */
import { releaseListApi as api } from '@app/api';
import {
  Button,
  Checkbox,
  Col,
  Divider,
  Form,
  Input,
  InputNumber,
  message,
  Radio,
  Row,
  Select,
} from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import scrollIntoView from 'dom-scroll-into-view';
import { ImageUploader } from '../common';

@connectSession
@(Form.create({ name: 'adForm' }) as any)
class AdForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const form = {
      title: '',
      visible: false,
      position: '',
      corner_show: 1,
      advertisement_source: '',
      elements: [{}],
    };
    const finalForm = {
      ...form,
      ...props.formContent,
      list_pic_custom_scale: props.formContent.list_pic_custom_scale || '4:1',
    };
    let maxId = 0;
    const keys = [];
    const pics: any = {};
    const urls: any = {};
    for (const i in finalForm.elements) {
      pics[i] = finalForm.elements[i].pic_url;
      urls[i] = finalForm.elements[i].url === 'null' ? '' : finalForm.elements[i].url || '';
      keys.push(parseInt(i, 10));
      maxId = parseInt(i, 10);
    }
    this.state = {
      ...finalForm,
      pics,
      urls,
      maxId,
      keys,
    };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body = {
          ...values,
          corner_show: values.corner_show === 1,
          visible: this.state.visible,
          channel_id: this.props.channelId,
          list_pic_custom_scale: this.state.list_pic_custom_scale
        };
        body.elements = this.state.keys.map((v: any) => ({
          pic_url: values.pics[v],
          url: values.urls[v] || null,
        }));
        delete body.pics;
        delete body.urls;
        setMLoading(this, true);
        const service = this.state.id ? api.updateAd : api.createAd;
        if (this.state.id) {
          body.id = this.state.id;
        }
        service(body)
          .then(() => {
            setMLoading(this, false);
            message.success('操作成功');
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  showChange = (e: any) => {
    this.setState({
      visible: e.target.checked,
    });
  };

  add = () => {
    const id = this.state.maxId + 1;
    const newKeys = [...this.state.keys, id];
    const pics = { ...this.state.pics, id: '' };
    const urls = { ...this.state.urls, id: '' };
    this.setState(
      {
        keys: newKeys,
        maxId: id,
        pics,
        urls,
      },
      () => {
        scrollIntoView(
          document.getElementById('add-btn-line'),
          document.getElementsByClassName('rox-drawer-content')[0]
        );
      }
    );
  };

  remove = (key: any) => {
    const newKeys = this.state.keys.filter((v: any) => v !== key);
    this.setState({
      keys: newKeys,
    });
  };

  hanldeEffectRatioType = (i: number, ratioType: string) => {
    if (i === 0) {
      if (this.state.keys.length > 1) {
        if (this.state.list_pic_custom_scale !== ratioType) {
          // 第一个广告位的比例变化了，清除底下所有广告位并toast提示
          setTimeout(() => {
            this.setState({
              keys: [this.state.keys[0]]
            })
          }, 0);
          message.error('修改比例后，其他广告位图自动删除')
        }
      }
      // 第一个广告位的比例要记录下
      this.setState({
        list_pic_custom_scale: ratioType
      })
    }
  }

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="广告标题">
          {getFieldDecorator('title', {
            initialValue: this.state.title,
            rules: [
              {
                required: true,
                message: '请填写广告标题',
              },
              {
                max: 20,
                message: '广告标题最长不能超过20个字',
              },
            ],
          })(<Input placeholder="请输入广告标题" />)}
          <Row style={{ lineHeight: '20px' }}>
            <Checkbox
              checked={this.state.visible}
              style={{ lineHeight: '20px' }}
              onChange={this.showChange}
            >
              显示在APP上
            </Checkbox>
          </Row>
        </Form.Item>
        <Form.Item label="插入位置">
          {getFieldDecorator('position', {
            initialValue: this.state.position,
            rules: [
              {
                required: true,
                message: '请输入插入位置',
                type: 'number',
              },
              {
                min: 1,
                message: '插入位置需要大于等于1',
                type: 'number',
              },
              {
                max: 500,
                message: '插入位置不能大于500',
                type: 'number',
              },
            ],
          })(
            <InputNumber
              min={1}
              max={500}
              placeholder="请输入大于0且小于500的数字"
              style={{ width: '100%' }}
            />
          )}
        </Form.Item>
        <Form.Item label="广告角标" required>
          {getFieldDecorator('corner_show', {
            initialValue: this.state.corner_show,
          })(
            <Radio.Group>
              <Radio value={1}>显示</Radio>
              <Radio value={0}>不显示</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item label="广告出处">
          {getFieldDecorator('advertisement_source', {
            initialValue: this.state.advertisement_source,
            rules: [
              {
                max: 20,
                message: '广告出处不能多于20个字',
              },
            ],
          })(<Input placeholder="输入广告出处" />)}
        </Form.Item>
        {this.state.keys.map((v: any, i: number) => (
          <Row key={v}>
            <Row>
              <Divider type="horizontal" />
              <Col span={4} style={{ textAlign: 'right' }}>
                <h3>广告位{i + 1}</h3>
              </Col>
              <Col span={18} style={{ textAlign: 'right' }}>
                <Button
                  type="danger"
                  onClick={() => this.remove(v)}
                  disabled={this.state.keys.length <= 1}
                >
                  删除广告位{i + 1}
                </Button>
              </Col>
            </Row>
            <Row>
              <Form.Item label="图片" extra="支持上传jpg,jpeg,png,gif图片格式，比例为4:1、16:9、4:3、1:1，多个广告位图片比例必须一致">
                {getFieldDecorator(`pics[${v}]`, {
                  initialValue: this.state.pics[v] || '',
                  rules: [
                    {
                      required: true,
                      message: '请上传广告位图片, 如不需要请删除本条目',
                    },
                  ],
                })(<ImageUploader showRatioTypeChange={i === 0} ratioType={this.state.list_pic_custom_scale} effectRatioType={(ratioType: any) => this.hanldeEffectRatioType(i, ratioType)} />)}
              </Form.Item>
              <Form.Item label="URL">
                {getFieldDecorator(`urls[${v}]`, {
                  initialValue: this.state.urls[v] || '',
                  rules: [
                    {
                      pattern: /^[^,，]+$/,
                      message: '禁止输入中英文逗号',
                    },
                  ],
                })(<Input placeholder="输入跳转URL" />)}
              </Form.Item>
            </Row>
          </Row>
        ))}
        <Row style={{ textAlign: 'center', marginBottom: 16 }} id="add-btn-line">
          <Button onClick={this.add} disabled={this.state.keys.length >= 5}>
            新增广告位
          </Button>
        </Row>
      </Form>
    );
  }
}

export default AdForm;
