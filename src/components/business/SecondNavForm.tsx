import { sysApi as api } from '@app/api';
import { Form, Input, message, Radio, Select, Checkbox, Tooltip, Icon } from 'antd';
import React from 'react';
import { compareVersions, setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader } from '../common';

type Api = 'updateSecondNav' | 'createSecondNav';

@connectSession
@(Form.create({ name: 'cityNavForm' }) as any)
class SecondNavForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);

    this.state = {
      ...props.formContent,
      privGroup: props.privGroup || [],
      appNavList: props.appNavList || [],
    };
    console.log(this.state.provinceId);
  }

  handleSubmit = (e: any) => {
    alert(1);
    e.preventDefault();
    this.doSubmit();
  };

  validateVersion = (rule: any, value: any, callback: any) => {
    // TODO 大小比较
    const { form } = this.props;
    const { min_version_str, max_version_str } = form.getFieldsValue([
      'min_version_str',
      'max_version_str',
    ]);
    if (!value) {
      callback();
      return;
    }
    if (value?.split('.')?.length != 3) {
      callback('请输入正确的版本号');
      return;
    }

    if (compareVersions(min_version_str, max_version_str) == -1) {
      if (rule.field === 'min_version_str') {
        callback('最低版本号不能高于最高版本号');
      } else {
        callback('最高版本号不能低于最低版本号');
      }
      return;
    }
    if (min_version_str !== '' && max_version_str !== '') {
      form.setFields({
        min_version_str: { value: min_version_str },
        max_version_str: { value: max_version_str },
      });
    }
    callback();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body: any = { ...values, sort_number: 1 };
        body.nav_target_type = values.nav_target_type || '';
        let func = 'create';
        if (this.state.id) {
          func = 'update';
          body.id = this.state.id;
        }
        body.category = 2;

        setMLoading(this, true);
        api[`${func}SecondNav` as Api](body)
          .then(() => {
            message.success('操作成功');
            setMLoading(this, false);
            this.props.onEnd();
          })
          .catch(() => setMLoading(this, false));
      } else {
        message.error('请检查表单内容');
      }
    });
  };
  render() {
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="名称">
          {getFieldDecorator('name', {
            initialValue: this.state.name,
            rules: [
              {
                required: true,
                message: '请填写名称',
              },
              // {
              //   pattern: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/,
              //   message: '名称不能包含特殊字符',
              // },
            ],
          })(<Input placeholder="请输入名称" />)}
        </Form.Item>
        <Form.Item label="图标（选中）：" extra="比例1:1 ，支持扩展名：.jpeg .png .jpg 等格式">
          {getFieldDecorator('nav_selected_icon_url', {
            initialValue: this.state.nav_selected_icon_url,
          })(<ImageUploader ratio={1} accept={['image/jpeg', 'image/png', 'image/jpg']} />)}
        </Form.Item>
        <Form.Item label="图标（未选中）：" extra="比例1:1 ，支持扩展名：.jpeg .png .jpg 等格式">
          {getFieldDecorator('nav_unselected_icon_url', {
            initialValue: this.state.nav_unselected_icon_url,
          })(<ImageUploader ratio={1} accept={['image/jpeg', 'image/png', 'image/jpg']} />)}
        </Form.Item>
        <Form.Item label="简介">
          {getFieldDecorator('information', {
            initialValue: this.state.information,
            rules: [
              {
                max: 20,
                message: '简介最多20个字',
              },
            ],
          })(<Input placeholder="请输入简介" />)}
        </Form.Item>
        <Form.Item label="页面类型">
          {getFieldDecorator('nav_kind', {
            initialValue: this.state.nav_kind ?? 0,
          })(
            <Radio.Group>
              <Radio value={0}>原生</Radio>
              <Radio value={1}>H5</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item label="频道地址">
          {getFieldDecorator('uri_scheme', {
            initialValue: this.state.uri_scheme,
            rules: [
              {
                required: true,
                message: '请输入频道地址',
              },
            ],
          })(<Input placeholder="请输入频道地址" />)}
        </Form.Item>

        <Form.Item
          label={
            <span style={{ position: 'relative' }}>
              <Tooltip title="请联系技术同学填写">
                <Icon type="question-circle" style={{ position: 'absolute', left: -30, top: 0 }} />
              </Tooltip>
              频道类型
            </span>
          }
        >
          {getFieldDecorator('nav_target_type', {
            initialValue: this.state.nav_target_type,
            // rules: [
            //   {
            //     required: true,
            //     message: '请输入频道类型',
            //   },
            // ],
          })(<Input placeholder="请输入navtype" />)}
        </Form.Item>

        <Form.Item label="状态">
          {getFieldDecorator('enabled', {
            initialValue: this.state.enabled,
          })(
            <Radio.Group>
              <Radio value={true}>上线</Radio>
              <Radio value={false}>下线</Radio>
            </Radio.Group>
          )}
        </Form.Item>

        <Form.Item label="特权组要求">
          {getFieldDecorator('require_permission_id', {
            initialValue: this.state.require_permission_id.toString(),
          })(
            <Select disabled={true}>
              <Select.Option value="0">所有</Select.Option>
              {this.state.privGroup.map((v: any) => (
                <Select.Option key={v.id} value={v.id.toString()}>
                  {v.group_name}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>

        <Form.Item label="所属首页分类">
          {getFieldDecorator('parent_id', {
            initialValue: this.state.parent_id,
            rules: [
              {
                required: true,
                message: '请选择所属首页分类',
              },
            ],
          })(
            <Select placeholder="请选择所属首页分类">
              <Select.Option value="" disabled={true}>
                请选择分类
              </Select.Option>
              {this.state.appNavList.map((v: any) => (
                <Select.Option value={v.id} key={v.id}>
                  {v.name}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>

        <Form.Item label="最低版本" extra="包含所输版本！示例：5.0.0">
          {getFieldDecorator('min_version_str', {
            initialValue: this.state.min_version_str,
            normalize: (value: any) => {
              return value?.replaceAll(' ', '');
            },
            rules: [
              // {
              //   required: true,
              //   message: '请输入最小版本号',
              // },
              {
                validator: this.validateVersion,
              },
            ],
          })(<Input placeholder="请输入最低版本" />)}
        </Form.Item>
        <Form.Item label="最高版本" extra="包含所输版本！示例：10.0.0">
          {getFieldDecorator('max_version_str', {
            initialValue: this.state.max_version_str,
            normalize: (value: any) => {
              return value?.replaceAll(' ', '');
            },
            rules: [
              // {
              //   required: true,
              //   message: '请输入最高版本号',
              // },
              {
                validator: this.validateVersion,
              },
            ],
          })(<Input placeholder="请输入最高版本" />)}
        </Form.Item>
      </Form>
    );
  }
}

export default SecondNavForm;
