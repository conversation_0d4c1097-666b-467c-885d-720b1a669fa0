/* eslint-disable array-callback-return */
/* eslint-disable no-nested-ternary */
import { opApi as api } from '@app/api';
import { Row, Form, Table, message, Spin, Select, Button, Transfer, Modal } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import debounce from 'lodash/debounce';
import clonedeep from 'lodash/cloneDeep';
import { TransferItem } from 'antd/es/transfer';

@connectSession
@(Form.create({ name: 'UGCTopicRecommendForm' }) as any)
class UGCTopicRecommendForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      searchResults: [],
      fetching: false,
      searched: false,
      articles: [],
      articleIds: [],
      showModal: false,
      key: Date.now(),
      transferData: [],
      targetKeys: [],
      title: '',
    };
    this.fetchNews = debounce(this.fetchNews, 500);
  }

  fetchNews = (value: string) => {
    this.setState({ fetching: true });
    api
      .searchTopicByName({ name: value, type: 10 })
      .then((r: any) => {
        this.setState({ fetching: false, searched: true });
        this.setState({
          searchResults: r.data.list.records,
        });
      })
      .catch(() => {
        this.setState({ fetching: false });
      });
  };

  handleChange = (svalue: any) => {
    const value = JSON.parse(svalue[0]);
    if (this.state.articleIds.indexOf(value.id) > -1) {
      message.error('话题已在列表中，请重新选择');
      return;
    }
    const state = {
      articleIds: [...this.state.articleIds, value.id],
      articles: [...this.state.articles, value],
      searchResults: [],
      searched: false,
    };
    this.setState({ ...state });
  };

  onSelectBlur = () => {
    this.setState({
      searchResults: [],
      searched: false,
    });
  };

  getColumns = () => {
    return [
      {
        title: '话题名称',
        key: 'name',
        dataIndex: 'name',
        render: (text: any) => <span>#{text}#</span>,
      },
      {
        title: '内容数量',
        key: 'count',
        dataIndex: 'article_count',
        width: 90,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <a onClick={this.handleDelete.bind(this, record)}>删除</a>
        ),
        width: 70,
      },
    ];
  };

  handleDelete = (record: any) => {
    const articleList = clonedeep(this.state.articles);
    let index = -1;
    articleList.map((v: any, i: number) => {
      if (v.id === record.id) {
        index = i;
      }
    });
    if (index === -1) {
      message.error('查找删除条目异常');
      return;
    }
    articleList.splice(index, 1);
    const ids: number[] = [];
    articleList.map((v: any) => {
      ids.push(v.id);
    });
    this.setState({
      articleIds: ids,
      articles: articleList,
    });
  };

  showTransfer = (type: number) => {
    api
      .getTopicRankList({
        type,
        current: 1,
        size: 100,
      })
      .then((res: any) => {
        this.setState({
          transferData: res.data.list.records,
          targetKeys: [],
          showModal: true,
          key: Date.now(),
          title:
            type === 0
              ? '热门话题（7天内最热的前100个话题，1小时更新一次）'
              : '最新话题（7天内最新的前100个话题，1小时更新一次）',
        });
      });
  };

  closeModal = () => {
    this.setState({
      showModal: false,
    });
  };

  submitModal = () => {
    const data: any[] = [];
    const ids: any[] = [];
    this.state.transferData.map((v: any) => {
      if (this.state.targetKeys.indexOf(v.id) > -1 && this.state.articleIds.indexOf(v.id) === -1) {
        data.push(v);
        ids.push(v.id);
      }
    });
    this.setState({
      showModal: false,
      articles: [...this.state.articles, ...data],
      articleIds: [...this.state.articleIds, ...ids],
    });
    message.success(`成功添加${data.length}条数据`);
  };

  handleTransferChange = (targetKeys: string[]) => {
    this.setState({
      targetKeys,
    });
  };

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    if (this.state.articles.length === 0) {
      message.error('请添加话题');
      return;
    }
    setMLoading(this, true);
    api
      .createRecommendTopic({ ids: this.state.articleIds.join(',') })
      .then((res: any) => {
        console.log(res);
        if (res.data && res.data.invalid_topic && res.data.invalid_topic.length > 0) {
          setMLoading(this, false);
          if (res.data.invalid_topic.length < this.state.articleIds.length) {
            Modal.info({
              title: '操作成功，部分已下架、删除、已存在的话题添加失败：',
              content: res.data.invalid_topic.join('、'),
              onOk: () => {
                this.props.onEnd();
              },
            });
          } else {
            Modal.info({
              title: '操作失败，已下架、删除、已存在的话题无法添加',
              onOk: () => {},
            });
          }
        } else {
          message.success('添加成功');
          setMLoading(this, false);
          this.props.onEnd();
        }
      })
      .catch((e) => {
        setMLoading(this, false);
        console.error(e);
      });
  };

  render() {
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const renderId = (record: TransferItem) => {
      return record.id;
    };

    const renderName = (record: TransferItem) => {
      return record.name;
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="批量添加话题" required={true}>
          <Row>
            <Select
              mode="multiple"
              value={[]}
              placeholder="输入话题名称搜索"
              notFoundContent={
                this.state.fetching ? (
                  <Spin size="small" style={{ margin: 'auto' }} />
                ) : this.state.searched ? (
                  '无结果'
                ) : null
              }
              filterOption={false}
              onSearch={this.fetchNews}
              onChange={this.handleChange}
              onBlur={this.onSelectBlur}
              style={{ width: 280 }}
            >
              {this.state.searchResults.map((d: any, i: number) => (
                <Select.Option key={`${i}`} value={JSON.stringify(d)}>
                  {`#${d.name}#`}
                </Select.Option>
              ))}
            </Select>
            <Button
              type="primary"
              style={{ marginLeft: 8 }}
              onClick={this.showTransfer.bind(this, 0)}
            >
              从热门话题中挑选
            </Button>
            <Button
              type="primary"
              style={{ marginLeft: 8 }}
              onClick={this.showTransfer.bind(this, 1)}
            >
              从最新话题中挑选
            </Button>
          </Row>
          <Row style={{ marginTop: 8 }}>
            <Table
              columns={this.getColumns()}
              rowKey="id"
              dataSource={this.state.articles}
              pagination={false}
            />
          </Row>
        </Form.Item>
        <Modal
          visible={this.state.showModal}
          title={this.state.title}
          onCancel={this.closeModal}
          onOk={this.submitModal}
          width={650}
          key={this.state.key}
        >
          <Transfer
            dataSource={this.state.transferData}
            rowKey={renderId}
            render={renderName}
            listStyle={{ textAlign: 'left', width: 270, height: 450 }}
            onChange={this.handleTransferChange}
            targetKeys={this.state.targetKeys}
          />
        </Modal>
      </Form>
    );
  }
}

export default UGCTopicRecommendForm;
