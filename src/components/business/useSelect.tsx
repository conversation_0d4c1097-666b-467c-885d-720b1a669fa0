
import React, { useState, useEffect, forwardRef, Ref,useCallback } from 'react';
import { Select } from 'antd';
import { communityApi } from "@app/api";
import _ from "lodash";
const { Option } = Select;

interface OptionData {
    id: number;
    nick_name: string;
    chao_id: string;
}

interface UserSelectProps {
    onChange?: (value: string) => void;
    value: any,
    initOptions: {
        chao_id: string,
        nick_name: string
    }
}

// 使用 forwardRef 包装 UserSelect 组件
const UserSelect = forwardRef((props: UserSelectProps, ref: Ref<Select>) => {
    const [options, setOptions] = useState<OptionData[]>([]);
    const [loading, setLoading] = useState(false);
    // 没有options 数据请求接口获取
    useEffect(() => {
        if (props.initOptions) {
            const { chao_id='', nick_name='' } = props.initOptions;
            const id = props.value;
            const arr: OptionData[] = [{ id, chao_id, nick_name }]; // 显式指定 arr 的类型为 OptionData[]
            setOptions(arr);
        }
    }, [props.initOptions, props.value]);
    const handleSearch = _.debounce(async (val: string) => {
        if (!val) return;
        setLoading(true);
        try {
            communityApi.recommendAccount_Search({ keyword: val }).then((res: any) => {
                let records: any = [...res.data.list];
                setOptions(records);
            });
        } catch (error) {
            console.error('Error fetching data:', error);
        }
        setLoading(false);
    },500);

    const handleSelectChange = (value: string) => {
        if (props.onChange) {
            props.onChange(value);
        }
    };

    useEffect(() => {
        // 聚焦搜索框
        if (ref) {
            ref.current?.focus();
        }
    }, [ref]);

    return (
        <Select
            showSearch
            optionFilterProp="children"
            loading={loading}
            placeholder="请输入用户昵称或小潮号"
            onSearch={handleSearch}
            filterOption={false}
            onChange={handleSelectChange}
            value={props.value || undefined}
            ref={ref} // 将 ref 属性传递给 Select 组件
        >
            {options.map((item) => (
                <Option key={item.id} value={item.id}>
                    {`${item.nick_name} | 小潮号：${item.chao_id} `}
                </Option>
            ))}
        </Select>
    );
});

export default UserSelect;