import { releaseListApi as api } from '@app/api';
import {
  Checkbox,
  Form,
  Input,
  message,
  Icon,
  Tooltip,
  InputNumber,
  Row,
  Switch,
  Radio,
} from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader } from '../common';
import ArLinkInput, { arLinkValidator, isArLink } from '../common/arLinkInput';

@connectSession
@(Form.create({ name: 'releaseRecommendForm' }) as any)
class ReleaseRecommendForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const s = {
      title: '',
      visible: false,
      position: '',
      url: '',
      width: '',
      height: '',
      jump_new_page: false,
      jump_url: '',
      ...props.formContent,
    };
    this.state = {
      ...s,
      jump_url: isArLink(s.jump_url) ? '' : s.jump_url,
      arUrl: isArLink(s.jump_url) ? s.jump_url : '',
      linkType: isArLink(s.jump_url) ? 1 : 0,
    };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    const fields = ['title', 'position', 'url', 'width', 'height'];
    if (this.state.jump_new_page) {
      if (this.state.linkType === 0) {
        fields.push('jump_url');
      } else {
        fields.push('arUrl');
      }
    }
    this.props.form.validateFieldsAndScroll(fields, (err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        const body = {
          ...values,
          visible: this.state.visible,
          channel_id: this.props.channelId,
          jump_new_page: this.state.jump_new_page,
        };
        if (this.state.linkType === 1) {
          body.jump_url = values.arUrl;
          delete body.arUrl;
        }
        delete body.time;
        let func = api.createRORecommend;
        if (this.state.id) {
          body.id = this.state.id;
          func = api.updateRORecommend;
        }
        func(body)
          .then(() => {
            setMLoading(this, false);
            message.success('操作成功');
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  handleJumpChange = (checked: boolean) => {
    this.setState({
      jump_new_page: checked,
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };

    const text = (
      <span>
        <p>输入说明：</p>
        <p>
          直接输入阿拉伯数字代表位置序号，例如要在头条频道信息流第8个位置插入该推荐位，则直接输入“8”即可。
        </p>
        <p>最大输入数字为500</p>
        <br />
        <p>注：</p>
        <p>直接在输入的指定位置上插入推荐位显示，不参与稿件的排序，且优先于媒立方稿件展示</p>
      </span>
    );
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="名称">
          {getFieldDecorator('title', {
            initialValue: this.state.title,
            rules: [
              {
                required: true,
                message: '请输入名称',
              },
              {
                max: 30,
                message: '名称不能超过30个字',
              },
            ],
          })(<Input placeholder="请输入推荐位标题" />)}
          <Row style={{ lineHeight: '30px' }}>
            <Checkbox
              checked={this.state.visible}
              onChange={(e: any) => this.setState({ visible: e.target.checked })}
            >
              在APP上显示
            </Checkbox>
          </Row>
        </Form.Item>
        <Form.Item label="URL地址">
          {getFieldDecorator('url', {
            initialValue: this.state.url,
            rules: [
              {
                required: true,
                message: '请填写URL地址',
              },
              {
                pattern: /^https?:\/\//,
                message: '请填写正确的URL地址',
              },
            ],
          })(<Input placeholder="请输入URL地址" />)}
        </Form.Item>
        <Form.Item label="输入位置">
          {getFieldDecorator('position', {
            initialValue: this.state.position,
            rules: [
              {
                required: true,
                message: '请填写位置',
              },
              {
                min: 1,
                message: '不能小于1',
                type: 'number',
              },
              {
                max: 500,
                message: '不能大于500',
                type: 'number',
              },
            ],
          })(<InputNumber max={500} style={{ width: 200 }} placeholder="请输入位置序号" min={1} />)}
          &emsp;
          <Tooltip title={text}>
            <Icon type="question-circle" />
          </Tooltip>
        </Form.Item>
        <Form.Item label="宽度">
          {getFieldDecorator('width', {
            initialValue: this.state.width,
            rules: [
              {
                required: true,
                message: '请填写位置',
              },
              {
                min: 1,
                message: '不能小于1',
                type: 'number',
              },
            ],
          })(<InputNumber min={1} placeholder="请输入具体像素数值" style={{ width: '100%' }} />)}
        </Form.Item>
        <Form.Item label="高度">
          {getFieldDecorator('height', {
            initialValue: this.state.height,
            rules: [
              {
                required: true,
                message: '请填写位置',
              },
              {
                min: 1,
                message: '不能小于1',
                type: 'number',
              },
            ],
          })(<InputNumber min={1} placeholder="请输入具体像素数值" style={{ width: '100%' }} />)}
        </Form.Item>
        <Form.Item label="跳转新页面">
          <Switch checked={this.state.jump_new_page} onChange={this.handleJumpChange} />
        </Form.Item>
        {this.state.jump_new_page && (
          <>
            <Form.Item label="跳转类型">
              <Radio.Group
                value={this.state.linkType}
                onChange={(e: any) => this.setState({ linkType: e.target.value })}
              >
                <Radio value={0}>链接</Radio>
                <Radio value={1}>AR</Radio>
              </Radio.Group>
            </Form.Item>
            {this.state.linkType === 0 && (
              <Form.Item label="跳转地址">
                {getFieldDecorator('jump_url', {
                  initialValue: this.state.jump_url,
                  preserve: true,
                  rules: [
                    {
                      required: true,
                      message: '请填写跳转地址',
                    },
                    {
                      pattern: /^https?:\/\//,
                      message: '请正确填写跳转地址',
                    },
                  ],
                })(<Input placeholder="请输入跳转地址" />)}
              </Form.Item>
            )}
            {this.state.linkType === 1 && (
              <Form.Item label="AR信息" required>
                {getFieldDecorator('arUrl', {
                  initialValue: this.state.arUrl,
                  preserve: true,
                  rules: [
                    {
                      validator: arLinkValidator,
                    },
                  ],
                })(<ArLinkInput />)}
              </Form.Item>
            )}
          </>
        )}
      </Form>
    );
  }
}

export default ReleaseRecommendForm;
