import { sysApi as api } from '@app/api';
import { releaseListApi as api1 } from '@app/api';
import { Form, Input, message, Radio, Select, Checkbox, Table } from 'antd';
import React from 'react';
import { setMLoading, setLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { OrderColumn } from '@components/common';

type Api = 'updateCityNav' | 'createCityNav';

@connectSession
@(Form.create({ name: 'cityNavForm' }) as any)
class CityNavForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      ...props.formContent,
      privGroup: props.privGroup || [],
      current: 1,
      size: 50,
      selectedRowKeys: [],  //table表格 勾选框
      selectParentId: null,
      selectId: null,   //新增选择的id
      loadingStatus: false,   //loading状态
    };
  }
  componentDidMount() {
    if (this.state.type == 1) {
      this.getAllProvince()
    }
  }
  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body: any = {
          ...values,
          sort_number: 1,
          enabled: true,
          belong: values.belong.join(','),
          url: '',
        };

        if (body.require_permission == 0) {
          body.require_permission_id = 0
        } else {
          body.require_permission_id = body.require_permission_id.join(',')
        }
        delete body.require_permission

        let func = 'create';
        if (this.state.id) {
          func = 'update';
          body.id = this.state.id;
        }
        setMLoading(this, true);
        api[`${func}CityNav` as Api](body)
          .then(() => {
            message.success('操作成功');
            setMLoading(this, false);
            this.props.onEnd();
          })
          .catch(() => setMLoading(this, false));
      } else {
        message.error('请检查表单内容');
      }
    });
  };
    // 全选 全不选
    onSelectChange = (selectedRowKeys: any, record: any) => {
      if (selectedRowKeys.length == this.state.ProvinceInfoList.length) {
        this.setState({
          loadingStatus: true
        })
        api1.showAreaGovStatus({
          parent_id: this.state.ProvinceInfoList[0].parent_id,
          all_show_status: 1
        }).then((v: any) => {
          this.setState({
            loadingStatus: false
          })
          message.success('操作成功');
          if(this.state.type == 1) {
            this.getAllProvince()
          } 
          if(this.state.type == 2) {
            this.getNewTableData()
          }
        }).catch(()=>{
          this.setState({
            loadingStatus: false
          })
        })
      }
      if (selectedRowKeys.length == 0) {
        this.setState({
          loadingStatus: true
        })
        api1.showAreaGovStatus({
          parent_id: this.state.ProvinceInfoList[0].parent_id,
          all_show_status: 0
        }).then((v: any) => {
          this.setState({
            loadingStatus: false
          })
          message.success('操作成功');
          if(this.state.type == 1) {
            this.getAllProvince()
          } 
          if(this.state.type == 2) {
            this.getNewTableData()
          }
        }).catch(()=>{
          this.setState({
            loadingStatus: false
          })
        })
      }
      this.setState({ selectedRowKeys });
    };
    // 选择单个
    onSelect = (record: any) => {
      this.setState({
        loadingStatus: true
      })

      api1.showAreaGovStatus({
        id: record.id,
        show_status: record.show_status == 1 ? 0 : 1
      }).then((v: any) => {
        this.setState({
          loadingStatus: false
        })
        message.success('操作成功');
        if(this.state.type == 1) {
          this.getAllProvince()
        } 
        if(this.state.type == 2) {
          this.getNewTableData()
        }
      }).catch(()=>{
        this.setState({
          loadingStatus: false
        })
      })
    }
    // 获取所有的省份信息
    getAllProvince = () => {
      api1.getAreaGovList({ parent_id: 0 }).then((v: any) => {
        this.setState({
          allAllProvinceList: v.data.area_gov_list,
        });
        const selectProvince = this.state.allAllProvinceList.filter((item: any, index: number) => {
          return item.name == this.state.name || item.original_name.search(this.state.name) != -1
        })
        // 获取某一个省下市的信息
        api1.getAreaGovList({ parent_id: selectProvince[0].id }).then((v: any) => {
          this.setState({
            ProvinceInfoList: v.data.area_gov_list
          }, () => {
            this.initSelectData()
          })
        })
      })
    };
    // 新增刷新 表格数据
    getNewTableData = () => {
      api1.getAreaGovList({ parent_id: this.state.selectId }).then((v: any) => {
        this.setState({
          ProvinceInfoList: v.data.area_gov_list
        }, () => {
          this.initSelectData()
        })
      })
    }
  
    // 新建 获取省份信息
    getNewProvince = () => {
      api1.getAreaGovList({ parent_id: 0, select_all_status: 0 }).then((v: any) => {
        this.setState({
          allAllProvinceList: v.data.area_gov_list
        })
      })
    }
    // 初始化 勾选数据
    initSelectData = () => {
      var indexs = []
      for (var i = 0; i < this.state.ProvinceInfoList.length; i++) {
        if (this.state.ProvinceInfoList[i].show_status == 1) {
          indexs.push(this.state.ProvinceInfoList[i].id);
        }
      }
      this.setState({
        selectedRowKeys: indexs
      })
    }
    // 更改城市
    changeProvince = (e: any) => {
      const selectProvince = this.state.allAllProvinceList.filter((item: any, index: number) => {
        return item.name == e
      })
      this.setState({
        selectId: selectProvince[0].id
      })
      api1.getAreaGovList({ parent_id: selectProvince[0].id, select_all_status: 0 }).then((v: any) => {
        this.setState({
          ProvinceInfoList: v.data.area_gov_list,
          selectParentId: selectProvince[0].id
        }, () => {
          this.initSelectData()
        })
      })
    }
  
    // 分类列表 排序
    exchangeOrder = (record: any, sortFlag: number) => {
      api1.sortAreaGov({ id: record.id, sort_flag: sortFlag }).then(() => {
          message.success('操作成功');
          if (this.state.type == 1) {
            this.getAllProvince()
          }
          else {
            // 获取某一个省下市的信息
            api1.getAreaGovList({ parent_id: this.state.selectParentId }).then((v: any) => {
              this.setState({
                ProvinceInfoList: v.data.area_gov_list
              }, () => {
                this.initSelectData()
              })
            })
          }
        })
        .catch(() => {
        });
    };
    getProvinceColumns = (hideGive: boolean = false) => {
      const getSeq = (i: number) => (this.state.current - 1) * this.state.size + i + 1;
      return [
        {
          title: '全选',
          dataIndex: 'name',
          render: (text: any, record: any, index: number) => record.name,
          width: 70,
        },
        {
          title: '排序',
          key: 'seq',
          render: (text: any, record: any, i: number) => (
            <OrderColumn
              perm="byte_ugc_sticker:exchange_order"
              start={1}
              end={this.state.ProvinceInfoList.length}
              pos={getSeq(i)}
              onUp={this.exchangeOrder.bind(this, record, 0)}
              onDown={this.exchangeOrder.bind(this, record, 1)}
            />
          ),
          width: 70,
        },
        {
          title: '城市编码',
          dataIndex: 'city_code',
          render: (text: any, record: any, index: number) =>
            record.city_code == 0 ? '' : record.city_code,
        },
      ];
    };

  render() {
    const { selectedRowKeys } = this.state;
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange,
      onSelect: this.onSelect,
    };
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="选择省份">
          {getFieldDecorator('name', {
            initialValue: this.state.name,
            rules: [
              {
                required: true,
                message: '请选择省份',
              },
            ],
          })(<Select placeholder="请选择省份" onFocus={this.getNewProvince} disabled={this.state.type == 1 ? true : false} onChange={this.changeProvince}>
          {this.state.allAllProvinceList?.map((v: any, index: any) => (
            <Select.Option value={v.name} key={index}>
              {v.name}
            </Select.Option>
          ))}
        </Select>)}
        </Form.Item>
        <Table
          rowSelection={rowSelection}
          style={{ paddingLeft: 50, paddingRight: 50 }}
          columns={this.getProvinceColumns(true)}
          rowKey="id"
          dataSource={this.state.ProvinceInfoList}
          pagination={false}
          loading={this.state.loadingStatus}
        />
        {/* <Form.Item label="频道地址">
          {getFieldDecorator('url', {
            initialValue: this.state.url,
          })(<Input placeholder="请输入频道地址" />)}
        </Form.Item> */}
        <Form.Item label="上线范围">
          {getFieldDecorator('belong', {
            initialValue: this.state.belong,
          })(
            <Checkbox.Group>
              <Checkbox value="1">城市频道</Checkbox>
              <Checkbox value="2">服务频道</Checkbox>
              <Checkbox value="3">同城频道</Checkbox>
            </Checkbox.Group>
          )}
        </Form.Item>
        <Form.Item label="特权组要求">
          {getFieldDecorator('require_permission', {
            initialValue: ((this.state.require_permission_id == 0 || !this.state.require_permission_id)) ? 0 : 1,
          })(
            <Radio.Group>
              <Radio value={0}>所有用户可见</Radio>
              <Radio value={1}>仅特权组可见</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {getFieldValue('require_permission') == 1 && (
          <Form.Item label=" " colon={false} style={{ marginTop: -20 }}>
            {getFieldDecorator('require_permission_id', {
              initialValue: (this.state.require_permission_id == 0 || !this.state.require_permission_id) ? [] : this.state.require_permission_id.toString().split(','),
              rules: [
                {
                  validator: (rule: any, value: any, callback: any) => {
                    if (value?.length > 0) {
                      callback()
                    } else {
                      callback('请至少选择1个特权组');
                    }  
                  },
                },
              ],
            })(
              <Checkbox.Group>
                {this.state.privGroup.map((v: any) => (
                  <Checkbox key={v.id} value={v.id.toString()}>
                    {v.group_name}
                  </Checkbox>
                ))}
              </Checkbox.Group>
            )}
          </Form.Item>
        )}
      </Form>
    );
  }
}

export default CityNavForm;
