/* eslint-disable array-callback-return */
/* eslint-disable no-nested-ternary */
import { opApi as api } from '@app/api';
import { Row, Form, Table, message, Spin, Select, Button, Transfer, Modal } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import debounce from 'lodash/debounce';
import clonedeep from 'lodash/cloneDeep';
import { TransferItem } from 'antd/es/transfer';

@connectSession
@(Form.create({ name: 'UGCTopicRecommendForm' }) as any)
class UGCTopicRecommendForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    console.log(props);
    this.state = {
      searchResults: [],
      userList: [],
      id: null,
      key: Date.now(),
      duration_id: props.id,
    };
    this.fetchUsers = debounce(this.fetchUsers, 500);
  }
  // 搜索新闻
  fetchUsers = (value: string) => {
    this.setState({ fetching: true });
    api
      .getUserList({
        id: value,
        status: 0,
        cert_types: '0, 1, 2',
      })
      .then((r: any) => {
        this.setState({ fetching: false, searched: true });
        this.setState({
          searchResults: r.data.account_list.records,
        });
      })
      .catch(() => {
        this.setState({ fetching: false });
      });
  };
  // 选项改变
  handleChange = (svalue: any) => {
    const value = JSON.parse(svalue[0]);
    const state = {
      id: value.id,
      userList: [value],
      searchResults: [],
      searched: false,
    };
    this.setState({ ...state });
  };

  onSelectBlur = () => {
    this.setState({
      searchResults: [],
      searched: false,
    });
  };

  getColumns = () => {
    return [
      {
        title: 'ID',
        key: 'id',
        dataIndex: 'id',
      },
      {
        title: '昵称',
        key: 'nick_name',
        dataIndex: 'nick_name',
      },
      {
        title: '手机号',
        key: 'phone_number',
        dataIndex: 'phone_number',
      },

      // {
      //   title: '操作',
      //   key: 'op',
      //   render: (text: any, record: any) => (
      //     <a onClick={this.handleDelete.bind(this, record)}>删除</a>
      //   ),
      //   width: 70,
      // },
    ];
  };

  // handleDelete = (record: any) => {
  //   this.setState({
  //     articleIds: ids,
  //     userList: articleList,
  //   });
  // };

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };
  // 提交
  doSubmit = () => {
    if (this.state.userList.length === 0) {
      message.error('请添加用户');
      return;
    }
    setMLoading(this, true);
    api
      .addLongVideoUser({
        duration_id: this.state.duration_id,
        account_id: this.state.id,
      })
      .then((res: any) => {
        console.log(res);
        message.success('添加成功');
        setMLoading(this, false);
        this.props.onEnd();
      })
      .catch((e) => {
        setMLoading(this, false);
        console.error(e);
      });
  };

  render() {
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="添加用户" required={true}>
          <Row>
            <Select
              mode="multiple"
              value={[]}
              placeholder="输入用户ID"
              notFoundContent={
                this.state.fetching ? (
                  <Spin size="small" style={{ margin: 'auto' }} />
                ) : this.state.searched ? (
                  '无结果'
                ) : null
              }
              filterOption={false}
              onSearch={this.fetchUsers}
              onChange={this.handleChange}
              onBlur={this.onSelectBlur}
              style={{ width: 280 }}
            >
              {this.state.searchResults.map((d: any, i: number) => (
                <Select.Option key={`${i}`} value={JSON.stringify(d)}>
                  {`${d.nick_name}`}
                </Select.Option>
              ))}
            </Select>
          </Row>
          <Row style={{ marginTop: 8 }}>
            <Table
              columns={this.getColumns()}
              rowKey="id"
              dataSource={this.state.userList}
              pagination={false}
            />
          </Row>
        </Form.Item>
      </Form>
    );
  }
}

export default UGCTopicRecommendForm;
