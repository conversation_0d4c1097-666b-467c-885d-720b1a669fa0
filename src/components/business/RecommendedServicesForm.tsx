import { serviceApi as api } from '@app/api';
import { ImageUploader } from '@components/common';
import { Divider, Form, Input, message, Radio, Select, Switch, TreeSelect } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';

type Api = 'updateAppService' | 'createAppService';

@connectSession
@(Form.create({ name: 'appServiceForm' }) as any)
class AppServiceForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const form = {
      id: '',
      area_id: '',
      category_id: '',
      recommend_title: '',
      ref_ids: '',
      url: '',
    };
    this.state = {
      ...form,
      ...props.formContent,
      type: 1,
      allRecomemndList: [],
      categoryList: props.categoryList,
    };
    if (props.formContent.category_id) {
      this.getAllRecomemnd(props.formContent.category_id);
    }
  }

  componentDidMount() {
    if (!this.state.category_id && !this.state.ref_ids && this.state.id) {
      this.setState({
        type: 2,
      });
    }
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        let func = 'createServiceRecommend';
        let body: any = {};
        // const refIdS = this.state.allRecomemndList.filter((x: any) => x.id === values.ref_ids)[0];
        if (this.state.type === 1) {
          body = {
            recommend_title: values.recommend_title,
            area_id: this.props.areaList[0].value,
            ref_ids: values.ref_ids,
            url: '', // refIdS.name,
          };
        } else {
          body = {
            recommend_title: values.recommend_title,
            area_id: this.props.areaList[0].value,
            url: values.url,
            ref_ids: '',
          };
        }

        if (this.state.id) {
          body.id = this.state.id;
          func = 'updateServiceRecommend';
        }
        setMLoading(this, true);
        api[func as Api](body)
          .then(() => {
            message.success('操作成功');
            setMLoading(this, false);
            this.props.onEnd();
          })
          .catch(() => setMLoading(this, false));
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  urlValidator = (rule: any, value: any, callback: any) => {
    const regex = /^https?:\/\//;
    if (value === '') {
      callback('请填写链接');
      return;
    }
    if (!regex.test(value)) {
      callback('请正确填写链接格式');
      return;
    }

    callback();
  };

  switchType = (e: any) => {
    this.setState({
      type: e.target.value,
    });
  };

  categoryOnchange = (e: any) => {
    this.setState({
      category_id: e,
    });
    this.getAllRecomemnd(e);
  };

  getAllRecomemnd = (category_id: any) => {
    api
      .getListAllRecommend({ area_id: this.props.areaList[0].value, category_id })
      .then((res: any) => {
        console.log(res);
        this.setState({
          allRecomemndList: res.data.web_link_list,
          ref_ids: res.data.web_link_list.length > 0 ? res.data.web_link_list[0].id : '',
        });
      });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form onSubmit={this.handleSubmit} {...formLayout}>
        <Form.Item label="新增类型">
          {getFieldDecorator('type', {
            initialValue: this.state.type,
          })(
            <Radio.Group onChange={this.switchType}>
              <Radio value={1}>本地服务</Radio>
              <Radio value={2}>自定义服务</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {this.state.type === 1 && (
          <Form.Item label="服务分类">
            {getFieldDecorator('category_id', {
              initialValue: this.state.category_id,
              rules: [
                {
                  required: true,
                  message: '请填写所属分类',
                },
              ],
            })(
              <Select placeholder="请选服务分类" onChange={this.categoryOnchange}>
                {this.state.categoryList.map((v: any) => (
                  <Select.Option value={v.id} key={v.id}>
                    {v.name}
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>
        )}
        {this.state.type === 1 && (
          <Form.Item label="应用名称">
            {getFieldDecorator('ref_ids', {
              initialValue: this.state.ref_ids,
              rules: [
                {
                  required: true,
                  message: '请选择应用名称',
                },
              ],
            })(
              <Select placeholder="请选应用名称">
                {this.state.allRecomemndList.map((v: any) => (
                  <Select.Option value={v.id} key={v.id}>
                    {v.name}
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>
        )}
        {this.state.type === 2 && (
          <Form.Item required={true} label="链接">
            {getFieldDecorator('url', {
              initialValue: this.state.url,
              rules: [
                {
                  required: true,
                  message: '请填写推链接',
                },
                {
                  validator: this.urlValidator,
                },
              ],
            })(<Input placeholder="请输入链接" />)}
          </Form.Item>
        )}
        <Form.Item label="推荐标题">
          {getFieldDecorator('recommend_title', {
            initialValue: this.state.recommend_title,
            rules: [
              {
                required: true,
                message: '请填写推荐标题',
              },
              {
                max: 5,
                message: '推荐标题不能超过5个汉字',
              },
              {
                pattern: /^[a-zA-Z0-9\u4e00-\u9fa5\n]+$/,
                message: '推荐标题不能包含特殊字符',
              },
            ],
          })(<Input placeholder="请填写推荐标题" />)}
        </Form.Item>
      </Form>
    );
  }
}

export default AppServiceForm;
