import { sysApi as api, systemApi } from '@app/api';
import { CKEditor } from '@components/common';
import { Button, Checkbox, Form, Icon, Input, InputNumber, message, Row, Select } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';

@connectSession
@(Form.create({ name: 'AppVersionForm' }) as any)
class AppVersionForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const formContent = {
      device_type: '1',
      version: '',
      remark: '',
      version_code: '',
      pkg_url: '',
      force_upgraded: false,
    };
    this.state = {
      ...formContent,
      ...props.formContent,
      file_name: '',
      allKeys: Object.keys(formContent),
    };
  }

  componentDidMount() {
    const values = this.state.allKeys.map((v: any) => {
      return { v: this.state[v] };
    });
    this.props.form.setFieldsValue(values);
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  }

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        let func = 'createAppVersion';
        const body = { ...values };
        if (this.state.id) {
          func = 'updateAppVersion';
          body.id = this.state.id;
        }
        setMLoading(this, true);
        api[func as keyof typeof api](body)
          .then(() => {
            message.success('成功');
            setMLoading(this, false);
            this.props.onEnd();
          })
          .catch(() => setMLoading(this, false));
      } else {
        message.error('请检查表单项');
      }
    });
  }

  fileInputChange = (e: any) => {
    const file = e.target.files[0];
    if (file) {
      setMLoading(this, true);
      const t = message.loading('正在上传...');
      systemApi
        .upload({ file })
        .then((r: any) => {
          t();
          setMLoading(this, false);
          message.success('上传成功');
          this.props.form.setFieldsValue({ pkg_url: r.data.url });
        })
        .catch(() => {
          t();
          setMLoading(this, false);
        });
    }
  }

  typeChange = (value: any) => {
    this.props.form.setFieldsValue({ pkg_url: '' });
  }

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const values = this.props.form.getFieldsValue(['device_type']);
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="系统类型">
          {getFieldDecorator('device_type', {
            initialValue: this.state.device_type.toString(),
            rules: [
              {
                required: true,
                message: '请选择系统类型',
              },
            ],
          })(
            <Select onChange={this.typeChange}>
              <Select.Option value="1">iOS</Select.Option>
              <Select.Option value="0">Android</Select.Option>
              <Select.Option value="2">鸿蒙</Select.Option>
            </Select>
          )}
        </Form.Item>
        <Form.Item label="版本名称">
          {getFieldDecorator('version', {
            initialValue: this.state.version,
            rules: [
              {
                required: true,
                message: '请填写版本名称',
              },
              {
                pattern: /^[0-9]+\.[0-9]+\.[0-9]+$/,
                message: '请正确输入版本名称，例：6.0.0',
              },
            ],
          })(<Input placeholder="请输入版本名称" />)}
        </Form.Item>
        <Form.Item label="版本编号" extra="例：3.1.0=30100, 3.1.2=30102, 3.1.6=30106">
          {getFieldDecorator('version_code', {
            initialValue: this.state.version_code,
            rules: [
              {
                required: true,
                message: '请填写版本编号',
                type: 'number',
              },
            ],
          })(
            <InputNumber
              min={0}
              max={999999999}
              style={{ width: '100%' }}
              placeholder="请输入版本编号"
            />
          )}
        </Form.Item>
        <Form.Item label="版本功能说明">
          {getFieldDecorator('remark', {
            initialValue: this.state.remark,
            rules: [
              {
                required: true,
                message: '请输入版本功能说明',
              },
            ],
          })(<CKEditor />)}
        </Form.Item>
        {values.device_type != '2' && <Form.Item label="安装包">
          {(values.device_type === '0' || values.device_type === '2') && (
            <Row style={{ marginBottom: 8 }}>
              <input
                type="file"
                accept={values.device_type === '0' ? ".apk" : '.app,.apk,.hap'}
                style={{ display: 'none' }}
                onChange={this.fileInputChange}
                ref="myfile"
              />
              <Button
                onClick={() => (this.refs.myfile as HTMLButtonElement).click()}
                style={{ marginRight: 8 }}
              >
                <Icon type="upload" /> 上传文件
              </Button>
              {values.device_type === '2' ? '只接受鸿蒙系统.app、.apk、.hap文件' : '只接受Android系统.apk文件'}
            </Row>
          )}
          {getFieldDecorator('pkg_url', {
            initialValue: this.state.pkg_url,
            rules: [
              {
                required: true,
                message: `请${(values.device_type === '0' || values.device_type === '2') ? '上传安装包' : '填写安装包地址'}`,
              },
              {
                pattern: /^https?:\/\//,
                message: '请正确填写地址',
              },
            ],
          })(
            <Input
              placeholder={(values.device_type === '0' || values.device_type === '2') ? '上传安装包' : '填写安装包地址'}
              disabled={values.device_type === '0' || values.device_type === '2'}
            />
          )}
        </Form.Item>}
        <Form.Item label="强制升级">
          {getFieldDecorator('force_upgraded', {
            initialValue: this.state.force_upgraded,
            valuePropName: 'checked',
          })(<Checkbox />)}
        </Form.Item>
      </Form>
    );
  }
}

export default AppVersionForm;
