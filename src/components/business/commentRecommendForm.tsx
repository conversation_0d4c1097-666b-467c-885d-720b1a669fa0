import { releaseListApi as api } from '@app/api';
import {
  Form,
  Icon,
  Input,
  message,
  Pagination,
  Row,
  Tooltip,
  Col,
  Select,
  Table,
  Divider,
  Modal,
  Button,
  Tabs,
} from 'antd';
import React from 'react';
import { generateTextWithLink, setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import moment from 'moment';
import scrollIntoView from 'dom-scroll-into-view';
import ImagePreviewColumn from "@app/components/common/imagePreviewColumn";

import './styles/commentRecommend.scss';
import { A } from '../common';

@connectSession
@(Form.create({ name: 'commentRecommendForm' }) as any)
class CommentRecommendForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      articleSearch: {
        cKeyword: '',
        cType: 2,
        search_type: 2,
        keyword: '',
        loading: false,
      },
      commentSearch: {
        cKeyword: '',
        cType: 3,
        search_type: 3,
        keyword: '',
        loading: false,
        key: Date.now(),
        visible: false,
      },
      selectedArticle: {
        id: '',
        title: '',
      },
      articleList: {
        size: 15,
        current: 1,
        total: 1,
        records: [],
      },
      commentList: {
        size: 15,
        current: 1,
        total: 1,
        records: [],
      },
      allCommentSearch: {
        cKeyword: '',
        keyword: '',
        loading: false,
      },
      allCommentList: {
        size: 15,
        current: 1,
        total: 0,
      },
      hotArticleSearch: {
        loading: false,
      },
      hotArticleList: [],
      disabledIds: props.disabledIds,
      selectedList: props.selectedList || [],
      selectedIds: (props.selectedList && props.selectedList.map((item: any) => item.id)) || [],
      tempList: [],
      tempIds: [],
      tab: '1',
      selectedRowKeys: [],
      selectedRows: [],
    };
  }

  componentDidMount() { }

  setLoading = (index: string, l: boolean) => {
    this.setState({
      [index]: { ...this.state[index], loading: l },
    });
  };

  getArticleList = (current: number = this.state.articleList.current) => {
    this.setLoading('articleSearch', true);
    const { search_type, keyword } = this.state.articleSearch;
    api
      .getCommentNewsList({
        search_type,
        keyword,
        current,
        size: 15,
      })
      .then((res: any) => {
        this.setState({
          articleList: {
            ...res.data.release_list,
          },
          articleSearch: { ...this.state.articleSearch, loading: false },
        });
      })
      .catch(() => {
        this.setLoading('articleSearch', false);
      });
  };

  getCommentList = (current: number = this.state.commentList.current) => {
    this.setLoading('commentSearch', true);
    const { search_type, keyword } = this.state.commentSearch;
    const { id } = this.state.selectedArticle;
    api
      .getCommentList({
        keyword,
        current,
        search_type: keyword ? search_type : 0,
        size: 15,
        article_id: id,
      })
      .then((res: any) => {
        this.setState({
          commentList: {
            ...res.data.list,
          },
          commentSearch: { ...this.state.commentSearch, loading: false },
        });
      })
      .catch(() => {
        this.setLoading('commentSearch', false);
      });
  };

  getAllCommentList = (current: number = this.state.allCommentList.current) => {
    this.setLoading('allCommentSearch', true);
    const { keyword } = this.state.allCommentSearch;
    api
      .getCommentList({
        keyword,
        current,
        search_type: keyword ? 3 : 0,
        size: 15,
      })
      .then((res: any) => {
        this.setState({
          allCommentList: {
            ...res.data.list,
          },
          allCommentSearch: { ...this.state.allCommentSearch, loading: false },
        });
      })
      .catch(() => {
        this.setLoading('allCommentSearch', false);
      });
  };

  getHotNewsList = () => {
    this.setLoading('hotArticleSearch', true);
    api
      .getHotNewsList()
      .then((res: any) => {
        this.setState({
          hotArticleList: res.data.list,
          hotArticleSearch: { loading: false },
        });
      })
      .catch(() => {
        this.setLoading('hotArticleSearch', false);
      });
  };

  articleColumns = () => {
    return [
      {
        title: '潮新闻ID',
        key: 'mlf_id',
        dataIndex: 'id',
        width: 85,
      },
      {
        title: '签发时间',
        key: 'time',
        dataIndex: 'published_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 150,
      },
      {
        title: '标题',
        key: 'list_title',
        dataIndex: 'list_title',
        render: (text: any) => (
          <Tooltip title={text}>
            <div className="ellipsis-comment" style={{ width: 400 }}>
              {text}
            </div>
          </Tooltip>
        ),
        width: 400,
      },
      {
        title: '所属频道',
        key: 'channel_name',
        dataIndex: 'channel_name',
        width: 150,
      },
    ];
  };

  hotArticleColumns = () => {
    return [
      {
        title: '潮新闻ID',
        key: 'mlf_id',
        dataIndex: 'id',
        width: 85,
      },
      {
        title: '签发时间',
        key: 'time',
        dataIndex: 'published_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 150,
      },
      {
        title: '标题',
        key: 'list_title',
        dataIndex: 'list_title',
        render: (text: any) => (
          <Tooltip title={text}>
            <div className="ellipsis-comment" style={{ width: 400 }}>
              {text}
            </div>
          </Tooltip>
        ),
        width: 400,
      },
      {
        title: '所属频道',
        key: 'channel_name',
        dataIndex: 'channel_name',
        width: 150,
      },
    ];
  };

  alterTempList = (record: any) => {
    let { tempList, tempIds } = this.state;
    const pos = tempIds.indexOf(record.id);
    if (pos > -1) {
      tempIds.splice(pos, 1);
      tempList.splice(pos, 1);
    } else {
      tempIds = [record.id, ...tempIds];
      tempList = [record, ...tempList];
    }
    this.setState({ tempIds, tempList });
  };

  commentColumns = (add: boolean = false) => {
    const { selectedIds, disabledIds, tempIds } = this.state;
    const list = selectedIds.concat(disabledIds).concat(tempIds);
    return [
      {
        title: '用户昵称',
        key: 'nickname',
        dataIndex: 'nick_name',
        width: 90,
        render: (text: any) => (
          <Tooltip title={text}>
            <div className="ellipsis-comment" style={{ width: 100 }}>
              {text}
            </div>
          </Tooltip>
        ),
      },
      {
        title: '评论内容',
        key: 'content',
        dataIndex: 'content',
        width: 240,
        render: (text: any, record: any) => (
          <Tooltip title={<span dangerouslySetInnerHTML={{ __html: generateTextWithLink(text, record.comment_link_list) }}></span>}>
            <div style={{ width: 240, overflow: 'hidden' }}>
              {!!text && <div className="ellipsis-comment" dangerouslySetInnerHTML={{ __html: generateTextWithLink(text, record.comment_link_list) }}>
              </div>}
              {(!!record.expression_url || !!record.pic_url) && <ImagePreviewColumn className='comment-img' text={record.expression_url || record.pic_url} imgs={[record.expression_url || record.pic_url]}></ImagePreviewColumn>}
            </div>
          </Tooltip>
        ),
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <Button
            size="small"
            type="primary"
            disabled={add && list.indexOf(record.id) > -1}
            onClick={this.alterTempList.bind(this, record)}
          >
            {add ? '添加' : '删除'}
          </Button>
        ),
        width: 50,
      },
    ];
  };

  exchangeOrder = (index: number, isAsc: boolean) => {

    const selectedList = [...this.state.selectedList]
    const destIdx = isAsc ? index - 1 : index + 1
    const tmp = selectedList[index]
    selectedList[index] = selectedList[destIdx]
    selectedList[destIdx] = tmp

    const selectedIds = [...this.state.selectedIds]
    const tmpId = selectedIds[index]
    selectedIds[index] = selectedIds[destIdx]
    selectedIds[destIdx] = tmpId

    this.setState({ selectedList, selectedIds })
  }

  selectedCommentColumns = () => {
    return [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, index: any) => {
          const selectedList = this.state.selectedList
          const order = selectedList.indexOf(record)
          return (
            <span>
              <A
                disabled={order === 0}
                className="sort-up"
                onClick={this.exchangeOrder.bind(this, order, true)}
              >
                <Icon type="up-circle" theme="filled" />
              </A>
              <A
                disabled={order === selectedList.length - 1}
                className="sort-down"
                style={{ marginLeft: 8 }}
                onClick={this.exchangeOrder.bind(this, order, false)}
              >
                <Icon type="down-circle" theme="filled" />
              </A>
            </span>
          )
        },
        width: 70,
        align: 'center',
      } as any,
      {
        title: '评论内容',
        key: 'content',
        dataIndex: 'content',
        width: 200,
        render: (text: any, record: any) => (
          <Tooltip title={<span dangerouslySetInnerHTML={{ __html: generateTextWithLink(text, record.comment_link_list) }}></span>}>
            <div style={{ width: 200, overflow: 'hidden' }}>
              {!!record?.content && <div className="ellipsis-comment" dangerouslySetInnerHTML={{ __html: generateTextWithLink(text, record.comment_link_list) }}>
              </div>}
              {(!!record.expression_url || !!record.pic_url) && <ImagePreviewColumn className='comment-img' text={record.expression_url || record.pic_url} imgs={[record.expression_url || record.pic_url]}></ImagePreviewColumn>}
            </div>
          </Tooltip>
        ),
      },
      {
        title: '新闻标题',
        key: 'title',
        dataIndex: 'title',
        width: 200,
        render: (text: any) => (
          <Tooltip title={text}>
            <div className="ellipsis-comment" style={{ width: 200 }}>
              {text}
            </div>
          </Tooltip>
        ),
      },
      {
        title: '用户昵称',
        key: 'nickname',
        dataIndex: 'nick_name',
        width: 100,
        render: (text: any) => (
          <Tooltip title={text}>
            <div className="ellipsis-comment" style={{ width: 100 }}>
              {text}
            </div>
          </Tooltip>
        ),
      },
      {
        title: '评论时间',
        key: 'created_at',
        dataIndex: 'created_at',
        width: 80,
        render: (text: any) => {
          const dateStr = moment(parseInt(text)).format('YYYY-MM-DD HH:mm:ss')
          return (
            <Tooltip title={dateStr}>
              <div style={{ width: 80, whiteSpace: 'normal', textAlign: 'center' }}>
                {dateStr}
              </div>
            </Tooltip>
          )
        }
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <Button size="small" type="primary" onClick={this.removeFromSelected.bind(this, record)}>
            移除
          </Button>
        ),
        width: 50,
      },
    ];
  };

  allCommentColumns = () => {
    return [
      {
        title: '潮新闻ID',
        key: 'channel_article_id',
        dataIndex: 'channel_article_id',
        width: 80,
        render: (text: any) => <div style={{ whiteSpace: 'normal', width: 80 }}>{text}</div>,
      },
      {
        title: '签发时间',
        key: 'published_at',
        dataIndex: 'published_at',
        render: (text: any) => (
          <div style={{ whiteSpace: 'normal', width: 80 }}>
            {moment(text).format('YYYY-MM-DD HH:mm:ss')}
          </div>
        ),
        width: 80,
      },
      {
        title: '标题',
        key: 'title',
        dataIndex: 'title',
        render: (text: any) => (
          <Tooltip title={text}>
            <div className="multi-ellipsis-comment" style={{ width: 170, height: 42 }}>
              {text}
            </div>
          </Tooltip>
        ),
        width: 170,
      },
      {
        title: '评论内容',
        key: 'content',
        dataIndex: 'content',
        width: 240,
        render: (text: any, record: any) => (
          <Tooltip title={<span dangerouslySetInnerHTML={{ __html: generateTextWithLink(text, record.comment_link_list) }}></span>}>
            <div style={{ width: 240, display: 'flex', flexDirection: 'column' }}>
              {!!record?.content && <div className="multi-ellipsis-comment" dangerouslySetInnerHTML={{ __html: generateTextWithLink(text, record.comment_link_list) }}>
              </div>}
              {(!!record.expression_url || !!record.pic_url) && <ImagePreviewColumn className='comment-img' text={record.expression_url || record.pic_url} imgs={[record.expression_url || record.pic_url]}></ImagePreviewColumn>}
            </div>

          </Tooltip>
        ),
      },
      {
        title: '用户昵称',
        key: 'nickname',
        dataIndex: 'nick_name',
        // width: 95,
        render: (text: any) => (
          <span style={{ whiteSpace: 'normal' }}>{text}</span>
          // <Tooltip title={text}>
          //   <div className="multi-ellipsis-comment" style={{ width: 95, height: 42 }}>
          //     {text}
          //   </div>
          // </Tooltip>
        ),
      },
    ];
  };

  handleAllCommentPGChange = (page: number) => {
    if (this.state.selectedRowKeys.length > 0) {
      Modal.confirm({
        title: '已有选择评论，是否先添加到已选区再翻页？',
        okText: '添加并翻页',
        cancelText: '直接翻页',
        onOk: () => {
          this.addSelectCommentRowsToSelectedComment();
          this.getAllCommentList(page);
        },
        onCancel: () => {
          this.setState({
            selectedRows: [],
            selectedRowKeys: [],
          });
          this.getAllCommentList(page);
        },
      });
    } else {
      this.getAllCommentList(page);
    }
  };

  removeFromSelected = (record: any) => {
    const { selectedIds, selectedList } = this.state;
    const pos = selectedIds.indexOf(record.id);
    selectedIds.splice(pos, 1);
    selectedList.splice(pos, 1);
    this.setState({ selectedIds, selectedList });
  };

  cChange = (index: any, key: any, value: any) => {
    let v = value;
    if (typeof value === 'object') {
      v = value.target.value;
    }
    this.setState({
      [index]: { ...this.state[index], [key]: v },
    });
  };

  // getArticleRowClass = (record: any) => {
  //   return this.state.selectedArticle.id === record.id ? 'row-selected' : '';
  // }

  rowEvents = (record: any) => {
    return {
      onClick: () => {
        this.setState(
          {
            selectedArticle: {
              id: record.uuid || record.original_id || record.id,
              title: record.list_title,
            },
            commentSearch: {
              cKeyword: '',
              cType: 3,
              search_type: 3,
              keyword: '',
              loading: true,
              key: Date.now(),
              visible: true,
            },
            tempList: [],
            tempIds: [],
          },
          () => {
            this.getCommentList(1);
          }
        );
      },
    };
  };

  closeComment = () => {
    this.setState({
      commentSearch: {
        ...this.state.commentSearch,
        visible: false,
      },
    });
  };

  articlePGChange = (page: number) => {
    this.getArticleList(page);
  };

  commentPGChange = (page: number) => {
    this.getCommentList(page);
  };

  handleKey = (index: any, e: any) => {
    if (e.which === 13) {
      this.doSearch(index);
    }
  };

  doSearch = (index: any) => {
    this.setState(
      {
        [index]: {
          ...this.state[index],
          search_type: this.state[index].cType,
          keyword: this.state[index].cKeyword,
        },
      },
      () => {
        if (index === 'articleSearch') {
          this.getArticleList(1);
        } else if (index === 'commentSearch') {
          this.getCommentList(1);
        } else if (index === 'allCommentSearch') {
          this.getAllCommentList(1);
        }
      }
    );
  };

  addTempToSelect = () => {
    const { tempList, selectedList, tempIds, selectedIds } = this.state;
    this.setState({
      selectedIds: [...tempIds, ...selectedIds],
      selectedList: [...tempList, ...selectedList],
    });
    this.closeComment();
    scrollIntoView(
      document.getElementById('selected-comments'),
      document.getElementsByClassName('rox-drawer-content')[0]
    );
  };

  customValidator = () => {
    if (this.state.selectedList.length < 3) {
      message.error('请至少选择3条评论');
      scrollIntoView(
        document.getElementById('selected-comments'),
        document.getElementsByClassName('rox-drawer-content')[0]
      );
      return false;
    }
    if (this.state.selectedList.length > 30) {
      message.error(`最多可选择30条评论`);
      return false;
    }
    return true
  }

  getSubmitData = () => {
    return this.state.selectedList.map((v: any) => {
      return {
        article_id: v.uuid || v.channel_article_id,
        account_id: v.account_id,
        comment_content: v.content,
        comment_id: v.id,
        channel_id: this.props.channelId,
        comment_created_at: v.created_at,
        nick_name: v.nick_name,
        article_title: v.title,
        expression_url: v.expression_url,
        comment_link_list: v.comment_link_list,
        pic_url: v.pic_url,
      };
    })
  }

  doSubmit = () => {
    if (!this.customValidator()) {
      return
    }
    const body = this.getSubmitData()
    setMLoading(this, true);
    api
      .createCommentRecommend(body)
      .then(() => {
        message.success('添加成功');
        setMLoading(this, false);
        this.props.onEnd();
      })
      .catch(() => {
        setMLoading(this, false);
      });
  };

  handleTabChange = (key: string) => {
    this.setState({
      tab: key,
    });
    if (key === '1') {
      this.setState({
        articleSearch: {
          cKeyword: '',
          cType: 2,
          search_type: 2,
          keyword: '',
          loading: false,
        },
        articleList: {
          size: 15,
          current: 1,
          total: 1,
          records: [],
        },
      });
    }
    if (key === '2') {
      this.setState({
        allCommentSearch: {
          keyword: '',
          cKeyword: '',
        },
        allCommentList: {
          size: 15,
          current: 1,
          total: 0,
          records: [],
        },
        selectedRows: [],
        selectedRowKeys: [],
      });
    }
    if (key === '3') {
      this.getHotNewsList();
    }
  };

  handleSelectCommentRows = (selectedRowKeys: any, selectedRows: any) => {
    this.setState({
      selectedRowKeys,
      selectedRows,
    });
  };

  addSelectCommentRowsToSelectedComment = () => {
    this.setState({
      selectedList: this.state.selectedRows.concat(this.state.selectedList),
      selectedIds: this.state.selectedRowKeys.concat(this.state.selectedIds),
      selectedRows: [],
      selectedRowKeys: [],
    });
    scrollIntoView(
      document.getElementById('selected-comments'),
      document.getElementsByClassName('rox-drawer-content')[0]
    );
  };

  render() {
    const {
      articleSearch,
      commentSearch,
      selectedList,
      articleList,
      commentList,
      selectedArticle,
      selectedIds,
      disabledIds,
      tempList,
      allCommentSearch,
      allCommentList,
      selectedRowKeys,
      hotArticleList,
    } = this.state;
    const commentRowSelections = {
      selectedRowKeys,
      getCheckboxProps: (record: any) => {
        return { disabled: selectedIds.concat(disabledIds).indexOf(record.id) > -1 };
      },
      onChange: this.handleSelectCommentRows,
    };
    return (
      <div className="comment-recommend-form">
        <Tabs defaultActiveKey="1" onChange={this.handleTabChange}>
          <Tabs.TabPane tab="搜索稿件" key="1">
            <Row style={{ marginBottom: 8 }}>
              <Col span={12} style={{ lineHeight: '32px' }}>
                <h3>选择新闻</h3>
              </Col>
              <Col span={12} style={{ textAlign: 'right' }}>
                <Select
                  style={{ width: 130 }}
                  value={articleSearch.cType}
                  onChange={this.cChange.bind(this, 'articleSearch', 'cType')}
                >
                  <Select.Option value={2}>搜索标题</Select.Option>
                  <Select.Option value={1}>搜索潮新闻ID</Select.Option>
                </Select>
                <Input
                  style={{ width: 180, marginLeft: 8 }}
                  placeholder="请输入关键字"
                  value={articleSearch.cKeyword}
                  onChange={this.cChange.bind(this, 'articleSearch', 'cKeyword')}
                  onKeyPress={this.handleKey.bind(this, 'articleSearch')}
                />
                <Button
                  style={{ verticalAlign: 'top', marginLeft: 8 }}
                  onClick={this.doSearch.bind(this, 'articleSearch')}
                >
                  搜索
                </Button>
              </Col>
            </Row>
            <Row>
              <Table
                bordered={true}
                loading={articleSearch.loading}
                dataSource={articleList.records}
                columns={this.articleColumns()}
                pagination={false}
                rowKey="id"
                onRow={this.rowEvents}
              />
            </Row>
            <Row style={{ marginTop: 8, marginBottom: 8, textAlign: 'center' }}>
              <Pagination
                total={articleList.total}
                pageSize={15}
                onChange={this.articlePGChange}
                showQuickJumper={true}
              />
            </Row>
          </Tabs.TabPane>
          <Tabs.TabPane tab="搜索评论" key="2">
            <Row style={{ marginBottom: 8 }}>
              <Col span={12} style={{ lineHeight: '32px' }}>
                <h3>搜索评论</h3>
              </Col>
              <Col span={12} style={{ textAlign: 'right' }}>
                <Input
                  style={{ width: 180, marginLeft: 8 }}
                  placeholder="请输入评论内容搜索"
                  value={allCommentSearch.cKeyword}
                  onChange={this.cChange.bind(this, 'allCommentSearch', 'cKeyword')}
                  onKeyPress={this.handleKey.bind(this, 'allCommentSearch')}
                />
                <Button
                  style={{ verticalAlign: 'top', marginLeft: 8 }}
                  onClick={this.doSearch.bind(this, 'allCommentSearch')}
                >
                  搜索
                </Button>
              </Col>
            </Row>
            <Row>
              <Table
                rowSelection={commentRowSelections}
                bordered={true}
                loading={allCommentSearch.loading}
                dataSource={allCommentList.records}
                columns={this.allCommentColumns()}
                pagination={false}
                rowKey="id"
              />
            </Row>
            <Row style={{ marginTop: 8, marginBottom: 8 }}>
              <Col span={6}>
                <Button
                  disabled={selectedRowKeys.length === 0}
                  type="primary"
                  onClick={this.addSelectCommentRowsToSelectedComment}
                >
                  添加所选评论
                </Button>
              </Col>
              <Col span={18} style={{ textAlign: 'right' }}>
                <Pagination
                  total={allCommentList.total}
                  pageSize={15}
                  onChange={this.handleAllCommentPGChange}
                  showQuickJumper={true}
                />
              </Col>
            </Row>
          </Tabs.TabPane>
          <Tabs.TabPane tab="热点新闻" key="3">
            <Row style={{ marginBottom: 8 }}>
              <Col span={12} style={{ lineHeight: '32px' }}>
                <h3>选择新闻</h3>
              </Col>
              <Col span={12} style={{ textAlign: 'right' }} />
            </Row>
            <Row>
              <Table
                bordered={true}
                loading={articleSearch.loading}
                dataSource={hotArticleList}
                columns={this.hotArticleColumns()}
                pagination={{ pageSize: 15 }}
                rowKey="id"
                onRow={this.rowEvents}
              />
            </Row>
          </Tabs.TabPane>
        </Tabs>
        <Row style={{ marginBottom: 8 }} id="selected-comments">
          <h3>已选择评论</h3>
        </Row>
        <Row>
          <Table
            bordered={true}
            dataSource={selectedList}
            columns={this.selectedCommentColumns()}
            rowKey="id"
            pagination={{
              pageSize: 10,
              size: 'small',
              showTotal: (total: number) =>
                `已选择${total}条评论，最多可添加30条评论`,
            }}
          />
        </Row>
        <Modal
          wrapClassName="comment-recommend-form-comments"
          visible={commentSearch.visible}
          title={
            <span>
              选择评论：《<div className="ellipsis-article-title">{selectedArticle.title}</div>》
            </span>
          }
          width="950px"
          onCancel={this.closeComment}
          onOk={this.addTempToSelect}
        >
          <Row className="comment-wrapper" gutter={24}>
            <Col span={12}>
              <Row>
                <Col span={4}>
                  <h3>评论列表</h3>
                </Col>
                <Col span={20} style={{ textAlign: 'right' }}>
                  <Select
                    size="small"
                    style={{ width: 120 }}
                    value={commentSearch.cType}
                    onChange={this.cChange.bind(this, 'commentSearch', 'cType')}
                  >
                    <Select.Option value={3}>搜索评论内容</Select.Option>
                    <Select.Option value={5}>搜索用户昵称</Select.Option>
                  </Select>
                  <Input
                    size="small"
                    style={{ width: 160, marginLeft: 8, marginRight: 8 }}
                    placeholder="请输入关键字"
                    value={commentSearch.cKeyword}
                    onChange={this.cChange.bind(this, 'commentSearch', 'cKeyword')}
                    onKeyPress={this.handleKey.bind(this, 'commentSearch')}
                  />
                  <Button
                    size="small"
                    style={{ verticalAlign: 'top' }}
                    onClick={this.doSearch.bind(this, 'commentSearch')}
                  >
                    搜索
                  </Button>
                </Col>
              </Row>
              <Divider type="horizontal" style={{ margin: '8px 0' }} />
              <Row>
                <Table
                  bordered={true}
                  loading={commentSearch.loading}
                  dataSource={commentList.records}
                  columns={this.commentColumns(true)}
                  pagination={false}
                  rowKey="id"
                />
              </Row>
              <Row style={{ marginTop: 8, marginBottom: 8, textAlign: 'right' }}>
                <Pagination
                  total={commentList.total}
                  pageSize={15}
                  onChange={this.commentPGChange}
                  showQuickJumper={true}
                  size="small"
                />
              </Row>
            </Col>
            <Col span={12}>
              <Row>
                <h3>已选择评论</h3>
              </Row>
              <Divider type="horizontal" style={{ margin: '8px 0' }} />
              <Row>
                <Table
                  bordered={true}
                  dataSource={tempList}
                  columns={this.commentColumns()}
                  rowKey="id"
                  pagination={{
                    pageSize: 15,
                    size: 'small',
                  }}
                />
              </Row>
            </Col>
          </Row>
        </Modal>
      </div>
    );
  }
}

export default CommentRecommendForm;
