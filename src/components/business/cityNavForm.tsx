import { sysApi as api } from '@app/api';
import { Form, Input, message, Radio, Select, Checkbox } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader } from '../common';

type Api = 'updateCityNav' | 'createCityNav';

@connectSession
@(Form.create({ name: 'cityNavForm' }) as any)
class CityNavForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      ...props.formContent,
      privGroup: props.privGroup || [],
      provinceList: props.provinceList || [],
    };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body: any = { ...values, sort_number: 1 };
        let func = 'create';
        if (this.state.id) {
          func = 'update';
          body.id = this.state.id;
        }

        if (body.require_permission == 0) {
          body.require_permission_id = 0;
        } else {
          body.require_permission_id = body.require_permission_id.join(',');
        }
        delete body.require_permission;

        setMLoading(this, true);
        api[`${func}CityNav` as Api](body)
          .then(() => {
            message.success('操作成功');
            setMLoading(this, false);
            this.props.onEnd();
          })
          .catch(() => setMLoading(this, false));
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  render() {
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="名称">
          {getFieldDecorator('name', {
            initialValue: this.state.name,
            rules: [
              {
                required: true,
                message: '请填写名称',
              },
              // {
              //   pattern: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/,
              //   message: '名称不能包含特殊字符',
              // },
            ],
          })(<Input placeholder="请输入名称" />)}
        </Form.Item>
        <Form.Item label="频道地址">
          {getFieldDecorator('url', {
            initialValue: this.state.url,
            rules: [
              {
                required: true,
                message: '请输入频道地址',
              },
            ],
          })(<Input placeholder="请输入频道地址" />)}
        </Form.Item>
        <Form.Item label="状态">
          {getFieldDecorator('enabled', {
            initialValue: this.state.enabled,
          })(
            <Radio.Group>
              <Radio value={true}>上线</Radio>
              <Radio value={false}>下线</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item label="上线范围">
          {getFieldDecorator('belong', {
            initialValue: this.state.belong,
          })(
            <Checkbox.Group>
              <Checkbox value="1">城市频道</Checkbox>
              {/* <Checkbox value="2">服务频道</Checkbox> */}
              <Checkbox value="3">同城频道</Checkbox>
            </Checkbox.Group>
          )}
        </Form.Item>

        <Form.Item label="所属省份">
          {getFieldDecorator('parent_id', {
            initialValue: this.state.parent_id,
            rules: [
              {
                required: true,
                message: '请选择省份',
              },
            ],
          })(
            <Select placeholder="请选择所属省份">
              <Select.Option value="" disabled={true}>
                请选择省份
              </Select.Option>
              {this.state.provinceList.map((v: any) => (
                <Select.Option value={v.id} key={v.id}>
                  {v.name}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>

        <Form.Item label="添加到桌面">
          {getFieldDecorator('nav_share_flag', {
            initialValue: this.state.nav_share_flag ?? false,
          })(
            <Radio.Group>
              <Radio value={false}>关闭</Radio>
              <Radio value={true}>开启</Radio>
            </Radio.Group>
          )}
        </Form.Item>

        {getFieldValue('nav_share_flag') && (
          <>
            <Form.Item label="名称">
              {getFieldDecorator('nav_share_name', {
                initialValue: this.state.nav_share_name,
              })(<Input placeholder="最多8字，不填写时默认为频道名称" maxLength={8} />)}
            </Form.Item>

            <Form.Item label="iOS图标" extra={'支持.jpg .jpeg .png 图片格式, 比例1:1'}>
              {getFieldDecorator('ios_icon', {
                initialValue: this.state.ios_icon,
                rules: [
                  {
                    required: true,
                    message: '请上传iOS图标',
                  },
                ],
              })(<ImageUploader ratio={1}></ImageUploader>)}
            </Form.Item>

            <Form.Item label="安卓图标" extra={'支持.jpg .jpeg .png 图片格式, 比例1:1'}>
              {getFieldDecorator('android_icon', {
                initialValue: this.state.android_icon,
                rules: [
                  {
                    required: true,
                    message: '请上传安卓图标',
                  },
                ],
              })(<ImageUploader ratio={1}></ImageUploader>)}
            </Form.Item>

            <Form.Item label="分享地址">
              {getFieldDecorator('nav_share_url', {
                initialValue: this.state.nav_share_url,
                rules: [
                  {
                    required: true,
                    message: '请输入分享地址',
                  },
                ],
              })(<Input placeholder="请输入分享地址" />)}
            </Form.Item>
          </>
        )}

        <Form.Item label="特权组要求">
          {getFieldDecorator('require_permission', {
            initialValue:
              this.state.require_permission_id == 0 || !this.state.require_permission_id ? 0 : 1,
          })(
            <Radio.Group>
              <Radio value={0}>所有用户可见</Radio>
              <Radio value={1}>仅特权组可见</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {getFieldValue('require_permission') == 1 && (
          <Form.Item label=" " colon={false} style={{ marginTop: -20 }}>
            {getFieldDecorator('require_permission_id', {
              initialValue:
                this.state.require_permission_id == 0 || !this.state.require_permission_id
                  ? []
                  : this.state.require_permission_id.toString().split(','),
              rules: [
                {
                  validator: (rule: any, value: any, callback: any) => {
                    if (value?.length > 0) {
                      callback();
                    } else {
                      callback('请至少选择1个特权组');
                    }
                  },
                },
              ],
            })(
              <Checkbox.Group>
                {this.state.privGroup.map((v: any) => (
                  <Checkbox key={v.id} value={v.id.toString()}>
                    {v.group_name}
                  </Checkbox>
                ))}
              </Checkbox.Group>
            )}
          </Form.Item>
        )}
      </Form>
    );
  }
}

export default CityNavForm;
