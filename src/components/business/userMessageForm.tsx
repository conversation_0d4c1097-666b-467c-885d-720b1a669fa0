import { userApi as api } from '@app/api';
import { FileSelector, ImageUploader } from '@components/common';
import { MSG_TIP_PIC } from '@utils/constants';
import { Form, Icon, Input, message, Modal, Radio, Tooltip } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';

@connectSession
@(Form.create({ name: 'userMessageForm' }) as any)
class UserMessageForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      userType: 0,
    };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    const keys = ['notice_title', 'body', 'image_url', 'url'];
    keys.push(this.state.userType === 0 ? 'chao_ids' : 'file');
    this.props.form.validateFieldsAndScroll(keys, (err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        api
          .createUserMessage(values)
          .then((r: any) => {
            if (r.data.is_all_success) {
              message.success('发送用户消息成功');
              setMLoading(this, false);
              this.props.onEnd();
            } else {
              setMLoading(this, false);
              const isFile = r.data.select_type === 1;
              Modal.error({
                title: isFile ? '名单中的用户发送失败，点击下载查看：' : '如下用户发送失败：',
                content: (
                  <span>
                    {isFile ? (
                      <a href={r.data.fail_accounts_file} target="_blank" rel="noreferrer">
                        {r.data.fail_accounts_file}
                      </a>
                    ) : (
                      r.data.fail_accounts
                    )}
                  </span>
                ),
                okText: '关闭',
              });
            }
          })
          .catch(() => setMLoading(this, false));
      } else {
        message.error('请检查表单项');
      }
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const text = (
      <span>
        <p>注：文件格式如下：</p>
        <p>
          <img src={MSG_TIP_PIC} width="150px" />
        </p>
      </span>
    );
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="消息标题">
          {getFieldDecorator('notice_title', {
            rules: [
              {
                required: true,
                message: '消息标题不能为空',
              },
              {
                max: 15,
                message: '消息标题不能超过15个字',
              },
            ],
          })(<Input placeholder="输入15字以内的消息标题" />)}
        </Form.Item>
        <Form.Item label="消息内容">
          {getFieldDecorator('body', {
            rules: [
              {
                required: true,
                message: '消息内容不能为空',
              },
              {
                max: 100,
                message: '消息内容不能超过100个字',
              },
            ],
          })(<Input.TextArea rows={3} placeholder="输入100字以内的消息内容" />)}
        </Form.Item>
        <Form.Item label="消息配图">
          {getFieldDecorator('image_url', {
            initialValue: '',
          })(<ImageUploader ratio={375 / 128} />)}
        </Form.Item>
        <Form.Item label="跳转链接">
          {getFieldDecorator('url', {
            initialValue: '',
            rules: [
              {
                pattern: /^https?:\/\//,
                message: '请输入正确的跳转链接',
              },
            ],
          })(<Input placeholder="输入跳转链接" />)}
        </Form.Item>
        <Form.Item label="用户填写方式" key="3">
          <Radio.Group
            value={this.state.userType}
            onChange={(e: any) => this.setState({ userType: e.target.value })}
          >
            <Radio value={0}>输入用户小潮号（最多输入2000个字）</Radio>
            <Radio value={1}>
              报表自动导入&nbsp;
              <Tooltip placement="bottom" title={text} arrowPointAtCenter={true}>
                <Icon type="question-circle" />
              </Tooltip>
            </Radio>
          </Radio.Group>
        </Form.Item>
        {this.state.userType === 0 && (
          <Form.Item label="小潮号">
            {getFieldDecorator('chao_ids', {
              preserve: true,
              rules: [
                {
                  required: true,
                  message: '请填写小潮号',
                },
                {
                  max: 2000,
                  message: '用户昵称不能超过2000个字',
                },
              ],
            })(<Input.TextArea rows={8} placeholder="输入小潮号，以换行分隔" />)}
          </Form.Item>
        )}
        {this.state.userType === 1 && (
          <Form.Item label="用户文件">
            {getFieldDecorator('file', {
              preserve: true,
              rules: [
                {
                  required: true,
                  message: '请选择文件',
                },
              ],
            })(<FileSelector accept=".xls,.xlsx" />)}
          </Form.Item>
        )}
      </Form>
    );
  }
}

export default UserMessageForm;
