/* eslint-disable array-callback-return */
/* eslint-disable no-nested-ternary */
import { opApi as api } from '@app/api';
import { Row, Form, Table, message, Spin, Select, Button, Transfer, Modal } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import debounce from 'lodash/debounce';
import clonedeep from 'lodash/cloneDeep';
import { TransferItem } from 'antd/es/transfer';

@connectSession
@(Form.create({ name: 'UGCTopicRecommendForm' }) as any)
class UGCTopicRecommendForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    console.log(props);
    this.state = {
      searchResults: [],
      news: [],
      id: [],
      key: Date.now(),
      topicId: props.id,
      topicType: props.type
    };
    this.fetchNews = debounce(this.fetchNews, 500);
  }
  // 搜索新闻
  fetchNews = (value: string) => {
    this.setState({ fetching: true });
    api
      .getTopicNewsList({
        keyword: value,
        search_type: 0,
        topic_id: this.state.topicId,
      })
      .then((r: any) => {
        this.setState({ fetching: false, searched: true });
        // 如果话题是视频话题，只能将视频内容设为置顶，即添加置顶稿件时，过滤掉非视频的内容
        const records = r.data.list.records
        this.setState({
          searchResults:
            this.state.topicType == 1 ? records.filter((item: any) => !item.down && item.doc_type === 10) : records.filter((item: any) => !item.down)
        });
      })
      .catch(() => {
        this.setState({ fetching: false });
      });
  };
  // 选项改变
  handleChange = (svalue: any) => {
    const value = JSON.parse(svalue[0]);
    const state = {
      id: value.id,
      news: [value],
      searchResults: [],
      searched: false,
    };
    this.setState({ ...state });
  };

  onSelectBlur = () => {
    this.setState({
      searchResults: [],
      searched: false,
    });
  };

  getColumns = () => {
    return [
      {
        title: '潮新闻ID',
        key: 'id',
        dataIndex: 'id',
        width: 90,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
        // render: (text: any, record: any) => <span>{record.doc_type === 12 ? record.content : record.list_title}</span>,
      },

      // {
      //   title: '操作',
      //   key: 'op',
      //   render: (text: any, record: any) => (
      //     <a onClick={this.handleDelete.bind(this, record)}>删除</a>
      //   ),
      //   width: 70,
      // },
    ];
  };

  // handleDelete = (record: any) => {
  //   this.setState({
  //     articleIds: ids,
  //     news: articleList,
  //   });
  // };

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };
  // 提交
  doSubmit = () => {
    if (this.state.news.length === 0) {
      message.error('请添加稿件');
      return;
    }
    setMLoading(this, true);
    api
      .creatTopManuscript({ topic_id: this.state.topicId, article_id: this.state.id })
      .then((res: any) => {
        console.log(res);
        message.success('添加成功');
        setMLoading(this, false);
        this.props.onEnd();
      })
      .catch((e) => {
        setMLoading(this, false);
        console.error(e);
      });
  };

  render() {
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="添加新闻" required={true}>
          <Row>
            <Select
              mode="multiple"
              value={[]}
              placeholder="输入潮新闻ID或标题"
              notFoundContent={
                this.state.fetching ? (
                  <Spin size="small" style={{ margin: 'auto' }} />
                ) : this.state.searched ? (
                  '无结果'
                ) : null
              }
              filterOption={false}
              onSearch={this.fetchNews}
              onChange={this.handleChange}
              onBlur={this.onSelectBlur}
              style={{ width: 280 }}
            >
              {this.state.searchResults.map((d: any, i: number) => (
                <Select.Option key={`${i}`} value={JSON.stringify(d)}>
                  {`${d.id} ${d.doc_type === 12 ? d.content : d.list_title}`}
                </Select.Option>
              ))}
            </Select>
          </Row>
          <Row style={{ marginTop: 8 }}>
            <Table
              columns={this.getColumns()}
              rowKey="id"
              dataSource={this.state.news}
              pagination={false}
            />
          </Row>
        </Form.Item>
      </Form>
    );
  }
}

export default UGCTopicRecommendForm;
