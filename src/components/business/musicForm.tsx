import { opApi as api } from '@app/api';
import { Checkbox, Form, Input, message, Radio } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader, FileUploader } from '../common';

@connectSession
@(Form.create({ name: 'musicForm' }) as any)
class MusicForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      ...props.formContent,
      classList: props.classList || [],
    };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  }

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        let func = 'createMusic';
        const body = {
          ...values,
          class_ids: values.class_ids.join(','),
        };
        if (this.state.id) {
          func = 'updateMusic';
          body.id = this.state.id;
        }
        body.play_duration = this.state.play_duration
        api[func as keyof typeof api](body)
          .then(() => {
            setMLoading(this, false);
            message.success('操作成功');
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  }
  receive=(msg:any)=>{
      const audioElement = new Audio(msg);
      let min = 0
      let sec = 0 
      audioElement.addEventListener("loadedmetadata", (_event) => {
         min = Math.floor(audioElement.duration/60)
         sec = Math.floor(audioElement.duration%60)
         this.setState({
          play_duration: min + ':' + sec
         })
      });
	}
  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit} ref="form">
        <Form.Item label="歌曲名">
          {getFieldDecorator('name', {
            initialValue: this.state.name,
            rules: [
              {
                required: true,
                message: '请填写歌曲名',
              },
              {
                max: 30,
                message: '歌曲名最长不能超过30个字',
              },
            ],
          })(<Input placeholder="请输入歌曲名" />)}
        </Form.Item>
        <Form.Item label="歌手名">
          {getFieldDecorator('singer', {
            initialValue: this.state.singer,
            rules: [
              {
                required: true,
                message: '请填写歌手名',
              },
              {
                max: 10,
                message: '歌手名最长不能超过10个字',
              },
            ],
          })(<Input placeholder="请输入歌手名" />)}
        </Form.Item>
        <Form.Item label="封面图" extra="支持jpg,jpeg,png,gif图片格式，比例为 1:1">
          {getFieldDecorator('pic_url', {
            initialValue: this.state.pic_url,
            rules: [
              {
                required: true,
                message: '请上传封面图',
              },
            ],
          })(<ImageUploader ratio={1 / 1} accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']} />)}
        </Form.Item>
        <Form.Item label="所属分类">
          {getFieldDecorator('class_ids', {
            initialValue: this.state.class_ids,
            rules: [
              {
                required: true,
                message: '请选择所属分类',
              },
            ],
          })(
            <Checkbox.Group style={{ width: '100%' }}>
              {this.state.classList.map((v: any, i: number) => (
                <Checkbox value={v.id.toString()} key={i}>
                  {v.class_name}
                </Checkbox>
              ))}
            </Checkbox.Group>
          )}
        </Form.Item>
        <Form.Item label="上传文件" extra={'仅支持MP3格式,当前文件时长：'+this.state.play_duration }>
          {getFieldDecorator('music_url', {
            initialValue: this.state.music_url,
            rules: [
              {
                required: true,
                message: '请上传音乐文件',
              },
            ],
          })(<FileUploader accept="audio/mp3" onChange={this.receive} />)}
        </Form.Item>
      </Form>
    );
  }
}

export default MusicForm;
