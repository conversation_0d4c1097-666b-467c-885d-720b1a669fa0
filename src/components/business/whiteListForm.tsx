import {communityApi, userApi as api} from '@app/api';
import {Checkbox, Form, Icon, message, Select, Tooltip} from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
const { Option } = Select;
@connectSession
@(Form.create({ name: 'whiteListForm' }) as any)
class WhiteListForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const {formContent} = this.props
    this.state = {
      isEdit: formContent ? formContent.isEdit : false,
      type: props.type,
      userList: formContent.account_id ? [{id:formContent.account_id,nick_name:formContent.nick_name,chao_id:formContent.chao_id}] : [],
      account_id: formContent ? formContent.account_id : '',
      nick_name: formContent ? formContent.nick_name : '',
      scenes: formContent ? formContent.scenes : [],
    };
  }
  handleSearch = (newValue: string) =>{
      if (!newValue || newValue =='')
          return
      const data = {
          current: 1,
          size: 20,
          status: 2,
          keyword: newValue
      }
      communityApi.recommendAccount_Search(data).then(res=>{
          const {list} =  res.data;
          this.setState({
              userList: list
          })
      })
  }

    handleChange = (newValue: string) => {
        this.setState({
            value: [newValue]
        })
    };


  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        const body = {
          ...values,
        };
        const request = this.state.isEdit ?  api.editWhiteList : api.createWhiteList
          request(body)
          .then((res: any) => {
            message.success('创建成功')
            setMLoading(this, false);
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const {userList,scenes,account_id} = this.state
    const options = userList.length > 0 ?
        userList.map((d:{id:number,nick_name:string}) => <Option key={d.id} value={d.id}>{d.nick_name}-小潮号：{d.chao_id}</Option>)
        :[]
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit} ref="form">
        <Form.Item label="白名单用户">
          {getFieldDecorator('account_id', {
            initialValue: account_id ? account_id : undefined,
            rules: [
              {
                required: true,
                message: '请输入用户昵称或小潮号',
              },
            ],
          })(
              <Select
                  disabled={this.state.isEdit}
                  showSearch
                  defaultActiveFirstOption={false}
                  suffixIcon={null}
                  filterOption={false}
                  onSearch={this.handleSearch}
                  onChange={this.handleChange}
                  placeholder="请输入用户昵称或小潮号"
              >
                  {options}
              </Select>
          )}
        </Form.Item>
        <Form.Item label="不检测场景">
          {getFieldDecorator('scenes', {
            initialValue: scenes ? scenes : [],
            rules: [
              {
                required: true,
                message: '请选择不检测场景',
              },
            ],
          })(
              <Checkbox.Group style={{marginTop:10}}>
                  {/*<Checkbox value="1" style={{width:'45%',marginLeft:0}}>*/}
                  {/*    发布内容-文字信息*/}
                  {/*    <Tooltip title="影响范围：客户端-发布视频、短图文的标题/描述文字">*/}
                  {/*        <Icon type="question-circle" />*/}
                  {/*    </Tooltip>*/}
                  {/*</Checkbox>*/}
                  <Checkbox value="2" style={{width:'45%',marginLeft:0}}>
                      发布内容-图片信息
                      <Tooltip title="影响范围：客户端及创作者平台-发布视频、短图文、长文章中的封面图/配图">
                          <Icon type="question-circle" />
                      </Tooltip>
                  </Checkbox>
                  <Checkbox value="3" style={{width:'45%',marginLeft:0}}>
                      编辑账号-文字信息
                      <Tooltip title="影响范围：客户端及创作者平台-修改昵称、账号简介">
                          <Icon type="question-circle" />
                      </Tooltip>
                  </Checkbox>
                  <Checkbox value="4" style={{width:'45%',marginLeft:0}}>
                      编辑账号-图片信息
                      <Tooltip title="影响范围：客户端及创作者平台-修改头像、个人主页背景图等">
                          <Icon type="question-circle" />
                      </Tooltip>
                  </Checkbox>
              </Checkbox.Group>
          )}
        </Form.Item>
      </Form>
    );
  }
}

export default WhiteListForm;
