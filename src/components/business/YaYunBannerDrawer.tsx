import React from 'react';
import { <PERSON>, Drawer, ImageUploader } from '@components/common';
import {
  DatePicker,
  Form,
  Icon,
  Input,
  InputNumber,
  Modal,
  Radio,
  Switch,
  Tooltip,
  message,
} from 'antd';
import connect from '@utils/connectSession';
import SearchAndInput from '../common/newNewsSearchAndInput';
import ColorSelectModal from './ColorSelectModal';
import { releaseListApi as api } from '@app/api';
import { searchToObject, setMLoading } from '@app/utils/utils';
import CountTextArea from '../common/CountTextArea';
import moment from 'moment';

const bannerLeftSVG = (style: any, color: string) => {
  return (
    <svg
      style={style}
      width="260px"
      height="98px"
      viewBox="0 0 260 98"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
    >
      <title>bg_upper@3x</title>
      <g id="修改" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <path
          d="M0,33.7382023 L0,87 C6.76353751e-16,92.5228475 4.4771525,97 10,97 L250,97 C255.522847,97 260,92.5228475 260,87 L260,14.4507217 C260,6.7187352 253.731986,0.4507217 246,0.4507217 C245.540525,0.4507217 245.081329,0.473341384 244.624078,0.518498621 L9.0171989,23.7866144 C3.89987111,24.2919925 1.14661622e-15,28.5959799 0,33.7382023 Z"
          id="路径-2备份-5"
          fill={color}
          transform="translate(130.000000, 48.000000) scale(-1, 1) translate(-130.000000, -48.000000) "
        ></path>
      </g>
    </svg>
  );
};

const bannerRightSVG = (style: any, color: string) => {
  return (
    <svg
      style={style}
      width="260px"
      height="93px"
      viewBox="0 0 260 93"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
    >
      <title>bg_bottom@3x</title>
      <g id="修改" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <path
          d="M0,43.8969524 L0,83 C8.0342647e-15,88.5228475 4.4771525,93 10,93 L250,93 C255.522847,93 260,88.5228475 260,83 L260,16.5618155 C260,8.82982899 253.731986,2.56181548 246,2.56181548 C245.219012,2.56181548 244.439393,2.62716631 243.669303,2.75718398 L0,43.8969524 L0,43.8969524 Z"
          id="路径-2备份"
          fill={color}
        ></path>
      </g>
    </svg>
  );
};

function preview(colorRecord: any) {
  const { picUrl, list_title, bottom_color } = colorRecord;
  const layerStyle: any = {
    position: 'absolute',
    left: 0,
    bottom: 0,
    width: '100%',
    height: '100%',
    fill: bottom_color,
  };
  return (
    <div style={{ position: 'relative', width: 200, height: 210, backgroundColor: '#fff' }}>
      <img src={picUrl} width={200} height={150} />
      <div style={{ position: 'absolute', left: 0, bottom: 0, width: 200, height: 75 }}>
        {bannerLeftSVG(layerStyle, bottom_color)}
        {bannerRightSVG(layerStyle, bottom_color)}
        <img style={layerStyle} src="/assets/color_select_left_border.png" />
        <h6
          style={{
            position: 'absolute',
            color: '#fff',
            fontSize: 12,
            margin: '20px 10px 0',
            display: '-webkit-box',
            textOverflow: 'ellipsis',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            height: 36,
          }}
        >
          {list_title}
        </h6>
      </div>
    </div>
  );
}

@connect
@(Form.create({ name: 'YaYunBannerDrawer' }) as any)
export default class YaYunBannerDrawer extends React.Component<any, any> {
  state = {
    showColorModal: false,
    colorData: {},
    colorDataIndex: -1,
    detail: {},
  };

  onOk = () => {
    const { form, onEnd } = this.props;
    form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        const { status, channelArticles } = values;
        api
          .contentRecommendFocusSave({
            type: 36,
            channel_id: searchToObject().channel_id,
            status: status ? 1 : 0,
            ref_ids: channelArticles.map((item: any) => item.id).join(','),
            ref_extensions: JSON.stringify(
              channelArticles.map((item: any) => ({
                article_id: item.id,
                list_pic: item.picUrl,
                bottom_color: item.bottom_color,
                color_type: item.color_type,
                list_title: item.focus_title,
              }))
            ),
            visible: values.visible,
            nav_type: values.nav_type.format('YYYY-MM-DD HH:mm:ss'),
            visible_org: values.visible_org,
            position: values.position || 0,
            origin_user: values.origin_user || ''
          })
          .then(() => {
            setMLoading(this, false);
            message.success('操作成功');
            onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  editFocusTitle = (record: any, index: number) => {
    let validator: any;
    Modal.confirm({
      title: '焦点图标题',
      content: (
        <CountTextArea
          initText={record.focus_title || record.list_title}
          placeholder="请输入焦点图标题"
          limitLength={100}
          setupValidator={(fn: Function) => (validator = fn)}
        />
      ),
      onOk: (destroy: Function) => {
        const title = validator && validator();
        if (title) {
          const articles = [...this.props.form.getFieldsValue().channelArticles];
          articles[index].focus_title = title;
          this.props.form.setFieldsValue({ channelArticles: articles });
          destroy();
        }
      },
    });
  };

  editPic = (url: string, index: number) => {
    let pic: string = url;
    let modal: any;
    const picChange = (u: string) => {
      pic = u;
      modal.update({
        content: (
          <>
            <ImageUploader value={pic} onChange={picChange} ratio={4 / 3} />
            <p>比例4:3</p>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '编辑图片',
      content: (
        <>
          <ImageUploader value={pic} onChange={picChange} ratio={4 / 3} />
          <p>比例4:3</p>
        </>
      ),
      onOk: async (destroy: Function) => {
        if (!pic) {
          message.error('请上传图片');
          return;
        }
        const articles = [...this.props.form.getFieldsValue().channelArticles];
        articles[index].picUrl = pic;
        // 请求自动取色接口
        const { data } = await api.contentRecommendGetColor({ url: pic });
        const { color } = data as any;
        articles[index].color_type = 1;
        articles[index].bottom_color = '';
        articles[index].auto_color = color.indexOf('0x') >= 0 ? `#${color.substr(2)}` : color;
        this.props.form.setFieldsValue({ channelArticles: articles });
        destroy();
      },
    });
  };

  editColor = async (_: string, data: any, index: number) => {
    const { color_type, auto_color, picUrl } = data;
    if (color_type === 0 && !auto_color) {
      // 请求自动取色接口
      const { data: colorData } = await api.contentRecommendGetColor({ url: picUrl });
      const { color } = colorData as any;
      data.auto_color = color.indexOf('0x') >= 0 ? `#${color.substr(2)}` : color;
    }
    this.setState({
      showColorModal: true,
      colorData: data,
      colorDataIndex: index,
    });
  };

  editColorClose = () => {
    this.setState({
      showColorModal: false,
      colorData: {},
      colorDataIndex: -1,
    });
  };

  editColorEnd = (index: number, color_type: string, bottom_color: string) => {
    const articles = [...this.props.form.getFieldsValue().channelArticles];
    articles[index].color_type = color_type;
    articles[index].bottom_color = bottom_color;
    this.props.form.setFieldsValue({ channelArticles: articles });
    this.editColorClose();
  };

  picWithColorCheck = (rule: any, value: [] = [], callback: Function) => {
    const errorArticle = value.some((item: any) => !item.picUrl || !item.bottom_color);
    if (errorArticle) {
      callback('请设置关联稿件所有的列表图与底部色调');
      return;
    }
    callback();
  };

  componentDidUpdate(prevProps: Readonly<any>, prevState: Readonly<any>, snapshot?: any): void {
    const {
      visible,
      form: { resetFields },
    } = this.props;
    if (!prevProps.visible && visible) {
      this.setState({ loading: true, detail: {} });
      resetFields();
      api
        .contentRecommendFocusDetail({ type: 36, channel_id: searchToObject().channel_id })
        .then((res) => {
          const data: any = res.data;
          const { recommend, article_list } = data;
          const extensions = JSON.parse(recommend.ref_extensions);
          this.setState({
            loading: false,
            detail: {
              status: recommend.status === 1,
              channelArticles: article_list.map((v: any) => {
                const data: any = {
                  channel_name: v.channel_name,
                  list_title: v.list_title,
                  id: v.id,
                };
                const article = extensions.find((item: any) => item.article_id == v.id);
                if (article) {
                  data.picUrl = article.list_pic;
                  data.bottom_color = article.bottom_color;
                  data.color_type = article.color_type;
                  data.focus_title = article.list_title;
                  if (article.color_type === 1) {
                    data.auto_color = article.bottom_color;
                  }
                }
                return data;
              }),
              nav_type: recommend.nav_type ? moment(recommend.nav_type) : undefined,
              visible: recommend.visible,
              visible_org: recommend.visible_org,
              position: recommend.position || 0,
              origin_user: recommend.origin_user || ''
            },
          });
        })
        .catch(() => {
          this.setState({ loading: false });
        });
    }
  }

  render() {
    const {
      visible,
      onClose,
      form: { getFieldDecorator },
    } = this.props;
    const detail: any = this.state.detail;
    const { showColorModal, colorData, colorDataIndex } = this.state;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const columns: any = [
      {
        title: '潮新闻ID',
        dataIndex: 'id',
        width: 80,
      },
      {
        title: '新闻频道',
        key: 'type',
        dataIndex: 'channel_name',
        width: 90,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
      },
      {
        title: '焦点图标题',
        key: 'focus_title',
        dataIndex: 'focus_title',
        width: 200,
        render: (text: string, record: any, index: number) => (
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span style={{ marginRight: 4 }}>{text || record.list_title}</span>
            <A style={{ whiteSpace: 'nowrap' }} onClick={() => this.editFocusTitle(record, index)}>
              修改
            </A>
          </div>
        ),
      },
      {
        title: '列表图',
        dataIndex: 'picUrl',
        width: 80,
        render: (text: string, _: any, index: number) => (
          <>
            <A onClick={() => this.editPic(text, index)}>{text ? '修改' : '上传'}</A>
          </>
        ),
      },
      {
        title: '底部色调',
        dataIndex: 'bottom_color',
        width: 80,
        render: (text: string, record: any, index: number) => {
          return (
            <A disabled={!record.picUrl} onClick={() => this.editColor(text, record, index)}>
              {text ? '修改' : '选择'}
            </A>
          );
        },
      },
    ];
    return (
      <Drawer
        skey="YaYunBannerDrawer"
        title="赛事焦点图管理"
        width={1200}
        visible={visible}
        onClose={onClose}
        onOk={this.onOk}
        okPerm={`content_recommend:${searchToObject().channel_id}:focus_save:36`}
      >
        <Form {...formLayout}>
          <h2>赛事焦点图</h2>
          <Form.Item label="显示位置">
            赛事焦点图稿件显示在频道顶部，详见示意图。
            <Tooltip title={<img src="/assets/yayun_banner_tip.png" width={150} height={214} />}>
              <Icon type="question-circle" />
            </Tooltip>
          </Form.Item>
          <Form.Item label="显示开关">
            {getFieldDecorator('status', {
              initialValue: detail.status,
              valuePropName: 'checked',
            })(<Switch />)}
          </Form.Item>
          <Form.Item label="关联稿件">
            {getFieldDecorator('channelArticles', {
              initialValue: detail.channelArticles,
              preserve: true,
              rules: [
                {
                  required: true,
                  message: '请关联新闻',
                  type: 'array',
                },
                {
                  max: 5,
                  message: '最多关联5条新闻',
                  type: 'array',
                },
                {
                  min: 3,
                  message: '为保证客户端显示效果，关联新闻数不能少于3条！',
                  type: 'array',
                },
                {
                  validator: this.picWithColorCheck,
                },
              ],
            })(
              <SearchAndInput
                max={5}
                func="listArticleRecommendSearch"
                columns={columns}
                placeholder="输入ID或标题关联稿件"
                body={{ doc_types: '2,3,4,5,9' }}
                order={true}
                addOnTop={true}
              />
            )}
          </Form.Item>

          <Form.Item
            label={
              <span style={{ position: 'relative' }}>
                <Tooltip title="部分特殊频道（如奥运）需配置该字段">
                  <Icon
                    type="question-circle"
                    style={{ position: 'absolute', left: -30, top: 0 }}
                  />
                </Tooltip>
                html高度
              </span>
            }
          >
            {getFieldDecorator('position', {
              initialValue: detail.position || undefined,
            })(
              <InputNumber
                style={{ width: '100%' }}
                placeholder="请输入纯数字，如375，单位为dp"
                min={0}
                precision={0}
                // max={10000}
              />
            )}
          </Form.Item>

          <Form.Item
            label={
              <span style={{ position: 'relative' }}>
                <Tooltip title="部分特殊频道（如奥运）需配置该字段">
                  <Icon
                    type="question-circle"
                    style={{ position: 'absolute', left: -30, top: 0 }}
                  />
                </Tooltip>
                html配置
              </span>
            }
          >
            {getFieldDecorator('origin_user', {
              initialValue: detail.origin_user || '',
            })(<Input.TextArea placeholder="请输入配置信息" rows={8} />)}
          </Form.Item>
          <h2>赛事倒计时</h2>
          <Form.Item label="显示开关">
            {getFieldDecorator('visible', {
              initialValue: detail.visible,
              valuePropName: 'checked',
            })(<Switch />)}
          </Form.Item>
          <Form.Item label="赛事开始日期">
            {getFieldDecorator('nav_type', {
              initialValue: detail.nav_type,
              rules: [
                {
                  required: true,
                  message: '请选择赛事开始日期',
                },
              ],
            })(<DatePicker showTime placeholder="请输入年-月-日 时-分-秒" format="YYYY-MM-DD HH:mm:ss" />)}
          </Form.Item>
          <Form.Item label="展示0天">
            {getFieldDecorator('visible_org', {
              initialValue: detail.visible_org || false,
              rules: [
                {
                  required: true,
                  message: '请选择',
                },
              ],
            })(
              <Radio.Group>
                <Radio value={true}>是</Radio>
                <Radio value={false}>否</Radio>
              </Radio.Group>
            )}
          </Form.Item>
        </Form>
        <ColorSelectModal
          preview={preview}
          visible={showColorModal}
          colorData={colorData}
          colorDataIndex={colorDataIndex}
          onEnd={this.editColorEnd}
          onCancel={this.editColorClose}
        />
      </Drawer>
    );
  }
}
