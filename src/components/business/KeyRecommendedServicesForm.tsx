import { serviceApi as api } from '@app/api';
import { ImageUploader } from '@components/common';
import { Divider, Form, Input, message, Radio, Select, Switch, TreeSelect } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';

type Api = 'updateAppService' | 'createAppService';

@connectSession
@(Form.create({ name: 'appServiceForm' }) as any)
class AppServiceForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const form = {
      id: '',
      area_id: '',
      recommend_title: '',
      ref_ids: '',
    };
    this.state = {
      ...form,
      ...props.formContent,
      status: 0,
      allRecomemndList: [],
    };
  }

  componentDidMount() {
    this.getAllRecomemnd();
    this.getVipDetail();
  }

  getVipDetail = () => {
    api.getServiceRecommendVipDetail({ area_id: this.state.area_id }).then((res: any) => {
      const data = res.data.recommend_service;
      this.setState({
        status: data.status,
        ref_ids: data.id,
        recommend_title: data.recommend_title,
      });
    });
  };

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        let body: any = {};
        if (this.state.status) {
          body = {
            status: values.status,
            area_id: this.state.area_id,
            recommend_title: values.recommend_title,
            ref_ids: values.ref_ids,
          };
        } else {
          body = {
            status: values.status,
            area_id: this.state.area_id,
          };
        }
        setMLoading(this, true);
        api
          .vipSaveServiceRecommend(body)
          .then(() => {
            message.success('操作成功');
            setMLoading(this, false);
            this.props.onEnd();
          })
          .catch(() => setMLoading(this, false));
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  switchType = (e: any) => {
    this.setState({
      status: e.target.value,
    });
  };

  getAllRecomemnd = () => {
    api.getListServiceRecommend({ area_id: this.state.area_id }).then((res: any) => {
      this.setState({
        allRecomemndList: res.data.recommend_list,
      });
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form onSubmit={this.handleSubmit} {...formLayout}>
        <Form.Item label="启用配置">
          {getFieldDecorator('status', {
            initialValue: this.state.status,
          })(
            <Radio.Group onChange={this.switchType}>
              <Radio value={0}>否</Radio>
              <Radio value={1}>是</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {this.state.status === 1 && (
          <Form.Item label="推荐服务">
            {getFieldDecorator('ref_ids', {
              initialValue: this.state.ref_ids,
              rules: [
                {
                  required: true,
                  message: '请选服务',
                },
              ],
            })(
              <Select placeholder="请选服务">
                {this.state.allRecomemndList.map((v: any) => (
                  <Select.Option value={v.id} key={v.id}>
                    {v.recommend_title}
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>
        )}
        {this.state.status === 1 && (
          <Form.Item label="推荐文案">
            {getFieldDecorator('recommend_title', {
              initialValue: this.state.recommend_title,
              rules: [
                {
                  required: true,
                  message: '请填写推荐标题',
                },
                {
                  max: 3,
                  message: '推荐标题不能超过3个汉字',
                },
                {
                  pattern: /^[a-zA-Z0-9\u4e00-\u9fa5\n]+$/,
                  message: '推荐标题不能包含特殊字符',
                },
              ],
            })(<Input placeholder="请填写推荐文案" />)}
          </Form.Item>
        )}
      </Form>
    );
  }
}

export default AppServiceForm;
