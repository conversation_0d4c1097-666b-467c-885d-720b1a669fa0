import ChooseCircleInfoModal from '@app/views/community/component/ChooseCircleInfoModal';
import React, { useState } from 'react';

export default function CircleInput(props: any) {
  const [chooseCircleState, setChooseCircleState] = useState<any>({
    visible: false,
    key: null,
  });

  const triggerChange = (value: any) => {
    props.onChange?.(value);
  };

  const chooseCircle = () => {
    setChooseCircleState({
      visible: true,
      key: Date.now(),
      record: props.value,
    });
  };

  const value = props.value || {};
  const circleName = value.circle_name || '';
  const boardName = value.board_name || '';
  const circleId = value.circle_id || '';
  const boardId = value.board_id || '';

  let showValue = props.value ? `${circleName || '无圈子'} - ${boardName || '无版块'}` : '';

  return (
    <>
      <span style={{ marginRight: 10 }}>{showValue}</span>
      <a onClick={() => chooseCircle()}>{props.value ? '修改' : '选择圈子'}</a>
      <ChooseCircleInfoModal
        {...chooseCircleState}
        onEnd={(res: any) => {
          triggerChange(res);
          setChooseCircleState({
            visible: false,
            key: null,
          });
        }}
        onCancel={() => {
          setChooseCircleState({
            visible: false,
            key: null,
          });
        }}
      ></ChooseCircleInfoModal>
    </>
  );
}
