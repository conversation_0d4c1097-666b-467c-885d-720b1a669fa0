import React, {forwardRef, useRef, useState, useEffect} from 'react';
import {
    Col,
    Form, Icon,
    message, Select, Tooltip
} from 'antd';

import ImageUploader from '@components/common/imageUploader';
import { opApi} from "@app/api";

interface FormRef {
    validateFields: (param: (...arg: any) => void) => void;
    getFieldsValue?: () => any;
    setFieldsValue?: () => void;
}

interface OptionData {
    id: number;
    name: string;
}

const HomeRemindForm = forwardRef<FormRef, {
    form: any,
    getData: () => void,
    onEnd: () => void,
    formContent: any,
    type: string,
    drawer: {
        visible: boolean,
        key: string,
        type: string,
    }
}>(({form, getData, onEnd, formContent, drawer,type}, ref) => {
    const [options, setOptions] = useState<OptionData[]>([]);
    const [loading, setLoading] = useState(false);
    useEffect(() => {
        if (formContent){
            setOptions([
                {
                    id: formContent.theme_id,
                    name: formContent.theme_name
                }
            ])
        }

    },[formContent])
    const handleSearch = async (val: string) => {
        if (!val) return;
        setLoading(true);
        let data = {
            keyword: val
        }
        try {
            opApi.getThemeSimpleList({status:1}).then((res: any) => {
                let records: any = [...res.data.list];
                setOptions(records);
            });
        } catch (error) {
            console.error('Error fetching data:', error);
        }
        setLoading(false);
    };

    const doSubmit = () => {
        form.validateFieldsAndScroll((err: any, values: any) => {
            if (!err) {
                let func
                if (drawer.type === 'edit') {
                    func = 'editThemeRemind';
                    values.id = formContent.id
                } else {
                    func = 'addThemeRemind'
                }
                opApi[func as keyof typeof opApi](values).then(() => {
                    message.success('操作成功');
                    getData()
                    onEnd()
                })
            }
        })
    }
    React.useImperativeHandle(ref, () => {
        return ({
            doSubmit
        });
    });
    const {getFieldDecorator, getFieldsValue, setFieldsValue, getFieldValue} = form;
    const formRef = useRef<any>(null);
    return (
        <>
            <Form ref={formRef} labelCol={{span: 4}} wrapperCol={{span: 18}}>
                <div style={{height:50}}></div>
                <Form.Item label={'主题名称'}>
                    {getFieldDecorator('theme_id', {
                        initialValue: formContent?.theme_id,
                        rules: [
                            {
                                required: true,
                                message: '请填写主题名称',
                            },
                        ],
                    })(
                        <Select
                            showSearch
                            optionFilterProp="children"
                            loading={loading}
                            placeholder="请输入要提醒的主题名称或主题ID"
                            onSearch={handleSearch}
                            disabled={drawer.type == 'edit'}
                            ref={ref} // 将 ref 属性传递给 Select 组件
                            style={{width:'80%'}}
                        >
                            {options.map((item) => (
                                <Option key={item.id} value={item.id}>
                                    {item.name}（ID：{item.id}）
                                </Option>
                            ))}
                        </Select>
                    )}
                    &emsp;
                    <Tooltip title={'只支持针对已上架的用户自选主题配置提醒'}>
                        <Icon type="question-circle" />
                    </Tooltip>
                </Form.Item>
                <Form.Item
                    label={<span>
                          <Tooltip placement="right" title={<img src='/assets/top_img_url.jpg' width={210}/>}>
                                <Icon type="question-circle" />
                            </Tooltip>&nbsp;
                        提醒上图</span>}
                    extra="支持jpg,jpeg,png图片格式，比例为 1:1">
                    {getFieldDecorator('top_img_url', {
                        initialValue: formContent?.top_img_url,
                        rules: [
                            {
                                required: true,
                                message: '请上传提醒上图',
                            },
                        ],
                    })(<ImageUploader
                        ratio={1 / 1}
                        accept={['image/jpeg', 'image/png', 'image/jpg']}>
                        </ImageUploader>
                    )}
                </Form.Item>
                <Form.Item label={<span>
                    <Tooltip placement="right" title={<img src='/assets/bottom_img_ur.jpg' width={210}/>}>
                                <Icon type="question-circle" />
                            </Tooltip>&nbsp;
                    提醒下图
                </span>} extra="支持jpg,jpeg,png图片格式，比例为 1:1">
                    {getFieldDecorator('bottom_img_url', {
                        initialValue: formContent?.bottom_img_url,
                        rules: [
                            {
                                required: true,
                                message: '请上传提醒下图',
                            },
                        ],
                    })(<ImageUploader
                        style={{width:100}}
                        ratio={1 / 1}
                        accept={['image/jpeg', 'image/png', 'image/jpg']}/>)}
                </Form.Item>
            </Form>
        </>
    );
});

export default Form.create({})(HomeRemindForm);
