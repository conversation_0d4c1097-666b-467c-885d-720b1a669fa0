import React from "react";
import AddForm from '@components/business/addForm';
import { releaseListApi } from "@app/api";
import { searchToObject } from "@app/utils/utils";
import moment from "moment";

export default class CompetitionRecommend extends React.Component<any> {

  state = {
    bannerData: [{
      date: undefined,
      times: [],
      name: '',
      jump_url: ''
    }, {
      date: undefined,
      times: [],
      name: '',
      jump_url: ''
    }, {
      date: undefined,
      times: [],
      name: '',
      jump_url: ''
    }],
    bannerObj: { title: '', position: '' },
    id: undefined
  }

  constructor(props: any) {
    super(props)
    this.props.wrappedComponentRef.current = this
  }

  setFormItem = () => {
    return {
      date: undefined,
      times: [],
      name: '',
      jump_url: ''
    }
  }

  getUpdateDate = (values: any) => {
    const { title, title_style, title_pic, position } = values
    const title_show = title_style >= 0
    const data: any = {
      title,
      title_show,
      title_pic: title_style >= 1 ? title_pic : '',
      article_category: 0,
      ref_type: 31,
      channel_id: searchToObject().channel_id,
      ref_extensions: JSON.stringify(values.json.map((item: any) => {
        const begin_date_str = item.date.format('YYYY-MM-DD')
        return {
          begin_date_str,
          begin_datetime: new Date(begin_date_str + ' ' + item.times[0]).getTime(),
          end_datetime: new Date(begin_date_str + ' ' + item.times[1]).getTime(),
          name: item.name,
          jump_url: item.jump_url || ''
        }
      })),
      id: this.state.id
    }
    if (position) {
      data.position = position
    }
    if (title_show) {
      data.title_style = title_style
    }
    if (!data.id) {
      delete data.id
    }
    return data
  }


  setEditData = (data: any) => {
    if (data) {
      const { recommend: { id, title, show_title, title_style = 0, title_pic = '', position }, match_list } = data
      // console.log('aaa', match_list)
      this.setState({
        id,
        bannerData: match_list.map((item: any) => {
          return {
            date: moment(item.begin_date_str),
            times: [moment(item.begin_datetime).format('HH:mm'), moment(item.end_datetime).format('HH:mm')],
            name: item.name,
            jump_url: item.jump_url
          }
        }),
        bannerObj: { title, title_style: show_title ? title_style : -1, title_pic, position }
      }, () => {
        this.props.wrappedComponentRef.current.init()
      })
    } else {
      this.setState({
        editData: null
      })
    }
  }

  render() {
    const { isEdit, maxPosition } = this.props
    return !isEdit || this.state.id ? <AddForm
      isEdit={isEdit}
      maxPosition={maxPosition}
      setEditData={this.setEditData}
      bannerData={this.state.bannerData} //复现数据
      bannerObj={this.state.bannerObj} //复现数据
      changeVisible={this.props.onEnd}
      showLoading={this.props.showLoading}
      wrappedComponentRef={(instance: any) => (this.props.wrappedComponentRef.current = instance)}
      getUpdateDate={this.getUpdateDate} //获取要提交的数据
      setFormItem={this.setFormItem()} //设置表单item
      joggle={isEdit ? releaseListApi.updateContentRecommend : releaseListApi.createContentRecommend} // 接口
      componentNames={'赛事日程推荐位'} //判断使用的组件
      addLabel={'添加一个日程'}
      headName={'赛事日程'}
      listLength={30}
      disabled={3}
    /> : null
  }
}
