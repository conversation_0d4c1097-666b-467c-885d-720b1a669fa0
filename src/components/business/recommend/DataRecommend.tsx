import React from "react"
import Recommend<PERSON><PERSON><PERSON>, { RecommendFormLayout } from "./RecommendCommon"
import { Checkbox, Form, Input, InputNumber, Switch } from "antd"
import Radio from "antd/es/radio"
import ArLinkInput, { arLinkValidator, isArLink } from '../../common/arLinkInput';

// 数据推荐位
@(Form.create({ name: 'DataRecommend' }) as any)
export default class DataRecommend extends React.Component<any> {
  state = {
    editData: null
  }

  specialValidateFields = () => {
    const { form: { getFieldsValue } } = this.props
    const { jump_enabled, linkType } = getFieldsValue();
    const fields = ['title', 'title_style', 'title_pic', 'url', 'position', 'width', 'height', 'jump_enabled', 'linkType']
    if (jump_enabled) {
      fields.push(linkType === 1 ? 'arUrl' : 'jump_url')
    }
    return fields
  }

  saveDataTransfer = (values: any) => {
    const { title, title_style, title_pic, position, jump_enabled, linkType, arUrl, jump_url } = values
    const title_show = title_style >= 0
    const data = {
      ...values,
      article_category: 0,
      title,
      title_show,
      title_pic: title_style >= 1 ? title_pic : '',
      ref_type: 25,
    }
    if (jump_enabled) {
      data.jump_model_url = linkType === 1 ? arUrl : jump_url
    }
    if (!position) {
      delete data.position;
    }
    if (title_show) {
      data.title_style = title_style
    }
    delete data.linkType
    delete data.jump_url
    delete data.arUrl
    return data
  }

  setEditData = (data: any) => {
    if (data) {
      const { recommend: { title, show_title, title_style = 0, title_pic = '', position, url, width, height, cycle_carousel, nav_type, jump_new_page, jump_url } } = data
      const isAr = isArLink(jump_url)
      const urlAttr = isAr ? 'arUrl' : 'jump_url'
      this.setState({
        editData: {
          title,
          title_style: show_title ? title_style : -1,
          title_pic,
          position,
          nav_type,
          round_carousel: cycle_carousel,
          url,
          width: parseFloat(width),
          height: parseFloat(height),
          jump_enabled: jump_new_page,
          linkType: isAr ? 1 : 0,
          [urlAttr]: jump_url
        }
      })
    } else {
      this.setState({
        editData: null
      })
    }
  }

  render() {
    const { form: { getFieldDecorator, getFieldsValue }, isEdit } = this.props
    const editData: any = this.state.editData || (isEdit ? {} : { jump_enabled: false, title_show: false, linkType: 0 })
    const values = getFieldsValue();
    const { jump_enabled = editData.jump_enabled, linkType = editData.linkType } = values
    return (
      <Form {...RecommendFormLayout}>
        <RecommendCommon {...this.props} editData={editData}>
          {/* <Form.Item style={{ marginLeft: 133, marginTop: -24, height: 30 }}>
            {getFieldDecorator('title_show', {
              initialValue: editData.title_show,
              valuePropName: 'checked',
            })(<Checkbox>标题在客户端显示</Checkbox>)}
          </Form.Item> */}
          <Form.Item label="URL地址">
            {getFieldDecorator('url', {
              initialValue: editData.url,
              rules: [
                {
                  required: true,
                  message: '请填写URL地址',
                },
                {
                  pattern: /^https?:\/\//,
                  message: '请填写正确的URL地址',
                },
              ],
            })(<Input placeholder="请输入URL地址" />)}
          </Form.Item>
        </RecommendCommon>
        <Form.Item label="宽度">
          {getFieldDecorator('width', {
            initialValue: editData.width,
            rules: [
              {
                required: true,
                message: '请填写位置',
              },
              {
                min: 1,
                message: '不能小于1',
                type: 'number',
              },
            ],
          })(<InputNumber min={1} placeholder="请输入具体像素数值" style={{ width: '100%' }} />)}
        </Form.Item>
        <Form.Item label="高度">
          {getFieldDecorator('height', {
            initialValue: editData.height,
            rules: [
              {
                required: true,
                message: '请填写位置',
              },
              {
                min: 1,
                message: '不能小于1',
                type: 'number',
              },
            ],
          })(<InputNumber min={1} placeholder="请输入具体像素数值" style={{ width: '100%' }} />)}
        </Form.Item>
        <Form.Item label="跳转新页面">
          {getFieldDecorator('jump_enabled', {
            initialValue: editData.jump_enabled,
            valuePropName: 'checked',
          })(<Switch />)}
        </Form.Item>
        {jump_enabled && (
          <>
            <Form.Item label="跳转类型">
              {getFieldDecorator('linkType', {
                initialValue: editData.linkType,
              })(
                <Radio.Group>
                  <Radio value={0}>链接</Radio>
                  <Radio value={1}>AR</Radio>
                </Radio.Group>
              )}

            </Form.Item>
            {linkType === 0 && (
              <Form.Item label="跳转地址">
                {getFieldDecorator('jump_url', {
                  initialValue: editData.jump_url,
                  preserve: true,
                  rules: [
                    {
                      required: true,
                      message: '请填写跳转地址',
                    },
                    {
                      pattern: /^https?:\/\//,
                      message: '请正确填写跳转地址',
                    },
                  ],
                })(<Input placeholder="请输入跳转地址" />)}
              </Form.Item>
            )}
            {linkType === 1 && (
              <Form.Item label="AR信息" required>
                {getFieldDecorator('arUrl', {
                  initialValue: editData.arUrl,
                  preserve: true,
                  rules: [
                    {
                      validator: arLinkValidator,
                    },
                  ],
                })(<ArLinkInput />)}
              </Form.Item>
            )}
          </>
        )}
      </Form>
    )
  }
}
