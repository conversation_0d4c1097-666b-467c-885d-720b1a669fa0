import React from "react";
import AddForm from '@components/business/addForm';
import { releaseListApi } from "@app/api";
import { searchToObject } from "@app/utils/utils";

function formItemTemplate() {
  return {
    group_name: '',
    group_url: '',
    selected_pic_url: '',
    unselected_pic_url: '',
    articles: [],
  }
}

export default class GenerateRecommend extends React.Component<any> {

  state = {
    bannerData: Array(3).fill(formItemTemplate()),
    bannerObj: { title: '', position: '', round_carousel: true },
    id: undefined
  }

  constructor(props: any) {
    super(props)
    this.props.wrappedComponentRef.current = this
  }

  getUpdateDate = (values: any) => {
    const { title, position, round_carousel } = values

    const data: any = {
      title,
      // article_category: 0,
      ref_type: 35,
      channel_id: searchToObject().channel_id,
      round_carousel,
      ref_extensions: JSON.stringify(values.json),
      id: this.state.id
    }
    if (position) {
      data.position = position
    }
    if (!data.id) {
      delete data.id
    }
    return data
  }


  setEditData = (data: any) => {
    if (data) {
      const { recommend: { id, title, position, cycle_carousel }, group_list } = data
      this.setState({
        id,
        bannerData: group_list,
        bannerObj: {
          title,
          position,
          round_carousel: cycle_carousel
        }
      }, () => {
        this.props.wrappedComponentRef.current.init()
      })
    } else {
      this.setState({
        editData: null
      })
    }
  }

  render() {
    const { isEdit, maxPosition } = this.props
    return !isEdit || this.state.id ? <AddForm
      isEdit={isEdit}
      maxPosition={maxPosition}
      setEditData={this.setEditData}
      bannerData={this.state.bannerData} //复现数据
      bannerObj={this.state.bannerObj} //复现数据
      changeVisible={this.props.onEnd}
      showLoading={this.props.showLoading}
      wrappedComponentRef={(instance: any) => (this.props.wrappedComponentRef.current = instance)}
      getUpdateDate={this.getUpdateDate} //获取要提交的数据
      setFormItem={formItemTemplate()} //设置表单item
      joggle={isEdit ? releaseListApi.updateContentRecommend : releaseListApi.createContentRecommend} // 接口
      componentNames='聚合推荐位' //判断使用的组件
      addLabel='添加一条内容'
      headName='聚合'
      listLengthTip='最多添加6条内容'
      listLength={6}
      disabled={3}
    /> : null
  }
}
