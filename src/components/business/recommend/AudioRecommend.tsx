import React from 'react';
import { Form, Icon, Input, Tooltip, Switch, Select, Modal, message } from 'antd';
import RecommendCommon, { RecommendFormLayout } from './RecommendCommon';
import Radio from 'antd/es/radio';
import SearchAndInput from '@components/common/newNewsSearchAndInput';
import { communityApi as api, listApi, searchApi } from '@app/api';
import _ from 'lodash';
import { A, ImageUploader } from '@app/components/common';
import { bizTypeMap, searchToObject } from '@app/utils/utils';
import moment from 'moment';

// 用户内容汇总推荐位
@(Form.create({ name: 'AudioRecommend' }) as any)
export default class AudioRecommend extends React.Component<any> {
  tableRef = React.createRef<any>();
  tableRef2 = React.createRef<any>();
  state: any = {
    editData: {
      round_carousel: true,
      column_list: [
        {
          id: 'column_tingc',
          name: '大家都在听',
        },
        // {
        //   id: 'column_zwb',
        //   name: '早晚报',
        // },
      ],
      column_list2: [],
      channelArticles: [],
    },
  };

  specialValidateFields = () => {
    const fields = [
      'title',
      'title_style',
      'title_pic',
      'position',
      'style',
      'round_carousel',
      'ref_ids',
      'column_list',
      'column_list2',
      'channelArticles',
    ];
    return fields;
  };

  customValidator = (values: any) => {
    const { style, column_list = [], column_list2 = [], ref_ids_type } = values
    
    // 判断column_list column_list2 有没有重复的
    const columnList = [...column_list, ...column_list2]
    const columnListSet = new Set(columnList.map((item: any) => item.id))
    if (columnListSet.size !== columnList.length) {
      message.error('区域一和区域二不允许有相同栏目')
      return false
    }
    return true
  }

  saveDataTransfer = (values: any) => {
    const {
      title,
      title_style,
      title_pic,
      position,
      column_list,
      column_list2,
      round_carousel,
      channelArticles,
    } = values;
    const title_show = title_style >= 0;
    const data: any = {
      // article_category: this.props.article_category,
      ref_type: 39,
      title,
      title_show,
      title_pic: title_style >= 1 ? title_pic : '',
      // style,
      round_carousel,
    };
    if (position) {
      data.position = position;
    }
    if (title_show) {
      data.title_style = title_style;
    }
    // data.ref_ids = report_list.map((item: any) => item.id).join(',')
    data.ref_ids = column_list?.map((item: any) => item.id).join(',');
    data.ref_ids2 = column_list2?.map((item: any) => item.id).join(',');
    data.ref_ids3 = channelArticles?.map((item: any) => item.id).join(',');
    const list = [];
    list.push(
      ...(column_list?.map((item: any) => {
        return {
          id: item.id,
          list_pic: item.list_pic,
        };
      }) ?? [])
    );
    list.push(
      ...(column_list2?.map((item: any) => {
        return {
          id: item.id,
          list_pic: item.list_pic,
        };
      }) ?? [])
    );
    data.ref_extensions = JSON.stringify(list);
    data.origin_user = JSON.stringify(
      channelArticles?.map((item: any) => {
        return {
          article_id: item.id,
          list_title: item.custom_title || item.list_title,
        };
      })
    );

    return data;
  };

  setEditData = (data: any) => {
    if (data) {
      const {
        column_list = [],
        column_two_list = [],
        article_list = [],
        recommend: {
          article_id,
          title,
          show_title,
          title_style = 0,
          title_pic = '',
          position,
          style,
          jump_new_page,
          jump_url,
          cycle_carousel = true,
          nav_type,
          ref_extensions,
          origin_user,
        },
      } = data;
      const extensions = ref_extensions ? JSON.parse(ref_extensions) : [];
      const originUsers = origin_user ? JSON.parse(origin_user) : [];
      this.setState(
        {
          article_id,
          hasJump: jump_new_page,
          editData: {
            title,
            title_style: show_title ? title_style : -1,
            title_pic,
            position,
            // style,
            // nav_type,
            // jump_model_url: jump_url,
            // circle_info: ref_ids_type === 0 ? undefined : circle.name,
            // circle,
            round_carousel: cycle_carousel,
            column_list: column_list.map((item: any) => {
              return {
                ...item,
                list_pic: extensions.find((v: any) => v.id === item.id)?.list_pic,
              };
            }),
            column_list2: column_two_list.map((item: any) => {
              return {
                ...item,
                list_pic: extensions.find((v: any) => v.id === item.id)?.list_pic,
              };
            }),
            channelArticles: article_list.map((item: any) => {
              return {
                ...item,
                custom_title: originUsers.find((v: any) => v.article_id === item.id)?.list_title,
              };
            }),
          },
        },
        () => {}
      );
    } else {
      this.setState({
        editData: null,
      });
    }
  };

  editPic = (url: string, index: number, type: number) => {
    let pic: string = url;
    let modal: any;
    const picChange = (u: string) => {
      pic = u;
      modal.update({
        content: (
          <>
            <ImageUploader value={pic} onChange={picChange} ratio={11 / 5} />
            <p style={{ marginTop: 5, color: '#aaa' }}>比例为11:5</p>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '编辑图片',
      content: (
        <>
          <ImageUploader value={pic} onChange={picChange} ratio={11 / 5} />
          <p style={{ marginTop: 5, color: '#aaa' }}>比例为11:5</p>
        </>
      ),
      onOk: (destroy: Function) => {
        if (!pic) {
          message.error('请上传图片');
          return;
        }
        const key = type === 0 ? 'column_list' : 'column_list2';

        const articles = [...this.props.form.getFieldsValue()[key]];
        articles[index].list_pic = pic;
        this.props.form.setFieldsValue({ [key]: articles });
        destroy();
      },
    });
  };

  onAddItem = (item: any) => {
    /*
      视频，取视频封面图
      短图文，取第一张图
      长图文，取封面图
    */
    // item.picUrl = item.list_pics
  };

  editTitle = (value: string, index: number) => {
    let title: string = value || '';
    let modal: any;
    const titleChange = (e: any) => {
      title = e.target.value;
      modal.update({
        content: (
          <>
            <Input.TextArea
              placeholder="最多输入40字"
              value={title}
              maxLength={40}
              onPressEnter={(e) => e.preventDefault()}
              onChange={titleChange}
            ></Input.TextArea>
            <div style={{ textAlign: 'right' }}>{title?.length}/40</div>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '自定义标题',
      content: (
        <>
          <Input.TextArea
            placeholder="最多输入40字"
            value={title}
            maxLength={40}
            onPressEnter={(e) => e.preventDefault()}
            onChange={titleChange}
          ></Input.TextArea>
          <div style={{ textAlign: 'right' }}>{title?.length}/40</div>
        </>
      ),
      onOk: (destroy: Function) => {
        if (!title?.trim()) {
          message.error('请输入自定义标题');
          return;
        }
        const articles = [...this.props.form.getFieldsValue().channelArticles];
        articles[index].custom_title = title?.trim();
        this.props.form.setFieldsValue({ channelArticles: articles });
        destroy();
      },
    });
  };

  render() {
    const {
      form: { getFieldDecorator, getFieldsValue, getFieldValue },
      isEdit,
    } = this.props;
    const getColumns = (type: number) => [
      {
        title: '名称',
        dataIndex: 'name',
        // width: 120,
      },
      {
        title: '创建时间',
        key: 'created_at',
        dataIndex: 'created_at',
        width: 180,
        render: (text: any, record: any) => {
          return !['column_tingc', 'column_zwb'].includes(record.id) ? (
            <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>
          ) : (
            <></>
          );
        },
      },
      {
        title: '背景图',
        dataIndex: 'list_pic',
        width: 114,
        align: 'center' as any,
        render: (text: string, record: any, index: number) => (
          <div style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
            {text && <img src={text} style={{ height: 40, maxWidth: 70 }} />}
            <A onClick={() => this.editPic(text, index, type)}>{text ? '修改' : '上传'}</A>
          </div>
        ),
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) =>
          !['column_tingc', 'column_zwb'].includes(record.id) ? (
            <A
              disabled={this.props.disabled}
              onClick={() => {
                if (type === 1) {
                  this.tableRef2.current?.handleDelete(record);
                } else {
                  this.tableRef.current?.handleDelete(record);
                }
              }}
            >
              删除
            </A>
          ) : (
            <></>
          ),
        width: 70,
      },
    ];

    const articleColumns = [
      {
        title: '潮新闻ID',
        dataIndex: 'id',
        width: 80,
      },
      {
        title: '新闻频道',
        key: 'type',
        dataIndex: 'channel_name',
        width: 90,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
      },
      {
        title: '自定义标题',
        key: 'custom_title',
        dataIndex: 'custom_title',
        render: (text: string, record: any, index: number) => {
          return (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <div>{record.custom_title || record.list_title}</div>
              <a
                style={{ flex: 'none', marginLeft: '5px' }}
                onClick={() => this.editTitle(record.custom_title || record.list_title, index)}
              >
                修改
              </a>
            </div>
          );
        },
      },
    ];

    const editData: any = this.state.editData;

    return (
      <Form {...RecommendFormLayout}>
        <RecommendCommon {...this.props} editData={editData} />

        <Form.Item label="循环轮播">
          {getFieldDecorator('round_carousel', {
            rules: [
              {
                required: true,
                message: '请设置是否轮播',
              },
            ],
            initialValue: editData.round_carousel,
            valuePropName: 'checked',
          })(<Switch />)}
        </Form.Item>

        <h3 style={{ marginLeft: 40 }}>区域一</h3>

        <Form.Item label="关联栏目">
          {getFieldDecorator('column_list', {
            initialValue: editData.column_list,
            preserve: true,
            rules: [
              {
                required: true,
                message: '请关联内容',
                type: 'array',
              },
              {
                validator: (rule: any, val: any, callback: any) => {
                  if (!val) {
                    return callback('');
                  } else if (val.filter((v: any) => !v.list_pic).length > 0) {
                    return callback('请上传背景图');
                  } else {
                    return callback();
                  }
                },
              },
              // {
              //   max: 20,
              //   message: '最多关联20条内容',
              //   type: 'array',
              // },
            ],
          })(
            <SearchAndInput
              wrappedComponentRef={this.tableRef}
              max={30}
              func="searchUGCTopic"
              columns={getColumns(0)}
              placeholder="输入名称搜索栏目"
              body={{ type: 0 }}
              order={true}
              addOnTop={false}
              categoryTip="栏目"
              onAddItem={this.onAddItem}
              indexKey={'list'}
              searchKey={'name'}
              hideOp={true}
              apiWithPagination={true}
              selectMap={(record: any) => record.name}
            />
          )}
        </Form.Item>

        <h3 style={{ marginLeft: 40 }}>区域二</h3>

        <Form.Item label="关联栏目">
          {getFieldDecorator('column_list2', {
            initialValue: editData.column_list2,
            rules: [
              // {
              // required: true,
              // message: '请关联内容',
              // type: 'array',
              // },
              {
                validator: (rule: any, val: any, callback: any) => {
                  if (!val) {
                    return callback();
                  } else if (val.filter((v: any) => !v.list_pic).length > 0) {
                    return callback('请上传背景图');
                  } else {
                    return callback();
                  }
                },
              },
              // {
              //   max: 20,
              //   message: '最多关联20条内容',
              //   type: 'array',
              // },
            ],
          })(
            <SearchAndInput
              wrappedComponentRef={this.tableRef2}
              max={30}
              func="searchUGCTopic"
              columns={getColumns(1)}
              placeholder="输入名称搜索栏目"
              body={{ type: 0 }}
              order={true}
              addOnTop={false}
              categoryTip="栏目"
              onAddItem={this.onAddItem}
              indexKey={'list'}
              searchKey={'name'}
              hideOp={true}
              apiWithPagination={true}
              selectMap={(record: any) => record.name}
            />
          )}
        </Form.Item>

        <h3 style={{ marginLeft: 40 }}>区域三</h3>

        <Form.Item label={'关联稿件'}>
          {getFieldDecorator('channelArticles', {
            initialValue: editData.channelArticles,
            rules: [
              // {
              //   required: true,
              //   message: '请关联新闻',
              //   type: 'array',
              // },
              {
                max: 5,
                message: '最多关联5条稿子',
                type: 'array',
              },
            ],
          })(
            <SearchAndInput
              max={5}
              func="listArticleRecommendSearch"
              columns={articleColumns}
              placeholder="输入ID或标题关联稿件"
              body={{ doc_types: '2' }}
              order={true}
              addOnTop={true}
              tips="最多关联5条稿子"
            />
          )}
        </Form.Item>
      </Form>
    );
  }
}
