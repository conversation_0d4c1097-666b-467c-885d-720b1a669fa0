import React from 'react';
import Recommend<PERSON>ommon, { RecommendFormLayout } from './RecommendCommon';
import { Form, Icon, Input, InputNumber, Radio, Switch, Tooltip } from 'antd';
import { ImageUploader } from '@components/common';
import RangeInput from '@app/components/common/RangeInput';

// 读报推荐位
@(Form.create({ name: 'RankingRecommend' }) as any)
export default class RankingRecommend extends React.Component<any> {
  state = {
    editData: null,
  };

  saveDataTransfer = (values: any) => {
    const { title, position, ref_extensions,  pic_url } = values;
    const data: any = {
      title,
      pic_url,
      ref_type: 41,
    };

    if (position) {
      data.position = position;
    }
    return data;
  };

  setEditData = (data: any) => {
    if (data) {
      const {
        recommend: { title, position, ref_extensions, ref_type,  pic_url = '' },
      } = data;
      this.setState({
        editData: {
          title,
          position,
          pic_url,
          isEdit: true,
        },
      });
    } else {
      this.setState({
        editData: null,
      });
    }
  };

  render() {
    const editData: any = this.state.editData || {};
    const {
      ref_extensions: {
        icon_name = '',
        name_image = '',
        top_image = '',
        bottom_image = '',
        morning_slogan = '',
        evening_slogan = '',
        weather_show_switch = 1,
        ref_ids_type = 0,
      } = {},
      isEdit,
    } = editData;
    const {
      form: { getFieldDecorator, getFieldValue, setFieldsValue, getFieldsValue },
    } = this.props;
    return (
      <Form {...RecommendFormLayout}>
        <RecommendCommon
          {...this.props}
          hiddenTitleType={'读报推荐位'}
          editData={editData}
        ></RecommendCommon>
        {/* <Form.Item label="推荐位名称" style={{ display: 'block' }}>
          {getFieldDecorator('title', {
            initialValue: editData.title,
            rules: [
              {
                required: true,
                message: '请输入推荐位名称',
              },
              {
                max: 15,
                message: '推荐位名称最长不能超过15个字',
              },
            ],
          })(<Input placeholder="请输入名称，不超过15个字" />)}
        </Form.Item> */}

        {/* {
          !isEdit && <Form.Item label="显示位置" style={{ display: 'block' }}>
            {getFieldDecorator('position', {
              initialValue: editData.position,
              rules: [
                {
                  required: true,
                  message: '请输入位置序号',
                  type: 'number',
                },
                {
                  max: 500,
                  message: `最大不能超过${500}`,
                  type: 'number',
                },
              ],
            })(<InputNumber style={{ width: 400 }} placeholder={`请输入在列表中显示的位置序号，1~500正整数`} min={1} max={500} />)}
            &emsp;
            <Tooltip title={(<span>
              <p>输入说明：</p>
              <p>
                直接输入阿拉伯数字代表位置序号，例如要在头条频道信息流第8个位置插入该推荐位，则直接输入“8”即可。
              </p>
              <p>最大输入数字为500</p>
              <br />
              <p>注：</p>
              <p>直接在输入的指定位置上插入推荐位显示，参与稿件列表的排序，且优先于稿件展示</p>
            </span>)}>
              <Icon type="question-circle" />
            </Tooltip>
          </Form.Item>
        } */}

        <Form.Item
          label="图片"
          extra={`支持扩展名：.jpg .jpeg .png，比例 4:3`}
        >
          {getFieldDecorator('pic_url', {
            initialValue: editData.pic_url || '',
            rules: [
              {
                required: true,
                message: '请选择图片',
              },
            ],
          })(
            <ImageUploader
              ratio={4/3}
            ></ImageUploader>
          )}
          {/* {!!url && consistentProportions && (
            <span style={{ color: 'red' }}>
              该图片比例非4:3，图片将自动居中截取，建议重新上传。
            </span>
          )} */}
        </Form.Item>

        <div style={{ paddingLeft: 115 }}>
          <p>说明：系统将自动读取「财经快讯」A股的内容显示在推荐位上，若数据为空时，则不展示该推荐位。</p>
        </div>
        {/* <Form.Item
          label="展示样式"
          extra="说明：系统自动读取「浙江热闻榜」、「春风悦读榜」最新的内容展示在App上。"
        >
          <img src="/assets/recommend_ranking_example.png" width={400} />
        </Form.Item> */}
      </Form>
    );
  }
}
