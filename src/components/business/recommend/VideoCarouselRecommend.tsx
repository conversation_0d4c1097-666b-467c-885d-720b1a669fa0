import React from 'react';
import { A, ImageUploader } from '@components/common';
import SearchAndInput from '@components/common/newNewsSearchAndInput';
import { Form, Icon, Input, Modal, Switch, Tooltip, message } from 'antd';
import RecommendCommom, { RecommendFormLayout } from './RecommendCommon';
import Radio from 'antd/es/radio';

// 视频轮播推荐位
@(Form.create({ name: 'VideoCarouselRecommend' }) as any)
export default class VideoCarouselRecommend extends React.Component<any> {
  state = {
    editData: null,
  };

  customValidator = (values: any) => {
    const { style } = values;
    if (
      values.channelArticles.filter((v: any) => {
        return !v.picUrl;
      }).length > 0
    ) {
      message.error('请上传全部稿件的图片');
      return false;
    }
    return true;
  };

  // 校验视频图片
  videoPicCheck = (item: any, s: number) => {
    return item.doc_type == 9 && !!item.list_pics?.length && s == 82;
  };

  saveDataTransfer = (values: any) => {
    const { title, title_style, title_pic, position, channelArticles, style, round_carousel } =
      values;
    const title_show = title_style >= 0;
    const data: any = {
      round_carousel,
      article_category: this.props.article_category,
      title,
      title_show,
      title_pic: title_style >= 1 ? title_pic : '',
      ref_type: 11,
      style,
      ref_ids: channelArticles.map((item: any) => item.uuid || item.id).join(','),
      ref_extensions: JSON.stringify(
        channelArticles.map((item: any) => ({
          article_id: item.uuid || item.id,
          list_pic: item.picUrl,
          list_title: item.custom_title || item.list_title,
        }))
      ),
    };
    if (position) {
      data.position = position;
    }
    if (title_show) {
      data.title_style = title_style;
    }
    return data;
  };

  setEditData = (data: any) => {
    if (data) {
      const {
        article_list,
        recommend: {
          title,
          show_title,
          title_style = 0,
          title_pic = '',
          position,
          style = 0,
          ref_extensions,
          cycle_carousel,
        },
      } = data;
      const extensions = JSON.parse(ref_extensions);
      this.setState({
        editData: {
          title,
          title_style: show_title ? title_style : -1,
          title_pic,
          position,
          style,
          round_carousel: cycle_carousel,
          channelArticles: article_list.map((v: any) => {
            const id = v.uuid || v.id;
            const data: any = {
              ...v,
              channel_name: v.channel_name,
              list_title: v.list_title,
              id,
              uuid: v.uuid,
            };
            const picArticle = extensions.find((item: any) => item.article_id == id);
            if (picArticle) {
              data.picUrl = picArticle.list_pic;
              data.custom_title = picArticle.list_title;
            }
            return data;
          }),
        },
      });
    } else {
      this.setState({
        editData: null,
      });
    }
  };

  handleStyleChange = (style: number) => {
    const { getFieldError, getFieldValue, setFields } = this.props.form;
    const [err] = getFieldError('channelArticles') || [''];
    const value = getFieldValue('channelArticles') || [];
    const oldStyle = getFieldValue('style') || 0;

    if (style == 82 || oldStyle == 82) {
      for (const item of value) {
        item.picUrl = '';
      }
    }

    console.log(style, oldStyle);
    if (value.length >= 3 || err.startsWith('为保证客户端显示效果')) {
      let errors;
      if ((style === 81 && value.length >= 5) || (style === 0 && value.length >= 3)) {
        errors = null;
      } else {
        errors = [new Error(`为保证客户端显示效果，关联新闻数不能少于${style === 81 ? 5 : 3}条！`)];
      }
      setFields({ channelArticles: { value, errors } });
    }
  };

  editPic = (url: string, index: number) => {
    const style = this.props.form.getFieldsValue().style;
    let pic: string = url;
    let modal: any;
    const picChange = (u: string) => {
      pic = u;
      modal.update({
        content: (
          <>
            <ImageUploader
              value={pic}
              onChange={picChange}
              ratio={style !== 82 ? 3 / 4 : 16 / 9}
              imgMaxWidth={314}
            />
            <p>比例{style !== 82 ? '3:4' : '16:9'}</p>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '编辑图片',
      content: (
        <>
          <ImageUploader
            value={pic}
            onChange={picChange}
            ratio={style !== 82 ? 3 / 4 : 16 / 9}
            imgMaxWidth={314}
          />
          <p>比例{style !== 82 ? '3:4' : '16:9'}</p>
        </>
      ),
      onOk: (destroy: Function) => {
        if (!pic) {
          message.error('请上传图片');
          return;
        }
        const articles = [...this.props.form.getFieldsValue().channelArticles];
        articles[index].picUrl = pic;
        this.props.form.setFieldsValue({ channelArticles: articles });
        destroy();
      },
    });
  };

  editTitle = (value: string, index: number) => {
    let title: string = value || '';
    let modal: any;
    const titleChange = (e: any) => {
      title = e.target.value;
      modal.update({
        content: (
          <>
            <Input.TextArea
              placeholder="最多输入40字"
              value={title}
              maxLength={40}
              onPressEnter={(e) => e.preventDefault()}
              onChange={titleChange}
            ></Input.TextArea>
            <div style={{ textAlign: 'right' }}>{title?.length}/40</div>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '自定义标题',
      content: (
        <>
          <Input.TextArea
            placeholder="最多输入40字"
            value={title}
            maxLength={40}
            onPressEnter={(e) => e.preventDefault()}
            onChange={titleChange}
          ></Input.TextArea>
          <div style={{ textAlign: 'right' }}>{title?.length}/40</div>
        </>
      ),
      onOk: (destroy: Function) => {
        if (!title?.trim()) {
          message.error('请输入自定义标题');
          return;
        }
        const articles = [...this.props.form.getFieldsValue().channelArticles];
        articles[index].custom_title = title?.trim();
        this.props.form.setFieldsValue({ channelArticles: articles });
        destroy();
      },
    });
  };

  render() {
    const {
      form: { getFieldDecorator, getFieldsValue },
      isEdit,
    } = this.props;
    const relatedArticleTip = (
      <p>可关联视频稿、小视频，最多15条，且每条视频均须上传推荐位使用的列表图，才能创建成功。</p>
    );

    const styleTip = (style: number) => (
      <img
        src={`/assets/${
          style === 0
            ? 'video_recommend_style1.svg'
            : style === 2
            ? 'video_recommend_style3.png'
            : 'video_recommend_style2.jpg'
        }`}
        width={200}
        height={style === 0 ? 159 : style === 2 ? 128 : 119}
      />
    );
    const editData: any = this.state.editData || {
      style: isEdit ? undefined : 0,
      round_carousel: true,
    };
    const { style = editData.style ?? 0 } = getFieldsValue();
    console.log('style', style);
    const columns = [
      {
        title: '潮新闻ID',
        dataIndex: 'id',
        width: 80,
        render: (_: any, v: any) => v.uuid || v.id,
      },
      {
        title: '新闻频道',
        key: 'type',
        dataIndex: 'channel_name',
        width: 90,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
        width: 150,
      },
      {
        // width: 90,
        title: '自定义标题',
        key: 'custom_title',
        dataIndex: 'custom_title',
        render: (text: string, record: any, index: number) => {
          return (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <div>{record.custom_title || record.list_title}</div>
              <a
                style={{ flex: 'none', marginLeft: '5px' }}
                onClick={() => this.editTitle(record.custom_title || record.list_title, index)}
              >
                修改
              </a>
            </div>
          );
        },
      },
      {
        title:
          style == 82 ? (
            <span>
              列表图&nbsp;
              <Tooltip title="视频稿有列表图时，会自动带入。若图片比例非16:9，请手动修改。">
                <Icon type="question-circle" />
              </Tooltip>
            </span>
          ) : (
            '列表图'
          ),
        // title: '列表图',
        dataIndex: 'picUrl',
        width: 90,
        render: (text: string, record: any, index: number) => {
          return (
            <>
              <A onClick={() => this.editPic(text, index)}>{text ? '修改' : '上传'}</A>
            </>
          );
        },
      },
    ];

    return (
      <Form {...RecommendFormLayout}>
        <RecommendCommom {...this.props} editData={editData} />
        <Form.Item label="显示样式">
          {getFieldDecorator('style', {
            initialValue: editData.style,
            rules: [
              {
                required: true,
                message: '请选择样式',
              },
            ],
          })(
            <Radio.Group onChange={(e) => this.handleStyleChange(e.target.value)}>
              <Radio value={0}>
                样式一&emsp;
                <Tooltip title={styleTip(0)}>
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
              <Radio value={81}>
                样式二&emsp;
                <Tooltip title={styleTip(1)}>
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
              <Radio value={82}>
                样式三&emsp;
                <Tooltip title={styleTip(2)}>
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item label="循环轮播">
          {getFieldDecorator('round_carousel', {
            initialValue: editData.round_carousel,
            valuePropName: 'checked',
          })(<Switch />)}
        </Form.Item>
        <Form.Item label="关联稿件">
          {getFieldDecorator('channelArticles', {
            initialValue: editData.channelArticles,
            preserve: true,
            rules: [
              {
                required: true,
                message: '请关联新闻',
                type: 'array',
              },
              {
                max: 15,
                message: '最多关联15条新闻',
                type: 'array',
              },
              {
                min: style === 81 ? 5 : 3,
                message: `为保证客户端显示效果，关联新闻数不能少于${style === 81 ? 5 : 3}条！`,
                type: 'array',
              },
            ],
          })(
            <SearchAndInput
              // key={style}
              max={15}
              func="listArticleRecommendSearch"
              columns={columns}
              placeholder="输入ID或标题关联稿件"
              body={{ doc_types: '9,10' }}
              order={true}
              addOnTop={true}
              map={(v: any) => {
                if (this.videoPicCheck(v, style)) {
                  return {
                    ...v,
                    picUrl: v.list_pics.split(',')[0],
                  };
                }
                return v;
              }}
              afix={
                <Tooltip title={relatedArticleTip}>
                  <Icon type="question-circle" />
                </Tooltip>
              }
            />
          )}
        </Form.Item>
      </Form>
    );
  }
}
