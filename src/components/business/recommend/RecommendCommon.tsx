import { ImageUploader } from '@app/components/common';
import { Form, Icon, Input, InputNumber, Tooltip } from 'antd';
import Radio from 'antd/es/radio';
import React, { useEffect, useState } from 'react';

export const RecommendFormLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 18 },
};

export default function RecommendCommon(props: any) {
  const {
    form: { getFieldDecorator },
    children,
    editData,
    isEdit,
    hiddenTitleType,
    isReport = false,
  } = props;
  const [title_style, setTitle_style] = useState(-1);
  let { maxPosition } = props;
  if (!maxPosition || maxPosition >= 500) {
    maxPosition = 500;
  } else {
    maxPosition += 1;
  }
  const positionTip = (
    <span>
      <p>输入说明：</p>
      <p>
        直接输入阿拉伯数字代表位置序号，例如要在头条频道信息流第8个位置插入该推荐位，则直接输入“8”即可。
      </p>
      <p>最大输入数字为{maxPosition}</p>
      <br />
      <p>注：</p>
      <p>直接在输入的指定位置上插入推荐位显示，参与稿件列表的排序，且优先于稿件展示</p>
    </span>
  );
  const titleStyleDefaultValue =
    typeof editData.title_style === 'undefined' ? -1 : editData.title_style;

  useEffect(() => {
    if (editData.title_style) {
      setTitle_style(editData.title_style);
    }
  }, [editData.title_style]);

  const titleStyleChange = (e: any) => {
    const val = e.target.value;
    setTitle_style(val);
    if (val === 1 || val === 2) {
      setTimeout(() => {
        const {
          form: { setFieldsValue },
        } = props;
        setFieldsValue({ title_pic: '' });
      }, 0);
    }
  };

  const titleStyleTip = (desc: string, picName: string, width: number, height: number) => {
    return (
      <div>
        <div style={{ marginBottom: 5 }} dangerouslySetInnerHTML={{ __html: desc }}></div>
        <img src={`/assets/${picName}.png`} width={width} height={height} />
      </div>
    );
  };

  return (
    <>
      <Form.Item label="推荐位名称" style={{ display: 'block' }}>
        {getFieldDecorator('title', {
          initialValue: editData.title,
          rules: [
            {
              required: true,
              message: '请输入推荐位名称',
            },
            {
              max: 15,
              message: '推荐位名称最长不能超过15个字',
            },
          ],
        })(<Input placeholder="请输入名称，不超过15个字" />)}
      </Form.Item>
      {!hiddenTitleType && (
        <Form.Item style={{ marginLeft: 130, marginTop: -5, whiteSpace: 'nowrap' }}>
          {getFieldDecorator('title_style', {
            initialValue: titleStyleDefaultValue,
          })(
            <Radio.Group onChange={titleStyleChange}>
              <Radio style={{ fontSize: 13 }} value={-1}>
                不显示标题
              </Radio>
              {!isReport && (
                <Radio style={{ fontSize: 13 }} value={0}>
                  标题为默认样式 &nbsp;
                  <Tooltip
                    title={titleStyleTip(
                      '图标默认为蓝色竖条标题为<br/>推荐位名称',
                      'recommend_title1',
                      200,
                      49
                    )}
                  >
                    <Icon type="question-circle" />
                  </Tooltip>
                </Radio>
              )}
              <Radio style={{ fontSize: 13 }} value={1}>
                标题为图片样式 &nbsp;
                <Tooltip title={titleStyleTip('标题为一整张图片', 'recommend_title2', 150, 49)}>
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
              {!isReport && (
                <Radio style={{ fontSize: 13 }} value={2}>
                  标题为图文样式 &nbsp;
                  <Tooltip
                    title={titleStyleTip(
                      '标题为一张自定义图标+推荐位名称（仅上传一张图标即可）',
                      'recommend_title3',
                      200,
                      92
                    )}
                  >
                    <Icon type="question-circle" />
                  </Tooltip>
                </Radio>
              )}
            </Radio.Group>
          )}
        </Form.Item>
      )}
      {!hiddenTitleType && title_style === 1 && (
        <Form.Item
          style={{ marginLeft: 120, marginTop: -20 }}
          extra="支持上传jpg,jpeg,png,gif图片格式, 比例未限制, 建议高度75px"
        >
          {getFieldDecorator('title_pic', {
            initialValue: editData.title_pic,
            rules: [
              {
                required: true,
                message: '请上传图片',
              },
            ],
          })(<ImageUploader isCutting />)}
        </Form.Item>
      )}
      {!hiddenTitleType && title_style === 2 && (
        <Form.Item
          style={{ marginLeft: 120, marginTop: -20 }}
          extra="支持上传jpg,jpeg,png,gif图片格式,比例为1:1,建议尺寸60px*60px"
        >
          {getFieldDecorator('title_pic', {
            initialValue: editData.title_pic,
            rules: [
              {
                required: true,
                message: '请上传图片',
              },
            ],
          })(<ImageUploader ratio={1 / 1} />)}
        </Form.Item>
      )}
      {children}
      {!isEdit && (
        <Form.Item label="显示位置" style={{ display: 'block' }}>
          {getFieldDecorator('position', {
            initialValue: editData.position,
            rules: [
              {
                required: true,
                message: '请输入位置序号',
                type: 'number',
              },
              {
                max: maxPosition,
                message: `最大不能超过${maxPosition}`,
                type: 'number',
              },
            ],
          })(
            <InputNumber
              style={{ width: 400 }}
              placeholder={`请输入在列表中显示的位置序号，1~${maxPosition}正整数`}
              min={1}
              max={maxPosition}
            />
          )}
          &emsp;
          <Tooltip title={positionTip}>
            <Icon type="question-circle" />
          </Tooltip>
        </Form.Item>
      )}
    </>
  );
}
