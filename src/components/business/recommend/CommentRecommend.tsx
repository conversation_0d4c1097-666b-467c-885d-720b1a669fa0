import RecommendCommon, { RecommendFormLayout } from './RecommendCommon';
import React from "react";
import '../styles/commentRecommend.scss';
import CommentRecommendForm from '@components/business/commentRecommendForm';
import { searchToObject } from '@app/utils/utils';
import { Form } from 'antd';

// 潮客潮语(评论推荐位)
@(Form.create({ name: 'CommentRecommend' }) as any)
export default class CommentRecommend extends React.Component<any> {

  state: any = {
    editData: null,
    commentRecommendFormRef: React.createRef<any>()
  }

  customValidator = () => {
    const commentRecommendFormRef = this.state.commentRecommendFormRef.current
    if (commentRecommendFormRef && commentRecommendFormRef.customValidator) {
      return commentRecommendFormRef.customValidator()
    }
    return false
  }

  saveDataTransfer = (values: any) => {
    const { title, title_style, title_pic, position } = values
    const title_show = title_style >= 0
    const data: any = {
      article_category: 0,
      title,
      title_show,
      title_pic: title_style >= 1 ? title_pic : '',
      ref_type: 21,
    }
    if (position) {
      data.position = position
    }
    if (title_show) {
      data.title_style = title_style
    }
    const commentRecommendFormRef = this.state.commentRecommendFormRef.current
    if (commentRecommendFormRef && commentRecommendFormRef.getSubmitData) {
      const commentData = commentRecommendFormRef.getSubmitData()
      data.ref_extensions = JSON.stringify(commentData)
    }
    return data
  }

  setEditData = (data: any) => {
    if (data) {
      const { recommend: { title, show_title, title_style = 0, title_pic = '', position }, comment_list } = data
      this.setState({
        editData: {
          title,
          title_style: show_title ? title_style : -1,
          title_pic,
          position,
          comment_list: comment_list.map((item: any) => ({
            channel_article_id: item.article_id,
            account_id: item.account_id,
            content: item.comment_content,
            id: item.comment_id,
            channel_id: item.channel_id,
            created_at: item.comment_created_at,
            nick_name: item.nick_name,
            title: item.article_title,
            expression_url: item.expression_url,
            comment_link_list: item.comment_link_list,
            pic_url: item.pic_url
          }))
        }
      })
    } else {
      this.setState({
        editData: null
      })
    }
  }

  render() {
    const editData: any = this.state.editData || {}
    const channelId = searchToObject().channel_id
    const { isEdit } = this.props
    return (
      <Form {...RecommendFormLayout}>
        <RecommendCommon {...this.props} editData={editData} />
        {(!isEdit || editData.comment_list) &&
          <CommentRecommendForm
            channelId={channelId}
            selectedList={editData.comment_list}
            disabledIds={editData.comment_list ? editData.comment_list.map((item: any) => item.id) : []}
            wrappedComponentRef={(form: any) => this.state.commentRecommendFormRef.current = form}
          />}
      </Form>

    )
  }
}