import React from "react"
import Recommend<PERSON><PERSON><PERSON>, { RecommendFormLayout } from "./RecommendCommon"
import { Form, Icon, Input, InputNumber, Radio, Switch, Tooltip } from "antd"
import { ImageUploader } from '@components/common';

// 读报推荐位
@(Form.create({ name: 'ReadPaperRecommend' }) as any)
export default class ReadPaperRecommend extends React.Component<any> {
  state = {
    editData: null,
    recommendType: 1,
  }

  saveDataTransfer = (values: any) => {
    const { title, position, ref_extensions } = values
    const data: any = {
      article_category: 0,
      title,
      ref_type: this.state.recommendType == 0 ? 32 : 34,
    }
    if (ref_extensions) {
      if (this.state.recommendType == 1) {
        ref_extensions.weather_show_switch = ref_extensions.weather_show_switch ? 1 : 0
      }
      data.ref_extensions = JSON.stringify(ref_extensions)
    }
    if (position) {
      data.position = position
    }
    return data
  }

  setEditData = (data: any) => {
    if (data) {
      const { recommend: { title, position, ref_extensions, ref_type } } = data
      this.setState({
        editData: {
          title,
          position,
          isEdit: true,
          ref_extensions: JSON.parse(ref_extensions),
        },
        recommendType: ref_type == 32 ? 0 : 1
      })
    } else {
      this.setState({
        editData: null
      })
    }
  }

  handleSubtypeChange = (v: any) => {
    this.setState({ recommendType: v })
  }

  render() {
    const editData: any = this.state.editData || {}
    const { ref_extensions: { icon_name = '', name_image = '', top_image = '', bottom_image = '', morning_slogan = '', evening_slogan = '', weather_show_switch = 1 } = {}, isEdit } = editData
    const { form: { getFieldDecorator }, } = this.props
    return (
      <Form {...RecommendFormLayout}>
        {/* <RecommendCommon {...this.props} hiddenTitleType={'读报推荐位'} editData={editData}></RecommendCommon> */}
        <Form.Item label="推荐位名称" style={{ display: 'block' }}>
          {getFieldDecorator('title', {
            initialValue: editData.title,
            rules: [
              {
                required: true,
                message: '请输入推荐位名称',
              },
              {
                max: 15,
                message: '推荐位名称最长不能超过15个字',
              },
            ],
          })(<Input placeholder="请输入名称，不超过15个字" />)}
        </Form.Item>

        <Form.Item label="推荐位类型">
          <Radio.Group value={this.state.recommendType} disabled={isEdit} onChange={(e) => this.handleSubtypeChange(e.target.value)}>
            <Radio key={0} value={0}>指尖读报</Radio>
            <Radio key={1} value={1}>早晚报</Radio>
          </Radio.Group>
        </Form.Item>
        {
          !isEdit && <Form.Item label="显示位置" style={{ display: 'block' }}>
            {getFieldDecorator('position', {
              initialValue: editData.position,
              rules: [
                {
                  required: true,
                  message: '请输入位置序号',
                  type: 'number',
                },
                {
                  max: 500,
                  message: `最大不能超过${500}`,
                  type: 'number',
                },
              ],
            })(<InputNumber style={{ width: 400 }} placeholder={`请输入在列表中显示的位置序号，1~500正整数`} min={1} max={500} />)}
            &emsp;
            <Tooltip title={(<span>
              <p>输入说明：</p>
              <p>
                直接输入阿拉伯数字代表位置序号，例如要在头条频道信息流第8个位置插入该推荐位，则直接输入“8”即可。
              </p>
              <p>最大输入数字为500</p>
              <br />
              <p>注：</p>
              <p>直接在输入的指定位置上插入推荐位显示，参与稿件列表的排序，且优先于稿件展示</p>
            </span>)}>
              <Icon type="question-circle" />
            </Tooltip>
          </Form.Item>
        }

        {this.state.recommendType == 0 ? (
          <>
            <Form.Item label="展示样式" extra="说明：系统自动读取「指尖读报」最新一期最新的3条内容，展示在App上。">
              <img src="/assets/recommend_readpaper.png" width={450} />
            </Form.Item>
            <Form.Item label="角标">
              {getFieldDecorator('ref_extensions.icon_name', {
                initialValue: icon_name,
                rules: [
                  {
                    required: false,
                    message: '请输入角标',
                  },
                  {
                    max: 4,
                    message: '推荐位名称最长不能超过4个字',
                  },
                ],
              })(<Input placeholder="最多4个字" maxLength={4} />)}
            </Form.Item>
            <Form.Item label="名称图" extra="支持jpg、png等格式，比例未限制，建议高度81px">
              {getFieldDecorator('ref_extensions.name_image', {
                initialValue: name_image,
                rules: [
                  {
                    required: false,
                    message: '请选择名称图',
                  },
                ],
              })(

                <ImageUploader
                  imgsize={1024}
                  isCutting={true}
                  accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
                />
              )}
            </Form.Item>
            <Form.Item label="背景上图" extra="支持jpg、png等格式，比例为 345:60，建议尺寸1035px * 180px">
              {getFieldDecorator('ref_extensions.top_image', {
                initialValue: top_image,
                rules: [
                  {
                    required: false,
                    message: '请选择背景上图',
                  },
                ],
              })(
                <ImageUploader
                  imgsize={1024}
                  ratio={23 / 18}
                  accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
                />
              )}
            </Form.Item>
            <Form.Item label="背景下图" extra="支持jpg、png等格式，比例为 1:1，建议尺寸1035px * 1035px">
              {getFieldDecorator('ref_extensions.bottom_image', {
                initialValue: bottom_image,
                rules: [
                  {
                    required: false,
                    message: '请选择背景下图',
                  },
                ],
              })(
                <ImageUploader
                  imgsize={1024}
                  ratio={1}
                  accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
                />
              )}
            </Form.Item>
          </>
        ) : (
          <>
            <Form.Item label={
              <span style={{ position: 'relative' }}>
                <Tooltip title={`仅在大图样式下显示`}>
                  <Icon type="question-circle" style={{ position: 'absolute', left: -25, top: 0 }} />
                </Tooltip>
                早报标语
              </span>
            }>
              {getFieldDecorator('ref_extensions.morning_slogan', {
                initialValue: morning_slogan,
                rules: [
                  {
                    required: false,
                    message: '请输入早报标语',
                  },
                  {
                    max: 10,
                    message: '最长不能超过10个字',
                  },
                ],
              })(<Input placeholder="最多10个字" maxLength={10} />)}

            </Form.Item>
            <Form.Item label={
              <span style={{ position: 'relative' }}>
                <Tooltip title={`仅在大图样式下显示`}>
                  <Icon type="question-circle" style={{ position: 'absolute', left: -25, top: 0 }} />
                </Tooltip>
                晚报标语
              </span>
            }>
              {getFieldDecorator('ref_extensions.evening_slogan', {
                initialValue: evening_slogan,
                rules: [
                  {
                    required: false,
                    message: '请输入晚报标语',
                  },
                  {
                    max: 10,
                    message: '最长不能超过10个字',
                  },
                ],
              })(<Input placeholder="最多10个字" maxLength={10} />)}

            </Form.Item>

            <Form.Item label="显示天气">
              {getFieldDecorator('ref_extensions.weather_show_switch', {
                initialValue: Boolean(weather_show_switch),
                valuePropName: 'checked',
              })(<Switch checkedChildren="开" unCheckedChildren="关"></Switch>)}
            </Form.Item>
            <Form.Item label="展示样式">
              <span style={{color: '#999' }}>具体样式在早晚报页面进行设置。</span>
              {/* <img src="/assets/recommend_mande_paper.png" width={400} /> */}
            </Form.Item>
          </>
        )}
      </Form>
    )
  }
}