import { Checkbox, Form, Icon, Input, InputNumber, Modal, Switch, Tooltip, message } from 'antd';
import Radio from 'antd/es/radio';
import SearchAndInput from '@components/common/newNewsSearchAndInput';
import RecommendCommon, { RecommendFormLayout } from './RecommendCommon';
import React from 'react';
import { ImageUploader } from '@app/components/common';

// 新闻汇总推荐位
@(Form.create({ name: 'NewsCollectRecommend' }) as any)
export default class NewsCollectRecommend extends React.Component<any> {
  state: any = {
    editData: null,
  };

  saveDataTransfer = (values: any) => {
    const { title, title_style, title_pic, position, channelArticles, style, pic_url, ref_ids2 } =
      values;
    const title_show = title_style >= 0;
    const data = {
      ...values,
      article_category: 0,
      title,
      title_show,
      title_pic: title_style >= 1 ? title_pic : '',
      ref_type: 12,
      pic_url: style == 14 ? pic_url : '',
      ref_ids2: ref_ids2,
      ref_ids: channelArticles.map((item: any) => item.uuid || item.id).join(','),
      ref_extensions: JSON.stringify(
        channelArticles.map((item: any) => ({
          article_id: item.uuid || item.id,
          list_title: item.custom_title || item.list_title,
        }))
      ),
    };
    if (!position) {
      delete data.position;
    }
    if (!title_show) {
      delete data.title_style;
    }
    delete data.channelArticles;
    return data;
  };

  setEditData = (data: any) => {
    if (data) {
      const {
        article_list,
        recommend: {
          title,
          show_title,
          title_style = 0,
          title_pic = '',
          position,
          style,
          cycle_carousel,
          nav_type,
          ref_extensions = '[]',
          pic_url,
          ref_ids2,
        },
      } = data;
      const extensions = JSON.parse(ref_extensions);
      this.setState({
        editData: {
          title,
          title_style: show_title ? title_style : -1,
          title_pic,
          pic_url,
          position,
          style,
          nav_type,
          ref_ids2: ref_ids2 || 5,
          round_carousel: cycle_carousel,
          channelArticles: article_list.map((v: any) => {
            const id = v.uuid || v.id;
            const result = {
              channel_name: v.channel_name,
              list_title: v.list_title,
              id,
              uuid: v.uuid,
            };
            const picArticle = extensions.find((item: any) => item.article_id == id);
            if (picArticle) {
              result.custom_title = picArticle.list_title;
            }

            return result;
          }),
        },
      });
    } else {
      this.setState({
        editData: null,
      });
    }
  };

  editTitle = (value: string, index: number) => {
    let title: string = value || '';
    let modal: any;
    const titleChange = (e: any) => {
      title = e.target.value;
      modal.update({
        content: (
          <>
            <Input.TextArea
              placeholder="最多输入40字"
              value={title}
              maxLength={40}
              onPressEnter={(e) => e.preventDefault()}
              onChange={titleChange}
            ></Input.TextArea>
            <div style={{ textAlign: 'right' }}>{title?.length}/40</div>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '自定义标题',
      content: (
        <>
          <Input.TextArea
            placeholder="最多输入40字"
            value={title}
            maxLength={40}
            onPressEnter={(e) => e.preventDefault()}
            onChange={titleChange}
          ></Input.TextArea>
          <div style={{ textAlign: 'right' }}>{title?.length}/40</div>
        </>
      ),
      onOk: (destroy: Function) => {
        if (!title?.trim()) {
          message.error('请输入自定义标题');
          return;
        }
        const articles = [...this.props.form.getFieldsValue().channelArticles];
        articles[index].custom_title = title?.trim();
        this.props.form.setFieldsValue({ channelArticles: articles });
        destroy();
      },
    });
  };

  render() {
    const {
      form: { getFieldDecorator, getFieldsValue, setFieldsValue },
      isEdit,
    } = this.props;
    const columns = [
      {
        title: '潮新闻ID',
        dataIndex: 'id',
        width: 80,
      },
      {
        title: '新闻频道',
        key: 'type',
        dataIndex: 'channel_name',
        width: 90,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
        width: 150,
      },
      {
        // width: 90,
        title: '自定义标题',
        key: 'custom_title',
        dataIndex: 'custom_title',
        render: (text: string, record: any, index: number) => {
          return (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <div>{record.custom_title || record.list_title}</div>
              <a
                style={{ flex: 'none', marginLeft: '5px' }}
                onClick={() => this.editTitle(record.custom_title || record.list_title, index)}
              >
                修改
              </a>
            </div>
          );
        },
      },
    ];

    const styleTip = (style: number) => (
      <img
        src={`/assets/news_collect_stype_tip${style}.${style == 2 ? 'png' : 'jpg'}`}
        width={200}
        height={style == 2 ? 44 : style === 0 ? 109 : 99}
      />
    );
    const editData: any =
      this.state.editData ||
      (isEdit ? {} : { round_carousel: false, style: 12, nav_type: '', ref_ids2: 5 });
    const values = getFieldsValue();
    let { style = editData.style, title_style } = values;
    if (typeof values.style === 'undefined') {
      title_style = editData.title_style;
    }
    return (
      <Form {...RecommendFormLayout}>
        <RecommendCommon {...this.props} editData={editData}>
          {style === 13 && title_style >= 0 && (
            <Form.Item label="频道跳转">
              {getFieldDecorator('nav_type', {
                initialValue: editData.nav_type,
              })(<Input style={{ width: 300 }} placeholder="请输入频道NAV_TYPE，联系技术获取。" />)}
            </Form.Item>
          )}
        </RecommendCommon>
        <Form.Item label="样式选择">
          {getFieldDecorator('style', {
            rules: [
              {
                required: true,
                message: '请选择样式',
              },
            ],
            initialValue: editData.style,
          })(
            <Radio.Group>
              <Radio value={12}>
                卡片轮播&emsp;
                <Tooltip title={styleTip(0)}>
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
              <Radio value={13}>
                显示列表图&emsp;
                <Tooltip title={styleTip(1)}>
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
              <Radio value={14}>
                快讯样式&emsp;
                <Tooltip title={styleTip(2)}>
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {style == 14 && (
          <Form.Item label="轮播稿件数">
            {getFieldDecorator('ref_ids2', {
              rules: [
                {
                  required: true,
                  message: '请输入轮播稿件数',
                },
              ],
              initialValue: editData.ref_ids2,
            })(<InputNumber min={1} max={30} precision={0} />)}
          </Form.Item>
        )}
        {(style === 12 || style === 13) && (
          <Form.Item label="循环轮播">
            {getFieldDecorator('round_carousel', {
              rules: [
                {
                  required: true,
                  message: '请设置是否轮播',
                },
              ],
              initialValue: editData.round_carousel,
              valuePropName: 'checked',
            })(<Switch />)}
          </Form.Item>
        )}
        {style == 14 && (
          <Form.Item label="上传图片" extra="支持扩展名 .jpg .jpeg .png 等格式图片，比例为 4:3">
            {getFieldDecorator('pic_url', {
              rules: [
                {
                  required: true,
                  message: '请上传图片',
                },
              ],
              initialValue: editData.pic_url,
            })(<ImageUploader ratio={4 / 3} />)}
          </Form.Item>
        )}
        <Form.Item label="关联稿件">
          {getFieldDecorator('channelArticles', {
            initialValue: editData.channelArticles,
            preserve: true,
            rules: [
              {
                required: true,
                message: '请关联新闻',
                type: 'array',
              },
              {
                max: 30,
                message: '最多关联30条新闻',
                type: 'array',
              },
              {
                min: 2,
                message: '为保证客户端显示效果，关联新闻数不能少于2条！',
                type: 'array',
              },
            ],
          })(
            <SearchAndInput
              max={30}
              func="listArticleRecommendSearch"
              columns={columns}
              placeholder="输入ID或标题关联稿件"
              body={{ doc_types: '2,3,4,5,8,9' }}
              order={true}
              addOnTop={true}
            />
          )}
        </Form.Item>
      </Form>
    );
  }
}
