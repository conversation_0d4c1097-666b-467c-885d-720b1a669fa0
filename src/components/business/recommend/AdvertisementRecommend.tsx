import { Checkbox, Form, Input, Divider, Row, Col, Button, Switch, message } from 'antd';
import React from 'react';
import RecommendCommon, { RecommendFormLayout } from './RecommendCommon';
import Radio from 'antd/es/radio';
import { ImageUploader } from '@app/components/common';
import '@views/recommend/components/operationsPage.scss';
import TMFormList, { FormListTitle, TMFormListRef } from '@app/components/common/TMFormList';
import uuid from 'uuid';
import NewImageUploader from '@app/components/common/newImageUploader';

const getItem = () => {
  return {
    pic: undefined,
    link_url: '',
  };
};

// 广告推荐位
@(Form.create({ name: 'AdvertisementRecommend' }) as any)
export default class AdvertisementRecommend extends React.Component<any> {
  formListRef = React.createRef<TMFormListRef>();
  state = {
    editData: {
      ad_source: '',
      corner_show: true,
      pic_custom_scale: '4:1',
      list: [getItem()],
      round_carousel: true,
    },
  };
  getRatio = (pic_custom_scale: string) => {
    switch (pic_custom_scale) {
      case '4:1':
        return 4 / 1;
      case '16:9':
        return 16 / 9;
      case '4:3':
        return 4 / 3;
      case '1:1':
        return 1 / 1;
      case '3:1':
        return 3 / 1;
      default:
        return 4 / 1;
    }
  };

  picRatioChange = (e: any) => {
    this.setState(
      {
        editData: {
          ...(this.state.editData || {}),
          pic_custom_scale: e.target.value,
        },
      },
      () => {
        this.props.form.validateFields();
      }
    );
  };

  saveDataTransfer = (values: any) => {
    const { title, title_style, title_pic, position, list } = values;
    const title_show = title_style >= 0;
    const data = {
      ...values,
      article_category: 0,
      title,
      title_show,
      title_pic: title_style >= 1 ? title_pic : '',
      ref_type: 24,
      ref_extensions: JSON.stringify(list.map((item: any) => ({ ...item, pic_url: item.pic.url }))),
    };
    delete data.list;
    return data;
  };

  setEditData = (data: any) => {
    if (data) {
      const {
        advertisement_list,
        recommend: {
          title,
          show_title,
          title_style = 0,
          title_pic = '',
          position,
          corner_show,
          list_pic_custom_scale,
          advertisement_source,
          extend = '',
          cycle_carousel = true,
        },
      } = data;
      this.setState({
        editData: {
          title,
          title_style: show_title ? title_style : -1,
          title_pic,
          position,
          pic_custom_scale: list_pic_custom_scale,
          corner_show,
          ad_source: advertisement_source,
          list: advertisement_list?.map((item: any) => {
            return {
              ...item,
              pic: {
                url: item.pic_url,
                ratio: this.getRatio(list_pic_custom_scale),
              },
            };
          }),
          extend,
          round_carousel: cycle_carousel,
        },
      });
    } else {
      this.setState({
        editData: null,
      });
    }
  };

  render() {
    const {
      form: { getFieldDecorator, getFieldsValue, getFieldValue },
    } = this.props;
    const editData: any = this.state.editData || {
      ad_source: '',
      corner_show: true,
      pic_custom_scale: '4:1',
      list: [getItem()],
      round_carousel: true,
    };
    const values = getFieldsValue();
    // const { pic_custom_scale = editData.pic_custom_scale } = values
    return (
      <Form className="operations" {...RecommendFormLayout}>
        <RecommendCommon {...this.props} editData={editData}>
          {/* <Form.Item style={{ marginLeft: 133, marginTop: -24, height: 30 }}>
            {getFieldDecorator('title_show', {
              initialValue: editData.title_show,
              valuePropName: 'checked',
            })(<Checkbox>标题在客户端显示</Checkbox>)}
          </Form.Item> */}
        </RecommendCommon>
        <Form.Item label="广告角标" required>
          {getFieldDecorator('ad_corner_show', {
            initialValue: editData.corner_show,
          })(
            <Radio.Group>
              <Radio value={true}>显示</Radio>
              <Radio value={false}>不显示</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item label="广告出处">
          {getFieldDecorator('ad_source', {
            initialValue: editData.ad_source,
            rules: [
              {
                max: 20,
                message: '广告出处不能多于20个字',
              },
            ],
          })(<Input placeholder="输入广告出处" />)}
        </Form.Item>
        <Form.Item label="自动轮播">
          {getFieldDecorator('round_carousel', {
            initialValue: editData.round_carousel,
            valuePropName: 'checked',
            rules: [
              {
                required: true,
                message: '请选择自动轮播',
              },
            ],
          })(<Switch />)}
        </Form.Item>
        <TMFormList
          ref={this.formListRef}
          dataList={editData.list}
          form={this.props.form}
          fromItem={() => getItem()}
          filed="list"
        >
          {(item: any, i: any, total: number) => (
            <Row key={i}>
              <FormListTitle
                total={total}
                i={i}
                title="广告位"
                upMove={() => this.formListRef.current?.upMove(i)}
                downMove={() => this.formListRef.current?.downMove(i)}
                removeItem={() => this.formListRef.current?.removeItem(i)}
              ></FormListTitle>

              {i === 0 && (
                <Form.Item label="图片比例">
                  {getFieldDecorator('pic_custom_scale', {
                    rules: [
                      {
                        required: true,
                        message: '请选择图片比例',
                      },
                    ],
                    initialValue: editData.pic_custom_scale,
                  })(
                    <Radio.Group onChange={this.picRatioChange}>
                      <Radio value="4:1">4:1</Radio>
                      <Radio value="16:9">16:9</Radio>
                      <Radio value="4:3">4:3</Radio>
                      <Radio value="1:1">1:1</Radio>
                      <Radio value="3:1">3:1</Radio>
                    </Radio.Group>
                  )}
                </Form.Item>
              )}
              <Row>
                <Form.Item
                  label="图片"
                  extra="支持上传jpg,jpeg,png,gif图片格式，比例为4:1、16:9、4:3、1:1、3:1，多个广告位图片比例必须一致"
                >
                  {getFieldDecorator(`list[${i}].pic`, {
                    initialValue: item.pic,
                    rules: [
                      {
                        required: true,
                        message: '请上传广告位图片, 如不需要请删除本条目',
                      },
                      {
                        validator: (rule: any, value: any, callback: any) => {
                          if (
                            value &&
                            !!value.url &&
                            value?.ratio != this.getRatio(editData.pic_custom_scale)
                          ) {
                            callback('图片比例不正确,请重新上传');
                          } else {
                            callback();
                          }
                        },
                      },
                    ],
                  })(<NewImageUploader ratioType={editData.pic_custom_scale} />)}
                </Form.Item>
                <Form.Item label="URL">
                  {getFieldDecorator(`list[${i}].link_url`, {
                    initialValue: item.link_url,
                    rules: [
                      {
                        pattern: /^[^,，]+$/,
                        message: '禁止输入中英文逗号',
                      },
                    ],
                  })(<Input placeholder="输入跳转URL" />)}
                </Form.Item>
              </Row>
            </Row>
          )}
        </TMFormList>

        <Row style={{ textAlign: 'center', marginBottom: 16 }} id="add-btn-line">
          <Button
            onClick={() => this.formListRef.current?.addItem()}
            disabled={(this.formListRef.current?.total ?? 0) >= 5}
          >
            新增广告位
          </Button>
        </Row>

        <Form.Item label="备注" extra={'备注信息仅后台可见，不会向用户展示，最多200字'}>
          {getFieldDecorator('extend', {
            initialValue: editData.extend || '',
            // rules: [
            //   max: 500,
            // ],
          })(
            <Input.TextArea rows={5} maxLength={200} placeholder="请输入备注信息"></Input.TextArea>
          )}
        </Form.Item>
      </Form>
    );
  }
}
