import React from "react";
import { Form } from 'antd';
import RecommendCommon, { RecommendFormLayout } from "./RecommendCommon";
import SearchAndInput from '@components/common/newNewsSearchAndInput';

// 用户内容单条推荐位
@(Form.create({ name: 'CommunitySingleRecommend' }) as any)
export default class CommunitySingleRecommend extends React.Component<any> {

  state: any = {
    editData: null,
  }

  saveDataTransfer = (values: any) => {
    const { channelArticles } = values
    const data = {
      article_category: this.props.article_category,
      ref_type: 28,
      ref_ids: channelArticles.map((item: any) => item.uuid).join(','),
      ...values,
    }
    delete data.channelArticles
    return data
  }

  setEditData = (data: any) => {
    if (data) {
      const { article_list, recommend: { title, position } } = data
      this.setState({
        editData: {
          title,
          position,
          channelArticles: article_list.map((v: any) => ({
            circle_name: v.articles.circle_name,
            list_title: v.list_title,
            id: v.id,
            uuid: v.uuid,
          }))
        }
      })
    } else {
      this.setState({
        editData: null
      })
    }
  }

  render() {
    const { form: { getFieldDecorator } } = this.props
    const columns = [
      {
        title: '潮新闻ID',
        dataIndex: 'uuid',
        width: 80,
      },
      {
        title: '关联圈子',
        key: 'circle_name',
        dataIndex: 'circle_name',
        width: 90,
      },
      {
        title: '内容标题',
        key: 'list_title',
        dataIndex: 'list_title',
      },
    ];
    const editData: any = this.state.editData || {}
    return (
      <Form {...RecommendFormLayout}>
        <RecommendCommon {...this.props} editData={editData} hiddenTitleType/>
        <Form.Item label="关联内容">
          {getFieldDecorator('channelArticles', {
            initialValue: editData.channelArticles,
            preserve: true,
            rules: [
              {
                required: true,
                message: '请关联内容',
                type: 'array',
              },
              {
                max: 1,
                message: '最多关联1条内容',
                type: 'array',
              },
            ]
          })(
            <SearchAndInput
              max={15}
              func="listArticleRecommendSearch"
              columns={columns}
              placeholder="输入ID或标题关联内容"
              body={{ doc_types: '10,12,13' }}
              order={true}
              addOnTop={true}
              categoryTip="内容"
            />
          )}
        </Form.Item>
      </Form>
    )
  }
}