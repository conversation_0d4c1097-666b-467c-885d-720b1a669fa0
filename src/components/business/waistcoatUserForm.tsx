import { userApi as api } from '@app/api';
import SearchAndInput from '@components/common/newsSearchAndInput';
import { Form, Input, InputNumber, message, Select, TreeSelect } from 'antd';
import React, { forwardRef, useEffect, useState } from 'react';
import _debounce from 'lodash/throttle';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader } from '@components/common';
import { citys } from '@app/assets/AllCity';

interface CityValue {
  province?: string;
  city?: string;
}

interface CitySelectProps {
  value?: CityValue;
  onChange?: (value: CityValue) => void;
  provinceMap?: any;
}

const CitySelect = ({ value = {}, onChange, provinceMap = {} }: CitySelectProps, ref: any) => {
  const [cityList, setCityList] = useState<any[]>([]);
  const [province, setProvince] = useState(value.province ?? '');
  const [city, setCity] = useState(value.city ?? '');

  useEffect(() => {
    const citys =
      value.province && Object.keys(provinceMap).length > 0
        ? provinceMap[value.province]?.citys
        : [];
    setCityList(citys);
  }, [provinceMap]);

  const triggerChange = (changedValue: CityValue) => {
    onChange?.({ province, city, ...value, ...changedValue });
  };

  const handleChangeProvince = (v: string, options: any) => {
    const citys: any[] = provinceMap[v].citys;
    setCityList(citys);
    setCity(citys[0]?.value);
    setProvince(v);
    triggerChange({ province: v, city: citys[0]?.value });
  };

  const handleChangeCity = (value: string, options: any) => {
    setCity(value);
    triggerChange({ city: value });
  };

  return (
    <span ref={ref}>
      <Input.Group compact>
        <Select
          style={{ width: 180 }}
          defaultValue={province}
          value={province}
          onChange={handleChangeProvince}
        >
          {Object.values(provinceMap).map((value: any) => (
            <Select.Option value={value.value} key={value.key}>
              {value.title}
            </Select.Option>
          ))}
        </Select>
        <Select style={{ width: 180 }} defaultValue={city} value={city} onChange={handleChangeCity}>
          {cityList.map((v: any) => (
            <Select.Option value={v.value} key={v.key}>
              {v.title}
            </Select.Option>
          ))}
        </Select>
      </Input.Group>
    </span>
  );
};

const CitySelectRef = forwardRef(CitySelect);

@connectSession
@(Form.create({ name: 'waistcoatUserForm' }) as any)
class WaistcoatUserForm extends React.Component<any, any> {
  doSubmit: any;

  constructor(props: any) {
    super(props);
    const form = {
      nick_name: '',
      information: '',
      phone_number: '',
      image_url: '',
    };

    this.state = {
      ...form,
      ...props.formContent,
      cityValue: {
        province: props.formContent?.province,
        city: props.formContent?.city,
      },
      provinceMap: {},
      // provinceList: [],
      cityList: [],
    };

    console.log(this.state);
  }

  componentDidMount() {
    this.getAreaList();
    this.doSubmit = _debounce(this.debDoSubmit, 800, {
      trailing: false,
    });
  }

  componentWillUnmount(): void {
    setMLoading(this, false);
  }

  getAreaList = () => {
    // api.getAllAreaList().then((res: any) => {
    let provinceMap: any = {};
    for (const province of citys) {
      provinceMap[province.name] = {
        title: province.name,
        value: province.name,
        key: province.name,
        citys: province.cities.map((city: any) => ({
          title: city.name,
          value: city.name,
          key: city.name,
        })),
      };
    }

    // const areaList = res.data.area_tree.map((v: any) => {
    //   return {
    //     title: v.name,
    //     value: v.id,
    //     key: v.id,
    //     children: v.children.map((vv: any) => {
    //       return { title: vv.name, value: vv.id, key: vv.id };
    //     }),
    //   };
    // });
    this.setState({ provinceMap });

    // });
  };

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  debDoSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        const body = { ...values };
        if (this.state.id) {
          body.id = this.state.id;
        }
        if (body.cityValue.province?.length && body.cityValue.city?.length) {
          body.location = body.cityValue.province + ',' + body.cityValue.city;
        } else if (body.cityValue.province?.length) {
          body.location = body.province;
        }
        if (body.location?.length) {
          body.location = '中国,' + body.location;
        }

        // if (values.area_names.indexOf('全局') > -1 || values.area_names.length === 0) {
        //   delete body.area_names;
        // } else {
        //   body.area_names = body.area_names.join(',');
        // }
        console.log('555555', body);
        api
          .createWaistcoatUser(body)
          .then(() => {
            message.success('操作成功');
            setMLoading(this, false);
            this.props.onEnd();
          })
          .catch(() => setMLoading(this, false));
      } else {
        console.log('111111', values);
        message.error('请检查表单内容');
      }
    });
  };

  getColumn = () => {
    return [
      {
        title: '新闻频道',
        key: 'channel',
        dataIndex: 'channel_name',
        width: 95,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
      },
    ];
  };

  articlesChange = (data: any) => {
    this.setState({ ...data });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };

    const values = this.props.form.getFieldsValue();

    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="用户昵称">
          {getFieldDecorator('nick_name', {
            initialValue: this.state.nick_name,
            rules: [
              {
                required: true,
                message: '请填写昵称',
              },
              {
                max: 16,
                message: '最多1-16个字符',
              },
            ],
          })(<Input placeholder="请输入昵称，最多1-16个字符" />)}
        </Form.Item>

        <Form.Item label="用户简介">
          {getFieldDecorator('information', {
            initialValue: this.state.information,
            rules: [
              {
                max: 30,
                message: '最多30个字符',
              },
            ],
          })(<Input placeholder="请输入简介，最多30个字符" />)}
        </Form.Item>

        <Form.Item label="绑定手机号">
          {getFieldDecorator('mobile', {
            initialValue: this.state.phone_number,
            rules: [
              {
                required: true,
                message: '请输入手机号',
              },
            ],
          })(<Input placeholder="请输入手机号" disabled={this.props.formContent} />)}
        </Form.Item>

        <Form.Item label="IP 属地">
          {getFieldDecorator('cityValue', {
            initialValue: this.state.cityValue,
            rules: [
              {
                required: true,
                message: '请选择IP属地',
                validator: (rule: any, value: any, callback: any) => {
                  console.log(value);
                  if (value?.province && value?.city) {
                    callback();
                  } else {
                    callback('请选择IP属地');
                  }
                },
              },
            ],
          })(<CitySelectRef provinceMap={this.state.provinceMap} />)}
        </Form.Item>

        <Form.Item label="头像" extra="支持jpg、jpeg、png图片格式，最大不能超过1MB">
          {getFieldDecorator('image_url', {
            initialValue: this.state.image_url,
          })(
            <ImageUploader
              ratio={1 / 1}
              accept={['image/jpeg', 'image/png', 'image/jpg']}
              imgsize={1024}
            />
          )}
        </Form.Item>
      </Form>
    );
  }
}

export default WaistcoatUserForm;
