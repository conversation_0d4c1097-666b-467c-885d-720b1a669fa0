import React from "react";
import { Drawer } from '@components/common';
import { Form, Input, TreeSelect, message, Popover, Icon, Table, Select, InputNumber } from 'antd';
import { systemApi, opApi } from '@app/api';
import { setMLoading } from '@utils/utils';
import connect from '@utils/connectSession';

const CodeRuleContent = (
  <Table
    dataSource={[
      {
        title: '启动页开屏广告',
        content: '固定：qdong_kaiping'
      },
      {
        title: '焦点图广告',
        content: '频道全/简拼+“_jiaodian”'
      },
      {
        title: '视频推荐位广告',
        content: '频道全/简拼+“_videorec”'
      },
      {
        title: '新闻汇总推荐位广告',
        content: '频道全/简拼+“_newssum”'
      },
      {
        title: 'feed流banner广告',
        content: '频道全/简拼+“_banner”+数字'
      },
      {
        title: 'feed流图文广告',
        content: '频道全/简拼+“_feed”+数字'
      },
    ]}
    columns={[
      {
        dataIndex: 'title',
        key: 'title',
      },
      {
        dataIndex: 'content',
        key: 'content',
      },
    ]}
    rowKey={(_, index) => `${index}`}
    showHeader={false}
    pagination={false}
    bordered />
)

@connect
@(Form.create({ name: 'BusinessAdCodeDrawer' }) as any)
export default class BusinessAdCodeDrawer extends React.Component<any, any> {

  state = {
    channelList: [],
    hzChannelList: [],
    codeList: [],
    loading: false,
  }

  handleOkClick = () => {
    const { form: { validateFields }, onEnd, record } = this.props
    validateFields((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        const { space } = values
        const params = { ...values }
        if (space === 'qdy') {
          params.space = ''
          params.space_type = 1
        }
        if (record && record.id) {
          params.id = record.id
          opApi.editCommercialAdCode(params).then(() => {
            setMLoading(this, false);
            message.success('修改成功');
            onEnd(true)
          }).catch(() => {
            setMLoading(this, false);
          })
        } else {
          opApi.addCommercialAdCode(params).then(() => {
            setMLoading(this, false);
            message.success('添加成功');
            onEnd(true)
          }).catch(() => {
            setMLoading(this, false);
          })
        }
      }
    })
  }

  mapToChannelListTreeData = (channel_list: any = []) => {
    return channel_list.map((item: any) => {
      return {
        title: item.name,
        value: item.id,
        key: item.id,
        children: this.mapToChannelListTreeData(item.children),
        disabled: item.children && item.children.length > 0
      }
    })
  }

  getChannelList = () => {
    systemApi
      .getAllChannelsTree()
      .then((r: any) => {
        const { data: { channel_list = [] } } = r
        const filterList = channel_list.filter((item: any) => item.name !== '潮客' && item.name !== '潮鸣号' && item.name !== '直播')
        const channelList = this.mapToChannelListTreeData(filterList)
        this.setState({ channelList, hzChannelList: [{ title: '启动页', value: 'qdy', key: 'qdy', children: [], disabled: false }, ...channelList] });
      })
      .catch(() => { });
  }

  getAdCodeList = () => {
    opApi.getAdCodeList({})
      .then(({ data }) => {
        const { list = [] } = data as any
        this.setState({
          codeList: list
        })
      })
      .catch(() => { })
  }

  handleSourceChange = (val: any) => {
    const { form: { setFieldsValue, getFieldsValue } } = this.props
    const { space } = getFieldsValue()
    if (val === 0 && space === 'qdy') {
      setFieldsValue({ space: undefined })
    }
    setFieldsValue({ code: undefined })
  }

  handelAdCodeSelect = (code: string) => {
    const { form: { setFieldsValue, getFieldsValue } } = this.props
    const { name } = getFieldsValue()
    if (!name) {
      const codeItem: any = this.state.codeList.find((item: any) => item.code === code)
      if (codeItem) {
        setFieldsValue({ name: codeItem.name })
      }
    }
  }

  componentDidUpdate(prevProps: Readonly<any>, prevState: Readonly<any>, snapshot?: any): void {
    const { visible, record, form: { setFieldsValue } } = this.props
    if (!prevProps.visible && visible) {
      setFieldsValue({ ad_type: record ? record.ad_type : undefined })
    }
  }

  componentDidMount() {
    this.getChannelList()
    this.getAdCodeList()
  }

  render() {
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const { record, visible, onClose, form: { getFieldDecorator, getFieldsValue } } = this.props
    const { ad_type = 0 } = getFieldsValue()

    return <Drawer
      title={`${record ? '编辑' : '创建'}广告位编码`}
      onClose={onClose}
      visible={visible}
      onOk={this.handleOkClick}
      skey={'BusinessAdCodeDrawer'}
    >
      <Form {...formLayout}>
        <Form.Item label="广告来源">
          {getFieldDecorator('ad_type', {
            rules: [
              {
                required: true,
                message: '请选择来源',
              },
            ],
            initialValue: record ? record.ad_type : undefined
          })(<Select placeholder="请选择来源" style={{ width: '570px' }} onChange={this.handleSourceChange}>
            <Select.Option value={0}>潮新闻</Select.Option>
            <Select.Option value={1}>洪泽</Select.Option>
          </Select>)}
        </Form.Item>
        <Form.Item label="广告位名称">
          {getFieldDecorator('name', {
            rules: [
              {
                required: true,
                message: '广告位名称不能为空',
              },
            ],
            initialValue: record ? record.name : ''
          })(<Input style={{ width: '570px' }} placeholder="请输入广告名称，最多100个字" maxLength={100} allowClear />)}
        </Form.Item>
        {
          ad_type === 0 && <Form.Item label="广告位编码">
            {getFieldDecorator('code', {
              rules: [
                {
                  required: true,
                  message: '广告位编码不能为空',
                },
              ],
              initialValue: record ? record.code : ''
            })(<Input style={{ width: '570px' }} placeholder="请输入广告编码，最多32个字符" maxLength={32} allowClear />)}
            &emsp;
            <Popover content={CodeRuleContent} title="注：广告位编码规则格式如下：" overlayStyle={{ width: '400px' }}>
              <Icon type="question-circle" />
            </Popover>
          </Form.Item>
        }
        {
          ad_type === 1 &&
          <Form.Item label="广告位编码">
            {getFieldDecorator('code', {
              rules: [
                {
                  required: true,
                  message: '广告位编码不能为空',
                },
              ],
              initialValue: record ? record.code : undefined
            })(
              <Select
                style={{ width: 570 }}
                placeholder="搜索广告位编码"
                onSelect={this.handelAdCodeSelect}
                showSearch>
                {this.state.codeList.map((item: any) => <Select.Option key={item.code} value={item.code}>{item.code}-{item.name}</Select.Option>)}
              </Select>
            )}
          </Form.Item>
        }
        {
          ad_type === 1 &&
          <Form.Item label="系统类型">
            {getFieldDecorator('os_type', {
              rules: [
                {
                  required: true,
                  message: '请选择系统类型',
                },
              ],
              initialValue: record && record.os_type ? record.os_type : undefined
            })(
              <Select
                style={{ width: 570 }}
                placeholder="请选择 iOS / 安卓">
                  <Select.Option value={2}>iOS</Select.Option>
                  <Select.Option value={1}>安卓</Select.Option>
              </Select>
            )}
          </Form.Item>
        }
        <Form.Item label="投放页面">
          {getFieldDecorator('space', {
            rules: [
              {
                required: true,
                message: '请选择频道',
              },
            ],
            initialValue: record ? (record.space || 'qdy') : undefined
          })(
            <TreeSelect
              treeData={ad_type === 1 ? this.state.hzChannelList : this.state.channelList}
              dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
              placeholder="请选择频道"
              treeDefaultExpandAll={false}
              style={{ width: '570px' }}
              allowClear
            />
          )}
        </Form.Item>
        {
          ad_type === 1 && <Form.Item label="广告位位置">
            {getFieldDecorator('position', {
              rules: [
                {
                  required: true,
                  message: '请输入广告位位置',
                },
              ],
              initialValue: record ? record.position : ''
            })(
              <InputNumber
                style={{ width: '570px' }}
                placeholder="请输入位置序号，最大99"
                min={1}
                max={99}
              />)}
          </Form.Item>
        }
      </Form>
    </Drawer>
  }
}