import React from "react";
import { Drawer, ImageUploader } from '@components/common';
import { Form, Input, message } from "antd";
import { setMLoading } from '@utils/utils';
import connect from '@utils/connectSession';
import { opApi } from '@app/api';

@connect
@(Form.create({ name: 'AddAdamantineDrawer' }) as any)
export default class AddAdamantineDrawer extends React.Component<any, any> {
  handleOkClick = () => {
    const { form: { validateFields }, onEnd, record } = this.props
    validateFields((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        if (record && record.id) {
          opApi.updateProposal({ ...values, id: record.id, type: 34 }).then(() => {
            setMLoading(this, false);
            message.success('修改成功');
            onEnd(true)
          }).catch(() => {
            setMLoading(this, false);
          })
        } else {
          opApi.createProposal({ ...values, type: 34 }).then(() => {
            setMLoading(this, false);
            message.success('添加成功');
            onEnd(true)
          }).catch(() => {
            setMLoading(this, false);
          })
        }
      }
    })
  }
  render() {
    const formLayout = {
      labelCol: { span: 3 },
      wrapperCol: { span: 19 },
    };
    const { record, visible, onClose, form: { getFieldDecorator } } = this.props
    return <Drawer
      title={`${record ? '编辑' : '新增'}金刚位`}
      onClose={onClose}
      visible={visible}
      onOk={this.handleOkClick}
      skey={'AddAdamantineDrawer'}
    >
      <Form {...formLayout}>
        <Form.Item label="名称">
          {getFieldDecorator('title', {
            rules: [
              {
                required: true,
                message: '名称不能为空',
              },
            ],
            initialValue: record ? record.title : ''
          })(<Input style={{ width: '570px' }} placeholder="最多输入5个字符" maxLength={5} allowClear />)}
        </Form.Item>
        <Form.Item label="图片" extra="图片比例为1:1 ，支持jpg, jpeg, png, gif格式">
          {getFieldDecorator('pic_url', {
            rules: [
              {
                required: true,
                message: '请上传图片',
              },
            ],
            initialValue: record ? record.pic_url : ''
          })(<ImageUploader ratio={1 / 1} />)}
        </Form.Item>
        <Form.Item label="链接">
          {getFieldDecorator('url', {
            rules: [
              {
                required: true,
                message: '链接不能为空',
              },
              {
                pattern: /^https?:\/\//,
                message: "请输入正确的链接格式"
              }
            ],
            initialValue: record ? record.url : ''
          })(<Input style={{ width: '570px' }} placeholder="请输入http、https开头的链接, 最多输入200个字符" maxLength={200} allowClear />)}
        </Form.Item>
      </Form>
    </Drawer>
  }
}