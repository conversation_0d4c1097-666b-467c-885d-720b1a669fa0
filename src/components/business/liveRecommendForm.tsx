import { releaseListApi } from '@app/api';
import ImageUploader from '@components/common/imageUploader';
import SearchAndInput from '@components/common/newsSearchAndInput';
import { Form, Icon, Input, InputNumber, message, Radio, Row, Tooltip, Checkbox } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';

@connectSession
@(Form.create({ name: 'liveRecommendForm' }) as any)
class LiveRecommendForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = { ...props.formContent };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        let api: keyof typeof releaseListApi = 'createLiveRecommend';
        console.log(1);
        if (this.state.id) {
          api = 'updateLiveRecommend';
        }
        console.log(2, api, releaseListApi[api]);
        console.log({
          ...values,
          [this.state.id ? 'id' : 'channel_id']: this.state.id || this.props.channelId,
          channel_article_ids: values.channel_article_ids.join(','),
        });
        try {
          releaseListApi[api]({
            ...values,
            [this.state.id ? 'id' : 'channel_id']: this.state.id || this.props.channelId,
            channel_article_ids: values.channel_article_ids.join(','),
          })
            .then(() => {
              setMLoading(this, false);
              message.success('操作成功');
              this.props.onEnd();
            })
            .catch(() => {
              setMLoading(this, false);
            });
        } catch (errr) {
          console.error(errr);
        }
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  getColumn = () => {
    return [
      {
        title: '频道',
        key: 'channel',
        dataIndex: 'channel_name',
        width: 95,
      },
      {
        title: 'id',
        dataIndex: 'id',
        width: 90,
      },
      {
        title: '新闻类型',
        key: 'type',
        dataIndex: 'doc_type_name',
        width: 90,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
      },
    ];
  };

  articlesChange = (data: any) => {
    this.setState({ ...data });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };

    const text = (
      <span>
        <p>输入说明：</p>
        <p>
          直接输入阿拉伯数字代表位置序号，例如要在头条频道信息流第8个位置插入该推荐位，则直接输入“8”即可。
        </p>
        <p>最大输入数字为500</p>
        <br />
        <p>注：</p>
        <p>直接在输入的指定位置上插入推荐位显示，不参与稿件的排序，且优先于媒立方稿件展示</p>
      </span>
    );
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="名称">
          {getFieldDecorator('title', {
            initialValue: this.state.title,
            rules: [
              {
                required: true,
                message: '请填写名称',
              },
              {
                max: 30,
                message: '名称最长不能超过30个字',
              },
            ],
          })(<Input placeholder="请输入名称" />)}
        </Form.Item>
        <Form.Item label="插入位置">
          {getFieldDecorator('position', {
            initialValue: this.state.position,
            rules: [
              {
                required: true,
                message: '请输入位置序号',
                type: 'number',
              },
              {
                max: 500,
                message: '最大不能超过500',
                type: 'number',
              },
            ],
          })(<InputNumber max={500} style={{ width: 200 }} placeholder="请输入位置序号" min={1} />)}
          &emsp;
          <Tooltip title={text}>
            <Icon type="question-circle" />
          </Tooltip>
        </Form.Item>
        <Form.Item label="关联直播">
          {getFieldDecorator('channel_article_ids', {
            initialValue: this.state.channel_article_ids,
            preserve: true,
            rules: [
              {
                required: true,
                message: '请关联新闻',
                type: 'array',
              },
              {
                max: 50,
                message: '最多关联50条新闻',
                type: 'array',
              },
              {
                min: 2,
                message: '为保证客户端显示效果，关联新闻数不能少于2条！',
                type: 'array',
              },
            ],
          })(
            <SearchAndInput
              max={50}
              func="channelTopSearch"
              columns={this.getColumn()}
              placeholder="输入ID或标题关联稿件"
              initialValues={{ list: this.state.channelArticles }}
              triggerInitialValueChange={this.articlesChange}
              body={{ channel_id: this.props.channelId, doc_types: '8' }}
              order={true}
              addOnTop
              afix={
                <Tooltip
                  title={
                    <div>
                      <p>若本频道的稿件被关联成功，将不再重复在APP的已签发信息流列表中显示。</p>
                      <p>
                        同理，本频道的稿件从推荐位上被取消关联后，稿件将重新按照原有序号以隐藏状态回到本频道的已签发列表中。
                      </p>
                      <p>能关联的稿件类型有:新闻，链接，图集，活动，视频，直播</p>
                      <p>为保证客户端显示效果，最少关联3条，最多关联50条。</p>
                    </div>
                  }
                >
                  <Icon type="question-circle" />
                </Tooltip>
              }
            />
          )}
        </Form.Item>
      </Form>
    );
  }
}

export default LiveRecommendForm;
