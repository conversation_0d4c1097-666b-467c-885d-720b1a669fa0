declare const BUILD_ENV: 'dev' | 'test' | 'prev' | 'prod' | 'testb';

import React, { forwardRef, useEffect } from 'react';
import './styles/FingerPaperPreview.scss';

const FingerPapperPreview = (props: any, ref: any) => {
  // https://test.tianmunews.com
  const host =
    {
      dev: 'https://tmtest.tidenews.com.cn',
      test: 'https://tmtest.tidenews.com.cn',
      prev: 'https://tmprev.tidenews.com.cn',
      prod: 'https://tidenews.com.cn',
      testb: 'https://tmtest.tidenews.com.cn',
    }[BUILD_ENV] || 'https://tmtest.tidenews.com.cn';

  useEffect(() => {
    let iframe = document.getElementById('iframe');
    const record = !!props.path
      ? {
          ...props.record,
        }
      : {
          ...props.record,
          content_list: JSON.parse(props.record?.content || '{}'),
        };
    iframe?.addEventListener(
      'load',
      function () {
        const iframeWindow = iframe?.contentWindow; // 利用iframe.contentWindow 也就是4.html 的window对象.
        setTimeout(() => {
          iframeWindow?.postMessage(JSON.stringify(record), host);
        }, 1000);
      },
      false
    );
  }, []);

  return (
    <div className="wrapper">
      <iframe
        id="iframe"
        src={`${host}/${props.path || 'finger_paper.html'}?inner=tidenews`}
      ></iframe>
    </div>
  );
};

export default forwardRef<any, any>(FingerPapperPreview);
