import React from 'react';
import { listApi, releaseListApi } from '@app/api';
import moment from 'moment';
import { Button, Modal, Table as ATable, Spin, message, Tooltip } from 'antd';
import connect from '@utils/connectSession';
import { requirePerm, searchToObject } from '@utils/utils';

@connect
class PushSubjectRecordModal extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      loading: false,
      list: [],
    };
  }

  componentDidUpdate(prevProps: Readonly<any>, prevState: Readonly<any>, snapshot?: any): void {
    if (!prevProps.visible && this.props.visible) {
      this.setState({ list: this.props.record?.ref_subjects || [] });
    }

    // if (prevProps.record !== this.props.record && this.props.record) {
    //   // 切换了稿件，请求稿件推频道信息
    //   const { id: article_id } = this.props.record;
    //   if (!article_id) {
    //     return;
    //   }
    //   this.loadList(article_id);
    // }
  }

  loadList(article_id: any) {
    this.setState({ loading: true });
    listApi
      .getToChannelList({ article_id })
      .then((r: any) => {
        // 这里返回的频道列表都是推过的，不能二次推
        const {
          data: { article, list = [] },
        } = r;
        if (article.id === article_id) {
          this.setState({ loading: false, list });
        } else {
          this.setState({ loading: false });
        }
      })
      .catch(() => {
        this.setState({ loading: false });
      });
  }

  handleCancelPub(record: any) {
    releaseListApi
      .revokeNews({ id: record.id, to_channel_id: record.channel_id })
      .then(() => {
        message.success('操作成功');
        const { id: article_id } = this.props.record;
        this.loadList(article_id);
      })
      .catch(() => {});
  }

  handleCancelAllPub() {
    const { id } = this.props.record;
    releaseListApi
      .revokeAllNews({ id })
      .then(() => {
        message.success('操作成功');
        const { id: article_id } = this.props.record;
        this.loadList(article_id);
      })
      .catch(() => {});
  }

  toMlf(id: number) {
    releaseListApi
      .toMlf('mlf_detail_url', { id })
      .then((r: any) => {
        window.open(r.data.url);
      })
      .catch((error) => {});
  }

  render() {
    const { visible, onCancel, skey, record } = this.props;
    const channel_id = searchToObject().channel_id;
    const that = this;
    const columns: any = [
      {
        title: '专题稿标题',
        key: 'list_title',
        dataIndex: 'list_title',
        render: (text: any, record: any) => {
          return (
            <Tooltip title={text}>
              <a
                className="line-max-2"
                onClick={() => {
                  this.toMlf(record.id);
                }}
              >
                {text}
              </a>
            </Tooltip>
          );
        },
      },
      {
        title: '所属频道',
        key: 'channel_name',
        dataIndex: 'channel_name',
        width: 100,
        align: 'center',
      },
      {
        title: '媒立方ID',
        key: 'metadata_id',
        dataIndex: 'metadata_id',
        width: 100,
        align: 'center',
      },
      {
        title: '潮新闻ID',
        key: 'id',
        dataIndex: 'id',
        width: 80,
        align: 'center',
      },
      {
        title: '签发时间',
        key: 'published_at',
        dataIndex: 'published_at',
        render: (text: any, record: any) =>
          text && <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 95,
        align: 'center',
      },
    ];
    const isAllUnused = !this.state.list.some((item: any) => item.pushed === 2);
    return (
      <Modal
        visible={visible}
        title="相关专题"
        width="600px"
        footer={null}
        onCancel={onCancel}
        key={skey}
      >
        <Spin tip="正在加载..." spinning={this.state.loading}>
          <ATable dataSource={this.state.list} pagination={false} columns={columns} rowKey="id" />
        </Spin>
      </Modal>
    );
  }
}

export default PushSubjectRecordModal;
