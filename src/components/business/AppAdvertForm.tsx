import { opApi as api } from '@app/api';
import SearchAndInput from '@components/common/newsSearchAndInput';
import { Form, Input, message, Radio, Select, TreeSelect } from 'antd';
import React from 'react';
import _debounce from 'lodash/throttle';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader, TwoInput } from '../common';

const InputGroup = Input.Group;
@connectSession
@(Form.create({ name: 'pushNotifyForm' }) as any)
class AppAdvert extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const form = {
      title: '',
      long: '',
      width: '',
      close_type: 1,
      close_second: 3,
      show_type: 2,
      close_text: '',
      show_position: 0,
    };
    this.state = {
      ...form,
      ...props.formContent,
    };
    // console.log(props.formContent);
    // console.log(this.state);
    this.state.width =
      (props.formContent.advert_pic_ratio && props.formContent.advert_pic_ratio.split(':')[0]) || 3;
    this.state.long =
      (props.formContent.advert_pic_ratio && props.formContent.advert_pic_ratio.split(':')[1]) || 4;
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        const body = { ...values };
        console.log(body);
        if (
          body.show_position == 0 &&
          values.advert_pic_ratio.width / values.advert_pic_ratio.long > 1
        ) {
          message.error('图片宽高比例不符合规范');
          setMLoading(this, false);
        } else {
          body.advert_pic_ratio =
            body.show_position == 0
              ? `${values.advert_pic_ratio.width}:${values.advert_pic_ratio.long}`
              : '1125:240';
          delete body.long;
          delete body.width;
          let func = 'creatAppadvertpage';
          if (this.state.id) {
            func = 'updateAppadvertpage';
            body.id = this.state.id;
          }
          api[func as keyof typeof api](body)
            .then(() => {
              message.success('操作成功');
              setMLoading(this, false);
              this.props.onEnd();
            })
            .catch(() => setMLoading(this, false));
        }
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  urlValidator = (rule: any, value: any, callback: any) => {
    const regex = /^https?:\/\//;
    if (value === '') {
      callback('请填写链接');
      return;
    }
    if (!regex.test(value)) {
      callback('请正确填写链接格式');
      return;
    }
    callback();
  };

  articlesChange = (data: any) => {
    this.setState({ ...data });
  };

  render() {
    const { getFieldDecorator, getFieldValue, getFieldsValue, setFieldsValue } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const values = this.props.form.getFieldsValue();

    const TwoInputValidator = (rule: any, value: any, callback: any) => {
      if (value.width === '') {
        callback('请填宽度数值');
        return;
      }
      if (value.long === '') {
        callback('请填高度数值');
        return;
      }
      callback();
    };

    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="显示位置">
          {getFieldDecorator('show_position', {
            initialValue: this.state.show_position ?? 0,
            rules: [
              {
                required: true,
                message: '请选择显示位置',
              },
            ],
          })(
            <Radio.Group
              onChange={() => {
                const values = getFieldsValue();
                setFieldsValue({ ...values, pic_url: undefined });
              }}
            >
              <Radio value={0}>中间</Radio>
              <Radio value={1}>底部</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item
          label="图标"
          extra={`支持jpg,jpeg,png,gif ${
            getFieldValue('show_position') == 1 ? ',推荐尺寸1125*240px' : ''
          }`}
        >
          {getFieldDecorator('pic_url', {
            initialValue: this.state.pic_url,
            rules: [
              {
                required: true,
                message: '请上传图标',
              },
            ],
          })(
            <ImageUploader
              ratio={getFieldValue('show_position') == 0 ? undefined : 1125 / 240}
              imgsize={1024}
              accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
            />
          )}
        </Form.Item>

        {getFieldValue('show_position') == 0 && (
          <InputGroup>
            <Form.Item label="广告图宽高比例">
              {getFieldDecorator('advert_pic_ratio', {
                initialValue: { width: this.state.width, long: this.state.long },
                rules: [
                  {
                    required: true,
                    message: '请填写广告图宽高比例',
                  },
                  { validator: TwoInputValidator },
                ],
              })(<TwoInput />)}
            </Form.Item>
          </InputGroup>
        )}

        <Form.Item label="广告主题">
          {getFieldDecorator('title', {
            initialValue: this.state.title,
            rules: [
              {
                required: true,
                message: '输入广告主题',
              },
              {
                max: 15,
                message: '广告主题不能超过15个字',
              },
            ],
          })(<Input placeholder="输入广告主题" />)}
        </Form.Item>
        <Form.Item label="广告链接">
          {getFieldDecorator('url', {
            initialValue: this.state.url,
            rules: [
              {
                required: true,
                message: '请填写链接',
              },
              {
                validator: this.urlValidator,
              },
            ],
          })(<Input placeholder="输入广告链接" />)}
        </Form.Item>

        <Form.Item label="关闭方式">
          {getFieldDecorator('close_type', {
            initialValue: this.state.close_type ?? 1,
            rules: [
              {
                required: true,
                message: '请选择关闭方式',
              },
            ],
          })(
            <Radio.Group>
              <Radio value={1}>自动关闭</Radio>
              <Radio value={0}>手动关闭</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {getFieldValue('close_type') == 1 && (
          <Form.Item label="关闭时间">
            {getFieldDecorator('close_second', {
              initialValue: this.state.close_second || 3,
              rules: [
                {
                  required: true,
                  message: '请选择关闭时间',
                },
              ],
            })(
              <Select style={{ width: 120, marginRight: 10 }}>
                {[3, 4, 5, 6, 7, 8, 9].map((item: any) => {
                  return (
                    <Select.Option key={item} value={item}>
                      {item}
                    </Select.Option>
                  );
                })}
              </Select>
            )}
            <span>秒</span>
          </Form.Item>
        )}

        <Form.Item label="出现次数">
          {getFieldDecorator('show_type', {
            initialValue: this.state.show_type ?? 2,
            rules: [
              {
                required: true,
                message: '请选择关闭方式',
              },
            ],
          })(
            <Radio.Group>
              <Radio value={2}>每次打开都显示</Radio>
              <Radio value={0}>每天1次</Radio>
              <Radio value={1}>总共1次</Radio>
            </Radio.Group>
          )}
        </Form.Item>

        {getFieldValue('show_position') == 0 && (
          <Form.Item label="关闭按钮文字">
            {getFieldDecorator('close_text', {
              initialValue: this.state.close_text,
              rules: [
                {
                  max: 2,
                  message: '最多两个字',
                  whitespace: true,
                },
              ],
            })(
              <Input placeholder="可设置任意词语，如“活动”、“广告”，最多2个字，不配置时默认显示为“关闭”" />
            )}
          </Form.Item>
        )}
      </Form>
    );
  }
}

export default AppAdvert;
