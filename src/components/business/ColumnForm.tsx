import { opApi as api } from '@app/api';
import { Checkbox, Form, Icon, Input, message, Radio, Tooltip } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader } from '../common';
import { ColorSelectButton } from './ColorSelectModal';

@connectSession
@(Form.create({ name: 'columnForm' }) as any)
class ColumnForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      ...props.formContent,
      classList: props.classList || [],
    };

    console.log('传递参数');
    console.log(props.formContent);
    console.log(this.state.is_studio);
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        let func = 'createColumn';
        const body = {
          ...values,
          type: 0,
        };
        if (values.colorInfo) {
          delete body.colorInfo;
          body.color = values.colorInfo.bottom_color;
          body.color_type = values.colorInfo.color_type;
        }
        if (this.state.id) {
          func = 'updateColumn';
          body.id = this.state.id;
        }
        api[func as keyof typeof api](body)
          .then(() => {
            setMLoading(this, false);
            message.success('操作成功');
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  render() {
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit} ref="form">
        <Form.Item label="栏目头像" extra="支持jpg,jpeg,png,gif图片格式，比例为 1:1">
          {getFieldDecorator('logo_url', {
            initialValue: this.state.logo_url,
            rules: [
              {
                required: true,
                message: '请上传话题头像',
              },
            ],
          })(
            <ImageUploader
              ratio={1 / 1}
              accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
            />
          )}
        </Form.Item>
        <Form.Item
          label={
            <span style={{ position: 'relative' }}>
              <Tooltip
                overlayStyle={{ maxWidth: 800 }}
                title={
                  <div>
                    <img width={500} height={271} src="/assets/column_bg_tips.png"></img>
                    <p>背景图规范</p>
                  </div>
                }
              >
                <Icon type="question-circle" style={{ position: 'absolute', left: -30, top: 0 }} />
              </Tooltip>
              背景图
            </span>
          }
          extra="支持jpg,jpeg,png图片格式，比例为 375:206"
        >
          {getFieldDecorator('background_url', {
            initialValue: this.state.background_url,
          })(<ImageUploader ratio={375 / 206} />)}
        </Form.Item>
        {getFieldValue('background_url') && (
          <Form.Item label="背景主色调" required>
            {getFieldDecorator(`colorInfo`, {
              initialValue: !!this.props.formContent
                ? {
                    color_type: this.state.color_type,
                    bottom_color: this.state.color,
                  }
                : {},
              rules: [
                {
                  validator(_: any, { bottom_color }: any, callback: any) {
                    if (bottom_color) {
                      callback();
                    } else {
                      callback(new Error('请选择背景色'));
                    }
                  },
                },
              ],
            })(
              <ColorSelectButton
                picUrl={getFieldValue('background_url')}
                colors={[
                  {
                    name: '橙色',
                    color: '#FF822C',
                  },
                  {
                    name: '蓝色',
                    color: '#0F63F8',
                  },
                  {
                    name: '黄色',
                    color: '#FFB12C',
                  },
                  {
                    name: '绿色',
                    color: '#5DB922',
                  },
                  {
                    name: '紫色',
                    color: '#8157FF',
                  },
                  {
                    name: '红色',
                    color: '#D71E0D',
                  },
                  {
                    name: '棕色',
                    color: '#BC792C',
                  },
                  {
                    name: '桃红色',
                    color: '#FF2C7A',
                  },
                  {
                    name: '青色',
                    color: '#03BDBD',
                  },
                  {
                    name: '红棕色',
                    color: '#A84B1F',
                  },
                ]}
                preview={(colorData: any) => {
                  return (
                    <div
                      style={{
                        position: 'relative',
                        background: 'white',
                        height: '127px',
                        overflow: 'hidden',
                      }}
                    >
                      <img
                        style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                        src={getFieldValue('background_url')}
                      ></img>
                      <div
                        style={{
                          position: 'absolute',
                          left: 0,
                          right: 0,
                          top: 0,
                          bottom: 0,
                          background: `linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 50%, rgba(0, 0, 0, 1) 100%), linear-gradient(to bottom, rgba(35, 85, 191, 0) 0%, ${
                            colorData.bottom_color || '#18579E'
                          } 60%, ${colorData.bottom_color || '#18579E'} 100%)`,
                        }}
                      ></div>
                      <div
                        style={{
                          position: 'absolute',
                          top: '60px',
                          left: 0,
                          right: 0,
                          height: '38.5px',
                          display: 'flex',
                          flexDirection: 'row',
                          alignItems: 'center',
                          padding: '0 9px',
                        }}
                      >
                        <img
                          style={{
                            flex: 'none',
                            width: '38.5px',
                            height: '38.5px',
                            objectFit: 'cover',
                            marginRight: '5px',
                            borderRadius: '10px',
                            overflow: 'hidden',
                          }}
                          src={getFieldValue('logo_url')}
                        ></img>
                        <div
                          style={{
                            flex: '1',
                            display: 'flex',
                            flexDirection: 'column',
                          }}
                        >
                          <span>{this.props.formContent?.record?.name}</span>
                          {!!this.props.formContent?.record?.description && (
                            <span
                              style={{
                                fontSize: '8px',
                              }}
                            >
                              {this.props.formContent?.record?.description}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                }}
              />
            )}
          </Form.Item>
        )}
        <Form.Item label="栏目名称">
          {getFieldDecorator('name', {
            initialValue: this.state.name,
            rules: [
              {
                required: true,
                message: '请填写话题名称',
              },
              {
                max: 15,
                message: '话题名称最长不能超过15个字',
              },
              {
                pattern: !/#/,
                message: '不能输入字符#',
              },
            ],
          })(<Input placeholder="请输入话题名称，最长15字" />)}
        </Form.Item>
        <Form.Item label="栏目简介">
          {getFieldDecorator('description', {
            initialValue: this.state.description,
            rules: [
              {
                max: 30,
                message: '话题简介最长不能超过30个字',
              },
            ],
          })(<Input.TextArea placeholder="请输入话题简介，最长30字" rows={3} />)}
        </Form.Item>
        <Form.Item label="栏目分类">
          {getFieldDecorator('class_ids', {
            initialValue: `${this.state.class_ids || ''}`,
            rules: [
              {
                required: true,
                message: '请选择栏目分类',
              },
            ],
          })(
            <Radio.Group style={{ width: '100%' }}>
              {this.state.classList.map((v: any, i: number) => (
                <Radio value={v.id.toString()} key={i}>
                  {v.name}
                </Radio>
              ))}
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item
          label={
            <span style={{ position: 'relative' }}>
              <Tooltip title="工作室栏目可被用户关注">
                <Icon type="question-circle" style={{ position: 'absolute', left: -30, top: 0 }} />
              </Tooltip>
              工作室栏目
            </span>
          }
        >
          {getFieldDecorator('is_studio', {
            initialValue: this.state.is_studio ? 1 : 0,
            rules: [
              {
                required: true,
                message: '请选择是否为工作室栏目',
              },
            ],
          })(
            <Radio.Group name="is_studio">
              <Radio value={0}>否</Radio>
              <Radio value={1}>是</Radio>
            </Radio.Group>
          )}
        </Form.Item>
      </Form>
    );
  }
}

export default ColumnForm;
