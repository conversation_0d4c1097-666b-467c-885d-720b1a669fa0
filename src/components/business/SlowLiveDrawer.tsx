import React from "react";
import { Drawer, ImageUploader } from '@components/common';
import { Spin, Form, message } from 'antd';
import { releaseListApi } from '@app/api';
import connect from '@utils/connectSession';
import Radio from "antd/es/radio";
import '@app/components/business/styles/business.scss'
import { setMLoading } from "@app/utils/utils";

@connect
@(Form.create({ name: 'LiveMoveArticleDrawer' }) as any)
export default class LiveMoveArticleDrawer extends React.Component<any, any> {
  state = {
    loading: false,
    belongs: 0,
    perface_pic: '',
    live_city_list: []
  }
  handleCityBelongChange = (e: any) => {
    this.setState({ belongs: e.target.value })
  }
  handleCityClick = (city: any) => {
    const live_city_list = this.state.live_city_list.map((item: any) => {
      item.selected = false
      return item
    })
    city.selected = true
    this.setState({
      live_city_list,
    })
    const { form: { setFields } } = this.props
    setFields({ belongs: { value: 1, errors: null } })
  }
  handleOkClick = () => {
    const { form: { validateFields }, onEnd } = this.props
    validateFields((err: any, values: any) => {
      if (!err) {
        const { record } = this.props
        const data: any = {
          id: record.id,
          belonged: values.belongs,
          perface_pic: values.perface_pic || ''
        }
        if (values.belongs) {
          const city: any = this.state.live_city_list.find((item: any) => item.selected)
          if (city) {
            data.city = city.name
          }
        }
        setMLoading(this, true)
        releaseListApi.configSlowLive(data)
          .then(() => {
            setMLoading(this, false);
            message.success('操作成功')
            onEnd()
          }).catch(() => {
            setMLoading(this, false);
          })
      }
    })
  }

  componentDidUpdate(prevProps: Readonly<any>, prevState: Readonly<any>, snapshot?: any): void {
    if (!prevProps.visible && this.props.visible) {
      // 请求详情数据
      const { record } = this.props
      if (record && record.id) {
        this.setState({ loading: true })
        releaseListApi.getSlowLiveConfig({ id: record.id, province: '浙江' })
          .then(({ data }) => {
            this.setState({
              loading: false,
              ...(data as any),
            })
          }).catch(() => {
            this.setState({ loading: false })
          })
      }
    }
  }

  render() {
    const { visible, onClose, skey, form } = this.props
    const { getFieldDecorator } = form
    const { belongs, perface_pic, live_city_list } = this.state
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Drawer
        title="慢直播设置"
        onClose={onClose}
        visible={visible}
        skey={skey}
        onOk={this.handleOkClick}
      >
        <Spin
          tip="正在加载..."
          spinning={this.state.loading}>
          <Form {...formLayout} >
            <Form.Item label="选择类型">
              {getFieldDecorator('belongs', {
                initialValue: belongs,
                rules: [
                  { required: true },
                  {
                    validator(rule: any, value: any, callback: any) {
                      if (value === 1 && live_city_list.every((item: any) => !item.selected)) {
                        callback('请选择所属城市')
                      } else {
                        callback()
                      }
                    }
                  }
                ],
              })(
                <Radio.Group onChange={this.handleCityBelongChange}>
                  <Radio value={0}>无所属城市</Radio>
                  <Radio value={1}>有所属城市</Radio>
                </Radio.Group>
              )}
              {
                belongs === 1 &&
                <div className='slow_live_city_list'>
                  {live_city_list.map((item: any) => (
                    <span key={item.code}
                      className={`custom_tag ${!item.enabled ? 'disabled' : (item.selected ? 'selected' : '')}`}
                      onClick={() => this.handleCityClick(item)}>
                      {item.name}
                    </span>
                  ))}
                </div>
              }
            </Form.Item>
            {belongs === 1 && <Form.Item label="慢直播卡片封面图" extra="支持jpg、jpeg、png、gif图片格式，最多不超过1兆">
              {getFieldDecorator('perface_pic', {
                initialValue: perface_pic,
                rules: [
                  {
                    required: true,
                    message: '请上传慢直播卡片封面图',
                  },
                ],
              })(<ImageUploader imgsize={1024} ratio={16 / 9} />)}
            </Form.Item>}
          </Form>
        </Spin>
      </Drawer>


    )
  }
}