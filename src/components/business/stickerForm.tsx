import { opApi as api } from '@app/api';
import { Checkbox, Form, Input, message, Radio } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader, FileUploader } from '../common';

@connectSession
@(Form.create({ name: 'stickerForm' }) as any)
class StickerForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      ...props.formContent,
    };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        let func = 'createSticker';
        const body = {
          ...values,
          type: this.props.type,
        };
        if (this.state.id) {
          func = 'updateSticker';
          body.id = this.state.id;
        }
        if (this.props.type === 2) {
          body.android_resource_url = values.ios_resource_url;
        }
        api[func as keyof typeof api](body)
          .then(() => {
            setMLoading(this, false);
            message.success('操作成功');
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="贴纸名称">
          {getFieldDecorator('name', {
            initialValue: this.state.name,
            rules: [
              {
                required: true,
                message: '请填写贴纸名称',
              },
              {
                max: 5,
                message: '贴纸名称最长不能超过5个字',
              },
            ],
          })(<Input placeholder="请输入贴纸名称" />)}
        </Form.Item>
        <Form.Item label="封面图" extra="支持jpg,jpeg,png图片格式，比例为 1:1">
          {getFieldDecorator('pic_url', {
            initialValue: this.state.pic_url,
            rules: [
              {
                required: true,
                message: '请上传封面图',
              },
            ],
          })(<ImageUploader ratio={1 / 1} />)}
        </Form.Item>
        {this.props.type === 2 ? (
          <Form.Item label="贴纸文件" extra="仅支持上传zip格式文件。">
            {getFieldDecorator('ios_resource_url', {
              initialValue: this.state.ios_resource_url,
              rules: [
                {
                  required: true,
                  message: '请上传iOS贴纸文件',
                },
              ],
            })(<FileUploader accept=".zip" />)}
          </Form.Item>
        ) : (
          <>
            <Form.Item label="【iOS】贴纸文件" extra="仅支持上传zip格式文件。">
              {getFieldDecorator('ios_resource_url', {
                initialValue: this.state.ios_resource_url,
                rules: [
                  {
                    required: true,
                    message: '请上传iOS贴纸文件',
                  },
                ],
              })(<FileUploader accept=".zip" />)}
            </Form.Item>
            <Form.Item label="【安卓】贴纸文件" extra="仅支持上传zip格式文件。">
              {getFieldDecorator('android_resource_url', {
                initialValue: this.state.android_resource_url,
                rules: [
                  {
                    required: true,
                    message: '请上传安卓贴纸文件',
                  },
                ],
              })(<FileUploader accept=".zip" />)}
            </Form.Item>
          </>
        )}
      </Form>
    );
  }
}

export default StickerForm;
