import { Button, Col, Input, Modal, Row, Tooltip, message } from 'antd';
import Radio from 'antd/es/radio';
import React, { useEffect, useState } from 'react';
import { A } from '../common';
import { releaseListApi as api } from '@app/api';

const defaultColors = [
  {
    name: '橄榄黄',
    color: '#A99E00',
  },
  {
    name: '翠绿',
    color: '#77B000',
  },
  {
    name: '橄榄绿',
    color: '#246227',
  },
  {
    name: '薄荷绿',
    color: '#009559',
  },
  {
    name: '朱红色',
    color: '#D7443B',
  },
  {
    name: '棕红色',
    color: '#AF6737',
  },
  {
    name: '棕黄色',
    color: '#B6830D',
  },
  {
    name: '橘黄色',
    color: '#FF8100',
  },
  {
    name: '蓝紫色',
    color: '#1E3AFF',
  },
  {
    name: '蓝灰色',
    color: '#4E7C92',
  },
  {
    name: '紫色',
    color: '#603EED',
  },
  {
    name: '蓝色',
    color: '#008FF6',
  },
];

export default function ColorSelectModal(props: any) {
  const { visible, onCancel, colorData, onEnd, colorDataIndex, preview, colors } = props;
  const colorMap = colors || defaultColors;
  const [colorRecord, setColorRecord] = useState(colorData);
  let { color_type = 1, bottom_color = '' } = colorRecord;
  if (props.onlyManual) {
    color_type = 0;
  }
  useEffect(() => {
    if (visible) {
      const { color_type = 1, bottom_color = '', auto_color = '' } = props.colorData;
      setColorRecord({
        ...props.colorData,
        bottom_color: props.onlyManual ? bottom_color : (color_type === 1 ? auto_color : bottom_color),
      });
    }
  }, [visible]);

  const colorIsValidate = () => /^#[\d|a-f|A-Z]{6}$/.test(colorRecord.bottom_color);

  const handleColorTypeChange = (e: any) => {
    const color_type = e.target.value;
    setColorRecord({
      ...colorRecord,
      color_type,
      bottom_color: color_type === 1 ? colorRecord.auto_color : colorRecord.bottom_color,
    });
  };

  const onOk = () => {
    if (colorIsValidate()) {
      onEnd(colorDataIndex, color_type, bottom_color);
    } else {
      message.error('请输入正确的色值');
    }
  };

  return (
    <Modal
      visible={visible}
      title="选择颜色"
      width={480}
      onCancel={onCancel}
      onOk={onOk}
      destroyOnClose={true}
      maskClosable={false}
    >
      {!props.onlyManual && (
        <Radio.Group
          value={color_type}
          style={{ display: 'flex', justifyContent: 'space-between', width: 200 }}
          onChange={handleColorTypeChange}
        >
          <Radio value={1}>智能取色</Radio>
          <Radio value={0}>人工取色</Radio>
        </Radio.Group>
      )}
      {color_type === 0 && (
        <Row style={{ marginTop: 15 }}>
          <Col span={5}>推荐色调</Col>
          <Col span={19}>
            <Radio.Group
              value={bottom_color}
              onChange={(e) => setColorRecord({ ...colorRecord, bottom_color: e.target.value })}
            >
              {colorMap.map((item: any) => {
                return (
                  <Radio key={item.color} value={item.color} style={{ width: 74 }}>
                    {item.name}
                  </Radio>
                );
              })}
            </Radio.Group>
          </Col>
        </Row>
      )}
      <Row style={{ marginTop: 15 }}>
        <Col span={5} style={{ lineHeight: '32px' }}>
          {color_type === 0 ? '自定义色值' : '色值'}
        </Col>
        <Col span={19} style={{ display: 'flex', alignItems: 'center' }}>
          <Input
            value={bottom_color}
            style={{ width: 200, marginRight: 12 }}
            onChange={(e) => setColorRecord({ ...colorRecord, bottom_color: e.target.value })}
            disabled={color_type !== 0}
          />
          <Tooltip title={preview(colorRecord)}>
            <A disabled={!colorIsValidate()}>预览</A>
          </Tooltip>
        </Col>
      </Row>
      {color_type === 1 && (
        <div style={{ marginTop: 20, color: '#aaa', fontSize: 13 }}>
          注：系统根据图片整体色彩自动取相适应的颜色
        </div>
      )}
    </Modal>
  );
}

export class ColorSelectButton extends React.Component<any> {
  state = {
    showColorModal: false,
    colorData: {},
  };

  componentDidUpdate(prevProps: any) {
    if (this.props.picUrl !== prevProps.picUrl) {
      // 当 someProp 发生变化时执行相应的操作
      this.props.onChange({});
    }
  }

  handleColorButtonClick = async () => {
    const { picUrl, value } = this.props;
    const colorData = { ...value };
    const { auto_color } = colorData;
    if (!auto_color && !this.props.onlyManual) {
      // 请求自动取色接口
      const { data } = await api.contentRecommendGetColor({ url: picUrl });
      if (data?.color) {
        const { color } = data as any;
        colorData.auto_color = color.indexOf('0x') >= 0 ? `#${color?.substr(2)}` : color;
      }
    }
    this.setState({
      showColorModal: true,
      colorData,
    });
  };

  editColorClose = () => {
    this.setState({
      showColorModal: false,
      colorData: {},
    });
  };

  editColorEnd = (index: number, color_type: string, bottom_color: string) => {
    const { auto_color } = this.state.colorData as any;
    this.props.onChange({
      color_type,
      auto_color,
      bottom_color,
    });
    this.editColorClose();
  };

  render(): React.ReactNode {
    console.log('props', this.props);

    const { picUrl, value, preview } = this.props;
    const { showColorModal, colorData } = this.state;
    const { bottom_color = '' } = value || {};
    return (
      <>
        <ColorSelectModal
          onlyManual={this.props.onlyManual}
          preview={preview}
          visible={showColorModal}
          colorData={colorData}
          onEnd={this.editColorEnd}
          onCancel={this.editColorClose}
          colors={this.props.colors}
        />
        <div style={{ display: 'flex', alignItems: 'center', marginTop: 4 }}>
          {bottom_color && (
            <span
              style={{
                width: 20,
                height: 20,
                marginRight: 5,
                backgroundColor: bottom_color,
              }}
            ></span>
          )}
          <Button
            disabled={!this.props.onlyManual && !picUrl}
            onClick={this.handleColorButtonClick}
          >
            {bottom_color ? '修改' : '选择'}颜色
          </Button>
        </div>
      </>
    );
  }
}
