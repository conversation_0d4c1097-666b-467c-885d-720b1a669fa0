import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Icon, Select, Modal, Row, Col, message, Tooltip } from 'antd';
import connectSession from '@utils/connectSession';
import SearchAndInput from '@components/common/newNewsSearchAndInput';
import { userApi as api } from '@app/api';
import { cloneDeep, reject, xor } from 'lodash';
import { setMLoading } from '@utils/utils';
import 'emoji-mart/css/emoji-mart.css';
import { Picker } from 'emoji-mart';
import { resolve } from 'path';

// const { Title } = Typography;
let count = 0;
const { confirm } = Modal;
@connectSession
@(Form.create({ name: 'CommentPresuppositionForm' }) as any)
class CommentPresuppositionForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const { formContent } = props;
    this.state = {
      ...props,
      pickerConfig: {
        Show: false,
        top: '0px',
        right: '300px',
      },
      inputId: 0,
      form: {
        channelArticles: [],
        content: [],
      },
      commentConfig: [
        {
          id: 0,
          content: '',
        },
      ],
    };
    count = 0;
    if (formContent.id) {
      this.getInfo();
    }
  }

  componentDidMount() {
    const { pickerConfig } = this.state;
    // document.onclick = () => {
    //   this.setState({
    //     pickerConfig: {
    //       ...pickerConfig,
    //       Show: false,
    //     },
    //   });
    // };
  }

  getInfo = () => {
    const { formContent } = this.state;
    api.getInfoCommentPreset({ id: formContent.id }).then((res: any) => {
      console.log(res.data.comment_preset_infos);
      const ary: any = [];
      const inputAry: any = [];
      const channelArticles = res.data.comment_preset_infos;
      channelArticles.forEach((x: any) => {
        ary.push({
          tipId: x.id,
          id: x.channel_article_id,
          channel_name: x.channel_name,
          list_title: x.channel_article_title,
        });
      });
      try {
        JSON.parse(formContent.content).forEach((x: any) => {
          inputAry.push({
            id: count,
            content: x,
          });
          count += 1;
        });
      } catch (error) {
        inputAry.push({
          id: count,
          content: formContent.content,
        });
      }

      this.setState({
        form: {
          plan_name: formContent.plan_name,
          id: formContent.id,
          channelArticles: ary,
        },
        commentConfig: inputAry,
      });
    });
  };

  doSubmit = () => {
    const { formContent } = this.state;
    if (formContent.disabled) return this.props.onClose();
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const data: any = {};
        data.draft_info = [];
        values.channelArticles.forEach((x: any) => {
          data.draft_info.push({
            channelArticleId: x.id,
            channelArticleTitle: x.list_title,
          });
        });
        data.draft_info = JSON.stringify(data.draft_info);
        data.plan_name = values.plan_name;
        const filterContent = values.content.filter((item: any) => {
          return !!item;
        });
        data.content = JSON.stringify(filterContent);

        if (formContent.comment_preset_id) {
          data.comment_preset_id = formContent.comment_preset_id;
          api
            .updateCommentPreset(data)
            .then((r) => {
              message.success('修改成功');
              setMLoading(this, false);
              this.props.onEnd();
            })
            .catch(() => {
              setMLoading(this, false);
            });
        } else {
          api
            .createCommentPreset(data)
            .then((r) => {
              message.success('新建成功');
              setMLoading(this, false);
              this.props.onEnd();
            })
            .catch(() => {
              setMLoading(this, false);
            });
        }
      } else {
        message.error('请检查表单内容');
      }
    });
    return true;
  };

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  getColumn = () => {
    return [
      {
        title: '序号',
        dataIndex: 'eq',
        width: 70,
        render: (_: any, v: any, i: number) => i + 1,
      },
      {
        title: '潮新闻ID',
        dataIndex: 'id',
        width: 80,
      },
      {
        title: '新闻频道',
        key: 'type',
        dataIndex: 'channel_name',
        width: 90,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
      },
    ];
  };

  PickerEmoji = (e: any, data: any) => {
    console.log(e.target);
    const element = document.getElementsByClassName('rox-drawer-content');
    e.nativeEvent.stopImmediatePropagation();
    const { top } = e.target.getBoundingClientRect();
    console.log(top);
    const { pickerConfig } = this.state;
    this.setState({
      inputId: data.id,
      pickerConfig: {
        ...pickerConfig,
        top: top - 50 + element[0].scrollTop,
        show: !pickerConfig.show,
      },
    });
  };

  addEmoji = (emoji: any) => {
    const { commentConfig, inputId } = this.state;
    let text = '';
    const fileObje = commentConfig.map((temp: any) => {
      const obj = cloneDeep(temp);
      if (obj.id === inputId) {
        obj.content += emoji.native;
        text = obj.content;
      }
      return obj;
    });
    this.setState({
      commentConfig: fileObje,
    });

    this.props.form.setFieldsValue({
      [`content[${inputId}]`]: text,
    });
  };

  inputChange = (e: any, t: any) => {
    const { commentConfig } = this.state;
    this.setState({
      commentConfig: commentConfig.map((temp: any) => {
        const obj = cloneDeep(temp);
        if (obj.id === t.id) {
          obj.content = e.target.value;
        }
        return obj;
      }),
    });
  };

  addInput = () => {
    const { commentConfig } = this.state;
    count += 1;
    this.setState({
      commentConfig: [
        ...commentConfig,
        {
          id: count,
          content: '',
        },
      ],
    });
  };

  removeInput = (t: any) => {
    const { commentConfig } = this.state;
    const arrayConment: any = [];
    commentConfig.forEach((data: any, i: number) => {
      if (data.id !== t.id) {
        arrayConment.push(data);
      }
    });
    console.log(arrayConment);
    this.setState({
      commentConfig: arrayConment,
    });
  };

  // 删除关联稿件
  searchAndInputOnChange = (e: any, deleteObj: any) => {
    console.log(deleteObj);
    if (deleteObj && Object.keys(deleteObj).length > 0) {
      if (deleteObj.tipId) {
        const that = this;
        console.log(deleteObj.tipId);
        confirm({
          title: '确认删除吗？',
          onOk() {
            setMLoading(that, true);
            api
              .deleteOneCommentPreset({ id: deleteObj.tipId })
              .then((res) => {
                message.success('删除成功');
                deleteObj.callBack();
                setMLoading(that, false);
              })
              .catch(() => {
                setMLoading(that, false);
              });
          },
          onCancel() {},
        });
      } else {
        deleteObj.callBack();
      }
    }
  };

  render() {
    const that = this;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 16 },
    };
    const { getFieldDecorator } = this.props.form;
    const { form, formContent, commentConfig, pickerConfig } = this.state;
    const text2 = <p>只能关联直播稿，最多15条</p>;
    // 验证 稿件是否被关联过
    const validatorFn = async (val: any) => {
      const res: any = await api.getChannelArticleIdExistsCommentPreset({
        channel_article_id: val.id,
      });
      if (res.data.channel_article_id_exists) {
        return '此稿件已配置评论预设';
      }
      return false;
    };

    return (
      <>
        <Form {...formLayout} onSubmit={this.handleSubmit} style={{ position: 'relative' }}>
          <Form.Item label="方案名">
            <Col span={14}>
              {getFieldDecorator('plan_name', {
                initialValue: form.plan_name,
                rules: [
                  {
                    required: true,
                    message: '方案名不能为空',
                  },
                  {
                    max: 15,
                    message: '最多15个字',
                  },
                ],
              })(
                <Input
                  disabled={formContent.disabled}
                  placeholder="请输入提示语方案名 至多15个汉字"
                />
              )}
            </Col>
          </Form.Item>
          <Form.Item label="关联稿件">
            {getFieldDecorator('channelArticles', {
              initialValue: form.channelArticles,
              preserve: false,
              rules: [
                {
                  required: true,
                  message: '请关联新闻',
                  type: 'array',
                },
                {
                  max: 15,
                  message: '最多关联15条新闻',
                  type: 'array',
                },
                {
                  min: 1,
                  message: '为保证客户端显示效果，关联新闻数不能少于1条！',
                  type: 'array',
                },
              ],
            })(
              <SearchAndInput
                max={15}
                disabled={formContent.disabled}
                func="conmentSearch"
                columns={this.getColumn()}
                placeholder="输入ID或标题关联稿件"
                body={{ doc_types: '8' }}
                order={false}
                addOnTop={true}
                validator={validatorFn}
                onChange={this.searchAndInputOnChange}
                onChangeCallback={true}
                afix={
                  <Tooltip title={text2}>
                    <Icon type="question-circle" />
                  </Tooltip>
                }
              />
            )}
          </Form.Item>
          {commentConfig.map((data: any, index: number) => {
            return (
              <Form.Item label="预设评论" key={data.id}>
                <Col span={14}>
                  {getFieldDecorator(`content[${data.id}]`, {
                    initialValue: data.content,
                    rules: [
                      {
                        required: true,
                        message: '预设评论不能为空',
                      },
                      {
                        max: 20,
                        message: '最多20个字',
                      },
                    ],
                  })(
                    <Input
                      disabled={formContent.disabled}
                      placeholder="请输入预设评论至多20个汉字"
                      onChange={(e) => this.inputChange(e, data)}
                      addonAfter={
                        <Icon
                          onClick={(e) => {
                            if (!formContent.disabled) {
                              this.PickerEmoji(e, data);
                            }
                          }}
                          type="smile"
                        />
                      }
                    />
                  )}
                </Col>
                <Col span={2} />
                <Col span={8}>
                  {commentConfig.length > 1 && !formContent.disabled && (
                    <Button type="danger" size="small" onClick={() => this.removeInput(data)}>
                      删除
                    </Button>
                  )}
                </Col>
              </Form.Item>
            );
          })}
          {!formContent.disabled && commentConfig.length < 10 && (
            <Form.Item>
              <Col span={9} />
              <Col span={6}>
                <Button disabled={formContent.disabled} type="primary" onClick={this.addInput}>
                  <Icon type="plus" />
                  增加预设评论
                </Button>
              </Col>
              <Col span={9} />
            </Form.Item>
          )}
          {pickerConfig.show && (
            <Picker
              title="表情包"
              native={false}
              onSelect={this.addEmoji}
              showPreview={false}
              showSkinTones={false}
              emojiTooltip={true}
              style={{ position: 'absolute', top: pickerConfig.top, right: pickerConfig.right }}
              i18n={{
                search: '搜索表情符号',
                categories: {
                  search: '搜索结果',
                  recent: '常用',
                  people: '标签符号与人物',
                  nature: '动物与自然',
                  foods: '食物与饮料',
                  activity: '活动',
                  places: '旅行与地点',
                  objects: '物体',
                  symbols: '符号',
                  flags: '旗帜',
                  custom: '习俗',
                },
              }}
            />
          )}
        </Form>
      </>
    );
  }
}

export default CommentPresuppositionForm;
