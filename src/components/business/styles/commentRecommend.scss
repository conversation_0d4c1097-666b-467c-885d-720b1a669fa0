.comment-recommend-form {
  .ant-table-tbody > tr > td,
  .ant-table-thead > tr > th {
    padding: 6px 8px !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .ant-table-thead > tr > th {
    padding: 8px 8px !important;
  }

  .row-selected,
  .row-selected:hover,
  td.row-selected:hover,
  .row-selected > td:hover,
  .ant-table-tbody > .row-selected:hover:not(.ant-table-expanded-row),
  .ant-table-tbody > .row-selected:hover:not(.ant-table-expanded-row) > td {
    background-color: #1890ff !important;
  }

  .ellipsis-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 550px;
  }

  .ellipsis-comment {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .multi-ellipsis-comment {
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
    text-overflow: ellipsis;
    white-space: normal;
  }

  .delete-btn {
    color: #ddd;
    transition: all linear 0.3s;
  }

  .delete-btn:hover {
    color: #1890ff;
  }
}

.comment-recommend-form-comments {
  .ant-table-tbody > tr > td,
  .ant-table-thead > tr > th {
    padding: 4px 8px !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .ant-table-thead > tr > th {
    padding: 8px 8px !important;
  }

  .ellipsis-comment {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .ellipsis-article-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    max-width: 600px;
    vertical-align: top;
  }
}
