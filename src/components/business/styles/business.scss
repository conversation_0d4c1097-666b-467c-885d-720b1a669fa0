.ugc_topic_form_weditor {
  .w-e-toolbar {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }

  .w-e-text-container {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;

    p {
      font-size: 14px !important;
    }
  }
}

.gpt_weditor {
  .w-e-text-container {
    height: 300px !important;
  }
}

.gpt-tooltip {
  .ant-tooltip-content {
    max-height: 500px;
    overflow: auto;
  }
}

.slow_live_city_list {
  display: flex;
  flex-wrap: wrap;
  width: 340px;

  span {
    margin-right: 10px;
    margin-bottom: 10px;
    padding: 0 18px;
    white-space: nowrap;
    border: 1px solid #ccc;
    line-height: 26px;
    cursor: pointer;
  }
}

.custom_tag {
  &.disabled {
    cursor: default;
    pointer-events: none;
    color: #ddd;
    border-color: #ddd;
  }

  &.selected {
    color: #1890ff;
    border-color: #1890ff;
  }
}

.ugcTopicSort .ant-form-item-children,
.ugcTopicSort .ant-form-item-children .ant-row {
  display: flex;
  height: 40px;
}

.ugcTopicSort .ant-select-selection {
  margin-top: 4px;
}

.report_expert_preview {
  background-color: red;
  padding: 0 8px;
  height: 25px;
  line-height: 25px;
  border-top-right-radius: 10px;
  border-bottom-left-radius: 10px;
}

.report_expert_bg_preview {
  width: 166px;
  height: 144px;
  background: linear-gradient(180deg, #FFF4ED 0%, #FFFFFF 100%);
  border-radius: 6px;
  border: 1px solid #FFFFFF;
  display: flex;
  flex-direction: column;
  align-items: center;

  padding: 15px 0 0;

  img {
    width: 42px;
    height: 42px;
  }

  .title {
    margin-top: 10px;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #000000;
    line-height: 20px;
  }

  .desc {
    margin-top: 25px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 10px;
    color: #999999;
    line-height: 14px;
    text-align: center;
  }

}