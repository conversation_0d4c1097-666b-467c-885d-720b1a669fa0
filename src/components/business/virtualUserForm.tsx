import { userApi as api } from '@app/api';
import { Checkbox, Form, Input, message, Modal, Radio, Select } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader } from '@components/common';

@connectSession
@(Form.create({ name: 'virtualUserForm' }) as any)
class VirtualUserForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      type: props.type,
    };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        const body = {
          ...values,
          type: this.state.type,
          tmh_class_ids: values.tmh_class_ids || '',
        };
        api
          .createVirtualUser(body)
          .then((res: any) => {
            setMLoading(this, false);
            Modal.success({
              title: `${this.state.type === 1 ? '潮鸣号' : '邀请码'}创建成功`,
              content: (
                <p>
                  {res.data.exist_info && (
                    <p style={{ color: 'red' }}>{res.data.exist_info} 已存在，为您重新生成了一个</p>
                  )}
                  <p>昵称：{res.data.nick_name}</p>
                  <p>虚拟号：{res.data.phone_number}</p>
                  <p>邀请码：{res.data.ref_code}</p>
                  {this.state.type === 1 && <p>密码：{res.data.pwd}</p>}
                </p>
              ),
              onOk: () => {
                this.props.onEnd();
              },
            });
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  render() {
    console.log('x', this.props);
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit} ref="form">
        <Form.Item label="用户昵称">
          {getFieldDecorator('nick_name', {
            initialValue: '',
            rules: [
              {
                required: true,
                message: '请填写用户昵称',
              },
              {
                max: 16,
                message: '用户昵称不能多于16个字',
              },
            ],
          })(<Input placeholder="1-16个字符" />)}
        </Form.Item>
        <Form.Item label="虚拟号">
          {getFieldDecorator('phone_number', {
            initialValue: '',
            rules: [
              {
                required: true,
                message: '请填写虚拟号',
              },
              {
                pattern: /^([0-9]{11})|(tm[a-zA-Z0-9_]{4,16})$/,
                message: '6-18位以tm开头字符，只能包含英文字母、数字、下划线；或11位虚拟手机号',
              },
            ],
          })(
            <Input placeholder="6-18位以tm开头字符，只能包含英文字母、数字、下划线；或11位虚拟手机号" />
          )}
        </Form.Item>
        <Form.Item label="邀请码">
          {getFieldDecorator('ref_code', {
            initialValue: '',
            rules: [
              {
                pattern: /^[0-9a-zA-Z]+$/,
                message: '邀请码只能包含数字或字母',
              },
              {
                min: 6,
                message: '邀请码长度不能小于6位',
              },
              {
                max: 15,
                message: '邀请码长度不能大于15位',
              },
            ],
          })(<Input placeholder="6-15位数字或字母" />)}
        </Form.Item>
        {this.state.type === 1 && (
          <Form.Item label="密码">
            {getFieldDecorator('pwd', {
              initialValue: '',
              rules: [
                {
                  required: true,
                  message: '请填写密码',
                },
                {
                  pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]+$/,
                  message: '密码仅支持数字和字母的组合',
                },
                {
                  min: 8,
                  message: '密码长度不能小于8位',
                },
                {
                  max: 30,
                  message: '密码长度不能大于30位',
                },
              ],
            })(<Input placeholder="密码为8-30位，仅支持数字和字母的组合" />)}
          </Form.Item>
        )}
        {this.state.type === 1 && (
          <Form.Item label="认证类型">
            {getFieldDecorator('cert_type', {
              initialValue: 1,
              rules: [
                {
                  required: true,
                  message: '请选择认证类型',
                },
              ],
            })(
              <Radio.Group>
                <Radio value={1}>个人认证</Radio>
                <Radio value={2}>机构认证</Radio>
              </Radio.Group>
            )}
          </Form.Item>
        )}
        {this.state.type === 1 && (
          <Form.Item label="认证信息">
            {getFieldDecorator('cert_information', {
              initialValue: '',
              rules: [
                {
                  required: true,
                  message: '请填写认证信息',
                },
                {
                  max: 25,
                  message: '认证信息不能超25个字',
                },
              ],
            })(<Input placeholder="最多25字" />)}
          </Form.Item>
        )}
        {this.state.type === 1 && (
          <Form.Item label="账号头像" extra="支持jpg,jpeg,png图片格式， 比例为1:1">
            {getFieldDecorator('image_url', {
              initialValue: '',
            })(
              <ImageUploader
                ratio={1 / 1}
                accept={['image/jpeg', 'image/png', 'image/jpg']}
                imgsize={10 * 1024}
              />
            )}
          </Form.Item>
        )}
        {this.state.type == 1 && (
          <Form.Item label="分类">
            {getFieldDecorator('tmh_class_ids', {
              // initialValue: undefined,
            })(
              <Radio.Group>
                {this.props.classList.map((v: any) => (
                  <Radio value={v.id} key={v.id}>
                    {v.name}
                  </Radio>
                ))}
              </Radio.Group>
            )}
          </Form.Item>
        )}
      </Form>
    );
  }
}

export default VirtualUserForm;
