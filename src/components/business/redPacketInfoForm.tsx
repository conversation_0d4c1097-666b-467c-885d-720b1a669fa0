/* eslint-disable max-classes-per-file */
import { opApi as api, searchApi } from '@app/api';
import { A, CardEditor } from '@components/common';

import {
  Checkbox,
  Form,
  Input,
  message,
  Select,
  Table,
  InputNumber,
  Row,
  Spin,
  Switch,
  Modal,
  Radio,
} from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader } from '../common';

import './styles/redPacketForm.scss';

class SelectedRedPacket extends React.Component<any, any> {
  static getDerivedStateFromProps(nextProps: any) {
    if ('value' in nextProps) {
      return { value: nextProps.value || [] };
    }
    return null;
  }

  constructor(props: any) {
    super(props);
    this.state = {
      value: props.value || [],
    };
  }

  render() {
    return <Table dataSource={this.state.value} pagination={false} columns={this.props.columns} />;
  }
}

@connectSession
@(Form.create({ name: 'redPacketForm' }) as any)
class RedPacketForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      ...props.formContent,
      redPacketList: [],
      loading: false,
      fetching: false,
    };
  }

  componentDidMount() {
    this.getPacketList();
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  getPacketList = () => {
    this.setState({ loading: true });
    api
      .getQYRedPacketList({})
      .then((res: any) => {
        this.setState({
          redPacketList: res.data.qyRedPacketList,
        });
      })
      .catch(() => {
        this.setState({ loading: false });
      });
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        let func = 'createRedPacket';
        const body = {
          ...values,
        };
        if (!body.rule) {
          body.rule = '';
        }
        delete body.redPackeShow;
        if (this.state.id) {
          body.id = this.state.id;
        }
        body.qyRedPacketId = this.state.qyRedPacketId;
        console.log('----------表单数据----------', body);
        setMLoading(this, true);
        api
          .updateRedPacket(body)
          .then(() => {
            message.success('操作成功');
            setMLoading(this, false);
            this.props.onEnd();
          })
          .catch(() => setMLoading(this, false));
      } else {
        message.error('请检查表单内容');
      }
    });
  };
  // doSubmit = () => {
  //   const submitQuery = (func: any, body: any) => {
  //     api[func as keyof typeof api](body)
  //       .then(() => {
  //         setMLoading(this, false);
  //         message.success('操作成功');
  //         this.props.onEnd();
  //       })
  //       .catch(() => {
  //         setMLoading(this, false);
  //       });
  //   };
  // };

  getCloudRedPacketColumns = (hideGive: boolean = false) => {
    return [
      {
        title: '红包名称',
        key: 'name',
        dataIndex: 'name',
      },
      {
        title: '红包状态',
        key: 'activityStatusStr',
        dataIndex: 'activityStatusStr',
      },
      {
        title: '操作',
        key: 'op',
        width: 140,
        render: (text: any, record: any) => {
          return <A onClick={() => this.delSelected()}>删除</A>;
        },
      },
    ];
  };
  // 删除已选择红包
  delSelected = () => {
    this.setState({
      redPackeShow: [],
      qyRedPacketId: '',
    });
    console.log(this.state);
  };
  switchShow = (e: any) => {
    this.setState({
      listShowStatus: e.target.value,
    });
  };
  switchRule = (e: any) => {
    this.setState({
      ruleShowStatus: e.target.value,
    });
  };
  cChange = (index: any, key: any, value: any) => {
    if (value) {
      this.setState({
        redPackeShow: this.state.redPacketList.filter((v: any) => v.id == value),
        qyRedPacketId: value,
      });
    }
  };
  cleanSelect = () => {};
  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const { page, redPacketList, type: stateType } = this.state;
    const values = this.props.form.getFieldsValue();
    const type = values.type || stateType;

    return (
      <Form {...formLayout} onSubmit={this.handleSubmit} className="redpacket-form">
        <Form.Item label="活动名称">
          {getFieldDecorator('name', {
            initialValue: this.state.name,
            rules: [
              {
                required: true,
                message: '请填写活动名称',
              },
              {
                max: 10,
                message: '活动名称最长不能超过10个字',
              },
            ],
          })(<Input placeholder="请输入活动名称" />)}
        </Form.Item>
        <Form.Item label="列表显示">
          {getFieldDecorator('listShowStatus', {
            initialValue: this.state.listShowStatus,
          })(
            <Radio.Group onChange={this.switchShow}>
              <Radio value={1}>显示</Radio>
              <Radio value={0}>隐藏</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {this.state.listShowStatus === 1 ? (
          <>
            <Form.Item label="活动描述">
              {getFieldDecorator('description', {
                initialValue: this.state.description,
                rules: [
                  {
                    required: true,
                    message: '请填写描述文案',
                  },
                  {
                    max: 30,
                    message: '描述文案不能超过30个字',
                  },
                ],
              })(<Input placeholder="请输入不超过30个字的描述文案" />)}
            </Form.Item>
            <Form.Item label="活动链接">
              {getFieldDecorator('url', {
                initialValue: this.state.url,
                rules: [
                  { required: true, message: '请填写跳转地址' },
                  { pattern: /^https?:\/\//, message: '请正确输入url' },
                ],
              })(<Input placeholder="输入URL地址" />)}
            </Form.Item>

            <Form.Item label="活动图片" extra="支持jpg,jpeg,png,gif图片格式">
              {getFieldDecorator('img', {
                initialValue: this.state.img,
                rules: [
                  {
                    required: true,
                    message: '请上传活动图片',
                  },
                ],
              })(
                <ImageUploader
                  ratio={1 / 1}
                  accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
                />
              )}
            </Form.Item>
            <Form.Item label="规则显示">
              {getFieldDecorator('ruleShowStatus', {
                initialValue: this.state.ruleShowStatus,
              })(
                <Radio.Group onChange={this.switchRule}>
                  <Radio value={1}>显示</Radio>
                  <Radio value={0}>隐藏</Radio>
                </Radio.Group>
              )}
            </Form.Item>
            {this.state.ruleShowStatus === 1 ? (
              <Form.Item label="活动规则">
                {getFieldDecorator('rule', {
                  initialValue: this.state.rule,
                  rules: [
                    {
                      required: true,
                      message: '请填写活动规则',
                    },
                  ],
                })(<Input.TextArea placeholder="请输入活动规则" rows={3} />)}
              </Form.Item>
            ) : (
              <></>
            )}
          </>
        ) : (
          <Form.Item label="活动规则">
            {getFieldDecorator('rule', {
              initialValue: this.state.rule,
            })(<Input.TextArea placeholder="请输入活动规则" rows={3} />)}
          </Form.Item>
        )}

        <Form.Item label="关联红包" wrapperCol={{ span: 18 }}>
          {getFieldDecorator('redPackeShow', {
            initialValue: this.state.redPackeShow,
            type: 'array',
            rules: [
              {
                required: true,
                message: '请选择红包',
              },
            ],
          })(
            <span>
              <Select
                ref="sel"
                allowClear={true}
                placeholder="请选择需要关联的红包"
                onChange={this.cChange.bind(this, 'articleSearch', 'cType')}
              >
                {this.state.redPacketList.map((v: any) => (
                  <Select.Option
                    key={v.id}
                    value={v.id.toString()}
                    disabled={v.relationStatus == 1}
                  >
                    【{v.activityStatusStr}】 {v.name} 【
                    {v.relationStatus === 1 ? '已关联' : '未关联'}】
                  </Select.Option>
                ))}
              </Select>
              <SelectedRedPacket
                columns={this.getCloudRedPacketColumns(true)}
                rowKey="id"
                value={this.state.redPackeShow}
              />
            </span>
          )}
        </Form.Item>
      </Form>
    );
  }
}

export default RedPacketForm;
