import { opApi as api } from '@app/api';
import {
  Checkbox,
  Form,
  Input,
  message,
  Select,
  DatePicker,
  InputNumber,
  Radio,
  Switch,
} from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader } from '../common';
import ArLinkInput, { arLinkValidator, isArLink } from '../common/arLinkInput';

@connectSession
@(Form.create({ name: 'activityForm' }) as any)
class ActivityForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const s = {
      time: [],
      title: '',
      base_count: 0,
      list_pic: '',
      url: '',
      show_number: false,
      second_floor_list_pic: '',
      ...props.formContent,
    };
    this.state = {
      ...s,
      url: isArLink(s.url) ? '' : s.url,
      arUrl: isArLink(s.url) ? s.url : '',
      linkType: isArLink(s.url) ? 1 : 0,
    };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    const fields = ['title', 'time', 'list_pic', 'second_floor_list_pic', 'base_count', 'show_number'];
    if (this.state.linkType === 0) {
      fields.push('url');
    } else {
      fields.push('arUrl');
    }
    this.props.form.validateFieldsAndScroll(fields, (err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        const body = { ...values };
        body.second_floor_list_pic = values.second_floor_list_pic || '';
        body.begin = values.time[0].format('YYYY-MM-DD HH:mm:ss');
        body.end = values.time[1].format('YYYY-MM-DD HH:mm:ss');
        if (this.state.linkType === 1) {
          body.url = values.arUrl;
          delete body.arUrl;
        }
        body.base_count = values.base_count || 0;
        delete body.time;
        let func = api.createActivity;
        if (this.state.id) {
          body.id = this.state.id;
          func = api.updateActivity;
        }
        func(body)
          .then(() => {
            setMLoading(this, false);
            message.success('添加成功');
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="活动标题">
          {getFieldDecorator('title', {
            initialValue: this.state.title,
            rules: [
              {
                required: true,
                message: '请输入活动标题',
              },
              {
                max: 20,
                message: '活动标题不能超过20个字',
              },
            ],
          })(<Input placeholder="请输入活动标题" />)}
        </Form.Item>
        <Form.Item label="活动列表图" extra="支持jpg,jpeg,png格式">
          {getFieldDecorator('list_pic', {
            initialValue: this.state.list_pic,
            rules: [
              {
                required: true,
                message: '请上传活动列表图',
              },
            ],
          })(<ImageUploader ratio={345 / 90} />)}
        </Form.Item>
        <Form.Item label="2楼列表图" extra="比例16:9，支持jpg,jpeg,png格式">
          {getFieldDecorator('second_floor_list_pic', {
            initialValue: this.state.second_floor_list_pic,
          })(<ImageUploader ratio={16 / 9} accept={['image/jpeg', 'image/png', 'image/jpg']} />)}
        </Form.Item>
        <Form.Item label="活动时间">
          {getFieldDecorator('time', {
            initialValue: this.state.time,
            rules: [
              {
                required: true,
                message: '请选择活动时间',
              },
            ],
          })(<DatePicker.RangePicker showTime />)}
        </Form.Item>
        {/* <Form.Item label="初始人数">
          {getFieldDecorator('base_count', {
            initialValue: this.state.base_count,
          })(<InputNumber min={0} />)}
        </Form.Item>
        <Form.Item label="是否显示活动人数">
          {getFieldDecorator('show_number', {
            initialValue: this.state.show_number,
            valuePropName: 'checked',
          })(<Switch checkedChildren="显示" unCheckedChildren="不显示" />)}
        </Form.Item> */}
        <Form.Item label="类型">
          <Radio.Group
            value={this.state.linkType}
            onChange={(e: any) => this.setState({ linkType: e.target.value })}
          >
            <Radio value={0}>链接</Radio>
            <Radio value={1}>AR</Radio>
          </Radio.Group>
        </Form.Item>
        {this.state.linkType === 0 && (
          <Form.Item label="URL地址">
            {getFieldDecorator('url', {
              initialValue: this.state.url,
              preserve: true,
              rules: [
                {
                  required: true,
                  message: '请填写URL地址',
                },
                {
                  pattern: /^https?:\/\//,
                  message: '请填写正确的URL地址',
                },
              ],
            })(<Input placeholder="请输入URL地址" />)}
          </Form.Item>
        )}
        {this.state.linkType === 1 && (
          <Form.Item label="AR信息" required>
            {getFieldDecorator('arUrl', {
              initialValue: this.state.arUrl,
              preserve: true,
              rules: [
                {
                  validator: arLinkValidator,
                },
              ],
            })(<ArLinkInput />)}
          </Form.Item>
        )}
      </Form>
    );
  }
}

export default ActivityForm;
