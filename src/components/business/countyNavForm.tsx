import { sysApi as api } from '@app/api';
import { Form, Input, message, Radio, Select, Checkbox } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';

type Api = 'updateCityNav' | 'createCityNav';

@connectSession
@(Form.create({ name: 'cityNavForm' }) as any)
class CountyNavForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);

    this.state = {
      ...props.formContent,
      privGroup: props.privGroup || [],
      provinceList: props.provinceList || [],
      countyList: props.countyList || [],
    };
    console.log(this.state.provinceId);
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body: any = { ...values, sort_number: 1 };
        let func = 'create';
        if (this.state.id) {
          func = 'update';
          body.id = this.state.id;
        }
        body.require_permission_id = this.state.require_permission_id || '0'
        setMLoading(this, true);
        api[`${func}CountyNav` as Api](body)
          .then(() => {
            message.success('操作成功');
            setMLoading(this, false);
            this.props.onEnd();
          })
          .catch(() => setMLoading(this, false));
      } else {
        message.error('请检查表单内容');
      }
    });
  };
  // 获取区县列表
  getCountyList = (value: any) => {
    this.setState(
      {
        provinceId: value,
        parent_id: '',
        countyList: [],
      },
      () => {
        api.getCityNavList({ current: 1, size: 1000, parent_id: value }).then((res: any) => {
          if (res.data.area_list.length === 0) {
            message.error('暂无区县分类，无法查看区县列表');
            this.setState({
              countyList: [],
              parent_id: '',
            });
            return;
          }
          this.setState({
            countyList: res.data.area_list,
            parent_id: '',
          });
        });
      }
    );
  };
  render() {
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="名称">
          {getFieldDecorator('name', {
            initialValue: this.state.name,
            rules: [
              {
                required: true,
                message: '请填写名称',
              },
              // {
              //   pattern: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/,
              //   message: '名称不能包含特殊字符',
              // },
            ],
          })(<Input placeholder="请输入名称" />)}
        </Form.Item>
        <Form.Item label="频道地址">
          {getFieldDecorator('url', {
            initialValue: this.state.url,
            rules: [
              {
                required: true,
                message: '请输入频道地址',
              },
            ],
          })(<Input placeholder="请输入频道地址" />)}
        </Form.Item>
        <Form.Item label="状态">
          {getFieldDecorator('enabled', {
            initialValue: this.state.enabled,
          })(
            <Radio.Group>
              <Radio value={true}>上线</Radio>
              <Radio value={false}>下线</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item label="上线范围">
          {getFieldDecorator('belong', {
            initialValue: this.state.belong,
          })(
            <Checkbox.Group>
              <Checkbox value="1">城市频道</Checkbox>
              {/* <Checkbox value="2">服务频道</Checkbox> */}
              <Checkbox value="3">同城频道</Checkbox>
            </Checkbox.Group>
          )}
        </Form.Item>

        <Form.Item label="所属城市">
          <Select
            placeholder="请选择所属省份"
            value={this.state.provinceId}
            onChange={this.getCountyList}
            style={{ marginRight: 8, width: 130 }}
          >
            {/* <Select.Option disabled={true} value={this.state.provinceId}>
              请选择省份
            </Select.Option> */}
            {this.state.provinceList.map((v: any) => (
              <Select.Option value={v.id} key={v.id + 1}>
                {v.name}
              </Select.Option>
            ))}
          </Select>
          {getFieldDecorator('parent_id', {
            initialValue: this.state.parent_id,
            rules: [
              {
                required: true,
                message: '请选择所属城市',
              },
            ],
          })(
            <Select placeholder="请选择所属城市" style={{ marginRight: 8, width: 130 }}>
              <Select.Option value="" disabled={true}>
                请选择城市
              </Select.Option>
              {this.state.countyList.map((v: any) => (
                <Select.Option value={v.id} key={v.id}>
                  {v.name}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>
      </Form>
    );
  }
}

export default CountyNavForm;
