import { releaseListApi as api } from '@app/api';
import { setConfig } from '@action/config';
import { Form, Radio, Input, message, Select, Table, Button, Row, Col, Divider, Modal } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { A } from '../common';
import moment from 'moment';


class SuperLuckyTable extends React.Component<any, any> {
    static getDerivedStateFromProps(nextProps: any) {
        if ('value' in nextProps) {
            return { value: nextProps.value || [] };
        }
        return null;
    }

    constructor(props: any) {
        super(props);
        this.state = {
            value: props.value || [],
        };
    }

    render() {
        return <Table dataSource={this.state.value} pagination={false} columns={this.props.columns} />;
    }
}

@connectSession
@(Form.create({ name: 'SuperLuckyBagForm' }) as any)
class SuperLuckyBagForm extends React.Component<any, any> {
    constructor(props: any) {
        super(props);
        this.state = {
            ...props.formContent,
            allActivityList: [],    //所有的分类信息
            join_type: 2,
            isSelect: false,    // 选择是否禁用   disabled false 可使用     true禁用
            id: '',
        };
    }
    componentDidMount() {
        this.changeRed()
    }
    // 福袋时间冲突 变为红色
    changeRed() {
        const luckBagList = this.state.luckBagList
        // array11 获取到时间冲突的数据
        const array11 = luckBagList.filter((item: any, index: number, arr: any) => {
            return arr[index]?.end_time > arr[index + 1]?.start_time
        })
        // 遍历 在时间冲突的数据中添加red字段red:true
        array11.map((item: any, index: number, arr: any) => {
            luckBagList.map((item1: any, index1: number, arr1: any) => {
                if (item.qy_activity_id == item1.qy_activity_id) {
                    luckBagList[index1].red = true
                }
            })
        })
        this.setState({
            luckBagList: luckBagList
        })
    }

    handleSubmit = (e: any) => {
        e.preventDefault();
        this.addToList();
    };
    // 选择参与方式
    onChange = (e: any) => {
        this.setState({
            join_type: e.target.value
        })
    }
    // 获取所有的活动项目
    getAllRecomemnd = () => {
        api.qySelectList({ type: 1 }).then((v: any) => {
            this.setState({
                allActivityList: v.data.qy_activity_list,
            });
        })
    };
    // 添加至列表
    addToList = () => {
        this.props.form.validateFieldsAndScroll((err: any, values: any) => {
            if (!err) {
                setMLoading(this, true);
                const body = {
                    ...values,
                };
                if (this.state.id) {
                    body.id = this.state.id;
                    body.start_time = this.state.start_time
                    body.end_time = this.state.end_time
                    body.qy_activity_id = this.state.qy_activity_id1
                } else {
                    this.getAllRecomemnd()
                    let time = this.state.allActivityList.filter((item: any, index: number) => {
                        return item.qy_activity_id == { ...values }.qy_activity_id
                    })
                    body.start_time = time[0].start_time
                    body.end_time = time[0].end_time
                }

                body.article_id = this.state.article_id
                if (body.content) {
                    body.content = body.content.trim()
                }
                body.type = 1,
                    api.saveToLive({ ...body }).then(() => {
                        setMLoading(this, false);
                        message.success('操作成功');
                        this.onReset()
                        this.getLuckyList()
                        if (this.state.id) {
                            this.setState({
                                qy_activity_id: undefined,
                                qy_activity_id1: undefined,
                                join_type: 2,
                                content: '',
                                isSelect: false,
                                id: '',
                                start_time: '',
                                end_time: '',
                            })
                        }

                    }).catch(() => {
                        setMLoading(this, false);
                    })
            } else {
                message.error('请完善必填项信息后再添加');
            }
        });
    }
    // 获取列表数据
    getLuckyList() {
        api.getLiveList({
            channel_id: this.state.channel_id,
            article_id: this.state.article_id,
            type: 1
        }).then((res: any) => {
            const luckBagList = res.data.list
            const array11 = luckBagList.filter((item: any, index: number, arr: any) => {
                return arr[index]?.end_time > arr[index + 1]?.start_time
            })
            array11.map((item: any, index: number, arr: any) => {
                luckBagList.map((item1: any, index1: number, arr1: any) => {
                    if (item.qy_activity_id == item1.qy_activity_id) {
                        luckBagList[index1].red = true
                    }
                })
            })
            this.setState({
                luckBagList: luckBagList
            })
        }).catch(() => {

        })
    }
    // 删除
    delSelected = (record: any) => {
        Modal.confirm({
            title: (
                <p>
                    确认删除吗？
                </p>
            ),
            onOk: () => {
                this.props.dispatch(setConfig({ loading: true }));
                let deleteData = {
                    id: record.id,
                    type: 1,
                    article_id: this.state.article_id,
                    start_time: moment(record.start_time).format('YYYY-MM-DD HH:mm:ss'),
                    end_time: moment(record.end_time).format('YYYY-MM-DD HH:mm:ss')
                }
                api
                    .deleteLiveActive(deleteData)
                    .then(() => {
                        message.success('操作成功');
                        this.props.dispatch(setConfig({ loading: false }));
                        this.getLuckyList();
                    })
                    .catch(() => this.props.dispatch(setConfig({ loading: false })));
            },
        });
    }
    // 编辑
    editList = (record: any) => {
        this.onReset()
        this.setState({
            qy_activity_id: record.name,
            qy_activity_id1: record.qy_activity_id,
            join_type: record.join_type,
            content: record.content,
            isSelect: true,
            id: record.id,
            start_time: moment(record.start_time).format('YYYY-MM-DD HH:mm:ss'),
            end_time: moment(record.end_time).format('YYYY-MM-DD HH:mm:ss'),
        })
    }
    // 清空表单数据
    onReset = () => {
        this.props.form.resetFields();
    }
    getCloudRedPacketColumns = (hideGive: boolean = false) => {
        return [
            {
                title: '项目名称',
                dataIndex: 'name',
            },
            {
                title: '参与方式',
                dataIndex: 'join_type',
                render: (text: any, record: any, index: number) =>
                    record.join_type === 1 ? '发布自定义评论' : '发布一键评论',
            },
            {
                title: '活动时间',
                dataIndex: 'start_time',
                render: (text: any, record: any, index: number) => (
                    record.red === true ?
                        (<div style={{ whiteSpace: 'normal', width: 300, color: 'red' }}>
                            {moment(record.start_time).format('YYYY-MM-DD HH:mm:ss')}—{moment(record.end_time).format('YYYY-MM-DD HH:mm:ss')}
                        </div>) :
                        (<div style={{ whiteSpace: 'normal', width: 300, }}>
                            {moment(record.start_time).format('YYYY-MM-DD HH:mm:ss')}—{moment(record.end_time).format('YYYY-MM-DD HH:mm:ss')}
                        </div>)
                ),
            },
            {
                title: '状态',
                dataIndex: 'status',
                render: (text: any, record: any, index: number) =>
                    record.status == 1 ? '上线' : '下线',
            },
            {
                title: '操作',
                width: 140,
                render: (text: any, record: any) => {
                    return <span>
                        <A disabled={record.enable_update_status == 0 ? true : false} onClick={() => this.editList(record)}>编辑</A>
                        <Divider type="vertical" />
                        <A disabled={record.enable_update_status == 0 ? true : false} onClick={() => this.delSelected(record)}>删除</A>
                    </span>
                },
            },
        ];
    };

    render() {
        const { getFieldDecorator } = this.props.form;
        const formLayout = {
            labelCol: { span: 4 },
            wrapperCol: { span: 18 },
        };
        return (
            <>
                <Form {...formLayout} onSubmit={this.handleSubmit}>
                    <Form.Item label="活动项目">
                        {getFieldDecorator('qy_activity_id', {
                            initialValue: this.state.qy_activity_id,
                            rules: [
                                {
                                    required: true,
                                    message: '请填写活动项目',
                                },
                            ],
                        })(
                            <Select placeholder="请选择项目" onFocus={this.getAllRecomemnd} disabled={this.state.isSelect}>
                                {this.state.allActivityList.map((v: any, index: any) => (
                                    <Select.Option value={v.qy_activity_id} key={index}>
                                        {v.name}
                                    </Select.Option>
                                ))}
                            </Select>
                        )}
                    </Form.Item>
                    <Form.Item label="参与方式">
                        {getFieldDecorator('join_type', {
                            initialValue: this.state.join_type,
                            rules: [
                                {
                                    required: true,
                                    message: '请选择参与方式',
                                },
                            ],
                        })(
                            <Radio.Group onChange={this.onChange}>
                                <Radio value={1} style={{ margin: 8 }}>发布自定义评论</Radio>
                                <Radio value={2} style={{ margin: 8 }}>发布一键评论</Radio>
                            </Radio.Group>
                        )}
                    </Form.Item>
                    {
                        this.state.join_type == 2 ? (
                            <Form.Item label="评论语">
                                {getFieldDecorator('content', {
                                    initialValue: this.state.content,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请填写一键评论语',
                                            whitespace:true,
                                        },
                                    ],
                                })(
                                    <Input.TextArea placeholder='请输入一键评论语，最多200个字符' autoSize={{ minRows: 5, maxRows: 7 }} maxLength={200} />
                                )}
                            </Form.Item>
                        ) : null
                    }
                </Form>
                <Row className="layout-infobar">
                    <Col span={24}>
                        <Button type='primary' style={{ float: 'right' }} onClick={this.addToList}>
                            添加方案至列表
                        </Button>
                    </Col>
                </Row>
                <SuperLuckyTable
                    columns={this.getCloudRedPacketColumns(true)}
                    rowKey="id"
                    value={this.state.luckBagList}
                />
            </>

        );
    }
}

export default SuperLuckyBagForm;

