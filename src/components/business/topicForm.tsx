import { opApi as api } from '@app/api';
import { Checkbox, Form, Input, message, Radio } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader } from '../common';

@connectSession
@(Form.create({ name: 'topicForm' }) as any)
class TopicForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      ...props.formContent,
      classList: props.classList || [],
    };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  }

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        let func = 'createTopic';
        const body = {
          ...values,
          class_ids: values.class_ids.join(','),
          type: 0,
        };
        if (this.state.id) {
          func = 'updateTopic';
          body.id = this.state.id;
        }
        api[func as keyof typeof api](body)
          .then(() => {
            setMLoading(this, false);
            message.success('操作成功');
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  }

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit} ref="form">
        <Form.Item label="话题头像" extra="支持jpg,jpeg,png,gif图片格式，比例为 1:1">
          {getFieldDecorator('logo_url', {
            initialValue: this.state.logo_url,
            rules: [
              {
                required: true,
                message: '请上传话题头像',
              },
            ],
          })(<ImageUploader ratio={1 / 1} accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']} />)}
        </Form.Item>
        <Form.Item label="背景图" extra="支持jpg,jpeg,png图片格式，比例为 5:2">
          {getFieldDecorator('background_url', {
            initialValue: this.state.background_url,
          })(<ImageUploader ratio={5 / 2} />)}
        </Form.Item>
        <Form.Item label="话题名称">
          {getFieldDecorator('name', {
            initialValue: this.state.name,
            rules: [
              {
                required: true,
                message: '请填写话题名称',
              },
              {
                max: 15,
                message: '话题名称最长不能超过15个字',
              },
              {
                pattern: !/#/,
                message: '不能输入字符#',
              },
            ],
          })(<Input placeholder="请输入话题名称" />)}
        </Form.Item>
        <Form.Item label="话题简介">
          {getFieldDecorator('description', {
            initialValue: this.state.description,
            rules: [
              {
                max: 30,
                message: '话题简介最长不能超过30个字',
              },
            ],
          })(<Input.TextArea placeholder="请输入话题简介" rows={3} />)}
        </Form.Item>
        <Form.Item label="话题分类">
          {getFieldDecorator('class_ids', {
            initialValue: this.state.class_ids,
            rules: [
              {
                required: true,
                message: '请选择话题分类',
              },
            ],
          })(
            <Checkbox.Group style={{ width: '100%' }}>
              {this.state.classList.map((v: any, i: number) => (
                <Checkbox value={v.id.toString()} key={i}>
                  {v.name}
                </Checkbox>
              ))}
            </Checkbox.Group>
          )}
        </Form.Item>
      </Form>
    );
  }
}

export default TopicForm;
