import React from "react"
import { Modal, Form, Input, message, Button } from 'antd';
import { sysApi as api } from '@app/api';

@(Form.create({ name: 'AddReadPreferenceModal' }) as any)
class AddReadPreferenceModal extends React.Component<any, any> {

  state = {
    loading: false
  }

  handleOkClick = () => {
    const { form: { validateFields }, onEnd, record } = this.props
    validateFields((err: any, values: any) => {
      if (!err) {
        this.setState({ loading: true })
        const params = { ...values }
        const id = record && record.id
        if (id) {
          params.id = id
        }
        api[id ? 'updateHobby' : 'createHobby'](params).then(() => {
          message.success(`${id ? '修改' : '添加'}成功`);
          onEnd(false)
          this.setState({ loading: false })
        }).catch(() => {
          this.setState({ loading: false })
        })
      }
    })
  }

  render() {
    const { visible, onCancel, record, form: { getFieldDecorator } } = this.props
    const formLayout = {
      labelCol: { span: 6 },
      wrapperCol: { span: 16 },
    };
    return (
      <Modal
        visible={visible}
        title={`${record ? '编辑' : '添加'}一级标签`}
        width="450px"
        onCancel={onCancel}
        destroyOnClose={true}
        maskClosable={false}
        footer={[
          <Button key="back" onClick={onCancel}>取消</Button>,
          <Button key="submit" type="primary" loading={this.state.loading} onClick={this.handleOkClick}>确定</Button>
        ]}
        key='AddReadPreferenceModal'>
        <Form {...formLayout}>
          <Form.Item label="标签名">
            {getFieldDecorator('content', {
              rules: [
                {
                  required: true,
                  message: '标签名不能为空',
                },
                {
                  validator(rule: any, value: any, callback: any) {
                    const regex = /^[\u4e00-\u9fa5a-zA-Z0-9]+$/
                    if (!regex.test(value)) {
                      callback('标签名只能包含中文、英文与数字')
                      return;
                    }
                    callback()
                  },
                }
              ],
              validateFirst: true,
              initialValue: record ? record.content : ''
            })(<Input placeholder="请输入标签名，最多6个字" maxLength={6} allowClear />)}
          </Form.Item>
          <Form.Item label="关联标签id">
            {getFieldDecorator('ref_id', {
              rules: [
                {
                  required: true,
                  message: '标签id不能为空',
                },
              ],
              validateFirst: true,
              initialValue: record ? record.ref_id : ''
            })(<Input placeholder="请输入关联标签id，最多30位" maxLength={30} allowClear />)}
          </Form.Item>
        </Form>
      </Modal>
    )
  }
}

export default AddReadPreferenceModal