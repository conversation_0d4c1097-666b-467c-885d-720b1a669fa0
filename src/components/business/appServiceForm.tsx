import { serviceApi as api } from '@app/api';
import { ImageUploader } from '@components/common';
import { Divider, Form, Input, message, Radio, Select, Switch, TreeSelect } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';

type Api = 'updateAppService' | 'createAppService';

@connectSession
@(Form.create({ name: 'appServiceForm' }) as any)
class AppServiceForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const form = {
      name: '',
      logo: '',
      url: '',
      alert: false,
      alert_text: '',
      type: '1',
      category_id: '',
      area_ref_ids: [],
      search_ref_ids: [],
      risk_level: 0,
    };
    this.state = {
      ...form,
      ...props.formContent,
      categoryList: props.categoryList,
      keys: Object.keys(form),
    };
  }

  componentDidMount() {
    const data: any = {};
    // eslint-disable-next-line array-callback-return
    this.state.keys.map((v: any) => {
      data[v] = this.state[v];
    });
    this.props.form.setFieldsValue(data);
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    const vs = this.props.form.getFieldsValue(this.state.keys);
    console.log(vs);
    this.props.form.validateFieldsAndScroll(this.getFields(), (err: any, values: any) => {
      if (!err) {
        let func = 'createAppService';
        const body = {
          ...vs,
          ...values,
          alert: values.alert ? '1' : '0',
          type: 1,
          alert_text: values.alert_text || '',
          area_ref_ids: values.area_ref_ids.join(','),
          search_ref_ids: values.search_ref_ids.join(','),
        };
        if (this.state.id) {
          body.id = this.state.id;
          func = 'updateAppService';
        }
        setMLoading(this, true);
        api[func as Api](body)
          .then(() => {
            message.success('操作成功');
            setMLoading(this, false);
            this.props.onEnd();
          })
          .catch(() => setMLoading(this, false));
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  getFields = () => {
    const { alert } = this.props.form.getFieldsValue(['alert']);
    const fields = [
      'name',
      'url',
      'logo',
      'alert',
      'category_id',
      'area_ref_ids',
      'search_ref_ids',
    ];
    if (alert) {
      fields.push('alert_text');
    }
    return fields;
  };

  typeChange = () => {
    this.props.form.setFieldsValue({ url: '' });
  };

  urlValidator = (rule: any, value: any, callback: any) => {
    const { type } = this.props.form.getFieldsValue(['type']);
    if (type && type === '1') {
      const regex = /^https?:\/\//;
      if (value === '') {
        callback('请填写链接');
        return;
      }
      if (!regex.test(value)) {
        callback('请正确填写链接格式');
        return;
      }
    }
    callback();
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const values = this.props.form.getFieldsValue();
    return (
      <Form onSubmit={this.handleSubmit} {...formLayout}>
        <Form.Item label="名称">
          {getFieldDecorator('name', {
            initialValue: this.state.name,
            rules: [
              {
                required: true,
                message: '请填写名称',
              },
              {
                max: 8,
                message: '名称不能超过8个字',
              },
              {
                pattern: /^[a-zA-Z0-9\u4e00-\u9fa5\n]+$/,
                message: '名称不能包含特殊字符',
              },
            ],
          })(<Input.TextArea placeholder="请填写名称" rows={3} />)}
        </Form.Item>
        <Form.Item label="所属分类">
          {getFieldDecorator('category_id', {
            initialValue: this.state.category_id,
            rules: [
              {
                required: true,
                message: '请填写所属分类',
              },
            ],
          })(
            <Select placeholder="请选择分类">
              <Select.Option value="" disabled={true}>
                选择分类
              </Select.Option>
              {this.state.categoryList.map((v: any) => (
                <Select.Option key={v.id} value={v.id.toString()}>
                  {v.name}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>
        <Form.Item label="所属地区">
          {getFieldDecorator('area_ref_ids', {
            initialValue: this.state.area_ref_ids,
            rules: [
              {
                required: true,
                message: '请选择所属地区',
                type: 'array',
              },
            ],
          })(
            <Select mode="multiple" placeholder="请选择地区">
              {this.props.areaList.map((v: any) => (
                <Select.Option value={v.value} key={v.value}>
                  {v.title}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>
        <Form.Item label="搜索关联">
          {getFieldDecorator('search_ref_ids', {
            initialValue: this.state.search_ref_ids,
          })(
            <TreeSelect
              treeData={this.props.keywordList}
              placeholder="请选择关联词"
              treeDefaultExpandAll={false}
              treeCheckable={true}
            />
          )}
        </Form.Item>
        <Form.Item required={true} label="链接">
          {getFieldDecorator('url', {
            initialValue: this.state.url,
            rules: [
              {
                validator: this.urlValidator,
              },
            ],
          })(<Input placeholder="请输入链接" />)}
        </Form.Item>
        <Form.Item label="图标" extra="支持jpg,jpeg,png图片格式">
          {getFieldDecorator('logo', {
            initialValue: this.state.logo,
            rules: [
              {
                required: true,
                message: '请上传图标',
              },
            ],
          })(<ImageUploader ratio={1 / 1} />)}
        </Form.Item>
        {/* <Form.Item label="政务网服务编码" extra="浙江政务服务网的服务必填，其他第三方服务无需填写">
          {getFieldDecorator('service_code', {
            initialValue: this.state.service_code,
          })(<Input />)}
        </Form.Item> */}
        {/* <Form.Item label="来自政务网服务">
          {getFieldDecorator('service_type', {
            initialValue: this.state.from_service,
          })(
            <Radio.Group>
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </Radio.Group>
          )}
        </Form.Item> */}
        {/* <Form.Item label="是否需要登录政务网">
          {getFieldDecorator('service_login', {
            initialValue: this.state.service_login,
          })(
            <Radio.Group>
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </Radio.Group>
          )}
        </Form.Item> */}
        <Form.Item label="是否为风险服务">
          {getFieldDecorator('risk_level', {
            initialValue: this.state.risk_level,
          })(
            <Radio.Group>
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Divider type="horizontal" />
        <Form.Item label="弹框">
          {getFieldDecorator('alert', {
            initialValue: this.state.alert,
            valuePropName: 'checked',
          })(<Switch checkedChildren="开" unCheckedChildren="关" />)}
        </Form.Item>
        {values.alert && (
          <Form.Item
            label="弹框内容"
            extra={`已输入${values.alert_text ? values.alert_text.length : 0}/30个字`}
          >
            {getFieldDecorator('alert_text', {
              initialValue: this.state.alert_text,
              preserve: true,
              rules: [
                {
                  required: true,
                  message: '请填写弹框内容',
                },
                {
                  max: 30,
                  message: '弹框内容不能超过30个字',
                },
              ],
            })(<Input.TextArea rows={3} placeholder="请输入不超过30个字的弹框内容" />)}
          </Form.Item>
        )}
      </Form>
    );
  }
}

export default AppServiceForm;
