import React, {forwardRef, useRef, useState, useEffect} from 'react';
import {
    Form,
    Input,
    Radio, Col, Button, Modal,
    Row, message, Tabs, Checkbox, Icon, Tooltip
} from 'antd';

import ImageUploader from '@components/common/imageUploader';
import uuid from "uuid";
import {FileUploader} from "@components/common";
import ImgUploadList from '@components/common/imgUploadList'
import {opApi} from "@app/api";

interface FormRef {
    validateFields: (param: (...arg: any) => void) => void;
    getFieldsValue?: () => any;
    setFieldsValue?: () => void;
}

interface ProportionObject {
    divisor: number;
    dividend: number
};


const AppThemeMgrForm = forwardRef<FormRef, {
    form: any,
    getData: () => void,
    onEnd: () => void,
    formContent: any,
    type: number,
    drawer: {
        visible: boolean,
        key: string,
        type: string,
    }
}>(({form, getData, onEnd, formContent, drawer,type}, ref) => {
    // 初始化页面数据
    const intPageList = () => {
        let arr = []
        if (formContent?.page_list) {
            arr = formContent.page_list.map((item: {
                page_type: number,
                article_list: [],
                id: string
            }, index: number) => {
                item.id = uuid()
                item.article_list.map((cItem: {
                    pid: number;
                    page_type: number, cItem: { pid: number }
                }) => {
                    cItem.page_type = item.page_type;
                    cItem.pid = index
                })
                return item
            })
        }
        return arr
    }
    const [page_list, setPageList] = useState([])
    useEffect(() => {
        if (!page_list || page_list.length == 0) {
            setPageList(intPageList())
        }
    }, [])
    const [recordInfo, setRecordInfo] = useState({
        visible: false,
        titleName: '',
        key: '',
        pid: '',
        index:'',
        value: '',
        sizeMax: 40,
    })
    const proportionHandle = (record: {
        page_type: number;
        [key: string]: any
    }, index: number): ProportionObject => {
        let proportionObject: ProportionObject;
        switch (record.page_type) {
            case 5:
                proportionObject = {divisor: 375, dividend: 812};
                break;
            case 4:
                proportionObject = {divisor: 16, dividend: 9};
                break;
            case 1:
                if (index === 0) {
                    proportionObject = {divisor: 15, dividend: 18};
                } else {
                    proportionObject = {divisor: 3, dividend: 4};
                }
                break;
            case 3:
                proportionObject = {divisor: 3, dividend: 4};
                break;
            default:
                console.log('${record.page_type}', record.page_type)
                throw new Error(`Unsupported template type: ${record.page_type}`);
        }
        return proportionObject;
    };
    // 获取图片比例
    const getImageRatio = async (imageUrl: string) => {
        return new Promise((resolve, reject) => {
            const img = new Image();

            img.onload = () => {
                const width = img.width;
                const height = img.height;
                const ratio = width / height;
                resolve(ratio);
            };

            img.onerror = () => {
                resolve(2);
            };

            img.src = imageUrl;
        });
    }
    const editPic = async (url: string, record: any, index: number) => {
        let pic: string = url;
        let modal: any;
        const {divisor: w, dividend: h} = proportionHandle(record, index)
        let proportion = (w / h).toFixed(2)
        let consistentProportions = true
        if (url) {
            const afterCalculation = await getImageRatio(url)
            if (Math.abs(afterCalculation - proportion) <= 0.05) {
                consistentProportions = false
            } else {
                consistentProportions = true
            }
        }

        const picChange = (u: string) => {
            pic = u;
            modal.update({
                content: (
                    <>
                        <ImageUploader
                            value={pic}
                            onChange={picChange}
                            ratio={proportionHandle(record, index).divisor / proportionHandle(record, index).dividend}/>
                        <p> 比例{proportionHandle(record, index).divisor}:{proportionHandle(record, index).dividend}</p>
                    </>
                ),
            });
        };
        modal = Modal.confirm({
            title: '编辑图片',
            width: 500,
            content: (
                <>
                    <ImageUploader value={pic}
                                   onChange={picChange}
                                   imgMaxWidth={400}
                                   isCutting={proportionHandle(record, index).divisor === 375}
                                   ratio={w / h}/>
                    {consistentProportions && record.page_type !== 5 && url ?
                        <span style={{color: "red"}}>该图片比例非{w}:{h}，图片将自动居中截取，建议重新上传。</span> :
                        <p>{record.page_type === 5 && '建议'}比例{w}:{h}</p>}
                </>
            ),
            onOk: async (destroy: Function) => {
                if (!pic) {
                    message.error('请上传图片');
                    return;
                }
                const pageList = getFieldsValue().page_list
                pageList[record.pid].article_list[index].article_pic = pic
                setFieldsValue({page_list: pageList});
                destroy();
            },
        });
    };
    // 编辑
    const handleOperate = (key:string, value:object, pid:string, index:number) => {
        setRecordInfo({
            visible: true,
            titleName: key === 'doc_title' ? '自定义标题' : '摘要',
            sizeMax: key === 'doc_title' ? 40 : 60,
            pid,
            index,
            key,
            value,
        })
    }
    const infoSubmit = () => {
        if (!recordInfo.value)
            return message.error('请填写内容')

        const pageList = getFieldsValue().page_list
        const {pid, index, key, value} = recordInfo
        pageList[pid].article_list[index][key] = value
        setFieldsValue({page_list: pageList});
        setRecordInfo({
            ...recordInfo,
            visible: false
        })
    }
    useEffect(() => {
        const {page_list: arr} = getFieldsValue()

        if (arr) {
            setTimeout(() => {
                setFieldsValue({page_list: page_list})
            }, 0)
        }
    }, [page_list]);
    // 去除空格
    const handleInputChange = (e:any,name:string) => {
        const { value } = e.target;
        const trimmedValue = value.replace(/\s/g, ''); // 去除空格
        setTimeout(()=>{
            setFieldsValue({
                [name]: trimmedValue,
            });
        },1)

    };
    // 输入框字体校验
    const validateLogoColor = (rule:object, value:string, callback:any,formTitle:string,size) => {
        const regex = new RegExp(`^#[0-9a-zA-Z]{${size}}$`);
        if (!value) {
            callback(`请输入${formTitle}`);
        } else if (!regex.test(value)) {
            callback(`格式错误，请输入以#开头，后面跟着${size}位数字或英文字母的内容`);
        } else {
            callback();
        }
    };
    // 版本号校验
    const versionValidator = (rule:any, value:string, callback:any,formName:string,version:string) => {
        if(!value){
            return  callback(`请设置最${{high:'高',low:'低'}[version]}版本`);
        }
        if (version == 'high'){
            let lowArr = getFieldValue(formName)[0]?.split('.')
            let highArr = value.split('.')
            if (!getFieldValue(formName)[0])
                return callback('请先填写最低版本');
            if (lowArr &&  highArr && Number(lowArr[0]) >= Number(highArr[0])){
                for (let i = 0; i < Math.max(lowArr.length, highArr.length); i++) {
                    const num1 = lowArr[i] || 0; // 如果某个版本号没有更多的位数了，则按0处理
                    const num2 = highArr[i] || 0;
                    if (i === 1){
                        if (Number(num1) > Number(num2)){
                            return callback('最高版本必须大于等于最低版本');
                        }
                    }
                    if (i === 2 && (Number(lowArr[1]) === Number(highArr[1]))){
                        console.log('2：',Number(num1), Number(num2))
                        if (Number(num1) > Number(num2)){
                            return callback('最高版本必须大于等于最低版本');
                        }
                    }
                }
            }
            form.validateFieldsAndScroll([`${formName}[0]`])
        }else {
            let lowArr = value.split('.')
            let highArr = getFieldValue(formName)[1]?.split('.')
            if (lowArr && highArr && (Number(lowArr[0]) === Number(highArr[0]))){
                for (let i = 0; i < Math.max(lowArr.length, highArr.length); i++) {
                    const num1 = lowArr[i] || 0; // 如果某个版本号没有更多的位数了，则按0处理
                    const num2 = highArr[i] || 0;
                    if (i === 1){
                        if (Number(num1) > Number(num2)){
                            return callback('最低版本必须小于等于最高版本');
                        }
                    }
                    if (i === 2 && (Number(lowArr[1]) === Number(highArr[1]))){
                        if (Number(num1) > Number(num2)){
                            return callback('最低版本必须小于等于最高版本');
                        }
                    }
                }
            }else if (Number(lowArr[0]) > Number(highArr[0])){
                return callback('最低版本必须小于等于最高版本');
            }
        }
        const regex = /^\d+(\.\d+){2}$/; // 定义版本号格式的正则表达式
        if (!regex.test(value)) {
            return  callback('请输入正确格式的版本号');
        } else {
            return callback();
        }
    };
    // 下载模板
    const handleDownload = (url: string) => {
        const a = document.createElement('a');
        a.setAttribute('href', url);
        a.target = '_self';
        a.href = url;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    };


    const doSubmit = () => {
        form.validateFieldsAndScroll((err: any, values: any) => {
            if (!err) {
                if (type === 1){
                    delete values.summary
                    delete values.cover_url
                    delete values.preview_url_list
                    delete values.tip_type
                }
                if(values.name){
                    values.name = values.name.trim()
                }
                if(values.summary){
                    values.summary = values.summary.trim()
                }
                if (values.ios_version){
                    values.ios_version = `${values.ios_version[0]}-${values.ios_version[1]}`
                }
                if (values.android_version){
                    values.android_version = `${values.android_version[0]}-${values.android_version[1]}`
                }
                if (values.summary){
                    values.summary =  values.summary.replace(/\n/g, " ");
                }
                values.type = type
                let func
                if (drawer.type === 'edit') {
                    func = 'editTheme';
                    values.id = formContent.id
                } else {
                    func = 'addTheme'
                }
                opApi[func as keyof typeof opApi](values).then(() => {
                    message.success('操作成功');
                    getData()
                    onEnd()
                })
            }else {
                if ('tab_config_list' in err && Object.keys(err).length === 1){
                    console.log('errr',err.tab_config_list)
                    message.error('请检查「底部导航设置」的必填项不为空')
                }
            }
        })
    }
    React.useImperativeHandle(ref, () => {
        return ({
            doSubmit
        });
    });
    const {getFieldDecorator, getFieldsValue, setFieldsValue, getFieldValue} = form;
    const formRef = useRef<any>(null);
    return (
        <>
            <Form ref={formRef} labelCol={{span: 4}} wrapperCol={{span: 18}}>
                <div style={{
                    width: 800,
                    margin: '0 auto',
                    textAlign: "center",
                    height: 20,
                    position: "fixed",
                    background: '#fff',
                    zIndex: 10,
                    display:"flex",
                    justifyContent: "center"
                }}>
                    <Button.Group style={{ marginTop: -10}}>
                        <Button href="#basic-information">
                            基础信息
                        </Button>
                        <Button href="#style-configuration">
                            样式配置
                        </Button>
                    </Button.Group>
                </div>
                <div id={'basic-information'}>
                    <div style={{height:50}}></div>
                    <h3 style={{height: '40px', lineHeight: '40px', fontWeight: 600,}}>基础信息</h3>
                    <Form.Item label={'主题名称'}>
                        {getFieldDecorator('name', {
                            initialValue: formContent?.name,
                            rules: [
                                {
                                    required: true,
                                    message: '请填写主题名称',
                                },
                            ],
                        })(<Input
                            maxLength={10}
                            placeholder="请输入主题名称，最多10个字"/>)}
                    </Form.Item>
                    {
                        type === 0 &&  <Form.Item label="主题简介">
                            {getFieldDecorator('summary', {
                                initialValue: formContent?.summary
                            })(<Input
                                placeholder="请简单介绍主题风格等，最多50字，选填"
                                maxLength={50}/>)}
                        </Form.Item>
                    }
                    {
                        type === 0 && <Form.Item label="封面图" extra="支持jpg,jpeg,png图片格式，比例为 1:1">
                            {getFieldDecorator('cover_url', {
                                initialValue: formContent?.cover_url,
                                rules: [
                                    {
                                        required: true,
                                        message: '请上传封面图',
                                    },
                                ],
                            })(<ImageUploader ratio={1 / 1} accept={['image/jpeg', 'image/png', 'image/jpg']}/>)}
                        </Form.Item>
                    }
                    {
                        type === 0 &&  <Form.Item label="预览图">
                            {getFieldDecorator('preview_url_list', {
                                initialValue: formContent?.preview_url_list,
                                rules: [
                                    {
                                        required: true,
                                        message: '请上传1~10张预览图',
                                    },
                                ],
                            })(
                                <ImgUploadList
                                    ratio={400/864}
                                    max={10}
                                    customOp={true}
                                    accept={['image/jpeg', 'image/png', 'image/jpg']}
                                    tips={'用于让用户预览主题样式，可上传1～10张图片'} />
                            )}
                        </Form.Item>
                    }
                    {
                        type === 0 &&   <Form.Item label="列表角标">
                            {getFieldDecorator('tip_type', {
                                initialValue: formContent?.tip_type || 0,
                                rules: [
                                    {
                                        required: true,
                                        message: '请选择列表角标',
                                    },
                                ],
                            })(<Radio.Group>
                                <Radio value={0}>无</Radio>
                                <Radio value={1}>新</Radio>
                                <Radio value={2}>热</Radio>
                            </Radio.Group>)}
                        </Form.Item>
                    }
                    <Row style={{display: "flex", alignItems: "center", height: 40,marginBottom:35}}>
                        <Col span={4}>
                            <div className="ant-col ant-col-24 ant-form-item-label"
                                 style={{textAlign: "right"}}>
                                <label htmlFor="tip_type" className="ant-form-item-required" title="列表角标">
                                    iOS版本:&nbsp;</label>
                            </div>
                        </Col>
                        <Col span={20}>
                            <Row style={{display: "flex", alignItems: "center", height: 40}}>
                                <Col span={6} style={{height: 40}}>
                                    <Form.Item>
                                        {getFieldDecorator('ios_version[0]', {
                                            initialValue: formContent?.ios_version[0],
                                            rules: [
                                                {
                                                    validator: (rule:any,value:string,callback:any)=>versionValidator(rule, value, callback,'ios_version','low'),
                                                },
                                            ],
                                        })(<Input maxLength={10} placeholder="最低版本" />)}
                                    </Form.Item>
                                </Col>
                                <span style={{marginLeft: -40, marginRight: 10}}>-</span>
                                <Col span={6} style={{height: 40}}>
                                    <Form.Item>
                                        {getFieldDecorator('ios_version[1]', {
                                            initialValue: formContent?.ios_version[1],
                                            rules: [
                                                {
                                                    validator: (rule:any,value:string,callback:any)=>{versionValidator(rule, value, callback,'ios_version','high')},
                                                },
                                            ],
                                        })(<Input maxLength={10} placeholder="最高版本"/>)}
                                    </Form.Item>
                                </Col>
                                <Col span={8} style={{height: 40, lineHeight: '40px',}}>
                                    <span style={{color: 'rgb(0 0 0 / 45%)',marginLeft:-35}}>示例：5.0.0，仅区间内版本可使用主题</span>
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                    <Row style={{display: "flex", alignItems: "center", height: 40,marginBottom:24}}>
                        <Col span={4}>
                            <div className="ant-col ant-col-24 ant-form-item-label"
                                 style={{textAlign: "right"}}>
                                <label htmlFor="tip_type" className="ant-form-item-required" title="列表角标">
                                    Android版本:&nbsp;</label>
                            </div>
                        </Col>
                        <Col span={20}>
                            <Row style={{display: "flex", alignItems: "center", height: 40}}>
                                <Col span={6} style={{height: 40}}>
                                    <Form.Item>
                                        {getFieldDecorator('android_version[0]', {
                                            initialValue: formContent?.android_version[0],
                                            rules: [
                                                {
                                                    validator: (rule:any,value:string,callback:any)=>versionValidator(rule, value, callback,'android_version','low'),
                                                },
                                            ],
                                        })(<Input maxLength={10} placeholder="最低版本"/>)}
                                    </Form.Item>
                                </Col>
                                <span style={{marginLeft: -40, marginRight: 10}}>-</span>
                                <Col span={6} style={{height: 40}}>
                                    <Form.Item>
                                        {getFieldDecorator('android_version[1]', {
                                            initialValue: formContent?.android_version[1],
                                            rules: [
                                                {
                                                    validator: (rule:any,value:string,callback:any)=>versionValidator(rule, value, callback,'android_version','high'),
                                                },
                                            ],
                                        })(<Input maxLength={10} placeholder="最高版本"/>)}
                                    </Form.Item>
                                </Col>
                                <Col span={8} style={{height: 40, lineHeight: '40px',}}>
                                    <span style={{color: 'rgb(0 0 0 / 45%)',marginLeft:-35}}>示例：5.0.0，仅区间内版本可使用主题</span>
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                </div>
                <div id={'style-configuration'} style={{paddingTop:20}}>
                    <h3 style={{height: '40px', lineHeight: '40px',marginBottom:10, fontWeight: 600}}>样式配置</h3>
                    <h4>（一）图片配置</h4>
                    <Form.Item label="图片资源" extra={<span>
                        支持zip文件格式，请将主题用到的图片资源按要求命名后打包上传，
                        <a   onClick={() => {
                            handleDownload('https://app-stc.zjol.com.cn/static/custom/图片资源模板.zip');
                        }}>
                            点击下载模板</a></span>}>
                        {getFieldDecorator('resources_config.url', {
                            initialValue: formContent?.resources_config.url,
                            rules: [
                                {
                                    required: true,
                                    message: '请上传图片资源',
                                },
                            ],
                        })(<FileUploader accept=".zip" download={true}/>)}
                    </Form.Item>
                    <h4>（二）色值配置</h4>
                    <Form.Item label="全局主题色">
                        {getFieldDecorator('global_config.main_color', {
                            initialValue: formContent?.global_config.main_color,
                            rules: [
                                {
                                    required: true,
                                    validator: (rule:any,value:string,callback:any)=>validateLogoColor(rule,value,callback,'全局主题色',6),
                                },
                            ],
                        })(<Input placeholder="输入色值" style={{width: '50%'}} maxLength={9}/>)}
                        <span style={{width: '50%',color: 'rgb(0 0 0 / 45%)'}}>&nbsp;&nbsp;示例：#000000</span>
                    </Form.Item>
                    <Form.Item label="首页LOGO颜色">
                        {getFieldDecorator('global_config.logo_color', {
                            initialValue: formContent?.global_config.logo_color,
                            rules: [
                                {
                                    required: true,
                                    validator: (rule:any,value:string,callback:any)=>validateLogoColor(rule,value,callback,'首页LOGO颜色',6),
                                },
                            ],
                        })(<Input placeholder="输入色值" style={{width: '50%'}} maxLength={9}/>)}
                        <span style={{width: '50%',color: 'rgb(0 0 0 / 45%)'}}>&nbsp;&nbsp;示例：#000000</span>
                    </Form.Item>
                    <Form.Item label="首页搜索图标颜色">
                        {getFieldDecorator('global_config.search_main_color', {
                            initialValue: formContent?.global_config.search_main_color,
                            rules: [
                                {
                                    required: true,
                                    validator: (rule:any,value:string,callback:any)=>validateLogoColor(rule,value,callback,'首页搜索图标颜色',6),
                                },
                            ],
                        })(<Input placeholder="输入色值" style={{width: '50%'}} maxLength={9}/>)}
                        <span style={{width: '50%',color: 'rgb(0 0 0 / 45%)'}}>&nbsp;&nbsp;示例：#000000&nbsp;
                            <Tooltip title={'搜索框提示文字为搜索图标颜色+固定透明度'}>
                              <Icon type="question-circle" />
                            </Tooltip>
                        </span>
                    </Form.Item>
                    <Form.Item label="首页搜索框底色">
                        {getFieldDecorator('global_config.search_bg_color', {
                            initialValue: formContent?.global_config.search_bg_color,
                            rules: [
                                {
                                    required: true,
                                    validator: (rule:any,value:string,callback:any)=>validateLogoColor(rule,value,callback,'首页搜索框底色',8),
                                },
                            ],
                        })(<Input placeholder="输入色值" style={{width: '50%'}} maxLength={9}/>)}
                        <span style={{width: '50%',color: 'rgb(0 0 0 / 45%)'}}>&nbsp;&nbsp;示例：#FF000000，前两位为透明度</span>&nbsp;
                        <Tooltip title={'透明度的对应色值如下：100% : FF，95% : F2，90% : E6，85% : D9，80% : CC，75% : BF，70% : B3，65% : A6，60% : 99，55% : 8C，50% : 80，45% : 73，40% : 66，35% : 59，30% : 4D，25% : 40，20% : 33，15% : 26，10% : 1A，5% : 0D，0% : 00'}>
                            <Icon type="question-circle" />
                        </Tooltip>
                    </Form.Item>
                    <Form.Item label="首页导航文字颜色">
                        {getFieldDecorator('global_config.channel_text_style', {
                            initialValue: formContent?.global_config.channel_text_style || 1,
                            rules: [
                                {
                                    required: true,
                                    message: '请选择首页导航文字颜色',
                                },
                            ],
                        })(<Radio.Group onChange={() => {
                        }}>
                            <Radio value={1}>浅色风格</Radio>
                            <Radio value={2}>深色风格</Radio>
                        </Radio.Group>)}
                    </Form.Item>
                    <Form.Item label="首页频道选中样式">
                        {getFieldDecorator('global_config.channel_selected_style', {
                            initialValue: formContent?.global_config.channel_selected_style || 1,
                            rules: [
                                {
                                    required: true,
                                    message: '请选择首页频道选中样式',
                                },
                            ],
                        })(
                            <Radio.Group>
                                <Radio value={1}>下划线</Radio>
                                <Radio value={2}>左上角标</Radio>
                                <Radio value={3}>右上角标</Radio>
                            </Radio.Group>
                        )}
                        <span style={{width: '50%',color: 'rgb(0 0 0 / 45%)'}}>&nbsp;&nbsp;选择角标时，需将图标一并放入资源包中上传</span>
                    </Form.Item>
                    <h4>（三）底部导航配置</h4>
                    <Tabs type="card" forceRender={true}>
                        <Tabs.TabPane tab="首页" key="1">
                            <Form.Item label="类型" style={{display: "none"}}>
                                {getFieldDecorator('tab_config_list[0].type', {
                                    initialValue: '1',
                                    rules: [
                                        {
                                            required: true,
                                            message: '请选择类型',
                                        },
                                    ],
                                })(<Input/>)}
                            </Form.Item>
                            <Form.Item label="图标类型">
                                {getFieldDecorator('tab_config_list[0].img_type', {
                                    initialValue: formContent?.tab_config_list[0].img_type || 0,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请选择类型',
                                        },
                                    ],
                                })(
                                        <Radio.Group>
                                            <Radio value={0}>图片</Radio>
                                            <Radio value={1}>动画</Radio>
                                        </Radio.Group>
                                  )}
                                <span style={{width: '50%',color: 'rgb(0 0 0 / 45%)'}}>&nbsp;&nbsp;图片或动画，需一并放入资源包中上传</span>
                            </Form.Item>
                            <Form.Item label="选中字体颜色">
                                {getFieldDecorator('tab_config_list[0].select_color', {
                                    initialValue: formContent?.tab_config_list[0].select_color,
                                    rules: [
                                        {
                                            required: true,
                                            validator: (rule:any,value:string,callback:any)=>validateLogoColor(rule,value,callback,'选中字体颜色',6),
                                        },
                                    ],
                                })(<Input placeholder="输入色值" style={{width: '50%'}} maxLength={9}/>)}
                                <span style={{width: '50%',color: 'rgb(0 0 0 / 45%)'}}>&nbsp;&nbsp;示例：#000000</span>
                            </Form.Item>
                            <Form.Item label="未选中字体颜色">
                                {getFieldDecorator('tab_config_list[0].unselect_color', {
                                    initialValue: formContent?.tab_config_list[0].unselect_color,
                                    rules: [
                                        {
                                            required: true,
                                            validator: (rule:any,value:string,callback:any)=>validateLogoColor(rule,value,callback,'未选中字体颜色',6),
                                        },
                                    ],
                                })(<Input placeholder="输入色值" style={{width: '50%'}} maxLength={9}/>)}
                                <span style={{width: '50%',color: 'rgb(0 0 0 / 45%)'}}>&nbsp;&nbsp;示例：#000000</span>
                            </Form.Item>
                        </Tabs.TabPane>
                        <Tabs.TabPane tab="潮客" key="2">
                            <Form.Item label="类型" style={{display:"none"}}>
                                {getFieldDecorator('tab_config_list[1].type', {
                                    initialValue: '2',
                                    rules: [
                                        {
                                            required: true,
                                            message: '请选择类型',
                                        },
                                    ],
                                })(<Input/>)}
                            </Form.Item>
                            <Form.Item label="图标类型">
                                {getFieldDecorator('tab_config_list[1].img_type', {
                                    initialValue: formContent?.tab_config_list[1].img_type || 0,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请选择类型',
                                        },
                                    ],
                                })(
                                    <Radio.Group>
                                        <Radio value={0}>图片</Radio>
                                        <Radio value={1}>动画</Radio>
                                    </Radio.Group>
                                )}
                                <span style={{width: '50%',color: 'rgb(0 0 0 / 45%)'}}>&nbsp;&nbsp;图片或动画，需一并放入资源包中上传</span>
                            </Form.Item>
                            <Form.Item label="选中字体颜色">
                                {getFieldDecorator('tab_config_list[1].select_color', {
                                    initialValue: formContent?.tab_config_list[1].select_color,
                                    rules: [
                                        {
                                            required: true,
                                            validator: (rule:any,value:string,callback:any)=>validateLogoColor(rule,value,callback,'选中字体颜色',6),
                                        },
                                    ],
                                })(<Input placeholder="输入色值" style={{width: '50%'}} maxLength={9}/>)}
                                <span style={{width: '50%',color: 'rgb(0 0 0 / 45%)'}}>&nbsp;&nbsp;示例：#000000</span>
                            </Form.Item>
                            <Form.Item label="未选中字体颜色">
                                {getFieldDecorator('tab_config_list[1].unselect_color', {
                                    initialValue: formContent?.tab_config_list[1].unselect_color,
                                    rules: [
                                        {
                                            required: true,
                                            validator: (rule:any,value:string,callback:any)=>validateLogoColor(rule,value,callback,'未选中字体颜色',6),
                                        },
                                    ],
                                })(<Input placeholder="输入色值" style={{width: '50%'}} maxLength={9}/>)}
                                <span style={{width: '50%',color: 'rgb(0 0 0 / 45%)'}}>&nbsp;&nbsp;示例：#000000</span>
                            </Form.Item>
                        </Tabs.TabPane>
                        <Tabs.TabPane tab="视频" key="3">
                            <Form.Item label="类型" style={{display:"none"}}>
                                {getFieldDecorator('tab_config_list[2].type', {
                                    initialValue: '3',
                                    rules: [
                                        {
                                            required: true,
                                            message: '请选择类型',
                                        },
                                    ],
                                })(<Input/>)}
                            </Form.Item>
                            <Form.Item label="图标类型">
                                {getFieldDecorator('tab_config_list[2].img_type', {
                                    initialValue: formContent?.tab_config_list[2].img_type || 0,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请选择类型',
                                        },
                                    ],
                                })(
                                    <Radio.Group>
                                        <Radio value={0}>图片</Radio>
                                        <Radio value={1}>动画</Radio>
                                    </Radio.Group>
                                )}
                                <span style={{width: '50%',color: 'rgb(0 0 0 / 45%)'}}>&nbsp;&nbsp;图片或动画，需一并放入资源包中上传</span>
                            </Form.Item>
                            <Form.Item label="选中字体颜色">
                                {getFieldDecorator('tab_config_list[2].select_color', {
                                    initialValue: formContent?.tab_config_list[2].select_color,
                                    rules: [
                                        {
                                            required: true,
                                            validator: (rule:any,value:string,callback:any)=>validateLogoColor(rule,value,callback,'选中字体颜色',6),
                                        },
                                    ],
                                })(<Input placeholder="输入色值" style={{width: '50%'}} maxLength={9}/>)}
                                <span style={{width: '50%',color: 'rgb(0 0 0 / 45%)'}}>&nbsp;&nbsp;示例：#000000</span>
                            </Form.Item>
                            <Form.Item label="未选中字体颜色">
                                {getFieldDecorator('tab_config_list[2].unselect_color', {
                                    initialValue: formContent?.tab_config_list[2].unselect_color,
                                    rules: [
                                        {
                                            required: true,
                                            validator: (rule:any,value:string,callback:any)=>validateLogoColor(rule,value,callback,'未选中字体颜色',6),
                                        },
                                    ],
                                })(<Input placeholder="输入色值" style={{width: '50%'}} maxLength={9}/>)}
                                <span style={{width: '50%',color: 'rgb(0 0 0 / 45%)'}}>&nbsp;&nbsp;示例：#000000</span>
                            </Form.Item>
                            <Form.Item label="尺寸设置">
                                {getFieldDecorator('tab_config_list[2].max_size', {
                                    initialValue: formContent ? formContent.tab_config_list[2].max_size : true,
                                    valuePropName: 'checked',
                                })(<Checkbox
                                    value={formContent ? formContent.tab_config_list[2].max_size : true}
                                >最大尺寸</Checkbox>)}
                            </Form.Item>
                        </Tabs.TabPane>
                        <Tabs.TabPane tab="互动" key="4">
                            <Form.Item label="类型" style={{display:"none"}}>
                                {getFieldDecorator('tab_config_list[3].type', {
                                    initialValue: '4',
                                    rules: [
                                        {
                                            required: true,
                                            message: '请选择类型',
                                        },
                                    ],
                                })(<Input/>)}
                            </Form.Item>
                            <Form.Item label="图标类型">
                                {getFieldDecorator('tab_config_list[3].img_type', {
                                    initialValue: formContent?.tab_config_list[3].img_type || 0,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请选择类型',
                                        },
                                    ],
                                })(
                                    <Radio.Group>
                                        <Radio value={0}>图片</Radio>
                                        <Radio value={1}>动画</Radio>
                                    </Radio.Group>
                                )}
                                <span style={{width: '50%',color: 'rgb(0 0 0 / 45%)'}}>&nbsp;&nbsp;图片或动画，需一并放入资源包中上传</span>
                            </Form.Item>
                            <Form.Item label="选中字体颜色">
                                {getFieldDecorator('tab_config_list[3].select_color', {
                                    initialValue: formContent?.tab_config_list[3].select_color,
                                    rules: [
                                        {
                                            required: true,
                                            validator: (rule:any,value:string,callback:any)=>validateLogoColor(rule,value,callback,'选中字体颜色',6),
                                        },
                                    ],
                                })(<Input placeholder="输入色值" style={{width: '50%'}} maxLength={9}/>)}
                                <span style={{width: '50%',color: 'rgb(0 0 0 / 45%)'}}>&nbsp;&nbsp;示例：#000000</span>
                            </Form.Item>
                            <Form.Item label="未选中字体颜色">
                                {getFieldDecorator('tab_config_list[3].unselect_color', {
                                    initialValue: formContent?.tab_config_list[3].unselect_color,
                                    rules: [
                                        {
                                            required: true,
                                            validator: (rule:any,value:string,callback:any)=>validateLogoColor(rule,value,callback,'未选中字体颜色',6),
                                        },
                                    ],
                                })(<Input placeholder="输入色值" style={{width: '50%'}} maxLength={9}/>)}
                                <span style={{width: '50%',color: 'rgb(0 0 0 / 45%)'}}>&nbsp;&nbsp;示例：#000000</span>
                            </Form.Item>
                        </Tabs.TabPane>
                        <Tabs.TabPane tab="我的" key="5">
                            <Form.Item label="类型" style={{display:"none"}}>
                                {getFieldDecorator('tab_config_list[4].type', {
                                    initialValue: '5',
                                    rules: [
                                        {
                                            required: true,
                                            message: '请选择类型',
                                        },
                                    ],
                                })(<Input/>)}
                            </Form.Item>
                            <Form.Item label="图标类型">
                                {getFieldDecorator('tab_config_list[4].img_type', {
                                    initialValue: formContent?.tab_config_list[4].img_type || 0,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请选择类型',
                                        },
                                    ],
                                })(
                                    <Radio.Group>
                                        <Radio value={0}>图片</Radio>
                                        <Radio value={1}>动画</Radio>
                                    </Radio.Group>
                                )}
                                <span style={{width: '50%',color: 'rgb(0 0 0 / 45%)'}}>&nbsp;&nbsp;图片或动画，需一并放入资源包中上传</span>
                            </Form.Item>
                            <Form.Item label="选中字体颜色">
                                {getFieldDecorator('tab_config_list[4].select_color', {
                                    initialValue: formContent?.tab_config_list[4].select_color,
                                    rules: [
                                        {
                                            required: true,
                                            validator: (rule:any,value:string,callback:any)=>validateLogoColor(rule,value,callback,'选中字体颜色',6),
                                        },
                                    ],
                                })(<Input placeholder="输入色值" style={{width: '50%'}} maxLength={9}/>)}
                                <span style={{width: '50%',color: 'rgb(0 0 0 / 45%)'}}>&nbsp;&nbsp;示例：#000000</span>
                            </Form.Item>
                            <Form.Item label="未选中字体颜色">
                                {getFieldDecorator('tab_config_list[4].unselect_color', {
                                    initialValue: formContent?.tab_config_list[4].unselect_color,
                                    rules: [
                                        {
                                            required: true,
                                            validator: (rule:any,value:string,callback:any)=>validateLogoColor(rule,value,callback,'未选中字体颜色',6),
                                        },
                                    ],
                                })(<Input placeholder="输入色值" style={{width: '50%'}} maxLength={9}/>)}
                                <span style={{width: '50%',color: 'rgb(0 0 0 / 45%)'}}>&nbsp;&nbsp;示例：#000000</span>
                            </Form.Item>
                        </Tabs.TabPane>
                    </Tabs>
                </div>
            </Form>
        </>
    );
});

export default Form.create({})(AppThemeMgrForm);
