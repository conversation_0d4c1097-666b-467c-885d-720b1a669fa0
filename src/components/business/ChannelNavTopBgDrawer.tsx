import { sysApi as api } from '@app/api';
import { Form, Input, message, Tooltip, Icon, Switch, Spin, Divider, Radio } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { Drawer, FileUploader, ImageUploader } from '../common';

@connectSession
@(Form.create({ name: 'ChannelNavTopBgDrawer' }) as any)
class ChannelNavTopBgDrawer extends React.Component<any, any> {
  state = {
    loading: false,
    detail: {},
  };

  doSubmit = () => {
    const { form, record, onOk } = this.props;
    form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        const body = {
          ...values,
          id: record.id,
          channel_nav_type: values.channel_nav_type ? 1 : 0,
        };
        api
          .updateNavHeadStyle(body)
          .then(() => {
            setMLoading(this, false);
            message.success('操作成功');
            onOk();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  navBarItemValidator = (isSelected: boolean, callback: Function) => {
    const {
      form: { getFieldsValue },
    } = this.props;
    const values = getFieldsValue();
    const color = values[isSelected ? 'selected_color' : 'unselected_color'];
    const pic = values[isSelected ? 'selected_pic_url' : 'unselected_pic_url'];
    if (!color && !pic) {
      callback('请输入色值或选择图片');
      return;
    }
    if (color && pic) {
      callback('色值与选择图片只能选择1个');
      return;
    }
    if (color && !/^#[0-9ABCDEFGabcdefg]{6}$/.test(color)) {
      callback('色值格式不正确');
      return;
    }
    callback();
  };

  selectedNavBarItemValidator = (rule: any, value: string, callback: Function) => {
    this.navBarItemValidator(true, callback);
  };

  unselectedNavBarItemValidator = (rule: any, value: string, callback: Function) => {
    this.navBarItemValidator(false, callback);
  };

  componentDidMount(): void {
    if (!!this.props.record?.id) {
      api
        .queryNavHeadStyle({ id: this.props.record.id })
        .then((res) => {
          const data: any = res.data;
          data.head_style.status = Boolean(data.head_style.status);
          this.setState({ loading: false, detail: data.head_style });
        })
        .catch(() => {
          this.setState({
            loading: false,
          });
        });
    }
  }
  // componentDidUpdate(prevProps: Readonly<any>, prevState: Readonly<any>, snapshot?: any): void {
  //   const { visible, record } = this.props
  //   if (!prevProps.visible && visible && record) {
  //     this.setState({ loading: true, detail: {} })
  //     api.queryNavHeadStyle({ id: record.id })
  //       .then((res) => {
  //         const data: any = res.data
  //         data.head_style.status = Boolean(data.head_style.status)
  //         this.setState({ loading: false, detail: data.head_style })
  //       })
  //       .catch(() => {
  //         this.setState({
  //           loading: false
  //         })
  //       })
  //   }
  // }

  validatorColor(rule: any, value: any, callback: any, length: 6 | 8 = 6) {
    if (!!value) {
      let regex = new RegExp(`^#[0-9ABCDEFGabcdefg]{${length}}$`);
      if (!regex.test(value)) {
        callback('请输入正确格式的色值');
        return;
      }
    }
    callback();

    // if (!!value?.list_title?.trim() && !!value?.url?.trim()) {
    //   if (value?.list_title?.trim()?.length > maxLength) {
    //     console.log('validator', showCount)
    //     if (showCount == 0) {
    //       callback('标题不能超过' + maxLength + '字')
    //     } else {
    //       callback('标题不能超过' + maxLength + '字' + ',最好大于20字')
    //     }
    //     return
    //   }
    //   callback()
    // } else if (!value?.list_title?.trim() && !value?.url?.trim()) {
    //   callback('内容不能为空')
    // } else {
    //   if (!value?.list_title?.trim()) {
    //     callback('请输入标题')
    //   } else {
    //     callback('请输入跳转链接')
    //   }
    // }
  }

  // 添加动态校验方法，当开关关闭时不校验必填项
  getConditionalRules = (baseRules: any[]) => {
    const { form } = this.props;
    const isCustomStyleEnabled = form.getFieldValue('channel_nav_type');

    if (!isCustomStyleEnabled) {
      // 当开关关闭时，移除必填校验
      return baseRules.filter((rule) => !rule.required);
    }
    return baseRules;
  };

  render() {
    const {
      form: { getFieldDecorator, getFieldValue },
      visible,
      onClose,
      record,
    } = this.props;
    const detail: any = this.state.detail;
    const formLayout = {
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
    };
    const isYaYun = record && record.name === '亚运';

    return (
      <Drawer
        title={
          <>
            <span>设置单个频道头部样式&nbsp;</span>
            <span style={{ fontSize: 14, fontWeight: 'normal', color: 'gray' }}>
              注：仅针对6.3.0及以上版本生效，低版本不响应自定义样式
            </span>
          </>
        }
        visible={visible}
        skey="ChannelNavTopBgDrawer"
        onClose={onClose}
        onOk={this.doSubmit}
      >
        <Spin tip="正在加载..." spinning={this.state.loading}>
          <Form {...formLayout}>
            <Form.Item label="自定义样式">
              {getFieldDecorator('channel_nav_type', {
                initialValue: Boolean(detail.channel_nav_type),
                valuePropName: 'checked',
                rules: [
                  {
                    required: true,
                    // message: '请输入活成就介绍',
                  },
                ],
              })(<Switch />)}
            </Form.Item>
            <>
              <Form.Item label="导航选中" extra="支持jpg, png, gif等格式, 建议尺寸60px * 28px">
                {getFieldDecorator('selected_pic_url', {
                  initialValue: detail.selected_pic_url || '',
                  preserve: true,
                  rules: this.getConditionalRules([{ required: true, message: '请上传图片' }]),
                })(<ImageUploader />)}
              </Form.Item>

              <Form.Item
                label={
                  <span style={{ position: 'relative' }}>
                    <Tooltip title="如果用户当前使用的主题设置导航文字为浅色，则该频道在导航条未选中时，会显示浅色图片">
                      <Icon
                        type="question-circle"
                        style={{ position: 'absolute', left: -30, top: 0 }}
                      />
                    </Tooltip>
                    导航未选中-浅色
                  </span>
                }
                extra="支持jpg, png, gif等格式, 建议尺寸60px * 28px"
              >
                {getFieldDecorator('unselected_low_pic_url', {
                  initialValue: detail.unselected_low_pic_url || '',
                  preserve: true,
                  rules: this.getConditionalRules([{ required: true, message: '请上传图片' }]),
                })(<ImageUploader />)}
              </Form.Item>

              <Form.Item
                label={
                  <span style={{ position: 'relative' }}>
                    <Tooltip title="如果用户当前使用的主题设置导航文字为深色，则该频道在导航条未选中时，会显示深色图片">
                      <Icon
                        type="question-circle"
                        style={{ position: 'absolute', left: -30, top: 0 }}
                      />
                    </Tooltip>
                    导航未选中-深色
                  </span>
                }
                extra="支持jpg, png, gif等格式, 建议尺寸60px * 28px"
              >
                {getFieldDecorator('unselected_high_pic_url', {
                  initialValue: detail.unselected_high_pic_url || '',
                  preserve: true,
                  rules: this.getConditionalRules([{ required: true, message: '请上传图片' }]),
                })(<ImageUploader />)}
              </Form.Item>

              <Divider type="horizontal"></Divider>

              <Form.Item
                label="频道背景上图"
                extra="支持jpg, png, gif等格式, 比例未限制, 建议尺寸1125px * 1350px"
              >
                {getFieldDecorator('channel_up_pic_url', {
                  initialValue: detail.channel_up_pic_url || '',
                  preserve: true,
                  rules: this.getConditionalRules([{ required: true, message: '请上传图片' }]),
                })(<ImageUploader isCutting={true} />)}
              </Form.Item>
              <Form.Item
                label="频道背景下图"
                extra="支持jpg, png, gif等格式, 比例未限制, 建议宽度1125px"
              >
                {getFieldDecorator('channel_down_pic_url', {
                  initialValue: detail.channel_down_pic_url || '',
                  preserve: true,
                  rules: this.getConditionalRules([{ required: true, message: '请上传图片' }]),
                })(<ImageUploader isCutting={true} />)}
              </Form.Item>

              <Form.Item label="服务图标" extra="支持jpg, png, gif等格式, 建议尺寸114px * 114px">
                {getFieldDecorator('service_pic_url', {
                  initialValue: detail.service_pic_url || '',
                  preserve: true,
                  rules: this.getConditionalRules([{ required: true, message: '请上传图片' }]),
                })(<ImageUploader ratio={1} />)}
              </Form.Item>

              <Form.Item label="社区入口动画" extra="支持json格式文件 建议上传大小为42 * 42">
                {getFieldDecorator('animate_json_url', {
                  initialValue: detail.animate_json_url || '',
                  rules: [
                    {
                      required: true,
                      message: '请上传文件',
                    },
                  ],
                })(<FileUploader accept=".json" download={true} />)}
              </Form.Item>

              <Form.Item label="读报图标" extra="支持jpg, png, gif等格式, 建议尺寸114px * 114px">
                {getFieldDecorator('paper_pic_url', {
                  initialValue: detail.paper_pic_url || '',
                  preserve: true,
                  rules: this.getConditionalRules([{ required: true, message: '请上传图片' }]),
                })(<ImageUploader ratio={1} />)}
              </Form.Item>

              {/* <Form.Item label="频道主题色" extra="输入背景填充色值 例: #000000">
                {getFieldDecorator('channel_theme_value', {
                  initialValue: detail.channel_theme_value || '',
                  preserve: true,
                  rules: [{ required: true, message: '请输入色值' }, {
                    validator: (rule: any, value: any, callback: any) => this.validatorColor(rule, value, callback)
                  }],
                })(<Input placeholder="输入色值" />)}
              </Form.Item> */}
              <Form.Item label="LOGO颜色" extra="输入背景填充色值 例: #000000">
                {getFieldDecorator('channel_logo_color', {
                  initialValue: detail.channel_logo_color || '',
                  preserve: true,
                  rules: this.getConditionalRules([
                    { required: true, message: '请输入色值' },
                    {
                      validator: (rule: any, value: any, callback: any) =>
                        this.validatorColor(rule, value, callback),
                    },
                  ]),
                })(<Input placeholder="输入色值" />)}
              </Form.Item>

              <Form.Item
                label={
                  <span style={{ position: 'relative' }}>
                    {/* <Tooltip title=''>
                <Icon type="question-circle" style={{ position: 'absolute', left: -30, top: 0 }} />
              </Tooltip> */}
                    搜索图标颜色
                  </span>
                }
                extra="输入背景填充色值 例: #000000"
              >
                {getFieldDecorator('search_icon_color', {
                  initialValue: detail.search_icon_color || '',
                  preserve: true,
                  rules: this.getConditionalRules([
                    { required: true, message: '请输入色值' },
                    {
                      validator: (rule: any, value: any, callback: any) =>
                        this.validatorColor(rule, value, callback),
                    },
                  ]),
                })(<Input placeholder="输入色值" />)}
              </Form.Item>

              <Form.Item
                label={
                  <span style={{ position: 'relative' }}>
                    {/* <Tooltip title=''>
                <Icon type="question-circle" style={{ position: 'absolute', left: -30, top: 0 }} />
              </Tooltip> */}
                    搜索框底色
                  </span>
                }
                extra="输入背景填充色值 例: #FF000000，前两位为透明度"
              >
                {getFieldDecorator('search_bottom_color', {
                  initialValue: detail.search_bottom_color || '',
                  preserve: true,
                  rules: this.getConditionalRules([
                    { required: true, message: '请输入色值' },
                    {
                      validator: (rule: any, value: any, callback: any) =>
                        this.validatorColor(rule, value, callback, 8),
                    },
                  ]),
                })(<Input placeholder="输入色值" />)}
              </Form.Item>

              <Form.Item
                label={<span style={{ position: 'relative' }}>安卓大屏上图背景色</span>}
                extra="输入背景填充色值 例: #000000"
              >
                {getFieldDecorator('channel_theme_value', {
                  initialValue: detail.channel_theme_value || '',
                  rules: this.getConditionalRules([
                    {
                      validator: (rule: any, value: any, callback: any) =>
                        this.validatorColor(rule, value, callback, 6),
                    },
                  ]),
                })(<Input placeholder="输入色值" />)}
              </Form.Item>

              <Form.Item label="其他频道导航文字">
                {getFieldDecorator('other_font_style', {
                  initialValue: detail.other_font_style || 0,
                  preserve: true,
                  rules: this.getConditionalRules([
                    { required: true, message: '请选择其他频道导航文字风格' },
                  ]),
                })(
                  <Radio.Group>
                    <Radio value={0}>浅色风格</Radio>
                    <Radio value={1}>深色风格</Radio>
                  </Radio.Group>
                )}
              </Form.Item>
            </>
          </Form>
        </Spin>
      </Drawer>
    );
  }
}

export default ChannelNavTopBgDrawer;
