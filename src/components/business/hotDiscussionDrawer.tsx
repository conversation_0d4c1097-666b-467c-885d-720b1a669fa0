import { Checkbox, Form, InputNumber, Radio, Select, Tooltip, message } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import _ from 'lodash';
import { communityApi, opApi } from '@app/api';
import { Drawer } from '@app/components/common';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';

const HotDiscussionDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();

  const [searchResults, setSearchResults] = useState<any>([]);
  const [circleList, setCircleList] = useState<any>([]);
  const [range, setRange] = useState<any>(0);
  const { getFieldDecorator, getFieldsValue, getFieldValue, setFieldsValue } = props.form;

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val) {
      setSearchResults([]);
      return;
    }
    // type: 1, current: 1, size: 50, biz_type: props.formContent?.biz_type,
    opApi
      .searchTopicByName({ name: val, type: 10 })
      .then((res: any) => {
        setSearchResults(res.data.list.records || []);
      })
      .catch(() => {});
  }, 500);

  useEffect(() => {
    if (!!props.record && !!props.record.title) {
      handleAccountSearch(props.record.title);
    } else {
      handleAccountSearch('');
    }
    if (props.visible) {
      getCircleList();
      setRange(!props.record || props.record?.ref_ids2 == 0 ? '0' : '1');
    }
  }, [props.visible]);

  const getCircleList = () => {
    communityApi
      .getCircleList({ current: 1, size: 100, enabled: true })
      .then((res) => {
        const { list = [] } = res.data as any;
        setCircleList(list.records);
      })
      .catch(() => {});
  };

  const handleSubmit = () => {
    // e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        dispatch(setConfig({ mLoading: true }));
        let promise = null;
        const parmas: any = {};
        if (range == 0) {
          parmas.ref_ids2 = '0';
        } else {
          parmas.ref_ids2 = values.ref_ids2.join(',');
        }
        parmas.ref_ids = values.ref_ids.split('-')[0];
        parmas.title = values.ref_ids.split('-')[1];
        delete parmas.ref_ids3;

        if (!props.record) {
          parmas.position = values.position;
          // 新增
          promise = opApi.createHotRecommendTopic(parmas);
        } else {
          parmas.id = props.record.id;
          promise = opApi.updateHotRecommendTopic(parmas);
        }
        promise
          .then((res: any) => {
            message.success(!props.record ? '新增成功' : '更新成功');
            dispatch(setConfig({ mLoading: false }));
            props.onEnd && props.onEnd();
          })
          .catch(() => {
            // message.error('添加失败');
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
        dispatch(setConfig({ mLoading: false }));
      }
    });
  };

  return (
    <Drawer
      title={!props.record ? '添加热议话题' : '编辑热议话题'}
      visible={props.visible}
      skey={props.skey}
      onClose={props.onClose}
      maskClosable={false}
      // width={500}
      onOk={handleSubmit}
      okText="保存"
    >
      <Form {...formLayout}>
        <Form.Item label="话题名称">
          {getFieldDecorator('ref_ids', {
            initialValue: props.record
              ? `${props.record?.ref_ids}-${props.record?.title}`
              : undefined,
            rules: [
              {
                required: true,
                message: '请填写话题名称',
                whitespace: true,
              },
            ],
          })(
            <Select
              disabled={!!props.record}
              placeholder="输入话题名称搜索"
              onSearch={handleAccountSearch}
              showSearch
              filterOption={false}
            >
              {searchResults.map((d: any, i: number) => (
                <Select.Option key={`${i}`} value={`${d.id}-${d.name}`}>
                  {`#${d.name}#`}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>

        <Form.Item label="关联圈子" required>
          <Radio.Group value={range} onChange={(e) => setRange(e.target.value)}>
            <Radio value={'0'}>全部圈子</Radio>
            <Radio value={'1'}>部分圈子</Radio>
          </Radio.Group>
          {/* style={{ marginLeft: 114 }} */}
          {range == 1 && (
            <div>
              {getFieldDecorator('ref_ids2', {
                initialValue: props.record?.ref_ids2?.split(','),
                rules: [
                  {
                    required: true,
                    message: '请选择关联圈子',
                  },
                ],
              })(
                <Checkbox.Group>
                  {circleList.map((item: any) => (
                    <Checkbox key={item.id} value={`${item.id}`}>
                      {item.name + (item.enabled ? '' : '（已下线）')}
                    </Checkbox>
                  ))}
                </Checkbox.Group>
              )}
            </div>
          )}
        </Form.Item>

        {!props.record && (
          <Form.Item label="输入位置">
            {getFieldDecorator('position', {
              initialValue: props.record?.position,
              rules: [
                {
                  required: true,
                  message: '请填写位置',
                },
                {
                  min: 1,
                  message: '不能小于1',
                  type: 'number',
                },
                {
                  max: props.maxPosition,
                  message: `不能大于${props.maxPosition}`,
                  type: 'number',
                },
              ],
            })(
              <InputNumber
                max={props.maxPosition}
                style={{ width: 200 }}
                precision={0}
                placeholder="请输入位置序号"
                min={1}
              />
            )}
          </Form.Item>
        )}
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'HotDiscussionDrawer' })(
  forwardRef<any, any>(HotDiscussionDrawer)
);
