import { opApi as api, communityApi } from '@app/api';
import { Checkbox, Form, Input, message, Radio, Tooltip, Icon, Select } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader } from '../common';
import ReactWEditor from 'wangeditor-for-react';
import '@components/business/styles/business.scss';
import _, { set } from 'lodash';
import EditCircleInfoModal from '@app/views/community/component/EditCircleInfoModal';
import CircleInput from './circleInput';

@connectSession
@(Form.create({ name: 'UGCTopicForm' }) as any)
class UGCTopicForm extends React.Component<any, any> {
  descElRef = React.createRef<ReactWEditor>();

  handleEnterKeydown = (e: any) => {
    if (e.key === 'Enter') {
      e.preventDefault();
    }
  };

  constructor(props: any) {
    super(props);
    const activeUser = props.record?.created_user_name && props?.record?.account_id;
    this.state = {
      ...props.record,
      class_ids: props.record?.class_ids
        ? props.record?.class_ids.split(',').map((v: any) => v.toString())
        : [],
      show_style: props.record?.show_style || 0,
      rank_status: props.record?.rank_status || 0,
      type: props.record?.type || 2,
      show_type: props.record?.show_type || 1,
      classList: props.classList || [],
      ratio: 0,
      topicError: false,
      accountOptions: [],
      created_user_name: activeUser ? props?.record?.created_user_name : undefined,
      account_id: activeUser ? props?.record?.account_id : undefined,
      showAccount: !!props?.record?.account_id,
    };
  }

  componentDidMount() {
    if (this.state.rank_logo) {
      const img = new Image();
      img.src = this.state.rank_logo;
      img.onload = () => {
        if (img.width / img.height > 1.05) {
          this.setState({
            ratio: 2,
          });
        } else {
          this.setState({
            ratio: 1,
          });
        }
      };
    } else {
      this.setState({
        ratio: 1,
      });
    }
    const descEl = this.descElRef.current;
    if (descEl && descEl.editor) {
      const el = document.querySelector(`[data-we-id=${descEl.editor.id}] div[contenteditable]`);
      if (el) {
        el.addEventListener('keydown', this.handleEnterKeydown);
      }
    }

    if (this.props?.record?.created_user_name && this.state.account_id) {
      this.handleAccountSearch(this.props?.record?.created_user_name);
    }
  }

  componentWillUnmount() {
    const descEl = this.descElRef.current;
    if (descEl && descEl.editor) {
      const el = document.querySelector(`[data-we-id=${descEl.editor.id}] div[contenteditable]`);
      if (el) {
        el.removeEventListener('keydown', this.handleEnterKeydown);
      }
    }
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  ratioChagne = (e: any) => {
    this.setState({
      ratio: e.target.value,
    });
  };

  validateDesc = () => {
    // 判断简介是否输入，如果输入是否超过50字
    const descEl = this.descElRef.current;
    if (descEl && descEl.editor) {
      const desc = descEl.editor.txt.text();
      if (desc && desc.length > 50) {
        this.setState({
          topicError: true,
        });
        return false;
      }
    }
    this.setState({
      topicError: false,
    });
    return true;
  };

  doSubmit = () => {
    const values = this.props.form.getFieldsValue();
    const fields = [
      'name',
      'logo_url',
      'background_url',
      'class_ids',
      'show_style',
      'rank_status',
      'type',
      'show_type',
      'enable_recent_hot',
      'account_id',
      'circle',
    ];
    if (values.rank_status === 1) {
      fields.push('rank_desc');
      fields.push('rank_logo');
    }
    if (values.enable_recent_hot) {
      fields.push('hot_sort_by');
    }
    this.props.form.validateFieldsAndScroll(fields, (err: any, values: any) => {
      if (!err) {
        if (!this.validateDesc()) {
          return;
        }
        setMLoading(this, true);
        let func = 'createTopic';
        const body = {
          ...values,
          class_ids: values.class_ids.join(','),
          name: values.name?.trim() || '',
          description: this.state.description || '',
          hot_sort_by: values.hot_sort_by ?? 2,
          background_url: values.background_url || '',
        };
        delete body.circle;
        if (values.circle?.circle_id) {
          body.circle_id = values.circle.circle_id;
        }
        if (values.circle?.board_id) {
          body.board_id = values.circle.board_id;
        }

        if (this.state.id) {
          func = 'updateTopic';
          body.id = this.state.id;
        }
        api[func as keyof typeof api](body)
          .then(() => {
            setMLoading(this, false);
            message.success('操作成功');
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  handleTypeChange = (type: any) => {
    const {
      form: { getFieldsValue, setFieldsValue },
    } = this.props;
    const values = getFieldsValue();
    const { show_type } = values;
    if (type === 2 && show_type === 3) {
      setFieldsValue({ show_type: 1 });
    } else if (type === 1 && show_type === 1) {
      setFieldsValue({ show_type: 3 });
    }
  };

  handleAccountSearch = _.debounce((val: any) => {
    if (!val) {
      this.setState({
        ...this.state,
        accountOptions: [],
      });
      return;
    }
    // type: 1, current: 1, size: 50, biz_type: props.formContent?.biz_type,
    communityApi
      .recommendAccount_Search({ keyword: val })
      .then((res) => {
        this.setState({
          ...this.state,
          accountOptions: res?.data?.list || [],
        });
      })
      .catch(() => {});
  }, 500);

  chooseCircle = () => {};

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const { topicError } = this.state;
    const values = this.props.form.getFieldsValue();
    const { type, enable_recent_hot } = values;
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit} ref="form">
        <Form.Item label="话题名称">
          {getFieldDecorator('name', {
            initialValue: this.state.name,
            rules: [
              {
                required: true,
                message: '请填写话题名称',
              },
              {
                max: 15,
                message: '话题名称最长不能超过15个字',
              },
              {
                pattern: /^[^#<>\/]*$/,
                message: '不能输入字符#<>/',
              },
              {
                whitespace: true,
                message: '不能输入全空格',
              },
            ],
          })(<Input placeholder="请输入话题名称" />)}
        </Form.Item>
        <Form.Item label="话题头像" extra="支持jpg,jpeg,png图片格式，比例为 1:1">
          {getFieldDecorator('logo_url', {
            initialValue: this.state.logo_url,
            rules: [
              {
                required: true,
                message: '请上传话题头像',
              },
            ],
          })(<ImageUploader ratio={1 / 1} />)}
        </Form.Item>
        <Form.Item label="背景图" extra="支持jpg,jpeg,png图片格式，比例为 5:2">
          {getFieldDecorator('background_url', {
            initialValue: this.state.background_url || '',
          })(<ImageUploader ratio={5 / 2} />)}
        </Form.Item>
        <Form.Item label="话题分类">
          {getFieldDecorator('class_ids', {
            initialValue: this.state.class_ids,
            rules: [
              {
                required: true,
                message: '请选择话题分类',
              },
            ],
          })(
            <Checkbox.Group style={{ width: '100%' }}>
              {this.state.classList.map((v: any, i: number) => (
                <Checkbox value={v.id.toString()} key={i}>
                  {v.name}
                </Checkbox>
              ))}
            </Checkbox.Group>
          )}
        </Form.Item>
        <Form.Item label="话题类型">
          {getFieldDecorator('type', {
            initialValue: this.state.type,
            rules: [
              {
                required: true,
                message: '请选择话题类型',
              },
            ],
          })(
            <Radio.Group onChange={(e) => this.handleTypeChange(e.target.value)}>
              <Radio value={2}>普通话题</Radio>
              <Radio value={1}>
                视频话题&nbsp;
                <Tooltip
                  title="视频话题，仅限小视频内容参与，在话题页里只展示小视频内容"
                  overlayStyle={{ maxWidth: 410 }}
                >
                  <Icon type="question-circle-o" />
                </Tooltip>
              </Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item label="显示样式">
          {getFieldDecorator('show_type', {
            initialValue: this.state.show_type || 1,
            rules: [
              {
                required: true,
                message: '请选择显示样式',
              },
            ],
          })(
            <Radio.Group>
              {type === 2 && <Radio value={1}>单列</Radio>}
              {type === 1 && <Radio value={3}>三列</Radio>}
              <Radio value={2}>双列</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item className="ugcTopicSort" label="排序方式">
          {getFieldDecorator('enable_recent_hot', {
            initialValue: this.state.enable_recent_hot || false,
            valuePropName: 'checked',
          })(
            <Checkbox>
              开启近期热门&emsp;
              <Tooltip
                overlayStyle={{ maxWidth: 400 }}
                title={
                  <div>
                    近期热门，根据内容发布7天内的阅读/赞评等计算的值，再按发布时间做衰减后得到的综合排序。
                    <br />
                    开启该排序，将会让近期发布的热门内容，在列表中显示在靠前位置。（话题下依旧默认提供最热/最新排序）
                  </div>
                }
              >
                <Icon type="question-circle" />
              </Tooltip>
            </Checkbox>
          )}
          {enable_recent_hot && (
            <Form.Item style={{ marginLeft: 15 }} label="默认排序">
              {getFieldDecorator('hot_sort_by', {
                initialValue: this.state.hot_sort_by ?? 2,
              })(
                <Select style={{ width: 120 }}>
                  <Select.Option value={2}>按近期热门</Select.Option>
                  <Select.Option value={0}>按历史最热</Select.Option>
                </Select>
              )}
            </Form.Item>
          )}
        </Form.Item>
        <Form.Item label="发布关联圈子">
          {getFieldDecorator('circle', {
            initialValue: this.state.circle
              ? {
                  circle_id: this.state.circle?.id,
                  board_id: this.state.circle?.board_id,
                  circle_name: this.state.circle?.name,
                  board_name: this.state.circle?.board_name,
                }
              : null,
          })(<CircleInput />)}
          <Tooltip title="在话题下发布作品时，默认选中指定圈子及版块">
            <Icon style={{ marginLeft: 10 }} type="question-circle" />
          </Tooltip>
        </Form.Item>

        <Form.Item label="标识">
          {getFieldDecorator('show_style', {
            initialValue: this.state.show_style,
          })(
            <Radio.Group>
              <Radio value={0}>无</Radio>
              <Radio value={1}>显示新</Radio>
              <Radio value={2}>显示热</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {this.state.showAccount && (
          <Form.Item label="发起人">
            {getFieldDecorator('account_id', {
              initialValue: this.state.account_id,
              rules: [{ required: true, message: '请选择用户' }],
            })(
              <Select
                style={{ width: 400, marginRight: 8 }}
                // value={reporter}
                placeholder="输入用户昵称或小潮号"
                onSearch={(v) => this.handleAccountSearch(v)}
                showSearch
                filterOption={false}
              >
                {this.state.accountOptions.map((d: any) => (
                  <Select.Option key={d.id} value={d.id}>
                    {['潮客 - ', '潮鸣号 - ', '潮鸣号 - '][d.cert_type] + d.nick_name} | 小潮号：
                    {d.chao_id}
                  </Select.Option>
                ))}
              </Select>
            )}
            <Tooltip title="发起人会显示在话题页，可设置任意用户（可以不是首个申请该话题的用户）">
              <Icon type="question-circle-o" />
            </Tooltip>
          </Form.Item>
        )}
        <Form.Item
          label="话题简介"
          validateStatus={topicError ? 'error' : ''}
          help={topicError ? '话题简介最长不能超过50个字' : ''}
        >
          <ReactWEditor
            ref={this.descElRef}
            className="ugc_topic_form_weditor"
            defaultValue={this.state.description || ''}
            onChange={(description) => {
              this.setState({ description });
              this.validateDesc();
            }}
            instanceHook={{
              // 使用方法是，通常 key 代表的钩子是一个对象，可以利用方法来绑定。方法的形参第一位是当前实例的 editor，后面依次是 key 分割代表的对象。
              'config.menus': function (editor, config: any, menus) {
                config.height = 100;
                config.menus = ['link'];
                config.showFullScreen = false;
                config.linkCheck = function (text: string, link: string) {
                  if (text === link) {
                    message.error('请输入链接文字');
                    return;
                  }
                  return true;
                };
              },
            }}
          />
        </Form.Item>
        <Form.Item label="是否有奖">
          {getFieldDecorator('rank_status', {
            initialValue: this.state.rank_status,
          })(
            <Radio.Group>
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {values.rank_status === 1 && (
          <React.Fragment>
            <Form.Item
              label="奖励图标"
              extra={`支持jpg,jpeg,png图片格式，比例为${this.state.ratio}:1`}
            >
              <Radio.Group value={this.state.ratio} onChange={this.ratioChagne}>
                <Radio value={1}>1:1</Radio>
                <Radio value={2}>2:1</Radio>
              </Radio.Group>
              {getFieldDecorator('rank_logo', {
                initialValue: this.state.rank_logo,
                preserve: true,
                rules: [
                  {
                    required: true,
                    message: '请上传奖励图标',
                  },
                ],
              })(<ImageUploader ratio={this.state.ratio} />)}
            </Form.Item>
            <Form.Item label="奖励规则">
              {getFieldDecorator('rank_desc', {
                initialValue: this.state.rank_desc,
                preserve: true,
                rules: [
                  {
                    required: true,
                    message: '请输入奖励规则',
                  },
                ],
              })(<Input.TextArea placeholder="请输入奖励规则" rows={3} />)}
            </Form.Item>
          </React.Fragment>
        )}

        <EditCircleInfoModal
          record={null}
          visible={this.state.circleInfoModalVisiable}
          onCancel={() => this.setState({ circleInfoModalVisiable: false })}
          onEnd={() => {}}
        />
      </Form>
    );
  }
}

export default UGCTopicForm;
