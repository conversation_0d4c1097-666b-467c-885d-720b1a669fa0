import { opApi as api } from '@app/api';
import {
  Checkbox,
  Form,
  Icon,
  Input,
  Tooltip,
  message,
} from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';
import { ImageUploader, FileSelector } from '../common';
import Radio from 'antd/es/radio';

@connectSession
@(Form.create({ name: 'navIcon' }) as any)
class IndexProposalForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const s = {
      ...props.formContent,
    };

    this.state = {
      ...s,

    };
  }



  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    const { getFieldsValue } = this.props.form;
    const { config_type = 0 } = getFieldsValue()
    let fields: any[]
    if (config_type === 0) {
      fields = ['config_type', 'selectIcon', 'unselectIcon', 'selectColor', 'unSelectColor']
    } else {
      fields = ['config_type', 'selectFile', 'unselectFile', 'show_rule']
    }
    fields.push('max_size')
    this.props.form.validateFieldsAndScroll(fields, (err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        const { config_type = 0, selectFile, unselectFile } = values
        let body = {
          ...values,
        };
        body.id = this.state.id
        if (config_type === 0) {
          body.show_rule = 1
        } else {
          if (selectFile.file_json_url) {
            body.file_json_url = selectFile.file_json_url
            delete body.selectFile
          }
          if (unselectFile.file_json_url) {
            body.unselect_file_json_url = unselectFile.file_json_url
            delete body.unselectFile
          }
        }
        console.log(body)
        api.editNavIcons(body)
          .then((r: any) => {
            setMLoading(this, false);
            message.success('操作成功');
            this.props.onEnd();
          })
          .catch(() => {
            setMLoading(this, false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  render() {
    const { getFieldDecorator, getFieldsValue } = this.props.form;
    const { config_type = this.state.config_type } = getFieldsValue()
    const isVideo = this.state.id === 3 || this.state.nav_position === '视频'
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="导航位置">
          {this.state.nav_position}
        </Form.Item>
        {
          isVideo && <Form.Item style={{ paddingLeft: 60 }}>
            {getFieldDecorator('config_type', {
              initialValue: this.state.config_type,
            })(
              <Radio.Group>
                <Radio value={0}>图片</Radio>
                <Radio value={1}>动画</Radio>
              </Radio.Group>
            )}
          </Form.Item>
        }
        {config_type === 0 && <>
          <Form.Item label="选中图标" extra="支持jpg,jpeg,png,gif图片格式 建议上传大小90*90或120*120">
            {getFieldDecorator('selectIcon', {
              initialValue: this.state.select_icon,
              rules: [
                {
                  required: true,
                  message: '请上传图片',
                },
              ],
            })(
              <ImageUploader
                ratio={1 / 1}
                imgSize={200}
                accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
              />
            )}
          </Form.Item>
          <Form.Item label="未选中图标" extra="支持jpg,jpeg,png,gif图片格式 建议上传大小90*90或120*120">
            {getFieldDecorator('unselectIcon', {
              initialValue: this.state.unselect_icon,
              rules: [
                {
                  required: true,
                  message: '请上传图片',
                },
              ],
            })(
              <ImageUploader
                ratio={1 / 1}
                imgSize={200}
                accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
              />
            )}
          </Form.Item>
          <Form.Item label="选中字体色值" extra="未指定颜色时，默认使用品牌色">
            {getFieldDecorator('selectColor', {
              initialValue: this.state.select_color,
              rules: [
                {
                  pattern: /^#[\d|a-f|A-Z]{6}$/,
                  message: "请输入正确的色值"
                }
              ],
            })(
              <Input style={{ width: 300 }} placeholder='请输入色值，例如#333333' maxLength={7} />
            )}
          </Form.Item>
          <Form.Item label="未选中字体色值" extra="未指定颜色时，默认使用灰色">
            {getFieldDecorator('unSelectColor', {
              initialValue: this.state.unselect_color,
              rules: [
                {
                  pattern: /^#[\d|a-f|A-Z]{6}$/,
                  message: "请输入正确的色值"
                }
              ],
            })(
              <Input style={{ width: 300 }} placeholder='请输入色值，例如#333333' maxLength={7} />
            )}
          </Form.Item>
        </>}
        {config_type === 1 && <>
          <Form.Item label="选中动画" extra="支持json文件格式 建议上传大小90*90或120*120">
            {getFieldDecorator('selectFile', {
              initialValue: this.state.file,
              rules: [
                {
                  required: true,
                  message: '请上传动画json文件',
                },
              ],
            })(<FileSelector accept=".json" maxSize={500 / 1024} />)}
          </Form.Item>
          <Form.Item label="未选中动画" extra="支持json文件格式 建议上传大小90*90或120*120">
            {getFieldDecorator('unselectFile', {
              initialValue: this.state.fileTwo,
              rules: [
                {
                  required: true,
                  message: '请上传动画json文件',
                },
              ],
            })(<FileSelector accept=".json" maxSize={500 / 1024} />)}
          </Form.Item>
          {/* <Form.Item label="展示规则">
            {getFieldDecorator('show_rule', {
              initialValue: this.state.show_rule,
              rules: [
                {
                  required: true,
                  message: '请选择展示规则',
                },
              ],
            })(
              <Radio.Group>
                <Radio value={1}>用户点击后消失</Radio>
                <Tooltip title="动画每天展示一次，点击入口后动画消失，恢复线上默认样式，第二天继续展示。">
                  <Icon style={{ marginLeft: -8 }} type="question-circle" />
                </Tooltip>
                <Radio style={{ marginLeft: 10 }} value={0}>未选中时一直展示</Radio>
                <Tooltip title="无论用户是否点击入口，未选中时动画一直展示，选中后默认线上样式。">
                  <Icon style={{ marginLeft: -8 }} type="question-circle" />
                </Tooltip>
              </Radio.Group>
            )}
          </Form.Item> */}
        </>}
        {isVideo && <Form.Item label="尺寸设置">
          {getFieldDecorator('max_size', {
            initialValue: this.state.max_size,
            valuePropName: 'checked',
          })(<Checkbox>最大尺寸</Checkbox>)}
        </Form.Item>}
      </Form>
    );
  }
}

export default IndexProposalForm;
