import React, { forwardRef, useImperativeHandle } from 'react';
import { Form, Icon, Input } from 'antd';
import ImageUploader from '@components/common/imageUploader';

const { TextArea } = Input;
const FingerPaperForm = forwardRef<any, any>(({ form, handleDelete, length, handleMove }, ref) => {
  const { getFieldDecorator } = form;
  useImperativeHandle(
    ref,
    () => ({
      ...form,
    }),
    []
  );
  return (
    <div style={{ display: 'flex' }}>
      <div style={{ height: '40px', lineHeight: '40px', width: '40px' }}>内容</div>
      <Form labelCol={{ span: 3 }} wrapperCol={{ span: 21 }} style={{ width: '700px' }}>
        <Form.Item label="标题">
          {getFieldDecorator('title', {
            rules: [
              {
                required: true,
                message: '请填写标题',
              },
            ],
          })(<Input placeholder="请输入标题，最多可输入40字" />)}
        </Form.Item>
        <Form.Item label="简介">
          {getFieldDecorator('description', {
            rules: [
              {
                required: true,
                message: '请填写简介',
              },
            ],
          })(<TextArea placeholder="请输入简介，最多可输入60字" autoSize />)}
        </Form.Item>
        <Form.Item label="URL">
          {getFieldDecorator('url', {
            rules: [
              {
                required: true,
                message: '请填写url',
              },
            ],
          })(<Input placeholder="请输入跳转链接" />)}
        </Form.Item>
        <Form.Item label="图片" extra="支持jpg、png、gif等格式">
          {getFieldDecorator('image', {
            rules: [
              {
                required: true,
                message: '请选择图片',
              },
            ],
          })(
            <ImageUploader
              imgsize={1024}
              isCutting={true}
              ratio={1}
              accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
            />
          )}
        </Form.Item>
      </Form>
      {length > 1 ? (
        <div
          style={{
            height: '40px',
            lineHeight: '40px',
            width: '40px',
            textAlign: 'center',
            cursor: 'pointer',
          }}
        >
          <Icon type="swap" style={{ transform: 'rotate(90deg)' }} onClick={handleMove} />
        </div>
      ) : (
        ''
      )}
      {length > 1 ? (
        <div
          onClick={handleDelete}
          style={{
            height: '40px',
            lineHeight: '40px',
            width: '40px',
            textAlign: 'center',
            cursor: 'pointer',
          }}
        >
          <Icon type="delete" />
        </div>
      ) : (
        ''
      )}
    </div>
  );
});

export default Form.create({ name: 'FingerPaperFormItem' })(FingerPaperForm);
