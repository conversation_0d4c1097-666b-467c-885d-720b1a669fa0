import { releaseListApi } from '@app/api';
import ImageUploader from '@components/common/imageUploader';
import SearchAndInput from '@components/common/newsSearchAndInput';
import {
  Form,
  Icon,
  Input,
  InputNumber,
  message,
  Radio,
  Row,
  Tooltip,
  Checkbox,
  Switch,
} from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';

@connectSession
@(Form.create({ name: 'summaryRecommendForm' }) as any)
class SummaryRecommendForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = { ...props.formContent };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        let api: keyof typeof releaseListApi = 'createSummaryRecommend';
        if (this.state.id) {
          api = 'updateSummaryRecommend';
        }
        try {
          releaseListApi[api]({
            ...values,
            [this.state.id ? 'id' : 'channel_id']: this.state.id || this.props.channelId,
            channel_id: this.props.channelId,
            ref_ids: values.channel_article_ids.join(','),
            display_pic: values.style === 3 ? 1 : 0,
            style: values.style === 3 ? 1 : values.style,
            show_title: values.show_title ? 1 : 0,
            nav_type: (values.nav_type || '').trim(),
            cycle_carousel: values.cycle_carousel ? 1 : 0
          })
            .then(() => {
              setMLoading(this, false);
              message.success('操作成功');
              this.props.onEnd();
            })
            .catch(() => {
              setMLoading(this, false);
            });
        } catch (errr) {
          console.error(errr);
        }
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  getColumn = () => {
    return [
      {
        title: '潮新闻ID',
        dataIndex: 'id',
        width: 80,
      },
      {
        title: '新闻频道',
        key: 'type',
        dataIndex: 'channel_name',
        width: 90,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
      },
    ];
  };

  articlesChange = (data: any) => {
    this.setState({ ...data });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };

    const text = (
      <span>
        <p>输入说明：</p>
        <p>
          直接输入阿拉伯数字代表位置序号，例如要在头条频道信息流第8个位置插入该推荐位，则直接输入“8”即可。
        </p>
        <p>最大输入数字为500</p>
        <br />
        <p>注：</p>
        <p>直接在输入的指定位置上插入推荐位显示，不参与稿件的排序，且优先于媒立方稿件展示</p>
      </span>
    );

    const values = this.props.form.getFieldsValue();
    const style = values.style ?? this.state.style;
    const showTitle = values.show_title ?? this.state.show_title ?? false
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="名称">
          {getFieldDecorator('recommend_name', {
            initialValue: this.state.recommend_name,
            rules: [
              {
                required: true,
                message: '请填写名称',
              },
              {
                max: 30,
                message: '名称最长不能超过30个字',
              },
            ],
          })(<Input placeholder="请输入名称" />)}
        </Form.Item>

        {style === 3 && (<Form.Item style={{ marginLeft: 133 }}>
          {getFieldDecorator('show_title', {
            initialValue: this.state.show_title,
            valuePropName: 'checked',
          })(<Checkbox>标题在客户端显示</Checkbox>)}
        </Form.Item>)}

        {style === 3 && showTitle && (<Form.Item label="频道跳转">
          {getFieldDecorator('nav_type', {
            initialValue: this.state.nav_type,
          })(<Input style={{ width: 300 }} placeholder="请输入频道NAV_TYPE，联系技术获取。" />)}
        </Form.Item>)}

        <Form.Item label="插入位置">
          {getFieldDecorator('position', {
            initialValue: this.state.position,
            rules: [
              {
                required: true,
                message: '请输入位置序号',
                type: 'number',
              },
              {
                max: 500,
                message: '最大不能超过500',
                type: 'number',
              },
            ],
          })(<InputNumber max={500} style={{ width: 200 }} placeholder="请输入位置序号" min={1} />)}
          &emsp;
          <Tooltip title={text}>
            <Icon type="question-circle" />
          </Tooltip>
        </Form.Item>
        <Form.Item label="样式选择">
          {getFieldDecorator('style', {
            rules: [{
              required: true,
              message: '请选择样式',
            }],
            initialValue: this.state.style,
          })(
            <Radio.Group>
              <Radio value={2}>走马灯轮播</Radio>
              <Radio value={1}>卡片轮播</Radio>
              <Radio value={3}>显示列表图</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {
          style !== 2 && <Form.Item label="循环轮播">
            {getFieldDecorator('cycle_carousel', {
              rules: [{
                required: true,
                message: '请设置是否轮播',
              }],
              initialValue: this.state.cycle_carousel,
              valuePropName: 'checked',
            })(<Switch />)}
          </Form.Item>
        }
        <Form.Item label="关联稿件">
          {getFieldDecorator('channel_article_ids', {
            initialValue: this.state.channel_article_ids,
            preserve: true,
            rules: [
              {
                required: true,
                message: '请关联新闻',
                type: 'array',
              },
              {
                max: 15,
                message: '最多关联15条新闻',
                type: 'array',
              },
            ].concat(
              style === 2 ? []
                : [
                  {
                    min: 2,
                    message: '为保证客户端显示效果，关联新闻数不能少于2条！',
                    type: 'array',
                  },
                ]
            ),
          })(
            <SearchAndInput
              max={15}
              func="listArticleRecommendSearch"
              columns={this.getColumn()}
              placeholder="输入ID或标题关联稿件"
              initialValues={{ list: this.state.channelArticles }}
              triggerInitialValueChange={this.articlesChange}
              body={{ doc_types: '2,3' }}
              order={true}
              addOnTop={true}
            />
          )}
        </Form.Item>
      </Form>
    );
  }
}

export default SummaryRecommendForm;
