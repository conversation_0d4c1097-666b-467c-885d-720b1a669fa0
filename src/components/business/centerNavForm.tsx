import { sysApi as api } from '@app/api';
import { Checkbox, Form, Input, message, Radio, Select } from 'antd';
import React from 'react';
import { setMLoading } from '@utils/utils';
import connectSession from '@utils/connectSession';

type Api = 'createCenterNav' | 'updateCenterNav';

@connectSession
@(Form.create({ name: 'centerNavForm' }) as any)
class CenterNavForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      ...props.formContent,
      privGroup: props.privGroup || [],
    };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body: any = { ...values, min_version_str: '0.0.0', max_version_str: '10.0.0' };
        body.selected = true;
        let func = 'create';
        if (this.state.id) {
          func = 'update';
          body.id = this.state.id;
        }

        if (body.require_permission == 0) {
          body.require_permission_id = 0
        } else {
          body.require_permission_id = body.require_permission_id.join(',')
        }
        delete body.require_permission

        setMLoading(this, true);
        api[`${func}CenterNav` as Api](body)
          .then(() => {
            message.success('操作成功');
            setMLoading(this, false);
            this.props.onEnd();
          })
          .catch(() => setMLoading(this, false));
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  render() {
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="名称">
          {getFieldDecorator('name', {
            initialValue: this.state.name,
            rules: [
              {
                required: true,
                message: '请填写名称',
              },
              // {
              //   pattern: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/,
              //   message: '名称不能包含特殊字符',
              // },
            ],
          })(<Input placeholder="请输入名称" />)}
        </Form.Item>
        <Form.Item label="简介">
          {getFieldDecorator('information', {
            initialValue: this.state.information,
            rules: [
              {
                max: 7,
                message: '简介最多7个字',
              },
            ],
          })(<Input placeholder="请输入简介" />)}
        </Form.Item>
        <Form.Item label="频道地址">
          {getFieldDecorator('uri_scheme', {
            initialValue: this.state.uri_scheme,
            rules: [
              {
                required: true,
                message: '请输入频道地址',
              },
            ],
          })(<Input placeholder="请输入频道地址" />)}
        </Form.Item>
        <Form.Item label="状态">
          {getFieldDecorator('enabled', {
            initialValue: this.state.enabled,
          })(
            <Radio.Group>
              <Radio value={true}>上线</Radio>
              <Radio value={false}>下线</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {/* <Form.Item label="导航设置">
          {getFieldDecorator('selected', {
            initialValue: this.state.selected,
          })(
            <Radio.Group>
              <Radio value={true}>已选区</Radio>
              <Radio value={false}>待选区</Radio>
            </Radio.Group>
          )}
        </Form.Item> */}
        <Form.Item label="特权组要求">
          {getFieldDecorator('require_permission', {
            initialValue:
              (this.state.require_permission_id == 0 || !this.state.require_permission_id) ? 0 : 1,
          })(
            <Radio.Group>
              <Radio value={0}>所有用户可见</Radio>
              <Radio value={1}>仅特权组可见</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {getFieldValue('require_permission') == 1 && (
          <Form.Item label=" " colon={false} style={{ marginTop: -20 }}>
            {getFieldDecorator('require_permission_id', {
              initialValue: (this.state.require_permission_id == 0 || !this.state.require_permission_id) ? [] : this.state.require_permission_id.toString().split(','),
              rules: [
                {
                  validator: (rule: any, value: any, callback: any) => {
                    if (value?.length > 0) {
                      callback()
                    } else {
                      callback('请至少选择1个特权组');
                    }  
                  },
                },
              ],
            })(
              <Checkbox.Group>
                {this.state.privGroup.map((v: any) => (
                  <Checkbox key={v.id} value={v.id.toString()}>
                    {v.group_name}
                  </Checkbox>
                ))}
              </Checkbox.Group>
            )}
          </Form.Item>
        )}
      </Form>
    );
  }
}

export default CenterNavForm;
