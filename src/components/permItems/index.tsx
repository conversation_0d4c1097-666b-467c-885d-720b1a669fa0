import React from 'react';
import { useStore } from 'react-redux';
import { Menu, Button, Switch } from 'antd';
import { MenuItemProps } from 'antd/es/menu/MenuItem';
import { ButtonProps } from 'antd/es/button/button';
import { SwitchProps } from 'antd/es/switch';
import { A } from '../common';
import {indexOf} from "lodash";

const { Item } = Menu;

export function PermMenuItem(props: MenuItemProps & { perm: string }) {
  const store = useStore();
  const { permissions } = store.getState().session;
  const { perm, disabled: pDisabled, ...otherProps } = props;
  const disabled = perm !== '' && permissions.indexOf(perm) === -1;
  return <Item {...otherProps} disabled={pDisabled || disabled} />;
}

export function PermButton(props: ButtonProps & { perm: string }) {
  const store = useStore();
  const { permissions } = store.getState().session;
  const { perm, disabled: pDisabled, ...otherProps } = props;
  const disabled = perm !== '' && permissions.indexOf(perm) === -1;
  return <Button {...otherProps} disabled={pDisabled || disabled} />;
}

export function PermA(props: any) {
  const store = useStore();
  const { permissions } = store.getState().session;
  const { perm, disabled: pDisabled, ...otherProps } = props;
  let disabled
  if (perm.includes('|')){
    const permArr = perm.split('|')
    disabled = permissions.indexOf(permArr[0]) === -1 && permissions.indexOf(permArr[1]) === -1;
  }else {
    disabled = perm !== '' && permissions.indexOf(perm) === -1;
  }
  return <A {...otherProps} disabled={pDisabled || disabled} />;
}

export function PermSwitch(props: SwitchProps & { perm: string }) {
  const store = useStore();
  const { permissions } = store.getState().session;
  const { perm, disabled: pDisabled, ...otherProps } = props;
  const disabled = perm !== '' && permissions.indexOf(perm) === -1;
  return <Switch {...otherProps} disabled={pDisabled || disabled} />;
}
