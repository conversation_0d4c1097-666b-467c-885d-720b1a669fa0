import { Button, Form, Icon, InputNumber, Switch, Modal, Select, Spin, Tooltip, message, Input } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import _, { set } from 'lodash';
import { userApi, communityApi } from '@app/api';
import '@app/assets/index.scss';
import f from '@app/utils/fetch';
import ImageUploader from './imageUploader';
import { opApi as api } from '@app/api';


const GuideBannerModal = (props: any, ref: any) => {
  const form = props.form;
  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const [loading, setLoading] = useState(false);
  const [welcomeData, setWelcomeData] = useState({
    welcome: "",
    banner_pic_url: "",
    banner_link_url: ""
  });

  useEffect(() => {
    if (props.visible) {
      gptQuestionWelcome('');
    }
  }, [props.visible]);

  const handleSubmit = (e: any) => {
    e.preventDefault();

    form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true);
        api.gptQuestionWelcomeSave({ ...welcomeData, category: 3, banner_pic_url: values.banner_pic_url, banner_link_url: values.banner_link_url })
          .then((res: any) => {
            message.success('操作成功');
            setLoading(false);
            props.onEnd && props.onEnd();
          })
          .catch(() => {
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
        setLoading(false);
      }
    });
  };

  const gptQuestionWelcome = _.debounce((val: any) => {
    api.gptQuestionWelcomeQuery({ category: 3 })
      .then((data: any) => {
        const {
          data: { welcome = '', banner_pic_url = '', banner_link_url = '' },
        } = data;
        setWelcomeData(data);
        form.setFieldsValue({ banner_pic_url: banner_pic_url, banner_link_url: banner_link_url });


      })
      .catch((e) => { });

  }, 500);


  return (
    <Modal
      width={500}
      visible={props.visible}
      title={props.title || '引导banner'}
      key={props.skey}
      onCancel={() => {
        if (!loading) {
          props.onCancel && props.onCancel();
        }
      }}
      onOk={handleSubmit}
      maskClosable={false}
      destroyOnClose={true}
      confirmLoading={loading}
    >
      <Spin spinning={loading}>
        <Form {...formLayout}>

          <Form.Item label="图片" extra="支持扩展名 .jpg .jpeg .png 等格式图片，比例为5:7">
            {form.getFieldDecorator('banner_pic_url', {
              rules: [
                {
                  required: true,
                  message: '请上传图片',
                },
              ],
              initialValue: welcomeData.banner_pic_url
            })(<ImageUploader ratio={5 / 7} />)}
          </Form.Item>
          <Form.Item label="链接">
            {form.getFieldDecorator('banner_link_url', {
              rules: [
                {
                  required: false,
                  message: '链接不能为空',
                },
                {
                  pattern: /^https?:\/\//,
                  message: "请输入正确的链接格式"
                }
              ],
              initialValue: welcomeData.banner_link_url
            })(<Input placeholder="请输入http、https开头的链接, 最多输入200个字符" maxLength={200} allowClear />)}
          </Form.Item>

        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<any>({ name: 'GuideBannerModal' })(
  forwardRef<any, any>(GuideBannerModal)
);