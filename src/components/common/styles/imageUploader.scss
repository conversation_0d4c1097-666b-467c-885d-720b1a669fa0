.rox-image-cropper-container {
  width: 100%;
  height: auto;
  display: flex;
}

.rox-image-cropper-left {
  width: 696px;
}

.rox-image-cropper-left img {
  max-width: 90%;
  max-height: 300px;
}

.rox-image-cropper-right {
  width: 232px;
}

.rox-image-cropper-right, .rox-image-cropper-operation {
  width: 200px;
  margin: 0 auto 10px auto;
}

.rox-image-cropper-right, .rox-image-cropper-operation p {
  margin-bottom: 5px;
}

.rox-image-cropper-right .rox-image-cropper-operation div {
  margin: 5px 0;
}

.rox-image-cropper-right .rox-image-cropper-preview {
  width: 200px;
  height: 180px;
  overflow: hidden;
}

.rox-image-uploader-cropper {
  display: flex;
  line-height: 18px;
}

.rox-image-uploader-thumb {
  max-width: 400px;
  max-height: 200px;
}