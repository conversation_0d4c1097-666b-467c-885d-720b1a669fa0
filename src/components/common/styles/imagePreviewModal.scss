.image_preview_modal {
  width: 100%;
  height: 500px;
  padding: 25px;
  overflow: hidden;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: stretch;

  .image_wrapper {
    position: relative;
    overflow: hidden;
    flex: 1;
  }

  img {
    object-fit: contain;
    width: 100%;
    height: 100%;
    user-select: none;
  }

  .prev, .next {
    position: absolute;
    top: 50%;
    margin-top: -25px;
    width: 49px;
    height: 49px;
    border: 1px solid #EBEBEB;
    border-radius: 50%;
    z-index: 99;
    backdrop-filter: blur(5px);
    
    cursor: pointer;
    svg {
      color: #EBEBEB;
      width: 32px;
      height: 32px;
      position: absolute;
      top: 50%;
      left: 50%;
      margin-top: -16px;
    }
    &:hover {
      background: #fff;
      svg {
        color: #0F63F8;
      }
    }
  }
  .prev {
    left: 20px;
    svg {
      margin-left: -18px;
    }
  }
  .next {
    right: 20px;
    svg {
      margin-left: -16px;
    }
  }

}
