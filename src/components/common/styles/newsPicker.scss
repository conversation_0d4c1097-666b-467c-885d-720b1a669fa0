.newspicker-container {
  flex: 1;
  display: flex;
  flex-direction: row;
  overflow: hidden;
  width: 100%;
  height: 100%;

  .channel-list {
    width: 150px;
    display: flex;
    margin: 8px;
    flex-direction: column;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.08);
    border-radius: 5px;

    .title {
      width: 100%;
      height: 35px;
      line-height: 35px;
      padding-left: 16px;
      background: #1890ff;
      border-radius: 5px 5px 0 0;
      color: #FFFFFF;
      font-weight: 700;
    }

    .container {
      flex: 1;
      overflow: auto;
      padding-left: 8px;
    }

    .ant-tree li {
      padding: 2px 0!important;
    }
  }

  .article-list {
    width: 550px;
    display: flex;
    margin: 8px;
    flex-direction: column;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.08);
    border-radius: 5px;
    overflow: hidden;
    .ellipsis-title {
      width: 265px;
    }
  }

  .selected-list {
    width: 250px;
    display: flex;
    margin: 8px;
    flex-direction: column;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.08);
    border-radius: 5px;
    overflow: hidden;

    .title {
      width: 100%;
      height: 35px;
      line-height: 35px;
      padding-left: 16px;
      background: #1890ff;
      border-radius: 5px 5px 0 0;
      color: #FFFFFF;
      font-weight: 700;
    }

    .container {
      flex: 1;
      overflow: auto;
    }

    .ellipsis-title {
      width: 125px;
    }
  }

  .ant-table-tbody > tr > td, .ant-table-thead > tr > th {
    padding: 6px 8px !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px!important;
  }
  
  .ant-table-thead > tr > th  {
    padding: 8px 8px !important
  }

  .ellipsis-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .delete-btn {
    color: #ddd;
    transition: all linear 0.3s;
  }

  .delete-btn:hover{
    color: #1890ff;
  }
}