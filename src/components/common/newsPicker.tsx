import { searchApi, systemApi } from '@app/api';
import { Icon, Modal, Spin, Table, Tree } from 'antd';
import cloneDeep from 'lodash/cloneDeep';
import moment from 'moment';
import React from 'react';

import './styles/newsPicker.scss';

class NewsPicker extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      channelList: [],
      newsList: [],
      checkedNewsList: [],
      loading: false,
      tableKey: Date.now(),
    };
  }

  componentDidMount() {
    this.getChannelList();
  }

  getChannelList = () => {
    this.setState({ loading: true });
    systemApi
      .getAllChannelsTree()
      .then((r: any) => {
        this.setState({
          channelList: r.data.channel_list,
          loading: false,
        });
      })
      .catch(() => {
        this.setState({ loading: false });
      });
  }

  getArticleList = (channelId: string | number) => {
    this.setState({ loading: true });
    searchApi[this.props.func as keyof typeof searchApi]({ channel_id: channelId })
      .then((r: any) => {
        this.setState({ loading: false, newsList: r.data.article_list, tableKey: Date.now() });
      })
      .catch(() => {
        this.setState({ loading: false });
      });
  }

  handleSelectChannel = (selectedKeys: any) => {
    if (selectedKeys.length === 0) {
      this.setState({ newsList: 0 });
    } else {
      this.getArticleList(selectedKeys[0]);
    }
  }

  getArticleListColumn = () => {
    return [
      {
        title: '潮新闻ID',
        key: 'mlf_id',
        dataIndex: 'id',
        width: 85,
      },
      {
        title: '签发时间',
        key: 'time',
        dataIndex: 'published_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 150,
      },
      {
        title: '标题',
        key: 'list_title',
        dataIndex: 'list_title',
        render: (text: any) => <div className="ellipsis-title">{text}</div>,
      },
    ];
  }

  getSelectedListColumn = () => {
    return [
      {
        title: ' ',
        key: 'op',
        render: (text: any, record: any) => (
          <a onClick={() => this.deleteRecord(record)} className="delete-btn">
            <Icon type="close-circle" theme="filled" />
          </a>
        ),
        width: 15,
      },
      {
        title: '潮新闻ID',
        key: 'mlf_id',
        dataIndex: 'id',
        width: 85,
      },
      {
        title: '标题',
        key: 'list_title',
        dataIndex: 'list_title',
        render: (text: any) => <div className="ellipsis-title">{text}</div>,
      },
    ];
  }

  deleteRecord = (record: any) => {
    this.handleSelectArticle(record, false);
  }

  handleSelectArticle = (record: any, selected: boolean) => {
    const srows = cloneDeep(this.state.checkedNewsList);
    let nrows = [];
    if (selected) {
      nrows = [record].concat(srows);
    } else {
      srows.map((v: any) => {
        if (v.id !== record.id) {
          nrows.push(v);
        }
      });
    }
    this.setState({ checkedNewsList: nrows });
  }

  handleSelectAllArticle = (selected: any, selectedRows: any, changeRows: any) => {
    const srows = cloneDeep(this.state.checkedNewsList);
    let nrows = [];
    if (selected) {
      nrows = changeRows.concat(srows);
    } else {
      const cids = changeRows.map((v: any) => v.id);
      srows.map((v: any) => {
        if (cids.indexOf(v.id) === -1) {
          nrows.push(v);
        }
      });
    }
    this.setState({ checkedNewsList: nrows });
  }

  handleOk = () => {
    if (this.props.onOk) {
      this.props.onOk(this.state.checkedNewsList);
    } else {
      console.error('未绑定onOk Props属性');
    }
  }

  renderTreeNode = (node: any) => {
    return node.children.length > 0 ? (
      <Tree.TreeNode title={node.name} key={node.id} selectable={false}>
        {node.children.map((snode: any) => this.renderTreeNode(snode))}
      </Tree.TreeNode>
    ) : (
      <Tree.TreeNode title={node.name} key={node.id} />
    );
  }

  render() {
    const { onCancel, title } = this.props;
    const checkIds = this.state.checkedNewsList.map((v: any) => v.id);
    const rowSelection = {
      selectedRowKeys: checkIds,
      onSelect: this.handleSelectArticle,
      onSelectAll: this.handleSelectAllArticle,
    };
    return (
      <Modal
        visible={true}
        title={title}
        width={1050}
        onOk={this.handleOk}
        onCancel={onCancel}
        style={{ top: 30 }}
        bodyStyle={{ height: 670, width: '100%', display: 'flex', overflow: 'hidden' }}
      >
        <Spin
          tip="正在加载..."
          spinning={this.state.loading}
          wrapperClassName="newspicker-container"
          className="newspicker-container"
        >
          <div className="newspicker-container">
            <div className="channel-list">
              <div className="title">频道列表</div>
              <div className="container">
                <Tree switcherIcon={<Icon type="down" />} onSelect={this.handleSelectChannel}>
                  {this.state.channelList.map((node: any) => this.renderTreeNode(node))}
                </Tree>
              </div>
            </div>
            <div className="article-list">
              <Table
                columns={this.getArticleListColumn()}
                dataSource={this.state.newsList}
                rowKey="id"
                pagination={{
                  pageSize: 15,
                  size: 'small',
                  showTotal: (total: number) => `共${total}条`,
                }}
                rowSelection={rowSelection}
                key={this.state.tableKey}
              />
            </div>
            <div className="selected-list">
              <div className="title">已选择新闻</div>
              <div className="container">
                <Table
                  columns={this.getSelectedListColumn()}
                  dataSource={this.state.checkedNewsList}
                  pagination={false}
                  rowKey="id"
                />
              </div>
            </div>
          </div>
        </Spin>
      </Modal>
    );
  }
}

export default NewsPicker;
