import { Button, Icon, Input, Row } from 'antd';
import React from 'react';
import { APP_HOST } from '@utils/constants';
import { opApi as api } from '@app/api';

class ArLinkInput extends React.Component<any, any> {
  static getDerivedStateFromProps(nextProps: any) {
    if ('value' in nextProps) {
      const regex = /ar_scan/;
      const url = regex.test(nextProps.value) ? nextProps.value : '';
      let arId = '';
      let message = '';
      if (url) {
        const str = url.split('?')[1].split('&');
        const pairs: any = { ar_id: '', message: '' };
        // eslint-disable-next-line array-callback-return
        str.map((v: string) => {
          // eslint-disable-next-line prefer-destructuring
          pairs[v.split('=')[0]] = v.split('=')[1];
        });
        arId = pairs.ar_id || '';
        message = decodeURIComponent(pairs.message || '');
      }
      return {
        arId,
        message,
      };
    }
    return null;
  }

  constructor(props: any) {
    super(props);
    const regex = /native\/ar_scan/;
    const value = props.value || props.defaultValue || '';
    const url = regex.test(value) ? value : '';
    let arId = '';
    let message = '';
    let type = 0;
    if (url) {
      const str = url.split('?')[1].split('&');
      const pairs: any = { ar_id: '', message: '' };
      // eslint-disable-next-line array-callback-return
      str.map((v: string) => {
        // eslint-disable-next-line prefer-destructuring
        pairs[v.split('=')[0]] = v.split('=')[1];
      });
      arId = pairs.ar_id || '';
      message = decodeURIComponent(pairs.message || '');
      api.getArSceneDetail({ ar_id: arId }).then((t: any) => {
        type = t.data.ar_content.identify_method;
        this.state = {
          arId,
          message,
          type,
        };
      });
    } else {
      this.state = {
        arId,
        message,
        type,
      };
    }
  }

  triggerChange(field: 'arId' | 'message', value: any) {
    const s = { ...this.state, [field]: value };
    const { arId, message } = s;
    console.log(arId, message);
    this.setState({ arId, message });

    if (field === 'arId') {
      api
        .getArSceneDetail({ ar_id: arId })
        .then((t: any) => {
          const type = t.data.ar_content.identify_method;
          this.setState({
            type,
          });
        })
        .then((res) => {
          if (this.state.type === 1 || this.state.type === 2) {
            this.props.onChange(
              `${APP_HOST}/native/ar_scan.html?ar_id=${arId}&message=${encodeURIComponent(message)}`
            );
          }
          if (this.state.type === 3) {
            this.props.onChange(
              `${APP_HOST}/native/ar_scan_1.html?ar_id=${arId}&message=${encodeURIComponent(
                message
              )}`
            );
          }
        });
    } else {
      if (this.state.type === 1 || this.state.type === 2) {
        this.props.onChange(
          `${APP_HOST}/native/ar_scan.html?ar_id=${arId}&message=${encodeURIComponent(message)}`
        );
      }
      if (this.state.type === 3) {
        this.props.onChange(
          `${APP_HOST}/native/ar_scan_1.html?ar_id=${arId}&message=${encodeURIComponent(message)}`
        );
      }
    }
  }

  inputChagne(field: 'arId' | 'message', e: any) {
    if (this.props.onChange) {
      this.triggerChange(field, e.target.value);
    } else {
      this.setState({
        [field]: e.target.value,
      });
    }
  }

  render() {
    return (
      <Row>
        <Row>
          <span style={{ color: '#F5222d' }}>*</span> AR内容ID：
        </Row>
        <Row>
          <Input
            defaultValue={this.state.arId}
            onChange={this.inputChagne.bind(this, 'arId')}
            placeholder="请填写eazyAR后台对应的素材ID"
          />
        </Row>
        <Row>扫描提示语：</Row>
        <Row>
          <Input.TextArea
            defaultValue={this.state.message}
            onChange={this.inputChagne.bind(this, 'message')}
            placeholder="最多输入100个字，用于扫描时提醒用户如何操作"
            maxLength={100}
            rows={3}
          />
        </Row>
      </Row>
    );
  }
}

export default ArLinkInput;

export function arLinkValidator(rule: any, value: string, callback: Function) {
  const regex = /native\/ar_scan/;
  const url = regex.test(value) ? value : '';
  let arId = '';
  let message = '';
  if (url) {
    const str = url.split('?')[1].split('&');
    const pairs: any = { ar_id: '', message: '' };
    // eslint-disable-next-line array-callback-return
    str.map((v: string) => {
      // eslint-disable-next-line prefer-destructuring
      pairs[v.split('=')[0]] = v.split('=')[1];
    });
    arId = pairs.ar_id || '';
    message = decodeURIComponent(pairs.message || '');
  }
  if (!arId) {
    callback('请填写AR内容ID');
    return;
  }
  if (message.length > 100) {
    callback('扫描提示语长度不能超过100个字');
    return;
  }
  callback();
}

export function isArLink(value: string) {
  const regex = /native\/ar_scan/;
  return regex.test(value);
}

export function getArInfo(value: string) {
  const regex = /native\/ar_scan/;
  const url = regex.test(value) ? value : '';
  let arId = '';
  let message = '';
  if (url) {
    const str = url.split('?')[1].split('&');
    const pairs: any = { ar_id: '', message: '' };
    // eslint-disable-next-line array-callback-return
    str.map((v: string) => {
      // eslint-disable-next-line prefer-destructuring
      pairs[v.split('=')[0]] = v.split('=')[1];
    });
    arId = pairs.ar_id || '';
    message = decodeURIComponent(pairs.message || '');
  }
  return {
    arId,
    message,
  };
}
