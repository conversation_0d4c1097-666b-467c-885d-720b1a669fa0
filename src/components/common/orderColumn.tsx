import { Icon } from 'antd';
import A from './a';
import React from 'react';
import { requirePerm } from '@utils/utils';
import connectSession from '@app/utils/connectSession';

interface Props {
  disable?: boolean;
  disableUp?: boolean;
  disableDown?: boolean;
  start: number;
  end: number;
  pos: number;
  perm: string;
  onUp: () => void;
  onDown: () => void;
}

@connectSession
class OrderColumn extends React.Component<Props, {}> {
  constructor(props: any) {
    super(props);
  }

  render() {
    const { disable, disableUp, disableDown, perm, onUp, onDown, pos, start, end } = this.props;
    return (
      <span>
        {requirePerm(
          this,
          perm
        )(
          <A disabled={disable || disableUp || pos <= start || pos > end} className="sort-up" onClick={onUp}>
            <Icon type="up-circle" theme="filled" />
          </A>
        )}{' '}
        {requirePerm(
          this,
          perm
        )(
          <A disabled={disable || disableDown || pos >= end} className="sort-down" onClick={onDown}>
            <Icon type="down-circle" theme="filled" />
          </A>
        )}
      </span>
    );
  }
}

export default OrderColumn;
