import { systemApi as api } from '@app/api';
import {<PERSON><PERSON>, I<PERSON>, Row, message, Modal} from 'antd';
import React from 'react';
import {setLoading} from "@utils/utils";
interface stateType{
  file: string,
  loading: boolean,
  resultStatus: boolean,
  fileName: string,
  min: number,
  second: number
}
class PhoneFileUploader extends React.Component<any, stateType> {
  static getDerivedStateFromProps(nextProps: any) {
    if ('value' in nextProps) {
      return { file: nextProps.value || '' };
    }
    return '';
  }

  constructor(props: any) {
    super(props);
    this.state = {
      file: props.value || '',
      loading: false,
      resultStatus: !!props.value,
      fileName: '',
      min: 0,
      second: 0
    };
  }
  
  deleteFile = () => {
    this.setState({ file: '', fileName: '' });
    this.triggerChange('');
  };
  download = () =>{
    const link  = document.getElementById('file_title')
    link.click()
  }
  triggerChange(value: any) {
    this.props.onChange(value);
  }

  handleFileInputChange = (e: any) => {
    const file = e.target.files[0];
    if (file) {
      if (file.size/1024 > this.props.maxSize){
        return message.error(`请选择小于${this.props.maxSize}KB的文件`)
      }
      this.setState({ loading: true });
      e.target.value = null;
      api
        .phoneUpload({ file })
        .then((r: any) => {
          this.setState({ loading: false });
          const {fail_url,success_url,fail_count,right_count} = r.data
          Modal.confirm({
            title: '手机号上传结果',
            okText: fail_count > 0 ? '下载失败文件' : '确定',
            content: (
                <div >
                  上传成功<span style={{color:'green'}}>{right_count}条</span>，失败<span style={{color:"red"}}>{fail_count}条</span>
                </div>
            ),
            onOk: () => {
              if (fail_count > 0){
                this.download()
              }
            },
          })
          this.setState({
            fileName: file.name,
            resultStatus: success_url ? true : false,
          },()=>{
            console.log(this.state)
          });
          this.triggerChange(fail_url || success_url);
        })
        .catch(() => {
          this.setState({ loading: false });
        });
    }
  };

  render() {
    return (
      <Row>
        <Row>
          <input
            type="file"
            onChange={this.handleFileInputChange}
            ref="fileup"
            style={{ display: 'none' }}
            accept={this.props.accept}
          />
          <Button
            onClick={() => (this.refs.fileup as any).click()}
            style={{ marginRight: 8 }}
            loading={this.state.loading}
            type="primary"
          >
            <Icon type="upload" />
            选择文件
          </Button>
        </Row>
        {!!this.state.file && (
          <Row style={{display:"flex",alignItems:"center"}}>
            <a
                id='file_title'
                style={{ color: this.state.resultStatus  ? '#409eff' : 'red' ,
                  display:"inline-block",
                  maxWidth: 300,
                  marginRight: 24,
                  overflow:"hidden",
                  whiteSpace:"nowrap",
                  textOverflow:"ellipsis",
                  cursor:"pointer"
                }}
                href={this.state.file}
            >
              {this.state.fileName ? this.state.fileName : this.state.file}
            </a>
            <a onClick={this.deleteFile}
               style={{ color: '#ff7e00', textDecoration: 'none'}}>
              删除
            </a>
          </Row>
        )}
      </Row>
    );
  }
}

export default PhoneFileUploader;
