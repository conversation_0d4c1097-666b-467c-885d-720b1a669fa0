import { CommonObject } from '@app/types';
import React from 'react';

class A extends React.Component<CommonObject, CommonObject> {
  constructor(props: any) {
    super(props);
  }

  render() {
    const { disabled } = this.props;
    const props = { ...this.props };
    delete props.disabled;
    delete props.children;
    if (disabled) {
      props.className = props.className ? `${props.className} a-disabled` : 'a-disabled';
      delete props.onClick;
      delete props.href;
      return <a {...props}>{this.props.children}</a>;
    }
    return <a {...props}>{this.props.children}</a>;
  }
}

export default A;
