/* eslint-disable array-callback-return */
/* eslint-disable no-nested-ternary */
import { searchApi } from '@app/api';
import NewTableComponent from '@app/utils/newTable';
import A from '@components/common/a';
import NewsPicker from '@components/common/newsPicker';
import {
  Button,
  Icon,
  message,
  Row,
  Select,
  Spin,
  Table,
  Tooltip,
  Popconfirm,
  Modal,
  Form,
  InputNumber,
  Input,
} from 'antd';
import clonedeep from 'lodash/cloneDeep';
import debounce from 'lodash/debounce';
import React from 'react';
import { arrayMove } from 'react-sortable-hoc';

type State = {
  searchResults: Array<any>;
  list: Array<any>;
  fetching: boolean;
  searched: boolean;
  showPicker: boolean;
  categoryTip: any;
};
class SearchAndInput extends React.Component<any, State> {
  static getDerivedStateFromProps(nextProps: any) {
    if ('value' in nextProps) {
      return { list: nextProps.value };
    }
    return null;
  }

  constructor(props: any) {
    super(props);
    this.state = {
      searchResults: [],
      list: props.value || [],
      fetching: false,
      searched: false,
      showPicker: false,
      categoryTip: props.categoryTip || '新闻',
    };
    this.fetchNews = debounce(this.fetchNews, 500);
  }

  fetchNews = (value: string) => {
    if (!value) {
      this.setState({
        searchResults: [],
      });

      return;
    }

    this.setState({ fetching: true });
    const basicBody = this.props.body || {};
    if (this.props.isMedia) {
      basicBody.doc_types = [2, 3, 4, 5, 6, 7, 8, 9];
    }
    searchApi[this.props.func as keyof typeof searchApi]({
      ...basicBody,
      [this.props.searchKey || 'keyword']: value,
    })
      .then((r: any) => {
        this.setState({ fetching: false, searched: true });
        const result = this.props.apiWithPagination
          ? r.data[this.props.indexKey || 'article_list'].records
          : r.data[this.props.indexKey || 'article_list'];
        if (this.props.filterData) {
          this.setState({
            searchResults: result.filter(this.props.filterData),
          });
        } else {
          this.setState({
            searchResults: result,
          });
        }
      })
      .catch(() => {
        this.setState({ fetching: false });
      });
  };

  handleChange = async (svalue: any) => {
    const value = this.props.map ? this.props.map(JSON.parse(svalue[0])) : JSON.parse(svalue[0]);
    if (this.props.map) {
      console.log('map value', value);
    }
    if (this.state.list && this.state.list.length >= this.props.max) {
      message.error(`最多可关联${this.props.max}个${this.state.categoryTip}`);
      return;
    }
    if (
      this.state.list &&
      this.state.list.filter((v: any) => (v.uuid || v.id) == (value.uuid || value.id)).length > 0
    ) {
      message.error(`${this.state.categoryTip}已在列表中，请重新选择`);
      return;
    }
    // 模板类型取 最大值
    value.templateType = this.props.max;

    let validator: any;
    if (this.props.validator) {
      validator = await this.props.validator(value);
    }
    if (validator) {
      message.error(validator);
      return;
    }
    const list = [...(this.state.list || [])];
    if (this.props.addOnTop) {
      const { addOnTopIndex } = this.props;
      if (addOnTopIndex > 0) {
        list.splice(addOnTopIndex, 0, value);
      } else {
        list.unshift(value);
      }
    } else {
      list.push(value);
    }
    const state = {
      list,
      searchResults: [],
      searched: false,
    };
    if (this.props.onAddItem) {
      this.props.onAddItem(value);
    }
    this.setState({ ...state });
    this.triggerChange(state.list);
  };

  handleDelete = (record: any) => {
    const articleList = clonedeep(this.state.list);
    const { onChange, onChangeCallback } = this.props;
    let index = -1;
    articleList.map((v: any, i: number) => {
      if (v.id == record.id) {
        index = i;
      }
    });
    if (index === -1) {
      message.error('查找删除条目异常');
      return;
    }
    const deleteFN = () => {
      articleList.splice(index, 1);
      this.setState({
        list: articleList,
      });
    };
    // onChangeCallback   评论运营专用逻辑
    if (onChangeCallback) {
      // eslint-disable-next-line no-param-reassign
      record.callBack = deleteFN.bind(this);
      onChange(articleList, record);
    } else {
      deleteFN();
      this.triggerChange(articleList);
    }
  };

  exchangeOrder = (index: number, offset: number) => {
    const articleList = clonedeep(this.state.list);
    const temp = clonedeep(articleList[index]);
    articleList[index] = clonedeep(articleList[index - offset]);
    articleList[index - offset] = temp;
    this.setState({
      list: articleList,
    });
    this.triggerChange(articleList);
    if (this.props.orderChange) {
      this.props.orderChange(articleList);
    }
  };

  handleDrag = (oldIndex: number, newIndex: number) => {
    let articleList = clonedeep(this.state.list);
    articleList = arrayMove([...articleList], oldIndex, newIndex);
    this.setState({
      list: articleList,
    });
    this.triggerChange(articleList);
    if (this.props.orderChange) {
      this.props.orderChange(articleList);
    }
  };

  triggerChange = (value: any, deletObj = {}) => {
    const { onChange } = this.props;
    if (onChange) {
      onChange(value, deletObj);
    }
  };

  getColumns = () => {
    let { columns } = this.props;
    if (this.props.order) {
      columns = [
        {
          title: '排序',
          key: 'order',
          render: (text: any, record: any, i: number) => (
            <span>
              <A disabled={i === 0} className="sort-up" onClick={() => this.exchangeOrder(i, 1)}>
                <Icon type="up-circle" theme="filled" />
              </A>{' '}
              <A
                disabled={i === this.state.list.length - 1}
                className="sort-down"
                onClick={() => this.exchangeOrder(i, -1)}
              >
                <Icon type="down-circle" theme="filled" />
              </A>
            </span>
          ),
          width: 70,
        },
      ].concat(columns);
    }
    if (!this.props.hideOp) {
      if (this.props.customOp) {
        return columns.concat([
          {
            title: '操作',
            key: 'op',
            render: (text: any, record: any) => (
              <A disabled={this.props.disabled}>
                <Popconfirm
                  placement="topLeft"
                  title={'确定要删除吗'}
                  onConfirm={() => this.handleDelete(record)}
                  okText="确定"
                  cancelText="取消"
                >
                  删除
                </Popconfirm>
              </A>
            ),
            width: 100,
          },
        ]);
      } else {
        return columns.concat([
          {
            title: '操作',
            key: 'op',
            render: (text: any, record: any) => (
              <A disabled={this.props.disabled} onClick={() => this.handleDelete(record)}>
                删除
              </A>
            ),
            width: 70,
          },
        ]);
      }
    }
    return columns;
  };

  cancelPick = () => {
    this.setState({ showPicker: false });
  };

  confirmPick = (list: any) => {
    if (list.length === 0) {
      message.info(`未选择${this.state.categoryTip}`);
      this.cancelPick();
      return;
    }
    const newRows: any = [];
    const repeatRows: any = [];
    list.map((v: any) => {
      if (this.state.list.filter((vv: any) => vv.id == v.id).length > 0) {
        repeatRows.push(v);
      } else {
        newRows.push(v);
      }
    });
    if (newRows.length + this.state.list.length > this.props.max) {
      message.error(
        `最多可关联${this.props.max}篇${this.state.categoryTip}，已关联：${this.state.list.length}条`
      );
      return;
    }
    const state = {
      list: [...this.state.list, ...newRows],
    };
    this.setState({ ...state });
    this.triggerChange(state.list);
    if (repeatRows.length > 0) {
      message.info(
        `有${repeatRows.length}条${this.state.categoryTip}在列表，已添加${newRows.length}条${this.state.categoryTip}`
      );
    } else {
      message.info(`已添加${newRows.length}条${this.state.categoryTip}`);
    }
    this.cancelPick();
  };
  render() {
    const displayChannel = this.props.displayChannel || 'channel_name';
    const { getFieldDecorator } = this.props.form;
    return (
      <>
        <Row>
          <Select
            mode="multiple"
            value={[]}
            placeholder={this.props.placeholder || '输入新闻（专题）ID或标题关联'}
            notFoundContent={
              this.state.fetching ? (
                <Spin size="small" style={{ margin: 'auto' }} />
              ) : this.state.searched ? (
                '无结果'
              ) : null
            }
            filterOption={false}
            onSearch={this.fetchNews}
            onChange={this.handleChange}
            onBlur={() => this.setState({ searchResults: [], searched: false })}
            style={{ width: 500 }}
            disabled={this.props.disabled}
          >
            {this.state.searchResults.map((d: any, i: number) => (
              <Select.Option key={`${d[this.props.idField || 'id']}`} value={JSON.stringify(d)}>
                {this.props.selectMap
                  ? this.props.selectMap(d)
                  : `${d[this.props.idField || 'id']} - 【${d[displayChannel]}】  ${d.list_title}`}
              </Select.Option>
            ))}
          </Select>
          {this.props.tips && (
            <>
              &nbsp;
              <Tooltip title={this.props.tips}>
                <Icon type="question-circle" />
              </Tooltip>
            </>
          )}
          {Boolean(this.props.picker) && (
            <Button style={{ marginLeft: 8 }} onClick={() => this.setState({ showPicker: true })}>
              {this.props.picker}
            </Button>
          )}
          &nbsp;&nbsp;&nbsp;&nbsp;{this.props.afix || null}
        </Row>
        <Row style={{ marginTop: 8 }}>
          <NewTableComponent
            columns={this.getColumns()}
            rowKey="id"
            pagination={this.props.pagination ? this.props.pagination : false}
            tableList={{
              records: this.state.list,
            }}
            draggable={this.props.draggable}
            onDragEnd={this.handleDrag}
          />
        </Row>
        {this.state.showPicker && (
          <NewsPicker
            title={this.props.pickerTitle}
            func={this.props.pickerFunc}
            onCancel={this.cancelPick}
            onOk={this.confirmPick}
          />
        )}
      </>
    );
  }
}

export default Form.create<any>({})(SearchAndInput);
