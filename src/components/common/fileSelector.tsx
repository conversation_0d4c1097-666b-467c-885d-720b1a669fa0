/* eslint-disable react/no-string-refs */
import { <PERSON><PERSON>, I<PERSON>, Row, message } from 'antd';
import React from 'react';

class FileSelector extends React.Component<any, any> {
  static getDerivedStateFromProps(nextProps: any) {
    if ('value' in nextProps) {
      return { file: nextProps.value || '' };
    }
    return null;
  }

  constructor(props: any) {
    super(props);
    this.state = {
      file: props.value || null,
    };
  }

  deleteFile = () => {
    this.setState({ file: null });
    this.triggerChange(null);
  };

  handleFileInputChange = (e: any) => {
    const file = e.target.files[0];
    if (file) {
      const nameComponents = file.name.split('.')
      const ext = nameComponents[nameComponents.length - 1].toLowerCase()
      // 增加校验
      const { accept, maxSize } = this.props
      let errMsg = ''
      if (accept && !accept.includes(ext)) {
        errMsg = `请上传${accept}格式`
      }
      if (maxSize) {
        const fileSize = file.size / 1024
        const limitSize = maxSize * 1024
        const isM = maxSize >= 1
        if (fileSize > limitSize) {
          errMsg += `${errMsg ? ',' : ''}大小需${isM ? maxSize : limitSize}${isM ? 'M' : 'KB'}内`
        }
      }
      if (errMsg) {
        message.error(errMsg)
        e.target.value = ''
        return
      }
      this.setState({
        file,
      });
      this.triggerChange(file);
    }
  };

  triggerChange(value: any) {
    this.props.onChange(value);
  }

  render() {
    return (
      <Row>
        <Row>
          <input
            type="file"
            onChange={this.handleFileInputChange}
            ref="excelup"
            style={{ display: 'none' }}
            accept={this.props.accept}
          />
          <Button
            onClick={() => {
              (this.refs.excelup as any).click()
            }}
            style={{ marginRight: 8 }}
            type="primary"
          >
            <Icon type="upload" />
            选择文件
          </Button>
        </Row>
        {!!this.state.file && (
          <Row>
            <span style={{ color: '#409eff', marginRight: 24 }}>{this.state.file.name}</span>
            <a onClick={this.deleteFile} style={{ color: '#ff7e00', textDecoration: 'none' }}>
              删除
            </a>
          </Row>
        )}
      </Row>
    );
  }
}

export default FileSelector;
