import React from 'react';
import ReactDOM from 'react-dom';
import { Icon } from 'antd';
import './styles/imagePreview.scss';

export default function ImagePreview(props: any) {
  const { src, onClose, closeByMask } = props;
  return (
    src &&
    ReactDOM.createPortal(
      <div className="image_preview">
        <div
          className="image_preview_mask"
          onClick={() => {
            console.log(closeByMask, 'closeByMask');
            if (closeByMask) {
              onClose();
            }
          }}
        />
        <div className="image_preview_content">
          <img src={src} />
          <Icon className="close_btn" type="close-circle" onClick={onClose} />
        </div>
      </div>,
      document.body
    )
  );
}
