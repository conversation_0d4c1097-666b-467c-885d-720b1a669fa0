import React, { ReactNode, CSSProperties } from 'react';
import { useHistory } from 'react-router-dom';
import { Button } from 'antd';

interface P {
  url: string;
  type?: 'link' | 'default' | 'ghost' | 'primary' | 'dashed' | 'danger';
  children?: ReactNode;
  style?: CSSProperties;
}
// tslint:disable-next-line: variable-name
const NavButton = function(props: P) {
  const history = useHistory();
  console.log(history);
  const defaultOptions: P = {
    type: 'default',
    url: '/',
    style: {},
  };
  // tslint:disable-next-line: variable-name
  const _options_ = { ...defaultOptions, ...props };
  const pushHistory = () => {
    history.push(_options_.url);
  };
  return (
    <Button style={_options_.style} onClick={pushHistory} type={_options_.type}>
      {props.children}
    </Button>
  );
};

export default NavButton;
