import { clearSession } from '@app/action/session';
import { systemApi as api, opApi } from '@app/api';
import { connectAll as connect } from '@utils/connect';
import { ERROR_CODES } from '@utils/constants';
import { Button, Col, Icon, Layout, Modal, Popover, Row, Spin, message, notification } from 'antd';
import React from 'react';
import { withRouter, RouteComponentProps } from 'react-router';
import SideMenu from './sideMenu';
import HotNewsListDrawer from './hotNewsListDrawer';
import './styles/layout.scss';
import { IComponentProps, IAllProps } from '@app/types';
import ReactClipboard from 'react-clipboardjs-copy';
import moment from 'moment';

interface LayoutProps {
  [key: string]: any;
}

interface LayoutState {
  collapsed: boolean;
  sNotice: boolean;
  notice: any;
  hotNewsDrawerVisible: boolean;
  notificationIds: any;
  rankingVisible: boolean;
  rankingMessage: any;
  rankingMaxTime: any;
}

class MainLayout extends React.Component<
  RouteComponentProps & IComponentProps & IAllProps,
  LayoutState
> {
  hb: any;
  hotNewsTimerId: any;
  rankingMessageTimerId: any;
  constructor(props: any) {
    super(props);
    this.state = {
      collapsed: false,
      sNotice: false,
      notice: {},
      hotNewsDrawerVisible: false,
      notificationIds: [],
      rankingVisible: false,
      rankingMessage: [],
      rankingMaxTime: 0
    };
  }

  componentDidMount() {
    this.doHeartBeat();
    this.getNotice();
    // 判断是否有权限
    if (this.props.session.permissions.includes('article_metadata:hot_list')) {
      this.loopGetHotNewsList();
    }
    if (this.props.session.permissions.includes('ranking:list')) {
      this.getRankingPublishRemind();
    }
  }

  componentWillUnmount() {
    clearInterval(this.hb);
    clearInterval(this.hotNewsTimerId)
    clearInterval(this.rankingMessageTimerId)
  }

  copySuccess = () => {
    message.success('稿件ID已复制');
  };

  copyFail = () => {
    message.error('复制失败');
  };

  showHotNewsDrawer = (visible: boolean) => {
    this.setState({ hotNewsDrawerVisible: visible })
  }

  closeAllNotifications = () => {
    const { notificationIds } = this.state
    for (const id of notificationIds) {
      notification.close(id)
    }
    this.setState({
      notificationIds: []
    })
  }

  showHotNewsNotice = (data: any) => {
    const { created_at, list_title, id, source_channel_name, is_suspicious,
      published_at, read_cnt = 0, pushed_list = [] } = data
    const args: any = {
      key: id + ':' + published_at,
      message: '热门稿件提醒',
      description: <div className='layout-notice'>
        <div>热门稿件：{created_at > 0 ? moment(created_at).format('YYYY-MM-DD HH:mm:ss') : ''}</div>
        <div className='notice-news-title'>{list_title}{Boolean(is_suspicious) && <i className="hot-news-list_yi">疑</i>}</div>
        <div>原频道：<span>{source_channel_name} （稿件ID：{id}）
          <ReactClipboard
            action="copy"
            text={String(id)}
            onSuccess={this.copySuccess}
            onError={this.copyFail}
          >
            <i className='hot-news_copy'></i>
          </ReactClipboard>
        </span>
        </div>
        {published_at && <div>签发时间：{moment(parseInt(published_at)).format('YYYY-MM-DD HH:mm:ss')}</div>}
        {
          pushed_list.length > 0 && <div>推至多频道：
            {
              pushed_list.map((item: any, i: number) => <span key={item.to_channel_id}>
                {item.to_channel_name}{item.pushed === 1 && '（未使用）'}
                {
                  item.pushed === 2 && <span style={{ display: 'flex', alignItems: 'center' }}>
                    {`（稿件ID: ${item.article_id}）`}
                    <ReactClipboard
                      action="copy"
                      text={String(item.article_id)}
                      onSuccess={this.copySuccess}
                      onError={this.copyFail}
                    >
                      <i className="hot-news_copy"></i>
                    </ReactClipboard>
                  </span>
                }
                {i !== pushed_list.length - 1 ? '、' : ''}
              </span>)
            }
          </div>
        }
        <div>阅读数：{read_cnt}</div>
        <div style={{ justifyContent: 'flex-end' }}><Button type='primary' onClick={this.closeAllNotifications}>一键关闭</Button></div>
      </div>,
      duration: 0,
      placement: 'bottomRight',
      className: 'hot-news-notice',
    };
    notification.open(args);
  }

  doHeartBeat = () => {
    const INTERVAL_TIME = 30000;
    this.hb = setInterval(() => {
      api
        .rolling()
        .then()
        .catch((e: any) => {
          if (e.code && e.code === ERROR_CODES.LOG_TIME_OUT) {
            clearInterval(this.hb);
            Modal.error({
              title: '登录超时',
              content: '请重新登录',
              onOk: () => {
                this.props.dispatch(clearSession());
                this.props.history.push('/logout');
              },
            });
          }
        });
      this.getNotice();
    }, INTERVAL_TIME);
  }

  getNotice = () => {
    api
      .getNotice()
      .then((r: any) => {
        this.setState({
          notice: r.data.app_notice,
        });
      })
      .catch(() => this.setState({ notice: {} }));
  }

  loopGetHotNewsList = () => {
    const fn = () => {
      const timestamp = String(Date.now())
      const start = sessionStorage.getItem('hotNewsRequestTimeStamp') || timestamp
      api
        .getHotNewsList({ start })
        .then(({ data }) => {
          // 记录当前请求的时间
          sessionStorage.setItem("hotNewsRequestTimeStamp", timestamp)
          // 获取上一次弹窗的id
          const ids = JSON.parse(localStorage.getItem('lastHotNewsIds') || '[]')
          const newIds = []
          const notificationIds: any = [...this.state.notificationIds]
          const { release_list: { records = [] } } = data as any
          for (const data of records) {
            newIds.push(data.id)
            notificationIds.push(data.id + ':' + data.published_at)
            if (!ids.includes(data.id))
              this.showHotNewsNotice(data)
          }
          this.setState({ notificationIds })
          localStorage.setItem('lastHotNewsIds', JSON.stringify(newIds))
        })
        .catch(() => {
        })
    }
    this.hotNewsTimerId = setInterval(fn, 3 * 60 * 1000)
    // fn()
    sessionStorage.setItem("hotNewsRequestTimeStamp", String(Date.now()))
  }

  getRankingPublishRemind = () => {
    const fn = () => {
      opApi.rankingPublishRemind({}).then(({ data }) => {
        const { publish } = data as any
        if (publish.length > 0) {
          let needPublish = publish.filter((item: any) => item.need_publish).length > 0
          const maxTime = publish?.sort((a: any, b: any) => b.sync_time - a.sync_time)?.[0]?.sync_time || 0;

          const oldTime = parseInt(JSON.parse(localStorage.getItem('rankingPublishTime') || '0'))
          if (maxTime <= oldTime) {
            needPublish = false
          }

          this.setState({
            rankingVisible: needPublish,
            rankingMessage: publish,
            rankingMaxTime: Math.max(maxTime, oldTime)
          })
        }
      }).catch(() => {
      })
    }
    fn()
    this.rankingMessageTimerId = setInterval(fn, 5 * 60 * 1000)
  }

  onCollapse = (collapsed: boolean) => {
    this.setState({ collapsed });
  }

  collapse = () => {
    this.setState({
      collapsed: !this.state.collapsed,
    });
  }

  closeNotice = () => {
    this.setState({
      sNotice: false,
    });
  }

  logout = () => {
    api.logout().then(() => {
      this.props.dispatch(clearSession());
      this.props.history.push('/logout');
    });
  }

  showDetail = () => {
    this.setState({ sNotice: true });
  }
  render() {
    const content = this.state.notice.content || '';
    const hasHotNewsPermission = this.props.session.permissions.includes('article_metadata:hot_list')

    const maxTime = this.state.rankingMessage?.sort((a: any, b: any) => b.sync_time - a.sync_time)?.[0]?.sync_time || 0;

    let publishList = []
    let unpublishList = []
    for (const item of this.state.rankingMessage) {
      const name = item.type == 0 ? '24h热榜' : '市县热闻榜'
      if (item.need_publish) {
        publishList.push(name)
      } else {
        unpublishList.push(name)
      }
    }

    return (
      <Layout className="layout-container">
        <Layout.Header className="layout-header">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <img
                src="/assets/header.png?t=tmxw1"
                height="40"
                className="header-img"
                style={{ marginLeft: 32 }}
              />
            </div>
            <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', flex: 1 }}>
              {this.state.notice.status && (
                <div className="notice">
                  <div className="row">{this.state.notice.title}</div>
                  <div className="row" dangerouslySetInnerHTML={{ __html: content }} />
                  <div className="row">
                    值班电话：{this.state.notice.tel}
                    <span style={{ float: 'right' }}>
                      <a onClick={this.showDetail}>查看详情>></a>
                    </span>
                  </div>
                </div>
              )}
              {this.props.session.permissions.includes('ranking:list') &&
                <Popover overlayStyle={{ width: 300, visibility: this.state.rankingMessage?.length > 0 ? 'visible' : 'hidden' }} content={<div>
                  <p>亲爱的小编，榜单已于 {moment(maxTime).format('YYYY-MM-DD HH:mm:ss')} 更新，请及时审核并发布哦~</p>
                  {publishList.length > 0 && <p>已更新榜单：{publishList.join('、')}</p>}
                  {unpublishList.length > 0 && <p>未更新榜单：{unpublishList.join('、')}</p>}
                  <p>速戳 <a onClick={() => {
                    this.setState({ rankingVisible: false })
                    localStorage.setItem('rankingPublishTime', String(this.state.rankingMaxTime))
                    const needReload = this.props.location.pathname == "/view/ranking"
                    this.props.history.push('/view/ranking?is_tou_tiao=0&type=0&status=1')
                    if (needReload) {
                      window.location.reload()
                    }
                  }}>榜单管理>></a></p>
                </div>} title="榜单更新通知" trigger="click" onVisibleChange={(v) => {
                  if (!v) {
                    localStorage.setItem('rankingPublishTime', String(this.state.rankingMaxTime))
                    // 隐藏
                    this.setState({ rankingVisible: false })
                  }
                }}>
                  <div className='ranking'>
                    {this.state.rankingVisible && <div className='new'>新</div>}
                  </div>
                </Popover>
              }
              <div className="name-bar">
                {hasHotNewsPermission && <i onClick={() => this.showHotNewsDrawer(true)}>🔥</i>} 欢迎您，{this.props.session.admin.name}！[<a onClick={this.logout}>登出</a>]
              </div>
            </div>
          </div>
        </Layout.Header>
        <Layout className="layout-sub-container">
          <Layout.Sider
            className="layout-sidebar"
            collapsible={true}
            collapsed={this.state.collapsed}
            trigger={null}
          // onCollapse={this.onCollapse}
          // collapsedWidth={0}
          >
            <SideMenu />
          </Layout.Sider>
          <Layout>
            <Layout.Content className="layout-content">
              <Spin
                tip="正在加载..."
                spinning={Boolean(this.props.config.loading || this.props.tableList.loading)}
                wrapperClassName="spin"
                className="spin"
              >
                {this.props.children}
              </Spin>
            </Layout.Content>
          </Layout>
        </Layout>

        <Modal
          title="系统公告"
          visible={this.state.sNotice}
          footer={
            <Button type="primary" onClick={this.closeNotice}>
              关闭
            </Button>
          }
          onOk={this.closeNotice}
          onCancel={this.closeNotice}
        >
          <h3>{this.state.notice.title}</h3>
          <div dangerouslySetInnerHTML={{ __html: content }} />
          <p>值班电话：{this.state.notice.tel}</p>
        </Modal>
        <HotNewsListDrawer visible={this.state.hotNewsDrawerVisible} onCancel={() => this.showHotNewsDrawer(false)} />
        <div
          className={`collapse-trigger ${this.state.collapsed ? 'collapsed' : ''}`}
          onClick={this.collapse}
        >
          <Icon type={this.state.collapsed ? 'right' : 'left'} style={{ marginRight: 1 }} />
        </div>
      </Layout>
    );
  }
}

export default withRouter(connect()(MainLayout));
