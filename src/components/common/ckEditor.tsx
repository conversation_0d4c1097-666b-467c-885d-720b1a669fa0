import React from 'react';
import { Modal } from 'antd';
import ImageUploader from './imageUploader';

class CKEditor extends React.Component<any, any> {
  static getDerivedStateFromProps(nextProps: any) {
    if ('value' in nextProps) {
      return { content: nextProps.value || '' };
    }
    return null;
  }

  editor: any;

  constructor(props: any) {
    super(props);
    this.state = {
      content: props.value || '',
      id: 'ckeditor',
    };
  }

  componentDidMount() {
    (window as any).CKEDITOR.replace(this.state.id);
    this.editor = (window as any).CKEDITOR.instances.ckeditor;
    this.editor.on('change', (e: any) => {
      this.props.onChange(e.editor.getData());
    });
    this.editor.setData(this.state.content);
    (window as any).showInsertImage = this.showInsertImage.bind(this);
  }

  showInsertImage = () => {
    let img = '';
    const onChange = (value: string) => (img = value);
    Modal.confirm({
      title: '插入图片',
      icon: 'file-image',
      width: 502,
      content: (
        <div>
          <ImageUploader
            accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
            onChange={onChange}
          />
        </div>
      ),
      onOk: () => {
        if (img) {
          const html = `<p class="wg wgImage"><img class="wgImage-source" src="${img}" /></p>
                        <p>&nbsp;</p>`;
          this.editor.insertHtml(html, 'html');
        }
      },
    });
  };

  render() {
    return (
      <div>
        <textarea id={this.state.id} />
      </div>
    );
  }
}

export default CKEditor;
