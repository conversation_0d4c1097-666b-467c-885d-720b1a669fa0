import { Input, message } from "antd"
import React, { useState } from "react"
import "./styles/countTextArea.scss"

export default function CountTextArea(props: any) {
  const { placeholder, limitLength, initText = '', setupValidator } = props
  const [text, setText] = useState(initText)
  setupValidator(() => {
    if (text.length > limitLength) {
      message.error('长度不能超过100个字符！')
      return false
    }
    if (text.length === 0) {
      message.error('标题不能为空')
      return false
    }
    return text
  })
  return (
    <div className="count_textarea_container">
      <Input.TextArea value={text} placeholder={placeholder} onChange={(e) => setText(e.target.value)} />
      <div className="count_textarea_counter">
        <span>{text.length}</span> /
        <span>{limitLength}</span>
      </div>
    </div>
  )
}