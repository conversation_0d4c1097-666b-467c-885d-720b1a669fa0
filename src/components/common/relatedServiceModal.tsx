import {
  Button,
  Form,
  Icon,
  InputNumber,
  Switch,
  Modal,
  Select,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import _, { set } from 'lodash';
import { userApi, communityApi } from '@app/api';
import '@app/assets/index.scss';
import f from '@app/utils/fetch';

const RelatedServiceModal = (props: any, ref: any) => {
  const form = props.form;
  const channel_article_id = props.channel_article_id ?? '';
  const formLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };

  const [loading, setLoading] = useState(false);
  const [serviceOptions, setServiceOptions] = useState([]);
  const [selectDisabled, setSelectDisabled] = useState(false);

  const [relatedServiceIDs, setRelatedServices] = useState([]);

  const [detailData, setDetailData] = useState({
    status: 0,
    related_services_ids: '',
    ai_summary_flag: 1,
  });

  useEffect(() => {
    if (props.visible) {
      handleServiceSearch('');
      relatedServices('');
    }
  }, [props.visible]);

  const handleSubmit = (e: any) => {
    e.preventDefault();

    form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true);
        props
          .addApi({
            channel_article_id: channel_article_id,
            status: values.status ? '1' : '0',
            related_services_ids: values.related_services_ids
              ? values.related_services_ids.join(',')
              : '',
            ai_summary_flag: values.ai_summary_flag ? '1' : '0',
          })
          .then((res: any) => {
            message.success('操作成功');
            setLoading(false);
            props.onEnd && props.onEnd();
          })
          .catch(() => {
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
        setLoading(false);
      }
    });
  };

  const handleServiceSearch = _.debounce((val: any) => {
    if (!val) {
      setServiceOptions([]);
      return;
    }

    communityApi
      .operationWeblinkOnlist({ name: val })
      .then((res) => {
        setServiceOptions(res?.data?.web_link_list || []);
      })
      .catch(() => {});
  }, 500);

  const [selectedService, setSelectedService] = useState([]);

  const relatedServices = _.debounce((val: any) => {
    communityApi
      .channelQrticleRelatedService({ channel_article_id: channel_article_id })
      .then((res) => {
        const relatedServices = res?.data?.related_services || [];
        if (!!relatedServices) {
          const relatedServiceIDs = relatedServices.map((service) => service.id);
          setRelatedServices(relatedServiceIDs);

          setDetailData({
            status: res?.data?.status ?? 0,
            related_services_ids: relatedServiceIDs,
            ai_summary_flag: res?.data?.ai_summary_flag ?? 1,
          });

          form.setFieldsValue({
            status: (res?.data?.status ?? 0) === 1,
            related_services_ids: relatedServiceIDs,
            ai_summary_flag: (res?.data?.ai_summary_flag ?? 1) == 1,
          });
          setServiceOptions(relatedServices);
        }
      })
      .catch(() => {});
  }, 500);

  return (
    <Modal
      width={500}
      visible={props.visible}
      title={props.title || 'AI 划重点和服务 '}
      key={props.skey}
      onCancel={() => {
        if (!loading) {
          props.onCancel && props.onCancel();
        }
      }}
      onOk={handleSubmit}
      maskClosable={false}
      destroyOnClose={true}
      confirmLoading={loading}
    >
      <Spin spinning={loading}>
        <Form {...formLayout}>
          <Form.Item label="显示服务">
            {form.getFieldDecorator('status', {
              initialValue: detailData.status === 1,
              valuePropName: 'checked',
              rules: [
                {
                  required: true,
                },
              ],
            })(<Switch></Switch>)}
          </Form.Item>

          {!!form.getFieldValue('status') && (
            <Form.Item
              label="相关服务"
              help={serviceOptions.length === 0 ? '请输入服务名进行搜索' : ''}
            >
              {form.getFieldDecorator('related_services_ids', {
                initialValue: detailData.related_services_ids || undefined,
                rules: [{ required: true, message: '请选择至少一个服务' }],
              })(
                <Select
                  mode="multiple"
                  allowClear
                  placeholder="输入服务名"
                  onSearch={handleServiceSearch}
                  showSearch={true}
                  filterOption={false}
                  optionLabelProp="label"
                  onChange={(val, options: any) => {
                    if (options?.length <= 2) {
                      setSelectedService(options.map((d: any) => d.props.obj));
                    } else {
                      message.error('最多关联2个服务');
                      setTimeout(() => {
                        form.setFieldsValue({
                          related_services_ids: selectedService.map((d: any) => d.id),
                        });
                      }, 250);
                    }
                  }}
                >
                  {serviceOptions.map((d: any) => (
                    <Select.Option
                      disabled={selectDisabled}
                      key={d.id}
                      label={d.name}
                      value={d.id}
                      obj={d}
                    >
                      {props.optionMap ? props.optionMap(d) : `${d.name}`}
                    </Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
          )}

          <Form.Item label="显示AI划重点">
            {form.getFieldDecorator('ai_summary_flag', {
              initialValue: detailData.ai_summary_flag == 1,
              valuePropName: 'checked',
              rules: [
                {
                  required: true,
                },
              ],
            })(<Switch></Switch>)}
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<any>({ name: 'RelatedServiceModal' })(
  forwardRef<any, any>(RelatedServiceModal)
);
