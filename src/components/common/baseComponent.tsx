import { setConfig } from '@app/action/config';
import { ICommonProps, ISessionProps, CommonObject, RequestBody } from '@app/types';
import React from 'react';
import { RouteComponentProps } from 'react-router';
import { Breadcrumb } from 'antd';
import { listApi } from '@app/api';
import { getTableList } from '@app/action/tableList';

class BaseComponent<T, S, TMatchProps = {}> extends React.Component<
  RouteComponentProps<TMatchProps> & ICommonProps & ISessionProps & T,
  S
> {
  formRefs: CommonObject = {};

  changeRoute = (url: string) => {
    this.props.history.push(url);
  };

  dispatchTable = (
    func: keyof typeof listApi,
    index: string,
    filter: RequestBody = {},
    callBack: Function = () => {}
  ) => {
    this.props.dispatch(getTableList(func, index, filter, callBack));
  };

  setLoading = (loading: boolean) => {
    this.props.dispatch(
      setConfig({
        loading,
      })
    );
  };

  setConfirmLoading = (loading: boolean) => {
    this.props.dispatch(
      setConfig({
        mLoading: loading,
      })
    );
  };

  setMenu = (selectKeys = this.props.selectKeys, openKeys = this.props.openKeys) => {
    this.props.dispatch(
      setConfig({
        selectKeys,
        openKeys,
      })
    );
  };

  requirePerm = (perm: string) => {
    const { permissions } = this.props.session;
    const disabled = perm !== '' && permissions.indexOf(perm) === -1;
    // const disabled = false;
    return (elem: React.ReactElement) => {
      if (disabled) {
        return React.cloneElement(elem, { ...elem.props, disabled: true });
      }
      return React.cloneElement(elem, { ...elem.props });
    };
  };

  getCrumb = (crumb: string[] = this.props.breadCrumb) => {
    return (
      <Breadcrumb separator=">">
        <Breadcrumb.Item>潮新闻</Breadcrumb.Item>
        {crumb.map((item: string, i: number) => (
          <Breadcrumb.Item key={item}>{item}</Breadcrumb.Item>
        ))}
      </Breadcrumb>
    );
  };

  setFormRef = (index: string, instance: any) => {
    this.formRefs[index] = instance;
  };

  handleSubmitForm = (index: string) => {
    console.log(`submit-${index}`);
    console.log(this.formRefs);
    this.formRefs[index].doSubmit();
  };

  handleKey = (e: React.KeyboardEvent) => {
    if (e.which === 13) {
      this.doSearch();
    }
  };

  doSearch = () => {
    console.error('YOU HAVE NOT IMPLEMENT doSearch Function Yet');
  };
}

export default BaseComponent;
