import React, { useState } from 'react'
// import './index.scss';
import { PhotoSlider } from 'react-photo-view';

export default function ImagePreviewColumn(props: any) {
  const [imagePreview, setImagePreview] = useState({
    visible: false,
    imgs: [],
    index: 0
  })

  return <>
    <img src={props.text} className={props.className || 'list-pic'} style={{
      cursor: 'pointer'
    }} onClick={() => {
      setImagePreview({
        visible: true,
        imgs: props.imgs,
        index: 0
      })
    }}>
    </img>

    <PhotoSlider maskOpacity={0.5}
      images={imagePreview.imgs?.map((v: any) => ({ src: v, key: v })) || []}
      visible={imagePreview.visible}
      onClose={() => setImagePreview({
        ...imagePreview,
        visible: false
      })}
      index={imagePreview.index}
      onIndexChange={(index) => setImagePreview({
        ...imagePreview,
        index
      })}
    />

  </>
}