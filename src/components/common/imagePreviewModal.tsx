import { ShowModal } from '@app/utils/utils'
import React, { useState } from 'react'
import './styles/imagePreviewModal.scss'
import { Icon } from 'antd'

let n = 0
function ImagePreviewModal(props: any) {
    const { images = [] } = props
    const [index, setIndex] = useState(0)
    const src = images?.[index]
    const length = images.length
    return (
        <div className='image_preview_modal' onMouseLeave={props.onClose}>
            <p>{index + 1}/{length}</p>
            <div className='image_wrapper'>
                <img src={src} className='blank_bg' alt="" />
                <button onClick={()=>{console.log(document.getElementsByClassName('ant-modal-content'))}}>点击</button>
                {length > 1 && <div className='next' onClick={() => {
                    if (index < (images.length - 1)) {
                        setIndex(index + 1)
                    }
                }}>
                    <Icon type="right" />
                </div>}

                {length > 1 && <div className='prev' onClick={() => {
                    if (index > 0) {
                        setIndex(index - 1)
                    }
                }}>
                    <Icon type="left" />
                </div>}
            </div>
        </div>
    )
}

const showImagePreviewModal = (props: any) => {
    // 打开之前判断是否已经有model
    if (!props.images?.length) { return }
    const { destoryFn } = ShowModal(
        {
            width: '500px',
            closable: false,
            footer: null,
            // centered: true,
            bodyStyle: { padding: 0 }
        },
        (
            <ImagePreviewModal
                {...props}
                onClose={(e: any) => {
                    console.log('移到图片之外')
                    destoryFn()}
                }
            />
        )
    )
}

export default showImagePreviewModal