/* eslint-disable react/require-default-props */
/* eslint-disable jsx-a11y/media-has-caption */
import { Row, Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import moment from 'moment';
import Drawer from './drawer';
import { CommonObject } from '@app/types';
import { releaseListApi as api } from '@app/api'

interface IRoxPreviewMCNProps {
  visible: boolean;
  skey: string | number;
  onClose: () => void;
  data: CommonObject
}

function formatPic(pic: string) {
  const url = pic.toLowerCase()
  if (url.includes('oss') && url.includes('.heic')) {
    return url + (url.includes('?') ? '&' : '?') + 'x-oss-process=image/format,png'
  }
  return pic
}

function RoxDrawer(props: IRoxPreviewMCNProps) {
  const {
    visible,
    skey,
  } = props;

  const { onClose, data } = props;
  // const video = data.video_url
  // const cover = data.list_pics || data.first_cover || ''
  // const pics = cover.split(',') || []
  // const title = data.title || data.list_title || ''
  // const author = data.author || data.account_nick_name || data.nick_name
  // const create_date = data.published_at || data.article_published_at || data.published_timestamp
  // const html_content = data.doc_type === 12 ? data.content : data.html_content
  // const { recommend_text, recommend_url, circle_name, board_name } = data

  const [loading, setLoading] = useState(false)
  const [detail, setDetail] = useState({})

  useEffect(() => {
    if (visible) {
      let { doc_type, id, article_id } = data
      const isUGC = doc_type === 10 || doc_type === 12 || doc_type === 13
      setLoading(true)
      setDetail({})
      api.previewDetail({ id: id || article_id, type: isUGC ? 1 : 0 })
        .then(({ data }) => {
          setLoading(false)
          const { detail } = data as any
          setDetail(detail)
          console.log(detail)
        })
        .catch(() => {
          setLoading(false)
        })
    }
  }, [visible])

  const { list_title = '', author, published_at, circle = {}, recommend_url, recommend_text, content, video_url_with_watermark, list_pics = [], doc_type } = detail as any
  const { name: circle_name = '', board_name = '' } = circle
  console.log(detail)
  const showTitle = doc_type == 12 ? list_title.slice(0, 30) : list_title
  return (
    <Drawer visible={visible} skey={skey} title="预览稿件" closeText="关闭" onClose={onClose} maskClosable>
      <Spin
        tip="正在加载..."
        spinning={loading}>
        <Row style={{ textAlign: 'center', fontSize: 24, whiteSpace: 'normal', wordBreak: 'break-all' }}>{showTitle || '-'}</Row>
        <Row style={{ textAlign: 'center' }}>
          <span style={{ marginRight: 10 }}>作者：{author}</span>
          <span style={{ marginRight: 10 }}>稿件发布时间：{published_at && moment(published_at).format('YYYY-MM-DD HH:mm:ss')}</span>
          {(circle_name || board_name) && <span>关联圈子：{`${circle_name}${circle_name && board_name ? '-' : ''}${board_name}`}</span>}
        </Row>
        {recommend_url && (
          <>
            关联链接：{recommend_text} <br />
            <a href={recommend_url} target="_blank" rel="noreferrer">
              {recommend_url}
            </a>
          </>
        )}
        {content && <Row className='previewMCN' style={{ marginTop: 10, whiteSpace: 'pre-wrap' }} dangerouslySetInnerHTML={{ __html: content }} />}
        {video_url_with_watermark && (
          <Row style={{ textAlign: 'center', marginTop: 10 }}>
            <video src={video_url_with_watermark} poster={list_pics[0]} controls={true} style={{ maxWidth: '100%', maxHeight: 530, margin: '0 auto' }} />
          </Row>
        )}
        {(doc_type == 12 && !video_url_with_watermark) && (
            <Row style={{ textAlign: 'center', marginTop: 10 }}>
              {
                list_pics.map((pic: any) => (<div key={pic} style={{ marginBottom: 20 }}><img src={formatPic(pic)} style={{ maxWidth: '100%', maxHeight: 370 }} /></div>))
              }
            </Row>
          )
        }
      </Spin>
    </Drawer>
  );
}

export default RoxDrawer;
