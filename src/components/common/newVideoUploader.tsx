import { systemApi } from '@app/api';
import { <PERSON><PERSON>, Col, Icon, message, Modal, Popconfirm, Row } from 'antd';
import React from 'react';
import './styles/videoUploader.scss';

class NewVideoUploader extends React.Component<any, any> {
  private videoRef: React.RefObject<HTMLVideoElement>;

  static getDerivedStateFromProps(nextProps: any) {
    if ('value' in nextProps) {
      return { currentUrl: nextProps.value || '' };
    }
    return null;
  }

  constructor(props: any) {
    super(props);
    this.state = {
      currentUrl: props.value || '',
      size: props.size || 0,
      videoPost: props.videoPost || false,
      loading: false,
      videoDimensions: { width: 0, height: 0 },
      fileType: '',
    };
    this.videoRef = React.createRef();
  }

  handleFileInputChange = (e: any) => {
    const file = e.target.files[0];
    if (file) {
      console.log(222222222222222222222222222222,file.name);
      const fileType = file.type;
      this.setState({ fileType });
      const size = file.size;
      // ✅ 使用传入的 maxSize 参数，默认为 2GB
      const MAX_SIZE = this.props.maxSize || 2 * 1024 * 1024 * 1024; // 默认2GB
      if (size > MAX_SIZE) {
        const maxSizeInGB = MAX_SIZE / (1024 * 1024 * 1024);
        message.error(`选择的视频不能大于${maxSizeInGB}GB`);
        e.target.value = null;
        return;
      }

      // 保留原有的 size 参数兼容性检查
      if (size > this.state.size * 1024 * 1024 && this.state.size > 0) {
        message.error(`选择的视频不能大于${this.state.size}M`);
        e.target.value = null;
        return;
      }

      e.target.value = null;

      // ✅ 使用传入的 accept 参数进行文件格式验证
      const acceptTypes = this.props.accept || 'video/*';
      if (!this.isValidFileType(fileType, acceptTypes)) {
        message.error('视频选择格式不正确');
        return;
      }
      // if (file.name && file.name.includes('.m4v')) {
      //   message.error('视频选择格式不正确');
      //   return;
      // }
      this.doUpload(file);
    }
  };

  // ✅ 新增文件类型验证方法
  isValidFileType = (fileType: string, acceptTypes: string): boolean => {
    if (!acceptTypes) return true;
    
    // 🐛 确保 acceptTypes 是字符串类型
    const acceptTypesStr = typeof acceptTypes === 'string' ? acceptTypes : String(acceptTypes);
    const acceptArray = acceptTypesStr.split(',').map(type => type.trim());
    
    return acceptArray.some(acceptType => {
      if (acceptType === 'video/*') {
        return fileType.startsWith('video/');
      }
      if (acceptType.startsWith('.')) {
        // 处理扩展名格式，如 .mp4, .avi
        const extension = acceptType.toLowerCase();
        return fileType.includes(extension.substring(1));
      }
      // 处理 MIME 类型，如 video/mp4
      return fileType === acceptType;
    });
  };

  triggerChange = (url: string) => {
    console.log(url);
    const onChange = this.props.onChange;
    
    // 当删除视频时，重置视频尺寸
    if (!url) {
      this.setState({
        currentUrl: url,
        videoDimensions: { width: 0, height: 0 },
      });
      if (onChange) {
        onChange(url);
      }
      return;
    }
    
    this.setState({
      currentUrl: url,
    });
    if (onChange) {
      onChange(
        url +
          `?width=${this.state.videoDimensions.width}&height=${this.state.videoDimensions.height}`
      );
    }
  };

  clickUpload = () => {
    if (this.state.loading) {
      return;
    }
    if (!!this.props.disable) {
      message.error(this.props.disable);
      return;
    }

    (this.refs.videoUpload as HTMLDivElement).click();
  };

  doUpload = async (blob: any) => {
    this.setState({ loading: true });
    const params: any = {file_name: blob.name, type: 1 };

    const res = await systemApi.gerVideoUploadUrl(params);
    if (res.data.url) {
      const uploadUrl = res.data.url;
      const fileUrl = uploadUrl.split('?')[0];
      const uploadResponse = await fetch(uploadUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': 'video/mp4',
        },
        body: blob,
      });

      if (uploadResponse.ok) {
        message.success('上传成功');
        this.triggerChange(fileUrl);
        this.setState({ loading: false });
        this.convertImageUrlToBlob(`${fileUrl}?x-oss-process=video/snapshot,t_0,f_jpg,m_fast,ar_auto`);
      }
    }
  };
  convertImageUrlToBlob = async (imageUrl: string) => {
    try {
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const blob = await response.blob();
      const params: any = { file: blob };
      try {
        const uploadResult = await systemApi.uploadImg(params);
        if (uploadResult.data && uploadResult.data.url) {
          if (this.props.onSuccess) {
            this.props.onSuccess(uploadResult.data.url);
          }
        }
      } catch (error) {
        console.error('图片上传失败:', error);
      }
    } catch (error) {
      console.error('转换图片链接到文件流失败:', error);
      return null;
    }
  };
  handleVideoLoadedMetadata = () => {
    if (this.videoRef.current) {
      const video = this.videoRef.current;
      const dimensions = {
        width: video.videoWidth,
        height: video.videoHeight,
      };

      this.setState({ videoDimensions: dimensions });

      if (this.props.onVideoDimensionsChange) {
        this.props.onVideoDimensionsChange(dimensions);
      }
      if (this.props.onVideoReady) {
        this.props.onVideoReady(dimensions);
      }
      console.log('视频尺寸:', dimensions);
    }
  };

  render() {
    let videoURL = this.state.currentUrl;
    if (this.props.needMz) {
      videoURL = `http${videoURL?.split(',http')?.[1]}`;
    }

    return (
      <Row className="video-uploader">
        <input
          ref="videoUpload"
          type="file"
          accept={this.props.accept || "video/*"}
          aria-label="选择视频文件"
          onChange={this.handleFileInputChange}
          style={{ display: 'none' }}
        />
        {Boolean(this.state.currentUrl) && (
          <Row style={{ marginBottom: 8 }}>
            <video
              ref={this.videoRef}
              src={videoURL}
              className="rox-video-uploader-thumb"
              controls={true}
              onLoadedMetadata={this.handleVideoLoadedMetadata}
            />
          </Row>
        )}
        <Row>
          <Button
            style={{ marginRight: 8 }}
            type="primary"
            loading={this.state.loading}
            onClick={() => this.clickUpload()}
            disabled={this.props.disabled}
          >
            <Icon type="upload" />
            选择视频
          </Button>
          {Boolean(this.state.currentUrl) && (
            <Popconfirm title="确认删除视频?" onConfirm={() => this.triggerChange('')}>
              <Button disabled={this.props.disabled}>删除视频</Button>
            </Popconfirm>
          )}
        </Row>
      </Row>
    );
  }
}

export default NewVideoUploader;
