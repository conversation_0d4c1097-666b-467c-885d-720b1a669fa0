import React, { useState, useEffect } from 'react';
import { opApi } from '@app/api';
import { Modal } from 'antd';

function RenderDetail(props: { userId: string | number; visible: boolean }) {
  const [state, setState] = useState({
    userId: props.userId,
    visible: props.visible,
  });
  const [userInfo, setUser] = useState({ loading: true });
  useEffect(() => {
    setState({
      userId: props.userId,
      visible: props.visible,
    });
    setUser({ loading: true });
    opApi.getUserMoneyDetail({ account_id: state.userId });
  }, [props.userId, props.visible]);

  return (
    <Modal visible={state.visible} title="详情">
      {JSON.stringify(userInfo)}
    </Modal>
  );
}

export default RenderDetail;
