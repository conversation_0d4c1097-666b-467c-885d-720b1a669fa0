import { systemApi as api } from '@app/api';
import { But<PERSON>, Icon, Row, message } from 'antd';
import React from 'react';

class MultiFileUploader extends React.Component<any, any> {
  static getDerivedStateFromProps(nextProps: any) {
    if ('value' in nextProps) {
      return { file: nextProps.value || '' };
    }
    return '';
  }

  constructor(props: any) {
    super(props);
    this.state = {
      files: props.value || [],
      loading: false,
      fileNames: props.value || [],
    };
  }

  deleteFile = (index: number) => {
    const files = [...this.state.files];
    const fileNames = [...this.state.fileNames];
    files.splice(index, 1);
    fileNames.splice(index, 1);
    this.setState({ files, fileNames });
    this.triggerChange(files);
  };

  triggerChange(value: any) {
    this.props.onChange(value);
  }

  handleFileInputChange = (e: any) => {
    const file = e.target.files[0];
    if (file) {
      this.setState({ loading: true });
      e.target.value = null;
      api
        .upload({ file })
        .then((r: any) => {
          this.setState({ loading: false });
          message.success('上传成功');
          const files = [...this.state.files, r.data.url];
          this.setState({
            files,
            fileNames: [...this.state.fileNames, file.name],
          });
          this.triggerChange(files);
        })
        .catch(() => {
          this.setState({ loading: false });
        });
    }
  };

  render() {
    return (
      <Row>
        <Row>
          <input
            type="file"
            onChange={this.handleFileInputChange}
            ref="fileup"
            style={{ display: 'none' }}
            accept={this.props.accept}
          />
          <Button
            onClick={() => (this.refs.fileup as any).click()}
            style={{ marginRight: 8 }}
            loading={this.state.loading}
            type="primary"
          >
            <Icon type="upload" />
            {this.state.file.length > 0 ? '添加' : '选择'}文件
          </Button>
        </Row>
        {this.state.file.length > 0 &&
          this.state.file.map((v: any, i: number) => (
            <Row key={i}>
              <span style={{ color: '#409eff', marginRight: 24 }}>{this.state.fileNames[i]}</span>
              <a
                onClick={this.deleteFile.bind(this, i)}
                style={{ color: '#ff7e00', textDecoration: 'none' }}
              >
                删除
              </a>
            </Row>
          ))}
      </Row>
    );
  }
}

export default MultiFileUploader;
