import { Table, message, Drawer } from "antd";
import React, { useEffect, useState } from "react";
import { systemApi } from '@app/api';
import moment from "moment";
import ReactClipboard from 'react-clipboardjs-copy';

export default function HotNewsListDrawer(props: any) {
  const { visible, onCancel } = props
  const [loading, setLoading] = useState(false)
  // const [pager, setPager] = useState({
  //   current: 1,
  //   size: 10,
  // })
  const [total, setTotal] = useState(0)
  const [records, setRecords] = useState<any>([])
  const columns = [
    {
      title: '新闻标题',
      key: 'list_title',
      dataIndex: 'list_title',
      width: 250,
      render(text: any, record: any) {
        return <div style={{ display: 'flex' }}><span>{text}</span>{Boolean(record.is_suspicious) && <span className="hot-news-list_yi">疑</span>}</div>
      }
    },
    {
      title: '原频道',
      key: 'id',
      dataIndex: 'id',
      render(text: any, record: any) {
        return (
          <div>
            <div>{record.source_channel_name}</div>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {`（稿件ID: ${record.id}）`}
              <ReactClipboard
                action="copy"
                text={record.id}
                onSuccess={copySuccess}
                onError={copyFail}
              >
                <i className="hot-news_copy"></i>
              </ReactClipboard>
            </div>
          </div>
        )
      }
    },
    {
      title: '阅读数',
      key: 'read_cnt',
      dataIndex: 'read_cnt',
      width: 80,
    },
    {
      title: '消息发送时间',
      key: 'created_at',
      dataIndex: 'created_at',
      width: 120,
      render: (text: any) => <div style={{ width: 80 }}>{text ? moment(parseInt(text)).format('YYYY-MM-DD HH:mm:ss') : ''}</div>,
    },
    {
      title: '首次签发时间',
      key: 'published_at',
      dataIndex: 'published_at',
      width: 120,
      render: (text: any) => <div style={{ width: 80 }}>{text ? moment(parseInt(text)).format('YYYY-MM-DD HH:mm:ss') : ''}</div>,
    },
    {
      title: '推至其他频道',
      key: 'pushed_list',
      dataIndex: 'pushed_list',
      render: (pushed_list: any = [], record: any) => {
        if (pushed_list.length === 0) {
          return ''
        }
        return pushed_list.map((item: any) =>
          <div key={item.to_channel_id}>
            <div>{item.to_channel_name}{item.pushed === 1 && '（未使用）'}</div>
            {
              item.pushed === 2 && <div style={{ display: 'flex', alignItems: 'center' }}>
                {`（稿件ID: ${item.article_id}）`}
                <ReactClipboard
                  action="copy"
                  text={item.article_id}
                  onSuccess={copySuccess}
                  onError={copyFail}
                >
                  <i className="hot-news_copy"></i>
                </ReactClipboard>
              </div>
            }
          </div>)
      }
    },
  ]

  const getData = (pager: any) => {
    setLoading(true)
    systemApi.getHotNewsList({ ...pager })
      .then(({ data }) => {
        const { release_list: { records = [], total } } = data as any
        console.log('aaa', records)
        setLoading(false)
        setRecords(records)
        setTotal(total)
      })
      .catch(() => {
        setLoading(false)
      })
  }

  const pageChange = (current: number, size: number = 10) => {
    const pager = { current, size }
    // setPager(pager)
    getData(pager)
  };

  const copySuccess = () => {
    message.success('ID已复制');
  };

  const copyFail = () => {
    message.error('复制失败');
  };

  useEffect(() => {
    if (visible) {
      const pager = {
        current: 1,
        size: 10,
      }
      // setPager(pager)
      setTotal(0)
      getData(pager)
    }
  }, [visible])

  return (
    <Drawer
      visible={visible}
      title="热门稿件消息提醒"
      key="HotNewsListDrawer"
      destroyOnClose={true}
      maskClosable={false}
      width={1000}
      onClose={onCancel}
      bodyStyle={{ overflowY: 'hidden' }}
    >
      <Table
        loading={loading}
        columns={columns}
        pagination={{
          pageSizeOptions: ['10', '20', '50', '100'],
          showSizeChanger: true,
          total,
          showTotal: (total) => `共${total}条`,
          onChange: pageChange,
          onShowSizeChange: pageChange,
          size: 'small',
        }}
        dataSource={records}
        rowKey="id"
      />
    </Drawer>
  )
}