import React, { useEffect, useState } from 'react'
import { Icon } from "antd";

interface SortableColumnProps {
  onChange: (value: -1 | 0 | 1, sortBy: number) => void,
  pointerEvents: boolean,
  title: string,
  sort_by: number,
  currentSortBy: number,
}

export default function SortableColumn(props: SortableColumnProps) {
  const [sort_asc, setSort_asc] = useState(-1)

  useEffect(() => {
    if (props.sort_by != props.currentSortBy) {
      setSort_asc(-1)
    }    
  }, [props.sort_by, props.currentSortBy])

  const onChange = (value: -1 | 0 | 1) => {
    setSort_asc(value)
    props.onChange(value, props.currentSortBy)
  }

  return (
    <div style={{ display: 'flex', alignItems: 'center', pointerEvents: props.pointerEvents ? 'auto' : 'none' }} >
      <Icon type="undo"
        style={{ transform: 'rotateY(180deg) rotateZ(120deg)', cursor: 'pointer', marginRight: 3 }}
        onClick={() => onChange(-1)} />
      <div style={{ position: 'relative', cursor: 'pointer' }} onClick={() => {
        onChange(sort_asc === 0 ? 1 : 0)
      }}>
        {props.title}
        <Icon type="caret-up" style={{ position: 'absolute', color: props.pointerEvents && sort_asc === 1 ? '#1890ff' : '#999' }} />
        <Icon type="caret-down" style={{ position: 'absolute', bottom: 0, color: props.pointerEvents && sort_asc === 0 ? '#1890ff' : '#999' }} />
      </div>
    </div>
  );
}