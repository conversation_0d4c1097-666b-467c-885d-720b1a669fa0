import { Input } from 'antd';
import React from 'react'

export default class RangeInput extends React.Component<any, any> {
  handleInput1Change = (e: any) => {
    console.log({ [this.props.first || 'first']: e.target.value }, this.props.first)
    this.triggerChange({ [this.props.first || 'first']: e.target.value });
  };

  handleInput2Change = (e: any) => {
    this.triggerChange({ [this.props.second || 'second']: e.target.value });
  };

  triggerChange = (changedValue: any) => {
    const { onChange, value } = this.props;
    if (onChange) {
      onChange({
        ...value,
        ...changedValue,
      });
    }
  };

  render() {
    const { value, compact = true } = this.props;
    return (
      <Input.Group compact={compact}>
        <Input
          key={1}
          // type="text"
          style={{ width: compact ? 'calc(97% / 2)' : '100%',  marginRight: compact ? '3%' : '', marginBottom: compact ? '' : '10px' }}
          {...this.props.fisrtInputProps}
          value={value?.[this.props.first || 'first']}
          onChange={this.handleInput1Change.bind(this)}
        />

        <Input
          key={2}
          // type="text"
          style={{ width: compact ? 'calc(97% / 2)' : '100%' }}
          {...this.props.secondInputProps}
          value={value?.[this.props.second || 'second']}
          onChange={this.handleInput2Change.bind(this)}
        />
      </Input.Group>
    )
  }
}
