/* eslint-disable react/no-string-refs */
/* eslint-disable max-classes-per-file */
import { systemApi } from '@app/api';
import { Button, Col, Icon, message, Modal, Popconfirm, Row, Radio } from 'antd';
import Cropper from 'cropperjs';
import React, { forwardRef, useEffect, useRef, useState } from 'react';

import 'cropperjs/dist/cropper.css';
import './styles/imageUploader.scss';
import { ImageCropper } from './imageUploader';

function ratioFromRatioType(ratioType: string) {
  switch (ratioType) {
    case '4:1':
      return 4 / 1;
    case '16:9':
      return 16 / 9;
    case '4:3':
      return 4 / 3;
    case '1:1':
      return 1 / 1;
    case '3:1':
      return 3 / 1;
  }
  return 4 / 1;
}

const NewImageUploader = (props: any, ref: any) => {
  const imageUpload = useRef<HTMLInputElement>(null);
  const imgMaxWidth = props.imgMaxWidth || 400;
  const imgMaxHeight = props.imgMaxHeight || 200;
  const accept = props.accept || ['image/jpeg', 'image/png', 'image/jpg', 'image/gif'];
  const imgSize = props.imgsize || 10240; // 不传默认限制10M
  const currentUrl = props.value?.url || '';
  const ratio =
    props.ratio || (!!props.ratioType ? ratioFromRatioType(props.ratioType) : undefined);
  const [state, setState] = useState({
    showCropper: false,
    blob: '',
    type: '',
  });

  const [loading, setLoading] = useState(false);

  const handleFileInputChange = (e: any) => {
    const file = e.target.files[0];
    if (file) {
      // console.log(file)
      // if (file.size / 1024 > this.state.imgSize) {
      //   message.error(`图片上传过大，请上传小于${parseFloat((this.state.imgSize / 1024).toFixed(2))}M的图片！`);
      //   return;
      // }
      const blob = window.URL.createObjectURL(file);
      const imgType = file.type;
      e.target.value = null;
      if (accept.indexOf(imgType) === -1) {
        message.error('图片选择格式不正确');
        return;
      }
      if (props.isCutting) {
        doUpload(file);
        return;
      }

      if (imgType === 'image/gif') {
        doUpload(file);
        return;
      }
      setState({
        ...state,
        blob,
        showCropper: true,
        type: imgType,
      });
    }
  };

  const cropperEnd = (data: any) => {
    console.log(data);
    if (data) {
      triggerChange(data.url);
    }
    setState({ ...state, showCropper: false, blob: '', type: '' });
  };

  const triggerChange = (url: string) => {
    const { onChange } = props;
    if (onChange) {
      onChange(
        url
          ? {
              url,
              ratio: ratio, // 这里单纯只是为了实现比例切换外部报错  不是图片实际比例
            }
          : null
      );
    }
  };

  const clickUpload = () => {
    if (loading) {
      return;
    }
    if (!!props.disable) {
      message.error(props.disable);
      return;
    }
    imageUpload.current?.click();
  };

  const doUpload = (blob: any) => {
    if (blob.size / 1024 >= imgSize) {
      message.error(`图片上传过大，请上传小于${parseFloat((imgSize / 1024).toFixed(2))}M的图片！`);
    } else {
      setLoading(true);
      const params: any = { file: blob };
      if (props.needMz) {
        params.media_type = 0;
      }
      systemApi
        .uploadImg(params)
        .then((r: any) => {
          message.success('上传成功');
          triggerChange(r.data.url);
          setLoading(false);
        })
        .catch(() => {
          setLoading(false);
        });
    }
  };

  return (
    <Row className="image-uploader">
      <input
        ref={imageUpload}
        type="file"
        accept={accept.join(',')}
        onChange={handleFileInputChange}
        style={{ display: 'none' }}
      />
      {Boolean(currentUrl) && (
        <Row style={{ marginBottom: 8 }}>
          <img
            src={currentUrl}
            style={{
              maxWidth: `min(${`${imgMaxWidth}px`}, 100%)`,
              maxHeight: imgMaxHeight,
            }}
          />
        </Row>
      )}
      <Row>
        <Button
          style={{ marginRight: 8 }}
          type="primary"
          loading={loading}
          onClick={() => clickUpload()}
          disabled={props.disabled}
        >
          <Icon type="upload" />
          选择图片
        </Button>
        {Boolean(currentUrl) && (
          <Popconfirm
            title="确认删除图片?"
            okText="确定"
            cancelText="取消"
            onConfirm={() => {
              triggerChange('');
              // this.triggerEffectRatioType('4:1')
            }}
          >
            <Button disabled={props.disabled}>删除图片</Button>
          </Popconfirm>
        )}
      </Row>
      {state.showCropper && (
        <ImageCropper
          ratio={ratio}
          ratioType={props.ratioType}
          showRatioTypeChange={props.showRatioTypeChange}
          type={state.type}
          imgSize={imgSize}
          blobUrl={state.blob}
          onComplete={cropperEnd}
          fixedSize={props.fixedSize}
        />
      )}
    </Row>
  );
};

export default forwardRef(NewImageUploader);
