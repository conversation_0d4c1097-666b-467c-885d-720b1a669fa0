import { systemApi as api } from '@app/api';
import { But<PERSON>, Icon, Row, message } from 'antd';
import React from 'react';

class FileUploader extends React.Component<any, any> {
  static getDerivedStateFromProps(nextProps: any) {
    if ('value' in nextProps) {
      return { file: nextProps.value || '' };
    }
    return '';
  }

  constructor(props: any) {
    super(props);
    this.state = {
      file: props.value || '',
      loading: false,
      fileName: '',
      min: 0,
      second: 0
    };
  }
  
  deleteFile = () => {
    this.setState({ file: '', fileName: '' });
    this.triggerChange('');
  };
  // 下载模板
  handleDownload = () => {
    let url = this.state.file
    const a = document.createElement('a');
    a.setAttribute('href', url);
    a.target = '_self';
    a.href = url;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  triggerChange(value: any) {
    // const audioElement = new Audio(value);
    //       audioElement.addEventListener("loadedmetadata", (_event) => {
    //         this.setState({ min: parseInt(audioElement.duration/60,sec: audioElement.duration%60) }) // 通过在Audio对象loadedmetadata事件完成后，获取相关数据
    //       });
    this.props.onChange(value);
  }

  handleFileInputChange = (e: any) => {
    const file = e.target.files[0];

    if (file) {
      this.setState({ loading: true });
      e.target.value = null;
      api
        .upload({ file })
        .then((r: any) => {
          this.setState({ loading: false });
          message.success('上传成功');
          this.setState({
            file: r.data.url,
            fileName: file.name,
          });
          this.triggerChange(r.data.url);
        })
        .catch(() => {
          this.setState({ loading: false });
        });
    }
  };

  render() {
    return (
      <Row>
        <Row>
          <input
            type="file"
            onChange={this.handleFileInputChange}
            ref="fileup"
            style={{ display: 'none' }}
            accept={this.props.accept}
          />
          <Button
            onClick={() => (this.refs.fileup as any).click()}
            style={{ marginRight: 8 }}
            loading={this.state.loading}
            type="primary"
          >
            <Icon type="upload" />
            选择文件
          </Button>
        </Row>
        {!!this.state.file && (
          <Row>
            <span style={{ color: '#409eff', marginRight: 24 }}>
              {this.state.fileName ? this.state.fileName : this.state.file}
            </span>
            <a onClick={this.deleteFile} style={{ color: '#ff7e00', textDecoration: 'none' }}>
              删除
            </a>
            {
              this.props.download && <a onClick={this.handleDownload} style={{ color: '#409eff', textDecoration: 'none',marginLeft:8 }}>
                  下载
                </a>
            }
          </Row>
        )}
      </Row>
    );
  }
}

export default FileUploader;
