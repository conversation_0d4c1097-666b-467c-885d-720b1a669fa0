import React from 'react';
import { But<PERSON>, Col, Divider, Row } from 'antd';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

interface FormListTitleProps {
  total: number;
  i: number;
  title: string;
  upMove: (index: number) => void;
  downMove: (index: number) => void;
  removeItem: (index: number) => void;
  allowClear?: boolean;
  min?: number;
}
export const FormListTitle = (props: FormListTitleProps) => {
  const { total, i, title, allowClear, min = -1 } = props;
  return (
    <Row style={{ marginBottom: 10 }}>
      <Divider type="horizontal" style={{ margin: 0, marginBottom: 10 }} />
      <Col span={12} style={{ paddingLeft: 60 }}>
        {i == 0 ? (
          <Button
            onClick={() => props.upMove(i)}
            className="btn_mar"
            disabled
            type="primary"
            icon="up"
          />
        ) : (
          <Button onClick={() => props.upMove(i)} className="btn_mar" type="primary" icon="up" />
        )}
        {total && i != total - 1 ? (
          <Button
            style={{ marginLeft: 10 }}
            onClick={() => props.downMove(i)}
            className="btn_mar"
            type="primary"
            icon="down"
          />
        ) : (
          <Button
            style={{ marginLeft: 10 }}
            onClick={() => props.downMove(i)}
            className="btn_mar"
            disabled
            type="primary"
            icon="down"
          />
        )}
        <span className="title" style={{ marginLeft: 10 }}>
          {title}
          {i + 1}
        </span>
      </Col>
      <Col span={12} style={{ textAlign: 'right' }}>
        <Button
          type="danger"
          onClick={() => props.removeItem(i)}
          disabled={(!allowClear && total <= 1) || total <= min}
        >
          删除{title}
          {i + 1}
        </Button>
      </Col>
    </Row>
  );
};

export interface TMFormListProps {
  dataList: any[];
  form: {
    getFieldsValue: () => any;
    setFieldsValue: (values: any) => void;
  };
  fromItem: (length: number) => any;
  children: (item: any, index: number, total: number) => React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  filed: string;
}

export interface TMFormListRef {
  addItem: () => void;
  upMove: (index: number) => void;
  downMove: (index: number) => void;
  removeItem: (index: number) => void;
  total: number;
}

const TMFormList = forwardRef<TMFormListRef, TMFormListProps>(
  (props: TMFormListProps, ref: React.Ref<TMFormListRef>) => {
    const { dataList, form } = props;
    const { getFieldsValue, setFieldsValue } = form;
    const [list, setList] = useState(dataList);
    useEffect(() => {
      setList(dataList);
    }, [JSON.stringify(dataList)]);

    useEffect(() => {
      setFieldsValue({
        [props.filed]: list,
      });
    }, [list]);

    useImperativeHandle(
      ref,
      () => ({
        addItem,
        upMove,
        downMove,
        removeItem,
        total: list?.length || 0,
      }),
      [list]
    );

    const addItem = () => {
      const values = getFieldsValue();
      const formList = values[props.filed] || [];
      formList.push(props.fromItem(formList?.length));
      setList([...formList] as any);
    };

    const removeItem = (i: number) => {
      const values = getFieldsValue();
      const formList = values[props.filed] || [];
      formList.splice(i, 1);
      setList([...formList] as any);
    };

    //向上移动
    const upMove = (v: any) => {
      const value = getFieldsValue();
      const formList = value[props.filed] || [];
      const current = formList[v];
      const next = formList[v - 1];
      formList[v] = next;
      formList[v - 1] = current;
      setList([...formList] as any);
    };

    //向下移动
    const downMove = (v: any) => {
      const value = getFieldsValue();
      const formList = value[props.filed] || [];
      const current = formList[v];
      const next = formList[v + 1];
      formList[v] = next;
      formList[v + 1] = current;
      setList([...formList] as any);
    };

    return (
      <div className={props.className} style={props.style}>
        {list?.map((item: any, index: number) => props.children(item, index, list.length))}
      </div>
    );
  }
);

export default TMFormList;
