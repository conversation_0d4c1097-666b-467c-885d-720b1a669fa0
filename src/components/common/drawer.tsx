import { <PERSON><PERSON>, <PERSON><PERSON>, Spin } from 'antd';
import React, { Suspense } from 'react';
import connectSession from '@app/utils/connectSession';
import { CommonObject } from '@app/types';
import { PermButton } from '../permItems';

interface IRoxDrawerProps {
  visible: boolean;
  skey: string | number;
  title: any;
  onClose: () => void;
  onOk?: () => void;
  okText?: string;
  closeText?: string;
  config?: CommonObject;
  okPerm?: string;
  width?: number;
  maskClosable?: boolean;
  footer?: any;
}

@connectSession
class RoxDrawer extends React.Component<IRoxDrawerProps, any> {
  render() {

    const { visible, skey, title, width, maskClosable = false } = this.props;
    const { onClose, onOk, closeText, okText, okPerm } = this.props;
    const loading = this.props.config ? this.props.config.mLoading : false;
    return (
      <Drawer
        visible={visible}
        title={title}
        key={skey}
        className="rox-drawer"
        destroyOnClose={true}
        maskClosable={maskClosable}
        width={width || 850}
        onClose={onClose}
        bodyStyle={{ overflowY: 'hidden' }}
      >
        {loading && <div className="spin-overlay">
          <Spin />
        </div>}
        
        <div className="rox-drawer-content">
          <Suspense fallback={<div>loading...</div>}>{this.props.children}</Suspense>
        </div>
        <div className="rox-drawer-footer">
          {this.props.footer ? this.props.footer : (<><Button style={{ marginRight: 8 }} onClick={onClose}>
            {closeText || '取消'}
          </Button>
            {Boolean(onOk) && (
              <PermButton type="primary" perm={okPerm || ''} onClick={onOk} loading={loading}>
                {okText || '确定'}
              </PermButton>
            )}</>)}
        </div>
      </Drawer>
    );
  }
}

export default RoxDrawer;
