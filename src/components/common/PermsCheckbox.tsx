import { Checkbox, Col, Divider, Input, Row } from 'antd';
import CheckboxGroup from 'antd/lib/checkbox/Group';
import React from 'react'
import cloneDeep from 'lodash/cloneDeep';

export default class PermsCheckbox extends React.Component<any, any> {
  handleInput1Change = (e: any) => {
    console.log({ [this.props.first || 'first']: e.target.value }, this.props.first)
    this.triggerChange({ [this.props.first || 'first']: e.target.value });
  };

  handleInput2Change = (e: any) => {
    this.triggerChange({ [this.props.second || 'second']: e.target.value });
  };

  triggerChange = (changedValue: any) => {
    const { onChange, value } = this.props;
    if (onChange) {
      onChange(changedValue);
    }
  };

  colLayout = {
    xs: { span: 12 },
    sm: { span: 12 },
    lg: { span: 8 },
    xl: { span: 6 },
  };

  selectAll = (val: any, i: number) => {
    const s = cloneDeep(this.props.value)
    s[i].checked = !s[i].checked
    s[i].children = s[i].children.map((v: any) => {
      return {...v, checked: s[i].checked}
    })
    s[i].indeterminate = false
    this.triggerChange(s)
  }

  selectSingle = (val: any, i: number, j: number) => {
    const s = cloneDeep(this.props.value)
    s[i].children[j].checked = !s[i].children[j].checked

    const checkLength = s[i].children.filter((v: any) => v.checked).length
    s[i].checked = checkLength === s[i].children.length
    s[i].indeterminate = checkLength > 0 && checkLength < s[i].children.length
    this.triggerChange(s)
  }

  render() {
    const { value = [] } = this.props;
    return (
      <div>
        {value?.map((v: any, i: number) => {
          return (
            <div key={i}>
              <Row style={{
                // margin: '8px 8px 0 8px',
                paddingBottom: '8px',
                borderBottom: '1px solid #eee'
              }}>
                <Checkbox
                  checked={v.checked}
                  indeterminate={v.indeterminate}
                  onChange={this.selectAll.bind(this, v, i)}
                >
                  {v.name}（全部）
                </Checkbox>
              </Row>
              <Row style={{
                margin: '8px 0 0 20px',
                lineHeight: '28px'
              }}>
                {v.children.map(
                  (v: any, j: number) => (
                    <Col {...this.colLayout} key={j}>
                      <Checkbox
                        checked={v.checked}
                        onChange={this.selectSingle.bind(this, v, i, j)}
                      >
                        {v.name}
                      </Checkbox>
                    </Col>
                  )
                )}
              </Row>
            </div>
          )
        })}
      </div>
    )
  }
}
