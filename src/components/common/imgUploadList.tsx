/* eslint-disable array-callback-return */
/* eslint-disable no-nested-ternary */
import { searchApi } from '@app/api';
import A from '@components/common/a';
import NewsPicker from '@components/common/newsPicker';
import {
  Button,
  Icon,
  message,
  Row,
  Select,
  Spin,
  Table,
  Tooltip,
  Popconfirm,
  Modal,
  Form,
  InputNumber,
  Input
} from 'antd';
import clonedeep from 'lodash/cloneDeep';
import debounce from 'lodash/debounce';
import React from 'react';
import ImageUploader from "@components/common/imageUploader";

type State = {
  searchResults: Array<any>;
  list: Array<any>;
  fetching: boolean;
  searched: boolean;
  showPicker: boolean;
  categoryTip: any;
  tips: any;
  ratio: any;
};
class ImgUploadList extends React.Component<any, State> {
  static getDerivedStateFromProps(nextProps: any) {
    if ('value' in nextProps) {
      return { list: nextProps.value };
    }
    return null;
  }

  constructor(props: any) {
    super(props);
    this.state = {
      searchResults: [],
      list: props.value || [],
      fetching: false,
      searched: false,
      showPicker: false,
      categoryTip: props.categoryTip || '新闻',
      tips: null,
      ratio: props.ratio
    };
    this.fetchNews = debounce(this.fetchNews, 500);
  }

  fetchNews = (value: string) => {
    this.setState({ fetching: true });
    const basicBody = this.props.body || {};
    if (this.props.isMedia) {
      basicBody.doc_types = [2, 3, 4, 5, 6, 7, 8, 9]
    }
    searchApi[this.props.func as keyof typeof searchApi]({
      ...basicBody,
      keyword: value,
    })
      .then((r: any) => {
        this.setState({ fetching: false, searched: true });
        if (this.props.filterData) {
          this.setState({
            searchResults: r.data.article_list.filter(this.props.filterData),
          });
        } else {
          this.setState({
            searchResults: r.data.article_list,
          });
        }
      })
      .catch(() => {
        this.setState({ fetching: false });
      });
  };

  handleChange = async (value: any) => {
    if (this.state.list && this.state.list.length >= this.props.max) {
      message.destroy()
      message.error(`最多可上传${this.props.max}张照片`);
      return;
    }
    if (
      this.state.list &&
      this.state.list.filter((v: any) => { value === v }).length > 0
    ) {
      message.error(`图片已在列表中，请重新选择`);
      return;
    }

    let validator: any;
    if (this.props.validator) {
      validator = await this.props.validator(value);
    }
    if (validator) {
      message.error(validator);
      return;
    }
    const list = [...(this.state.list || [])]
    if (this.props.addOnTop) {
      const { addOnTopIndex } = this.props
      if (addOnTopIndex > 0) {
        list.splice(addOnTopIndex, 0, value)
      } else {
        list.unshift(value)
      }
    } else {
      list.push(value)
    }
    const state = {
      list,
      searchResults: [],
      searched: false,
    };
    if (this.props.onAddItem) {
      this.props.onAddItem(value)
    }
    this.setState({ ...state });
    this.triggerChange(state.list);
  };

  handleDelete = (record: any) => {
    const articleList = clonedeep(this.state.list);
    const { onChange, onChangeCallback } = this.props;
    let index = -1;
    articleList.map((v: any, i: number) => {
      if (v == record) {
        index = i;
      }
    });
    if (index === -1) {
      message.error('查找删除条目异常');
      return;
    }
    const deleteFN = () => {
      articleList.splice(index, 1);
      this.setState({
        list: articleList,
      });
    };
    // onChangeCallback   评论运营专用逻辑
    if (onChangeCallback) {
      // eslint-disable-next-line no-param-reassign
      record.callBack = deleteFN.bind(this);
      onChange(articleList, record);
    } else {
      deleteFN();
      this.triggerChange(articleList);
    }
  };


  exchangeOrder = (index: number, offset: number) => {
    const articleList = clonedeep(this.state.list);
    const temp = clonedeep(articleList[index]);
    articleList[index] = clonedeep(articleList[index - offset]);
    articleList[index - offset] = temp;
    this.setState({
      list: articleList,
    });
    this.triggerChange(articleList);
    if (this.props.orderChange) {
      this.props.orderChange(articleList)
    }
  };

  triggerChange = (value: any, deletObj = {}) => {
    const { onChange } = this.props;
    if (onChange) {
      onChange(value, deletObj);
    }
  };

  getColumns = () => {
    let columns = [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) => (
          <span>
            <A disabled={i === 0} className="sort-up" onClick={() => this.exchangeOrder(i, 1)}>
              <Icon type="up-circle" theme="filled" />
            </A>{' '}
            <A
              disabled={i === this.state.list.length - 1}
              className="sort-down"
              onClick={() => this.exchangeOrder(i, -1)}
            >
              <Icon type="down-circle" theme="filled" />
            </A>
          </span>
        ),
        width: 70,
      },
      {
        title: '序号',
        key: 'orders',
        render: (text: any, record: any, i: number) => (
          <span>{i + 1}</span>
        ),
        width: 70,
      },
      {
        title: '图片',
        key: 'img',
        render: (text: any, record: any, i: number) => {
          let src = record;
          if (this.props.needMz) {
            src = `http${record?.split(',http')?.[1]}`;
          }
          return (
            <span><img style={{ height: 70 }} src={src} /></span>
          )
        },
        width: 70,
      },
    ]
    if (this.props.customOp) {
      return columns.concat([
        {
          title: '操作',
          key: 'op',
          render: (text: any, record: any) => (
            <A disabled={this.props.disabled}>
              <Popconfirm placement="topLeft" title={'确定要删除吗'} onConfirm={() => this.handleDelete(record)} okText="确定" cancelText="取消">
                删除
              </Popconfirm>
            </A>
          ),
          width: 100,
        },
      ]);
    } else {
      return columns.concat([
        {
          title: '操作',
          key: 'op',
          render: (text: any, record: any) => (
            <A disabled={this.props.disabled} onClick={() => this.handleDelete(record)}>
              删除
            </A>
          ),
          width: 70,
        },
      ]);
    }
  };

  cancelPick = () => {
    this.setState({ showPicker: false });
  };
  confirmPick = (list: any) => {
    if (list.length === 0) {
      message.info(`未选择${this.state.categoryTip}`);
      this.cancelPick();
      return;
    }
    const newRows: any = [];
    const repeatRows: any = [];
    list.map((v: any) => {
      if (this.state.list.filter((vv: any) => vv.id == v.id).length > 0) {
        repeatRows.push(v);
      } else {
        newRows.push(v);
      }
    });
    const state = {
      list: [...this.state.list, ...newRows],
    };
    this.setState({ ...state });
    this.triggerChange(state.list);
    this.cancelPick();
  };
  maxPrompt = () => {
    if(this.state.list && this.state.list.length-1 >= this.props.max){
    }
  }
  render() {
    const { ratio } = this.props
    return (
      <>
        <Row>
          <div style={{ display: "flex", alignItems: "center" }}>
            <ImageUploader
              needMz={this.props.needMz}
              onChange={this.handleChange}
              value={null}
              ratio={ratio}
              accept={this.props.accept}
              isCutting={this.props.isCutting ?? false}
              disable={this.props.disable}
            />
            {this.props.tips && (
              <>&nbsp;<Tooltip title={this.props.tips}>
                <Icon type="question-circle" />
              </Tooltip></>
            )}
          </div>
          {!!this.props.extra && <div style={{ color: 'rgb(0 0 0 / 45%)' }}>
            {this.props.extra}
          </div>}
          {/* {Boolean(this.props.picker) && (
            <Button style={{ marginLeft: 8 }} onClick={() => this.setState({ showPicker: true })}>
              {this.props.picker}
            </Button>
          )} */}
        </Row>
        <Row style={{ marginTop: 8 }}>
          <Table
            columns={this.getColumns()}
            rowKey={(record: any) => record}
            dataSource={this.state.list}
            pagination={this.props.pagination ? this.props.pagination : false}
          />
        </Row>
        {this.state.showPicker && (
          <NewsPicker
            title={this.props.pickerTitle}
            func={this.props.pickerFunc}
            onCancel={this.cancelPick}
            onOk={this.confirmPick}
          />
        )}
      </>
    );
  }
}

export default Form.create<any>({})(ImgUploadList);
