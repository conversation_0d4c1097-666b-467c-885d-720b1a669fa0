import { Input } from 'antd';
import React from 'react';

class TwoInput extends React.Component {
  handleWidthChange = (e: any) => {
    this.triggerChange({ width: e.target.value, name: 'width' });
  };

  handleLongChange = (e: any) => {
    this.triggerChange({ long: e.target.value, name: 'long' });
  };

  triggerChange = (changedValue: any) => {
    console.log(this.props);
    console.log(changedValue);
    const { onChange, value } = this.props;
    if (onChange) {
      if (changedValue.name === 'width') {
        onChange({
          width: changedValue.width,
          long: value.long,
        });
      } else {
        onChange({
          width: value.width,
          long: changedValue.long,
        });
      }
    }
  };

  render() {
    const { value } = this.props;
    return (
      <span>
        <Input
          value={value.width}
          onChange={this.handleWidthChange}
          style={{ width: 100, textAlign: 'center' }}
          placeholder="宽"
        />
        <Input
          style={{
            width: 30,
            borderLeft: 0,
            pointerEvents: 'none',
            backgroundColor: '#fff',
          }}
          placeholder=":"
          disabled
        />
        <Input
          value={value.long}
          onChange={this.handleLongChange}
          style={{ width: 100, textAlign: 'center' }}
          placeholder="高"
        />
      </span>
    );
  }
}
export default TwoInput;
