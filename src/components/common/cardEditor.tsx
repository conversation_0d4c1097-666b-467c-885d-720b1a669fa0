/* eslint-disable react/no-danger */
import { Card } from 'antd';
import React from 'react';
import CKEditor from './ckEditor';

class CardEditor extends React.Component<any, any> {
  static getDerivedStateFromProps(nextProps: any) {
    if ('value' in nextProps && 'editing' in nextProps && nextProps.editing === false) {
      return { data: nextProps.value || '' };
    }
    return null;
  }

  constructor(props: any) {
    super(props);
    this.state = {
      data: props.value || '',
    };
  }

  onValueChange = (value: any) => {
    this.setState({ data: value });
  };

  getData = () => {
    return this.state.data;
  };

  render() {
    return (
      <Card title={this.props.title} bordered={false} style={{ marginBottom: 16 }}>
        {this.props.editing ? (
          <CKEditor
            value={this.state.data}
            onChange={this.onValueChange}
            style={{ height: '100%' }}
          />
        ) : (
          <div dangerouslySetInnerHTML={{ __html: this.state.data }} />
        )}
      </Card>
    );
  }
}

export default CardEditor;
