import React from "react"
import { TimePicker } from "antd"
import moment from "moment"

export default class TimeRangePicker extends React.Component {

  state = {
    beginTime: undefined,
    endTime: undefined
  }

  componentDidMount(): void {
    const { value = [] } = this.props as any
    this.setState({
      beginTime: value[0],
      endTime: value[1],
    })
  }

  render() {
    const { style, value = [], onChange } = this.props as any
    const format = 'HH:mm'
    return (
      <div style={{ display: 'flex', ...style }}>
        <TimePicker
          format={format}
          value={value[0] ? moment(value[0], format) : undefined}
          style={{ flex: 1 }}
          placeholder="请选择起始时间点"
          onChange={(_, beginTime: string) => {
            this.setState({ beginTime })
            onChange([beginTime, this.state.endTime])
          }} />
        <TimePicker
          format={format}
          value={value[1] ? moment(value[1], format) : undefined}
          style={{ flex: 1, marginLeft: 15 }}
          placeholder="请选择结束时间点"
          onChange={(_, endTime: string) => {
            this.setState({ endTime })
            onChange([this.state.beginTime, endTime])
          }} />
      </div>
    )
  }
}