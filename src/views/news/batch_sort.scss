@mixin text_ellipsis($line: 2) {
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line;
  word-break: break-all;
}

.batch_sort_row_dragging {
  z-index: 99999;
}

.bs_sortable_item {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
}

.bs_sortable_index {
  width: 30px;
  text-align: center;
  user-select: none;
}

.drag-handle {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 30px;
  width: 40px;
  // background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bs_sortable_article_wrapper {
  width: 375px;
  // height: 200px;
  margin-left: 40px;
  user-select: none;
  background-color: #F8F8F8;

  .item-wrapper {
    padding: 11px 0;
    margin: 0 15px;
    border-bottom: 1px solid rgba(236, 236, 236, 0.5);
  }
}

.author {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  .avatar {
    height: 40px;
    width: 40px;
    position: relative;
    flex: none;

    img {
      width: 100%;
      height: 100%;
      display: block;
      border-radius: 999999999px;
      overflow: hidden;
      // vertical-align: middle;
      object-fit: cover;
    }
  }

  .user_cert {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 9px;
    height: 9px;
  }

  .name {
    // flex: 1;
    margin-left: 10px;
    font-size: 14px;
    color: #151515;
    line-height: 22px;
    font-weight: 400;
    @include text_ellipsis(1);
    margin-bottom: 0;
  }

  .official_certification {
    flex: none;
  }
}

.live_status {
  position: absolute;
  left: 10px;
  top: 10px;
  height: 20px;
  padding: 0 5px;
  font-size: 9px;
  // transform: scale(0.8);
  // transform-origin: center;
  border-radius: 2px;
  overflow: hidden;
  color: #fff;
  z-index: 10;
  display: flex;
  align-items: center;

  &_0 {
    background-color: #ADD2FF;
    color: #0F63F8;
  }

  &_1 {
    background-color: #FF392D;
    color: white;
  }

  &_2 {
    background-color: #0F63F8;
    color: white;
  }

}

.recommend_title {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  padding: 0 15px;
  background-color: transparent;

  >div {
    display: flex;
    align-items: center;
    color: #151515;
    font: 500 18px / 25px 'PingFangSC-Medium', 'PingFang SC';

    i {
      content: '';
      width: 4px;
      min-width: 4px;
      height: 17px;
      margin-right: 10px;
      border-radius: 3px;
      background: linear-gradient(180deg, #0860D7 0%, #13CBED 100%);
    }

    .recommend_title_pic {
      width: 20px;
      height: 20px;
      object-fit: cover;
      margin-right: 5px;
    }

    .recommend_title_only_pic {
      height: 25px;
    }
  }
}

.title {
  font-size: 16px;
  color: #151515;
  line-height: 26px;
  font-weight: 400;
  @include text_ellipsis();

  // .subject_icon {
  //   display: inline-block;
  //   width: 44px;
  //   // height: .52rem;
  //   color: transparent;
  //   line-height: 26px;
  //   // background: url('../../assets/subject_recommend_title_icon.png') no-repeat left center / 34px 18px;
  // }

  .icon {
    display: inline-block;
    width: 36px;
    height: 19px;
    line-height: 19px;
    background: rgba(15, 99, 248, 1);
    border-radius: 6px 2px 6px 2px;
    text-align: center;
    margin-right: 5px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 12px;
    color: white;
    vertical-align: 1px;
  }

}

.only-word {}

.slp_wrapper {
  img {
    width: 100%;
    height: 115px;
    margin-bottom: 8px;
    object-fit: cover;
    overflow: hidden;
    display: block;
    border-radius: 6px;
  }
}

.image-text {
  display: flex;

  .img-wrapper {
    position: relative;
    width: 108px;
    height: 82px;
    border-radius: 6px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .duration {
      display: flex;
      align-items: center;
      position: absolute;
      z-index: 10;
      left: 0;
      bottom: -2px;
      height: 18px;
      padding: 0 7px;
      color: #fff;
      background-color: rgba(0, 0, 0, 0.5);
      font-size: 12px;
      // transform: scale(0.82);
      transform-origin: left center;
      border-top-right-radius: 6px;

      &::before {
        content: '';
        width: 7px;
        height: 9px;
        background: url('/assets/item_play_icon.png') no-repeat center / contain;
        margin-right: 5px;
      }
    }
  }

  .title-wrapper {
    flex: 1;
    margin-left: 12px;
  }
}

// 大图
.big-pic {
  .img_wrapper {
    position: relative;
    width: 100%;
    height: 194px;
    border-radius: 6px;
    overflow: hidden;

    img {
      object-fit: cover;
      width: 100%;
      height: 100%;
    }

    .play_icon {
      position: absolute;
      z-index: 10;
      left: 50%;
      top: 50%;
      width: 45px;
      height: 45px;
      background: rgba(34, 34, 34, 0.6);
      border-radius: 50%;
      transform: translate(-50%, -50%);

      &::before {
        content: '';
        position: absolute;
        left: 52%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 17px;
        height: 17px;
        background: url('/assets/item_play_icon.png') no-repeat center / contain;
      }
    }

    .duration {
      position: absolute;
      z-index: 10;
      left: 10px;
      bottom: 10px;
      color: #fff;
      font: 500 14px / 20px 'PingFangSC-Medium', 'PingFang SC';
      text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
    }
  }
}

// 三图
.tp_wrapper {
  .imgs_wrapper {
    display: flex;
    justify-content: space-between;
    height: 84px;
    margin-bottom: 8px;
    border-radius: 6px;
    overflow: hidden;

    .img {
      position: relative;
      width: 112px;
      border-radius: 0;
      height: 100%;
      object-fit: cover;
      // .front_img {
      //   border-radius: 0 !important;
      // }
    }
  }
}

.subject-recommend {
  position: relative;
  overflow: hidden;
  padding: 30px 0 15px;

  .img-wrapper {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    filter: blur(16px);

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      vertical-align: middle;
    }
  }

  .flag {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    right: 0;
    border-radius: 0px 0px 0px 20px;
    width: 67px;
    height: 25px;
    background-color: rgba(255, 255, 255, 0.25);
    color: #fff;
    font-size: 12px;

    // &::before {
    //   content: "";
    //   width: 15px;
    //   height: 15px;
    //   margin-left: 5px;
    //   margin-right: 5px;
    //   // background: url("./imgs/icons/subject_recommend_icon.png") no-repeat center / contain;
    // }
  }

  .title-wrapper {
    position: relative;
    z-index: 1;
    padding: 0 15px;

    .title {
      font: 600 20px / 28px "PingFangSC-Semibold", "PingFang SC";
      color: #fff;
    }

    .desc {
      width: 345;
      height: 44px;
      margin-top: 17px;
      padding-top: 7px;
      padding-left: 11px;
      backdrop-filter: blur(10px);
      background-color: rgba(255, 255, 255, 0.3);
      border-radius: 6px;

      .desc_txt {
        // width: 7.8rem;
        font-size: 10px;
        line-height: 15px;
        // transform: scale(0.8333);
        // transform-origin: left center;
        color: #fff;

        @include text_ellipsis(2);
      }
    }
  }

  .article-list {
    position: relative;
    padding: 11px 15px;
    display: flex;
    overflow: auto;

    &::-webkit-scrollbar {
      display: none;
    }

    .article-item {
      flex: none;
      height: 200px;
      width: 206px;
      border-radius: 10px;
      overflow: hidden;
      background-color: white;
      box-shadow: 0px 2px 15px 0px rgba(57, 57, 57, 0.52);

      &+.article-item {
        margin-left: 10px;
      }

      img {
        width: 100%;
        height: 120px;
        object-fit: cover;
      }

      .subject_item_bd {
        position: relative;
        padding: 10px;

        .title {
          font-size: 14px;
          color: #151515;
          font-weight: 400;
          line-height: 20px;
          min-height: 40px;
          @include text_ellipsis();
        }

        .subinfo {
          margin-top: 5px;
          font-size: 12px;
          color: #999;
        }
      }
    }
  }
}

.subject-recommend-2 {

  .title {
    margin-bottom: 10px;
  }

  .subject_content {
    background-color: white;
    border-radius: 12px;
    overflow: hidden;

    img {
      border-radius: 6px;
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
    }
  }
}

.hnrow_wrapper {
  position: relative;
  padding: 10px 15px 10px 37px;
  font-size: 16px;
  color: #151515;
  line-height: 26px;

  span {
    @include text_ellipsis();
  }

  &::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 17px;
    width: 12px;
    height: 12px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    background-image: url('/assets/recommend_news.png');
  }

  &.doc_2::before {
    background-image: url('/assets/recommend_news.png');
  }

  &.doc_3::before {
    background-image: url('/assets/recommend_link.png');
  }

  &.doc_4::before {
    background-image: url('/assets/recommend_pic.png');
  }

  &.doc_5::before {
    background-image: url('/assets/recommend_subject.png');
  }

  &.doc_8::before {
    background-image: url('/assets/recommend_live.png');
  }

  &.doc_9::before,
  &.doc_10::before,
  &.doc_11::before {
    background-image: url('/assets/recommend_video.png');
  }
}

.video_icon {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 18px;
  height: 18px;
  background: url('/assets/video_recommend_icon.png') no-repeat center / 100%;
}

.video-recommend-1 {
  .video-list {
    padding: 11px 15px;
    height: 288px;
    display: flex;
    overflow: auto;

    &::-webkit-scrollbar {
      display: none;
    }

    .video-item {
      flex: none;
      height: 100%;
      width: 200px;
      border-radius: 6px;
      overflow: hidden;
      position: relative;

      &+.video-item {
        margin-left: 8px;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        vertical-align: middle;
      }

      .info {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        // height: 1.8rem;
        padding: 6px 10px;
        background: linear-gradient(180deg, rgba(34, 34, 34, 0) 0%, #222222 100%);

        .title {
          font: 500 16px / 25px 'PingFangSC-Medium', 'PingFang SC';
          color: #fff;
          @include text_ellipsis();
        }
      }
    }
  }
}

.video-recommend-2 {
  padding: 11px 0;

  .video-list {
    height: 210px;
    position: relative;

    .video-item {
      height: 100%;
      width: 166.875px;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      margin: 0 auto;
      border-radius: 10px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .info {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        padding: 10px;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);

        .title {
          font-size: 14px;
          font-family: 'PingFangSC-Medium', 'PingFang SC';
          font-weight: 500;
          color: #FFFFFF;
          line-height: 20px;
          @include text_ellipsis(2);
        }

      }

      .mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: white;
        border-radius: 10px;
        pointer-events: none;
      }

    }
  }
}

.video-recommend-3 {
  .video-list {
    padding: 11px 15px;
    height: 238px;
    display: flex;
    overflow: auto;

    &::-webkit-scrollbar {
      display: none;
    }

    .video-item {
      flex: none;
      height: 100%;
      width: 213px;
      border-radius: 6px;
      overflow: hidden;
      position: relative;
      background-color: white;

      &+.video-item {
        margin-left: 8px;
      }

      img {
        width: 100%;
        height: 120px;
        overflow: hidden;
        object-fit: cover;
        vertical-align: middle;
      }

      .info {
        flex: 1;
        padding: 8px 10px;
        background: white;

        .time {
          font-size: 11px;
          line-height: 15px;
          font-weight: 400;
        }

        .title {
          margin-top: 6px;
          font: 400 16px / 25px 'PingFangSC-Regular', 'PingFang SC';
          color: #151515;
          @include text_ellipsis(2);
        }
      }
    }
  }
}

.readpaper_wrapper {
  padding: 11px 15px;

  .header {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    overflow: hidden;
    margin-bottom: 10px;

    .date_wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 6px;

      .day {
        font-size: 25px;
        font-family: DINOT, DINOT;
        font-weight: 900;
        color: #151515;
        line-height: 32px;
        margin-bottom: -7px;
      }

      .month {
        font-size: 13px;
        font-family: DINOT, DINOT;
        font-weight: 500;
        color: #151515;
        line-height: 17px;
      }
    }

    .type {
      height: 18px;
    }

    .slogan {
      flex: 1;
      font-size: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #151515;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }

  .cover_wrapper {
    height: 194px;
    overflow: hidden;
    position: relative;
    border-radius: 6px;

    .cover {
      width: 100%;
      height: 100%;
    }

    .mask {
      position: absolute;
      z-index: 10;
      left: 0;
      right: 0;
      bottom: 0;
      height: 70px;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.67) 100%);
    }

    .title {
      position: absolute;
      z-index: 11;
      left: 15px;
      right: 15px;
      bottom: 10px;
      font-size: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 22px;
    }
  }
}

.readpaper_wrapper_2 {
  padding: 11px 15px;

  .read_paper_content {
    height: 74px;
    // width: 100%;
    background-color: white;
    border-radius: 6px;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding-right: 12px;

    .read_paper_head {
      height: 100%;
      width: 69px;
      flex: none;
      position: relative;
    }

    .date_wrapper {
      position: absolute;
      top: 50%;
      left: 0;
      right: 15px;
      bottom: 0;
      text-align: center;

      span:first-child {
        font-weight: bold;
        font-size: 18px;
        color: #080808;
        line-height: 37px;
      }

      span:last-child {
        font-family: PingFangSC, PingFang SC;
        font-weight: 300;
        font-size: 10px;
        color: #080808;
        line-height: 37px;
      }
    }

    .m_head {
      background: url('/assets/m_paper_recommend_bg.png') no-repeat center / 100% 100%;
    }

    .e_head {
      background: url('/assets/e_paper_recommend_bg.png') no-repeat center / 100% 100%;
    }

    .paper_articles {
      height: 100%;
      flex: 1;
      overflow: hidden;

      .paper_articles_item {
        height: 100% !important;
        width: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        // align-items: center;
        justify-content: center;

        &>div {
          overflow: hidden;
          width: 100%;
          display: flex;
          align-items: center;

          &:nth-child(2) {
            margin-top: 12px;
          }

          .dot {
            background-color: black;
            width: 3px;
            height: 3px;
            border-radius: 50%;
            flex: none;
            margin-right: 6px;
          }

          .article-title {
            flex: 1;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #151515;

            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
}

.readpaper3_wrapper {
  padding: 11px 0;
  margin: 0 15px;
  border-bottom: 1px solid rgba(236, 236, 236, 0.5);

  .read_paper_content {
    display: flex;
    flex-direction: row;

    .m_logo {
      background: url('/assets/readpaper_m_logo.png') no-repeat center / cover;
    }

    .e_logo {
      background: url('/assets/readpaper_e_logo.png') no-repeat center / cover;
    }

    .read_paper_cover {
      width: 108px;
      height: 80px;
      border-radius: 6px;
      position: relative;

      .date_wrapper {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 2px;
        text-align: center;

        span:first-child {
          font-weight: bold;
          font-size: 18px;
          color: #000000;
          line-height: 23px;
        }

        span:last-child {
          font-weight: 300;
          font-size: 10px;
          color: #000000;
          line-height: 14px;
        }
      }
    }

    .info_wrapper {
      flex: 1;
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      margin-left: 12px;

      .readpaper_title {
        font-size: 16px;
        color: #151515;
        line-height: 26px;
        font-weight: 400;
        // @include text_ellipsis();
      }

      .date {
        margin-top: 10px;
        font-weight: 400;
        font-size: 11px;
        color: #999999;
        line-height: 16px;
      }
    }
  }
}

.news-recommend-card {
  .card-list {
    padding: 11px 15px;
    display: flex;
    overflow: auto;

    &::-webkit-scrollbar {
      display: none;
    }

    .nrc_wrapper {
      flex: none;
      width: 294px;
      height: 180px;
      padding: 0 15px;
      background-color: #fff;
      border-radius: 6px;
      display: flex;
      flex-direction: column;



      &+.nrc_wrapper {
        margin-left: 8px;
      }

      .top,
      .bottom {
        display: flex;
        flex: 1;
        align-items: center;

        .text {
          position: relative;
          height: fit-content;
          padding-left: 14px;
          font-size: 16px;
          line-height: 25px;
          @include text_ellipsis(3);
          color: #151515;

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background-color: #191919;
          }
        }
      }

      .bottom {
        border-top: 1px solid #F0F0F0;
      }
    }
  }
}

.news-recommend-imagetext {
  .card-list {
    padding: 11px 15px;
    display: flex;
    overflow: auto;

    &::-webkit-scrollbar {
      display: none;
    }

    .nrlp_wrapper {
      position: relative;
      height: 160px;
      width: 213px;
      border-radius: 6px;
      overflow: hidden;
      flex: none;

      &+.nrlp_wrapper {
        margin-left: 8px;
      }

      .cover {
        width: 100%;
        height: 100%;
      }

      .info {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 70px;
        padding: 6px 10px 0;
        background: linear-gradient(180deg, rgba(34, 34, 34, 0) 0%, #222222 100%);

        .title {
          font: 500 16px / 25px 'PingFangSC-Medium', 'PingFang SC';
          color: #fff;
          @include text_ellipsis();
        }
      }
    }
  }
}

.comment-recommend {
  .comment-list {
    padding: 11px 15px;
    display: flex;
    overflow: auto;

    &::-webkit-scrollbar {
      display: none;
    }

    // 潮客潮语
    .ckcr_wrapper {
      width: 258px;
      height: 190px;
      border-radius: 6px;
      background-color: #fff;
      flex: none;

      &+.ckcr_wrapper {
        margin-left: 8px;
      }

      .comment_wrapper {
        position: relative;
        height: 126px;
        padding: 20px 10px 0;
        background: linear-gradient(117deg, #FFFFFF 0%, #F2FBFF 100%);

        .title {
          font: 400 14px / 24px 'PingFangSC-Regular', 'PingFang SC';
          @include text_ellipsis(3);
        }

        .img_tag {
          position: absolute;
          left: 10px;
        }

        .subinfo {
          display: flex;
          justify-content: space-between;
          align-items: center;
          position: absolute;
          left: 10px;
          bottom: 4px;
          width: calc(100% - 20px);

          div[class^=account_user_name] {
            color: #666;
          }

          // .add_comment {
          //   display: flex;
          //   justify-content: space-between;
          //   align-items: center;
          //   width: 1.14rem;
          //   min-width: 1.14rem;
          //   margin-left: 0.1rem;
          //   color: #666;
          //   font-size: 0.22rem;
          //   transform: scale(0.92);
          //   transform-origin: right center;

          //   i {
          //     width: 0.16rem;
          //     height: 0.16rem;
          //     background: url('./imgs/content-relateive-news-arrow.png') no-repeat center / contain;
          //   }
          // }
        }
      }

      .origin_article {
        margin: 10px 12px 0 10px;
        font-size: 12px;
        line-height: 22px;
        color: #444;
        @include text_ellipsis();

        span {
          display: inline-block;
          height: 17px;
          line-height: 17px;
          margin-right: 6px;
          padding: 0 6px;
          color: #999;
          background: #F0F0F0;
          border-radius: 2px;
        }
      }
    }
  }
}

.column-recommend-1 {
  .column-list {
    padding: 11px 15px;
    display: flex;
    overflow: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .column-item {
    // flex: none;
    height: 30px;
    margin-right: 10px;
    padding: 0 14px;
    line-height: 30px;
    border-radius: 4px;
    border: 1px solid rgba(214, 221, 240, 0.5);
    white-space: nowrap;
    color: #0F63F8;
    background-color: #F3F7FD;
  }

}

.column-recommend-2 {
  .column-list {
    padding: 11px 15px;
    display: flex;
    overflow: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .column-item {
    width: 90px;
    // height: 107px;
    flex: none;
    // padding-top: 20px;
    overflow: hidden;

    &+.column-item {
      margin-left: 15px;
    }

    .icon_wrapper {
      width: 40px;
      height: 40px;
      margin: 0 auto;
      border-radius: 50%;
      overflow: hidden;
      display: block;
    }

    .column-title {
      margin-top: 11px;
      font-size: 14px;
      font-weight: 400;
      color: #191919;

      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      text-align: center;
    }
  }

}

.column-recommend-3 {
  .column-list {
    padding: 11px 15px;
    display: flex;
    overflow: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .column-item {
    flex: none;
    overflow: hidden;
    width: 154px;
    height: 120px;
    margin-right: 8px;
    padding: 10px;
    background-color: #fff;
    box-shadow: 0px 2px 10px 0px rgba(34, 34, 34, 0.03);
    border-radius: 6px;

    &+.column-item {
      margin-left: 7px;
    }

    .info {
      display: flex;
      align-items: center;
    }

    .icon_wrapper {
      width: 36px;
      min-width: 36px;
      height: 36px;
      border-radius: 50%;
      overflow: hidden;
    }

    .column-title {
      margin-left: 8px;
      font-size: 16px;
      font-weight: 400;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    p {
      margin-top: 10px;
      font-size: 12px;
      color: #666;
      line-height: 18px;
      @include text_ellipsis(3);
    }
  }

}


.column-recommend-4 {
  .column-list {
    padding: 11px 15px;
    display: flex;
    overflow: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .crs4_wrapper {
    flex: none;
    width: 116px;
    height: 150px;
    margin-right: 10px;
    padding-top: 25px;
    filter: drop-shadow(0px 2px 9px rgba(211, 211, 211, 0.5));

    .crs4_content {
      position: relative;
      width: 100%;
      height: 112px;
      background-color: #fff;
      background: #FFFFFF;
      border-radius: 8px;
      padding: 44px 12px 0;

      // box-shadow: 0px .04rem .18rem rgba(211, 211, 211, 0.5);
      .crs4_name {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 13px;
        color: #000000;
        line-height: 16px;
        text-shadow: 0px 2px 9px rgba(211, 211, 211, 0.5);
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .crs4_focus_btn {
        width: 52px;
        height: 22px;
        background-color: #0F63F8;
        border-radius: 11px;
        color: #fff;
        line-height: 22px;
        text-align: center;
        margin: 17px auto 0;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        text-shadow: 0px 2px 9px rgba(211, 211, 211, 0.5);
        position: relative;
      }

      .crs4_view_btn {
        margin: 17px auto 0;
        width: 52px;
        height: 22px;
        box-shadow: 0px 2px 9px 0px rgba(211, 211, 211, 0.5);
        border-radius: 11px;
        border: 1px solid #ECECEC;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #505050;
        line-height: 20px;
        text-shadow: 0px 2px 9px rgba(211, 211, 211, 0.5);
        text-align: center;
      }

      .crs4_avatar_wrap {
        position: absolute;
        left: 50%;
        top: -21px;
        transform: translateX(-50%);
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: #fff;
        border: 6px solid white;
      }

      .icon_wrapper {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        overflow: hidden;
      }
    }
  }

}

.ur_wrapper {

  .user_list_wrapper {
    padding: 11px 15px;
    display: flex;
    overflow: auto;

    &::-webkit-scrollbar {
      display: none;
    }

    .user {
      position: relative;
      width: 130px;
      min-width: 130px;
      height: 184px;
      margin: 0 5px;
      padding: 14px 10px;
      border-radius: 6px;
      box-shadow: 0px 3px 8px 0px rgba(128, 128, 128, 0.22);
      background-color: white;

      .official_certification {
        position: absolute;
        right: 0;
        top: 0;
      }

      .head {
        position: relative;
        width: 50px;
        height: 50px;
        margin: 0 auto;

        >img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 50%;
          overflow: hidden;
        }

        .icon_wrapper {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          border: 1px solid rgba(236, 236, 236, 0.5);
          overflow: hidden;
        }

        .user_cert {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 15px;
          height: 15px;
        }
      }

      .nick_name {
        margin-top: 14px;
        font: 500 14px / 14px 'PingFangSC-Medium', 'PingFang SC';
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: center;
      }

      .reason {
        margin-top: 8px;
        font-size: 12px;
        line-height: 16px;
        color: #999;
        text-align: center;
        @include text_ellipsis();
      }

      .focus_wrapper {
        position: absolute;
        left: 50%;
        bottom: 14px;
        transform: translateX(-50%);

        .focus {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 70px;
          height: 24px;
          border-radius: 12px;
          color: #fff;
          background-color: #0F63F8;

          // i {
          //   width: 9px;
          //   height: 8px;
          //   margin-right: 0.04rem;
          //   background: url('./imgs/icon_user_unfocused.png') no-repeat center / contain;
          // }
        }
      }
    }
  }
}

.ur_wrapper_2 {
  .user_list {
    padding: 0 15px;
    display: flex;
    overflow: auto;

    &::-webkit-scrollbar {
      display: none;
    }

    .user2_item {
      height: 112px;
      width: 150px;
      flex: none;
      display: flex;
      align-items: flex-end;
      position: relative;

      &+.user2_item {
        margin-left: 10px;
      }

      .avatar {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        border-bottom-right-radius: 0;
        position: absolute;
        right: 0;
        top: 0;
        object-fit: cover;
        vertical-align: middle;
      }

      .mask {
        width: 64px;
        height: 64px;
        position: absolute;
        right: 0;
        top: 1px; // 有误差 底部会漏光
        border-bottom-left-radius: 50%;
      }

      .info {
        width: 100%;
        height: 91px;
        // background-color: red;
        border-radius: 10px;
        padding: 13px 10px;

        .name {
          font-family: 'PingFangSC-Medium', 'PingFang SC';
          color: #FFFFFF;
          font-size: 14px;
          font-weight: 500;
          line-height: 20px;
          @include text_ellipsis(1);
          // margin-right: 1.28rem;
          width: 70px;
        }

        .cert {
          position: relative;
          z-index: 10;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 10px;
          border: 1px solid #FFFFFF;
          height: 20px;
          width: fit-content;
          max-width: 85px;
          padding: 0 6px;
          margin-top: 3px;
          color: #FFFFFF;
          display: flex;
          align-items: center;

          .cert_text {
            font-size: 10px;
            font-family: 'PingFangSC-Regular', 'PingFang SC';
            font-weight: 400;
            color: #FFFFFF;
            line-height: 20px;
            @include text_ellipsis(1);
          }

          .cert_icon {
            flex: none;
            height: 10px;
            width: 10px;
            margin-right: 4px;
          }
        }

        .reason {
          margin-top: 7px;
          font-family: 'PingFangSC-Regular', 'PingFang SC';
          color: #FFFFFF;
          font-size: 10px;
          font-weight: 400;
          line-height: 14px;
          width: 85px;
          @include text_ellipsis(2);
        }
      }

      .focus_wrapper {
        position: absolute;
        right: 0;
        bottom: 13px;
        height: 18px;
        background-color: white;
        border-top-left-radius: 9px;
        border-bottom-left-radius: 9px;
        display: flex;
        align-items: center;
        padding: 0 9px;

        .focus_icon {
          flex: none;
          width: 8px;
          height: 8px;
          padding-right: 1px;
        }

        .focus {
          font-size: 10px;
          font-family: 'PingFangSC-Regular', 'PingFang SC';
          font-weight: 400;
          color: #151515;
        }
      }

    }
  }
}

// 广告推荐位
.advr_wrapper {
  padding: 11px 15px;

  h5 {
    margin-bottom: 8px;
    font-weight: 400;
    font-size: 17px;
    color: #333;
  }

  .advr_swiper {
    position: relative;
    border-radius: 6px;
    overflow: hidden;

    .ad-list {
      display: flex;
      overflow: auto;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    img {
      height: 100%;
      object-fit: cover;
      vertical-align: middle;
      width: 345px;
    }

    .tag {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      right: 7px;
      bottom: 7px;
      width: 31px;
      height: 15px;
      border-radius: 2px;
      border: 1px solid rgba(255, 255, 255, 0.5);
      background-color: rgba(255, 255, 255, 0.5);
      z-index: 2;

      span {
        font-size: 9px;
        transform: scale(0.8);
        transform-origin: center center;
        color: #333;
      }
    }
  }

  p {
    margin-top: 8px;
    font-size: 12px;
    color: #acacac;
  }

}

// 数据推荐位
.dr_wrapper {
  padding: 8px 0;

  .content {
    padding: 15px;
    background-color: #fff;

    iframe {
      width: 100%;
      border: 0;
      border-radius: 6px;
      overflow: hidden;
      pointer-events: none;
    }
  }
}

// 话题样式1
.topic_style1_wrapper {
  margin: 11px 15px;
  padding: 8px;
  border-radius: 6px;
  background: url("/assets/topic1_recommend_bg.png") no-repeat top / 100% 0.7rem, linear-gradient(338deg, #FFFFFF 0%, #F2FBFF 100%);
  border: 1px solid #E0EBFF;

  .topic_style1_title {
    display: flex;
    align-items: center;

    .topic_title {
      display: flex;
      align-items: center;
      flex: 1;
      color: #151515;
      font: 500 16px / 18px "PingFangSC-Medium", "PingFang SC";

      &::before {
        content: '';
        width: 13px;
        height: 13px;
        margin-right: 6px;
        background: url("/assets/topic1_recommend1_icon.png") no-repeat center / contain;
      }
    }

    i {
      width: 12px;
      height: 12px;
      // background: url('./imgs/icon_list_more.png') no-repeat center / contain;
    }
  }

  .topic_style1_list {
    display: flex;
    flex-wrap: wrap;
    padding-top: 10px;

    .topic_item {
      display: flex;
      justify-content: space-between;
      width: 162.5px;
      margin-top: 10px;

      .item {
        overflow: hidden;
        flex: 1;
        display: flex;
        align-items: center;

        &:nth-child(odd) {
          margin-right: 5px;
        }

        span {
          // width: 0;
          // flex: 1;
          margin-right: 2px;
          font-size: 12px;
          color: #151515;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        i {
          flex: none;
          width: 18px;
          height: 19px;
          background-repeat: no-repeat;
          background-size: contain;

          &.new {
            margin-right: 2px;
            // background-image: url("../mobile/imgs/icons/topic_style1_new.png");
          }

          &.prize {
            margin-right: 2px;
            // background-image: url("../mobile/imgs/icons/topic_style1_prize.png");
          }
        }

        img {
          flex: none;
          height: 19px;
        }
      }
    }
  }
}

.topic_style2 {

  .topic_list {
    padding: 11px 15px;
    display: flex;
    overflow: auto;

    &::-webkit-scrollbar {
      display: none;
    }

    .topic_style2_wrapper {
      position: relative;
      height: 136px;
      width: 255px;
      flex: none;
      border-radius: 6px;
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center;

      &+.topic_style2_wrapper {
        margin-left: 10px;
      }

      .hd {
        padding: 15px 15px 13px;

        .main_content {
          display: flex;

          &>img {
            min-width: 45px;
            width: 45px;
            height: 45px;
            border-radius: 6px;
            overflow: hidden;
            object-fit: cover;
          }

          .topic_info {
            flex: 1;
            width: 0;
            margin-left: 9px;

            .topic_title {
              margin-top: 3px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              font: 500 14px / 20px "PingFangSC-Medium", "PingFang SC";
            }

            .subinfo {
              margin-top: 3px;
              font-size: 12px;
              color: #999;
            }
          }
        }

        .desc {
          width: 225px;
          margin-top: 10px;
          padding: 5px 10px;
          border-radius: 13px;

          .desc_txt {
            // width: 4.92rem;
            font-size: 10px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            // transform: scale(0.8333);
            // transform-origin: left center;
          }
        }
      }

      .bd {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 27px;
      }

      &.style1 {
        background-image: url("/assets/topic2_bg1.png");

        .desc {
          background: rgba(80, 0, 255, 0.05);
        }
      }

      &.style2 {
        background-image: url("/assets/topic2_bg2.png");

        .desc {
          background: rgba(0, 195, 37, 0.11);
        }
      }

      &.style3 {
        background-image: url("/assets/topic2_bg3.png");

        .desc {
          background: rgba(255, 94, 0, 0.11);
        }
      }

      &.style4 {
        background-image: url("/assets/topic2_bg4.png");

        .desc {
          background: rgba(0, 96, 255, 0.05);
        }
      }
    }
  }
}

.default-recommed {
  height: 100px;
  background-color: #acacac;
  text-align: center;
  line-height: 100px;
}

.pic_recommend {
  .pic_list {
    display: flex;
    overflow: auto;
    padding: 11px 15px;

    &::-webkit-scrollbar {
      display: none;
    }

    .pic_style1_wrapper {
      width: 144px;
      height: 72px;
      flex: none;

      &+.pic_style1_wrapper {
        margin-left: 6px;
      }

      img {
        width: 100%;
        height: 100%;
        border-radius: 6px;
        overflow: hidden;
        object-fit: cover;
      }
    }
  }
}

.pic_recommend_2 {
  .pic_style2_wrapper {
    position: relative;
    padding: 15px 0;

    .img_wrapper {
      // width: 310px;
      width: 100%;
      height: 207px;
      position: relative;
    }

    .scrollbar {
      position: absolute;
      top: 15px;
      right: 0;
      z-index: 1;
      width: 65px;
      height: 207px;
      // backdrop-filter: blur(10px);
      // background: rgba(48, 48, 48, 0.19);
      // background: url('../mobile/imgs/pic2recommend_bg.png') no-repeat center / cover;
      overflow: hidden;

      .img_list {
        height: 180px;
        padding: 10px 0;
      }

      .scrollbar_item {
        width: 100%;
        height: 34px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;

        img {
          width: 49px;
          height: 100%;
          object-fit: cover;
          border-radius: 6px;
          border: 2px solid transparent;
          overflow: hidden;
          background-color: transparent;

        }

        &.active {
          img {
            border-color: #fff;
          }
        }
      }
    }

    .pager {
      height: 26px;
      text-align: center;
      line-height: 26px;
      background: rgba(216, 216, 216, 0.2);
      backdrop-filter: blur(10px);

      span {
        font-size: 12px;
        font-family: "PingFangSC-Medium", "PingFang SC";
        color: #fff;

        &:first-child {
          color: #5D97FE;
        }
      }
    }
  }
}

.pic_recommend_style3_wrapper {

  .pic_list {
    padding: 0 15px;
    display: flex;
    overflow: auto;

    &::-webkit-scrollbar {
      display: none;
    }

    .pic_style3_wrapper {
      width: 105px;
      height: 116px;
      flex: none;
      position: relative;
      padding-top: 11px;

      img {
        width: 100%;
        height: 100%;
        border-radius: 10px;
        overflow: hidden;
      }

      &+.pic_style3_wrapper {
        margin-left: 10px;
      }

      &::before {
        content: '';
        position: absolute;
        width: 86px;
        height: 11px;
        top: 0;
        left: 50%;
        transform: translate(-50%);
        background: url('/assets/image_recommen3_shadow.png') no-repeat top / 100%;
      }
    }
  }
}

.common_recommend {
  .common_list {
    display: flex;
    overflow: auto;
    padding: 11px 15px;

    &::-webkit-scrollbar {
      display: none;
    }

    .crs1_wrapper {
      height: 30px;
      margin-right: 10px;
      padding: 0 14px;
      line-height: 30px;
      border-radius: 4px;
      border: 1px solid rgba(214, 221, 240, 0.5);
      white-space: nowrap;
      color: #0F63F8;
      background-color: #F3F7FD;
    }

  }
}

.common_recommend_2 {
  .common_list {
    display: flex;
    overflow: auto;
    padding: 11px 15px;
    background-color: white;

    &::-webkit-scrollbar {
      display: none;
    }

    .crs2_wrapper {
      flex: none;
      width: 90px;
      height: 107px;
      padding-top: 20px;

      &+.crs2_wrapper {
        margin-left: 15px;
      }

      .icon_wrapper {
        display: block;
        width: 40px;
        height: 40px;
        margin: 0 auto;
        border-radius: 50%;
        overflow: hidden;
      }

      div {
        margin-top: 11px;
        font-size: 14px;
        font-weight: 400;
        color: #191919;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        text-align: center;
      }
    }
  }
}

.common_recommend_3 {
  .common_list {
    display: flex;
    overflow: auto;
    padding: 11px 15px;

    &::-webkit-scrollbar {
      display: none;
    }

    // 通用推荐位 样式三
    .crs_common_wrapper {
      flex: none;
      width: 97px;
      height: 123px;
      margin-right: 10px;
      position: relative;
      border-radius: 10px 0;
      overflow: hidden;

      .icon_wrapper {
        width: 100%;
        height: 100%;
        vertical-align: middle;
        object-fit: cover;
      }

      .info_wrapper {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 70px;
        width: 100%;
        padding: 0 8px 8px;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        // align-items: center;

        .title {
          @include text_ellipsis(1);
          color: white;
          font: 14px / 20px "PingFangSC-Semibold", "PingFang SC";
        }

        .desc {
          @include text_ellipsis(1);
          color: white;
          font: 10px / 14px "PingFangSC-Semibold", "PingFang SC";
        }
      }
    }
  }
}

.ugc_recommend_1 {
  .article_list {
    display: flex;
    overflow: auto;
    padding: 11px 15px;

    &::-webkit-scrollbar {
      display: none;
    }

    .cscr_wrapper {
      flex: none;
      width: 283px;
      height: 110px;
      padding: 15px;
      background-color: white;
      margin-right: 11px;
      border-radius: 6px;

      .info_wrapper {
        display: flex;

        .img_wrapper {
          position: relative;
          width: 48px;
          min-width: 48px;
          height: 48px;
          margin-right: 8px;
          border-radius: 4px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          i {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, 0.5);

            &::before {
              content: "";
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-40%, -50%);
              width: 8px;
              height: 8px;
              background: url('/assets/item_play_icon.png') no-repeat center / contain;
            }
          }

          span {
            position: absolute;
            right: 0;
            bottom: 0;
            width: 15px;
            height: 15px;
            font-size: 12px;
            color: #fff;
            text-align: center;
            line-height: 15px;
            background-color: rgba(34, 34, 34, 0.6);
            border-top-left-radius: 2px;
          }
        }

        h6 {
          font-size: 16px;
          font-weight: 400;
          line-height: 24px;
          @include text_ellipsis();
        }
      }

      .subinfo {
        margin-top: 14px;

        .account {
          img {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            overflow: hidden;
            object-fit: cover;
            vertical-align: middle;
          }

          .account_name {
            margin-left: 10px;
            font-size: 12px;
            color: #666;
            font-family: PingFangSC-Medium, PingFang SC;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
      }
    }
  }
}

.ugc_recommend_2 {
  .list {
    display: flex;
    overflow: auto;
    padding: 11px 15px;

    &::-webkit-scrollbar {
      display: none;
    }

    .cbcr_wrapper {
      flex: none;
      display: flex;
      flex-direction: column;
      flex-wrap: wrap;
      column-gap: 6px;
      // row-gap: 0.12rem;
      width: 294px;
      height: 194px;
      margin-right: 6px;
      justify-content: space-between;
      overflow: hidden;

      .item1 {
        flex: none;
        width: 194px;
        height: 194px;
      }

      .item2 {
        flex: none;
        width: 94px;
        height: 94px;
      }

      .item3 {
        flex: none;
        width: 94px;
        height: 94px;
      }

      .cbc_item {
        overflow: hidden;
        position: relative;
        border-radius: 4px;

        .cover {
          width: 100%;
          height: 100%;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .info_wrapper {
          position: absolute;
          width: 100%;
          bottom: 0;
          left: 0;
          background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);
          padding: 6px;

          .title {
            @include text_ellipsis(2);
            color: white;
            font: 14px / 20px "PingFangSC-Semibold", "PingFang SC";
          }

          .author {
            margin-top: 4px;
            display: flex;
            align-items: center;
            gap: 5px;

            .avatar {
              height: 18px;
              width: 18px;
              position: relative;
              flex: none;

              img {
                display: block;
                border-radius: 999999999px;
                overflow: hidden;
                // vertical-align: middle;
                object-fit: cover;
              }
            }

            .user_cert {
              position: absolute;
              right: 0;
              bottom: 0;
              width: 9px;
              height: 9px;
            }

            .name {
              // flex: 1;
              font-size: 11px;
              color: #FFFFFF;
              line-height: 16px;
              font-weight: 400;
              @include text_ellipsis(1);
              margin-bottom: 0;
            }

            .official_certification {
              flex: none;
            }
          }
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          vertical-align: middle;
        }

        .play_btn {
          position: absolute;
          left: 6px;
          top: 6px;
          width: 18px;
          height: 18px;
          background-color: rgba(0, 0, 0, 0.3);
          border-radius: 50%;

          &::before {
            content: '';
            position: absolute;
            left: 52%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 7.5px;
            height: 7.5px;
            background: url('/assets/item_play_icon.png') no-repeat center / contain;
          }
        }

        .img_length {
          background-color: rgba(34, 34, 34, 0.6);
          position: absolute;
          top: 0;
          right: 0;
          text-align: center;
          width: 18px;
          height: 18px;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 18px;
          border-bottom-left-radius: 4px;
        }
      }
    }
  }
}

.vertical_lr_wrapper {
  .live_list_wrapper {
    display: flex;
    overflow: auto;
    padding: 11px 15px;
    overflow-x: scroll;

    &::-webkit-scrollbar {
      display: none;
    }

    .live {
      width: 155px;
      flex: none;
      height: 259px;
      margin-right: 10px;

      &:last-child {
        margin-right: 0;
      }

      .cover {
        position: relative;
        height: 207px;
        border-radius: 6px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          display: block;
        }
      }

      h6 {
        margin-top: 10px;
        font-size: 16px;
        color: #151515;
        line-height: 22px;
        font-weight: 400;
        @include text_ellipsis();
      }
    }

  }
}

.lr_wrapper {
  .live_list_wrapper {
    display: flex;
    overflow: auto;
    padding: 11px 15px;
    overflow-x: scroll;

    &::-webkit-scrollbar {
      display: none;
    }

    .live {
      width: 215px;
      min-width: 215px;
      height: 176px;
      margin-left: 15px;

      &:last-child {
        margin-right: 15px;
      }

      .cover {
        position: relative;
        height: 122px;
        border-radius: 6px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          display: block;
        }
      }

      h6 {
        margin-top: 10px;
        font-size: 16px;
        color: #151515;
        line-height: 22px;
        font-weight: 400;
        @include text_ellipsis();
      }
    }

  }
}

.live_forecast {
  padding: 11px 15px;

  .top {
    width: auto;
    height: 75px;
    display: flex;
    overflow-x: auto;

    .top_item {
      flex: none;
      height: 70px;
      width: 80px;
      border-radius: 6px;
      overflow: hidden;
      // margin: 0 .2rem;
      margin-right: 9px;
      justify-content: center;
      align-items: center;
      box-shadow: 0 1px 5px 0 rgba(208, 208, 208, 0.5);

      &:last-child {
        margin-right: 0;
      }

      img {
        height: 70px;
        width: 80px;
      }
    }
  }

  .top::-webkit-scrollbar {
    display: none;
  }

  .bottom {
    height: 118px;
    background: #fff;
    border-radius: 6px;
    margin-top: 8px;
    overflow: hidden;

    .forecast_list {
      width: 100%;
      height: 100%;
      display: flex;
      overflow: auto;

      &::-webkit-scrollbar {
        display: none;
      }

      .forecast_slide {
        width: 100%;
        flex: none;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }

      .forecast {
        display: flex;
        line-height: 22px;
        // margin: .3rem 0;
        font-size: 16px;

        .forecast_disc {
          width: 5px;
          height: 5px;
          margin: 7.5px 10px 0 10px;
          background: #151515;
          border-radius: 10px;
          flex-shrink: 0;
        }

        .forecast_content {
          padding-right: 10px;
          color: #151515;
          display: -webkit-box;
          /* 基于块级容器进行布局 */
          -webkit-line-clamp: 2;
          /* 最多显示两行内容 */
          -webkit-box-orient: vertical;
          /* 垂直方向布局 */
          overflow: hidden;
          /* 超出部分隐藏 */
          text-overflow: ellipsis;
          /* 超出部分显示省略号 */
        }

        .forecast_content_1 {
          padding-right: 10px;
          color: #151515;
          display: -webkit-box;
          /* 基于块级容器进行布局 */
          -webkit-line-clamp: 1;
          /* 最多显示1行内容 */
          -webkit-box-orient: vertical;
          /* 垂直方向布局 */
          overflow: hidden;
          /* 超出部分隐藏 */
          text-overflow: ellipsis;
          /* 超出部分显示省略号 */
        }
      }
    }
  }

  .content {}
}

.report_wrapper {
  padding: 11px 0 0;

  .header_wrapper {
    position: relative;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 11px;
    cursor: pointer;
    padding: 0 15px;

    .header_title {
      display: flex;
      flex-direction: column;
      flex: 1;

      >span {
        flex: none;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #151515;
      }
    }

    .next {
      flex: none;
      display: flex;
      align-items: center;

      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #999999;

      img {
        width: 11px;
        height: 11px;
        vertical-align: middle;
      }
    }
  }

  .report_list {
    padding: 0 15px 11px !important;
    display: flex;
    overflow: auto;

    &::-webkit-scrollbar {
      display: none;
    }

  }

  .report_card {
    position: relative;
    flex: none;
    width: 211px;
    height: 126px;
    background-color: white;
    border-radius: 4px;
    // box-shadow: 0px 2px 4px 0px rgba(209, 209, 209, 0.5);
    cursor: pointer;
    margin-right: 10px;

    .title_wrapper {
      background: radial-gradient(0% 66% at 27% 27%, #FFFFFF 0%, #F3F6F8 100%);
      padding: 12px;
      height: 90px;
    }

    .report_title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #151515;
      line-height: 22px;
      // padding: .24rem;
      height: 66px;
      text-overflow: ellipsis;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;

      span {
        display: inline-block;
        text-align: center;
        // width: .54rem;
        height: 15px;
        background: rgba(248, 141, 15, 0.5);
        border-radius: 4px 0px 4px 0px;
        margin-right: 5px;
        padding: 0 4px;

        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 10px;
        // color: #151515;
        line-height: 15px;
        vertical-align: 1px;
      }

      .tag_1 {
        background: rgba(15, 99, 248, 0.12);
        color: #0F63F8;
        // opacity: 0.12;
      }

      .tag_2 {
        background: rgba(249, 143, 18, 0.12);
        color: #F99516;
      }

      .tag_3 {
        background: rgba(67, 73, 255, 0.12);
        color: #4349FF;
      }
    }

    .status_lable {
      height: 36px;
      padding: 0 12px;
      line-height: 36px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #6B6B6B;
    }

    .report_status {
      position: absolute;
      right: 5px;
      bottom: 30px;
      width: 30px;
      height: 30px;

      img {
        width: 100%;
        height: 100%;
        vertical-align: middle;
      }
    }
  }

}

.book_rank {
  padding: 11px 15px 11px;
  background-color: #F8F8F8;

  .content {
    display: flex;
    height: 60px;
    box-shadow: 0px 1px 5px 0px rgba(208, 208, 208, 0.5);
    border-radius: 6px;
    background-color: #fff;
    align-items: center;
    padding: 10px;

    .left {
      width: 40px;
      height: 40px;
      flex: none;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .line {
      width: 1px;
      flex: none;
      height: 31px;
      background-color: #24A646;
      margin-left: 10px;
      margin-right: 10px;
    }

    .right {
      flex: 1;
      overflow: hidden;
      height: 40px;

      .forecast_list {
        width: 100%;
        height: 100%;

        &::-webkit-scrollbar {
          display: none;
        }

        .forecast {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          height: 100%;
          width: 100%;

          div:first-child {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #151515;
            line-height: 20px;

            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }

          div:last-child {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 17px;

            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
      }
    }
  }
}

.ranking_wrapper {
  padding: 11px 15px 11px;
  background-color: #F8F8F8;

  .content {
    display: flex;
    height: 68px;
    box-shadow: 0px 1px 5px 0px rgba(208, 208, 208, 0.5);
    border-radius: 6px;
    background-color: #fff;
    align-items: center;
    padding: 0 10px;

    .left {
      width: 82px;
      height: 42px;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 12px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .right {
      flex: 1;
      overflow: hidden;
      height: 68px;

      .forecast_list {
        width: 100%;
        height: 100%;

        .forecast {
          display: flex;
          flex-direction: column;
          justify-content: center;
          width: 100%;
          height: 100%;

          .list_title {
            color: #000;
            font-weight: 400;
            line-height: 22px;
            font-size: 14px;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            line-clamp: 2;
            -webkit-line-clamp: 2;
            word-break: break-all;
          }
        }
      }
    }
  }
}

.community-dynamic-item {
  .author {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .avatar {
      height: 40px;
      width: 40px;
      position: relative;
      flex: none;

      img {
        width: 100%;
        height: 100%;
        display: block;
        border-radius: 999999999px;
        overflow: hidden;
        // vertical-align: middle;
        object-fit: cover;
      }
    }

    .user_cert {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 9px;
      height: 9px;
    }

    .name {
      // flex: 1;
      margin-left: 10px;
      font-size: 14px;
      color: #151515;
      line-height: 22px;
      font-weight: 400;
      @include text_ellipsis(1);
      margin-bottom: 0;
    }

    .official_certification {
      flex: none;
    }
  }

  .community_content {
    margin: 0 0 6px;
    font-size: 16px;
    color: #3B424C;
    line-height: 24px;
    white-space: pre-wrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    word-break: break-all;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    line-clamp: 3;
  }

  .video {
    position: relative;
    height: 195px;
    border-radius: 6px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    &.is_vertical {
      width: 200px;
      height: 266px;
    }

    .play_button {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 45px;
      height: 45px;
      background: rgba(34, 34, 34, 0.6);
      border-radius: 50%;

      &::after {
        content: '';
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-45%, -50%);
        width: 17px;
        height: 20px;
        background: url('/assets/item_play_icon.png') no-repeat center / contain;
      }

      // span {
      //   font-size: .24rem;
      //   color: #fff;
      //   transform: scale(0.85);
      // }
    }

    span {
      position: absolute;
      left: 10px;
      bottom: 10px;
      color: #fff;
      text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
      line-height: 20px;
      font-weight: 500;
      font-size: 14px;
    }
  }

  .single_img {
    border-radius: 6px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      vertical-align: middle;
    }
  }

  .imgs {
    width: fit-content;
    display: flex;
    flex-wrap: wrap;
    border-radius: 6px;
    overflow: hidden;

    div {
      position: relative;
      width: 113px;
      height: 113px;
      margin-right: 3px;
      margin-top: 3px;

      &:last-child,
      &:nth-child(3n) {
        margin-right: 0;
      }

      &:nth-child(-n+3) {
        margin-top: 0;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      &.img_more {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        font-size: 16px;
        color: #fff;
        line-height: 111px;
        text-align: center;
      }
    }

    &.four_imgs {
      max-width: 231.5px;

      div:nth-child(2n) {
        margin-right: 0;
      }

      div:nth-child(3) {
        margin-top: 3px;
        margin-right: 3px;
      }
    }
  }

}

.bulletin_recommend {
  padding: 13px 0;

  .bulletin_content {
    margin: 0 15px;
    height: 55px;
    background-color: white;
    border-radius: 6px;
    display: flex;
    align-items: center;
    overflow: hidden;

    .bulletin_logo {
      width: 70px;
      height: 55px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #151515;
      line-height: 22px;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      word-break: break-all;
      text-overflow: ellipsis;
      white-space: normal;
    }
  }
}