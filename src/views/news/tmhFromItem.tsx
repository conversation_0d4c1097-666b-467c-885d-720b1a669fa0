import React, { useState, useEffect } from 'react'
import {
    // Form,
    Input,
    Tooltip,
    Icon,
    Cascader,
    Select,
    Row,
    Col,
    Checkbox,
    Button,
    AutoComplete,
    Radio,
    message
} from 'antd';
import connectSession from '@utils/connectSession';
import { ImageUploader } from '@components/common';
import _, { times } from 'lodash'
import { recommendApi } from '@app/api';
const { Option } = Select;

// @connectSession
// @(Form.create({ name: 'activityForm' }) as any)
export default function FromItem(props: any) {
    const getFieldDecorator = props.getFieldDecorator
    const getFieldsValue = props.getFieldsValue
    const setFieldsValue = props.setFieldsValue
    const v = props.v
    const Form = props.Form
    const [stateList, setStateList] = useState({ ...props.json })
    const [optionData, setOptionData] :any = useState([])
    const [refIds, setRefIds] :any = useState([...props.ref_ids])
    useEffect(() => {
        setStateList({ ...props.json })
        console.log(props, 'props=+++++++++++');
        setRefIds(props.ref_ids)
    }, [props.json])
    //单选，
    const changeRadio = (e: any,v: number) => {
        console.log(getFieldsValue());
        props.changeRadio(v,e)
    }
    //选择稿件
    const chooseSelect = (e: any, v: any) => {
        const values = getFieldsValue();
        let optionText=JSON.parse(e)
        console.log(optionText.id,'optionText.id=========');
        let ids=[...refIds]
        if(ids.findIndex((el: any)=>el==optionText.id)>=0){
          values['json'][v].source_list_title=''
          setFieldsValue({
            ...values
          })
          return message.error('该稿件已添加过');
        }
        ids.push(optionText.id)
        setRefIds([...ids])
        values['json'][v].source_list_title=`${optionText.id} - 【${optionText.channel_name}】- ${optionText.list_title} `
        const json = { ...stateList.json, ...(values.json ? values.json : {}) };
        setFieldsValue({
            ...values
        })
        props.chooseSelectItem(values,ids)
        setStateList({
            json
        })
    }
    const  handleSearch = _.debounce((val: any,v: number) => {
        let data={keyword:val.toString(),doc_type:'2,3,4,5,8,9'}
        recommendApi.searchManuscript(data).then((res: any)=>{
            let article_list=[...res.data.article_list]
            setOptionData(article_list)
        })
      }, 500)
       //更改标题
      const  changeManuscript = (e: any,v: number,val: any) => {
        console.log(val,'val==');
        let id=val.channel_article_id || val.source_list_title.split('-')[0].trim()
        let ids=refIds.findIndex((el: any)=>el==id)
        if(ids>=0){
            refIds.splice(ids,1)
        }
        setRefIds(refIds)
        const values = getFieldsValue();
        const json = { ...stateList.json, ...(values.json ? values.json : {}) };
        json[v]['source_list_title'] = ''
        setStateList({
            ...json
        })
      }
    return (
        <>  
        <div>{!!!stateList[v]?.auto_title}</div>
             <Col key={v} className='label_hei' style={{ display: `${stateList[v]?.source_list_title ? '' : 'none'}` }}>
                <Col span={4}><span className='verification_icon'>*</span> <span>选择稿件</span>:</Col>
                <div className='title_or_change'>
                  <div className='title_show_hide'>{stateList[v]?.source_list_title}</div> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span className='choose_change' onClick={(e)=>changeManuscript(e, v,stateList[v])}>更改</span>
                </div>
              </Col>
            <Form.Item label="选择稿件" style={{ display: `${stateList[v]?.source_list_title ? 'none' : ''}` }} labelAlign={'left'}>
                {getFieldDecorator(`json[${v}].source_list_title`, {
                    initialValue: props.json[v]?.source_list_title,
                    rules: [
                        {
                            required: true,
                            message: '请选择稿件',
                        },
                    ],
                })(<Select showSearch onSelect={(e) => chooseSelect(e, v)} onSearch={(e)=>handleSearch(e,v)} >
                    {
                        optionData.map((d: any) => <Option key={d.id} value={JSON.stringify(d)}>{`${d.id} - 【${d.channel_name}】- ${d.list_title} `}</Option>)
                    }
                </Select>)}
            </Form.Item>
            <Form.Item label="图片" extra="支持上传jpg,jpeg,png,gif图片格式">
                {getFieldDecorator(`json[${v}].image_url`, {
                    initialValue: stateList[v]?.image_url,
                })(
                    <ImageUploader ratio={16 / 9} accept={['image/png', 'image/jpeg', 'image/jpg', 'image/gif']} />
                )}
            </Form.Item>
            <Col span={24} className='radio_box' style={{ display: 'flex' }}>
                <Form.Item label="展示标题" labelAlign={'left'} style={{ width: '60%' }}>
                    {getFieldDecorator(`json[${v}].auto_title`, {
                        initialValue: stateList[v]?.auto_title,
                        rules: [
                            {
                                required: true,
                                message: '请输入要展示的标题，最多30字',
                            },
                        ],
                    })(
                        <Radio.Group style={{ marginLeft: '13%' }} onChange={(e) => changeRadio(e, v)}>
                            <Radio value={1}>使用稿件标题</Radio>
                            <Radio value={0}>自定义设置</Radio>
                        </Radio.Group>
                    )}
                </Form.Item>
                {!!!stateList[v]?.auto_title &&
                    <Form.Item labelAlign={'left'} className='form_radioform_radioform_radio' style={{ width: '45%', display: `${!!!stateList[v]?.auto_title ? '' : 'none'}` }}>
                        {getFieldDecorator(`json[${v}].title`, {
                            initialValue: stateList[v]?.title,
                            rules: [
                                {
                                    required: true,
                                    message: '请输入要展示的标题，最多30字',
                                },
                            ],
                        })(<Input style={{ width: '100%' }} maxLength={30} placeholder="请输入要展示的标题，最多30字" />)}
                    </Form.Item>
                }

            </Col>
        </>
    )
}
