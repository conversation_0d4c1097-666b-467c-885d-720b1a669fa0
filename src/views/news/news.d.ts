import { ICommonTableList } from '@app/types';

export type TUserDeletedListFilter = {
  current?: number;
  size?: number;
  search_type?: 1 | 2 | 3 | 4;
  keyword?: string;
  begin?: string;
  end?: string;
  channel_id?: string;
  doc_type?: number;
};

export type TUserDeletedNewsRecord = {
  id: number;
  list_title: string;
  author: string;
  video_url: string;
  cover_url: string;
  duration: number;
  create_date: number;
  published_at: number;
};

export type TUserDeletedNewsResData = {
  list: ICommonTableList<TUserDeletedNewsRecord>;
};

export type TUserComplaintTag = {
  id: number;
  tag: string;
};

export type TUserComplaintListFilter = {
  current?: number;
  size?: number;
  search_type?: 1 | 2;
  keyword?: string;
  begin?: string;
  end?: string;
  tag_id?: number | '';
  channel_id?: string;
  doc_type?: number;
};

export type TUserComplaintNewsRecord = {
  metadata_id: number;
  account_nick_name: string;
  list_title: string;
  video_url: string;
  list_pics: string;
  video_duration: number;
  created_at: number;
  tag_contents: string[];
  author: string;
  published_at: number;
};

export type TUserComplaintNewsResData = {
  list: ICommonTableList<TUserComplaintNewsRecord>;
};

export type TListArticleRecommendRecord = {
  id: string;
  title: string;
  published_by: string;
  published_at: number;
  list_pic: string;
};

export type TListArticleRecommendAllData = {
  pg: ICommonTableList<TListArticleRecommendRecord>;
  position_id: number;
  position: number;
};
