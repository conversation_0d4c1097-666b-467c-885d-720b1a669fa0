import { releaseListApi as api } from '@app/api';
import { ITableProps, IBaseProps, CommonObject } from '@app/types';
import { A, Table, BaseComponent, OrderColumn, Drawer, PreviewMCN } from '@components/common';
import { connectTable as connect } from '@utils/connect';
import Form from '@components/business/commentRecommendForm';
import { getCrumb } from '@utils/utils';
import { Button, Col, Icon, message, Modal, Row, InputNumber } from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';

type State = {
  channelId: string;
  channelName: string;
  articleId: string,
  ids: string[];
  positionId: number;
  displayPos: number | undefined;
  form: {
    key: number;
    visible: boolean;
  };
  preview: {
    visible: boolean;
    skey: number;
    data: CommonObject;
  };
};

type Props = IBaseProps<ITableProps<any, any>, { id: string; name: string, articleid: string }>;

class CommentRecommendManager extends BaseComponent<
  ITableProps<any, any>,
  State,
  { id: string; name: string, articleid: string  }
> {
  constructor(props: Props) {
    super(props);
    this.state = {
      channelId: props.match.params.id,
      channelName: decodeURIComponent(props.match.params.name),
      articleId: props.match.params.articleid || '',
      ids: [],
      displayPos: 5,
      positionId: 0,
      form: {
        key: Date.now(),
        visible: false,
      },
      preview: {
        visible: false,
        skey: Date.now() + 1,
        data: {}
      },
    };
  }

  componentDidMount() {
    this.getData();
  }

  componentDidUpdate(prevProps: Props) {
    if (
      prevProps.tableList.timestamp !== this.props.tableList.timestamp &&
      Boolean(this.props.tableList.allData.pg)
    ) {
      this.setState({
        ids: this.props.tableList.records.map((v: any) => {
          return v.comment_id;
        }),
        displayPos: this.props.tableList.allData.position,
        positionId: this.props.tableList.allData.position_id,
      });
    }
  }

  getData = () => {
    const { articleId } = this.state
    const isSubject = articleId.startsWith('subject:')
    this.dispatchTable('getCommentRecommendList', 'pg', {
      channel_id: isSubject ? articleId : this.state.channelId,
      size: 100,
    });
  };

  showPosChangeModal = () => {
    let pos = this.state.displayPos;
    const posChange = (value: number | undefined) => {
      pos = value;
    };
    Modal.confirm({
      title: '修改位置序号',
      content: (
        <div>
          <InputNumber
            placeholder="请输入修改序号"
            min={1}
            onChange={posChange}
            defaultValue={pos}
          />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!pos) {
          message.error('请填写序号');
          return;
        }
        api
          .updateRecommendPosition({
            id: this.state.positionId,
            position: pos,
          })
          .then(() => {
            message.success('操作成功');
            this.getData();
            closeFunc();
          });
      },
    });
  };

  getColumns = () => {
    const { total } = this.props.tableList;
    const { articleId, channelId } = this.state
    const isSubject = articleId.startsWith('subject:')
    return [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) => (
          <OrderColumn
            pos={i + 1}
            start={1}
            end={total}
            perm={isSubject ? `type_recommend:${channelId}:update_sort` : "type_recommend:12:update_sort"}
            onUp={this.exchangeOrder.bind(this, record.id, 1)}
            onDown={this.exchangeOrder.bind(this, record.id, 0)}
          />
        ),
        width: 70,
      },
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{i + 1}</span>,
        width: 70,
      },
      {
        title: '评论内容',
        key: 'content',
        dataIndex: 'content',
        width: '25%',
      },
      {
        title: '相关新闻',
        key: 'title',
        dataIndex: 'title',
        render: (text: any, record: any) => (
          <a
            onClick={this.toMlf.bind(this, record.ref_ids, 'mlf_detail_url', record)}
            className="list-title"
          >
            {text}
          </a>
        ),
      },
      {
        title: '用户昵称',
        key: 'creator',
        dataIndex: 'creator',
        width: 150,
      },
      {
        title: '评论时间',
        key: 'published_at',
        dataIndex: 'published_at',
        render: (text: any, record: any) => (
          <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>
        ),
        width: 95,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) =>
          this.requirePerm(isSubject ? `type_recommend:${channelId}:delete` : 'type_recommend:12:delete')(
            <A onClick={this.deleteRecommend.bind(this, record.id)}>删除</A>
          ),
        width: 70,
      },
    ];
  };

  toMlf = (id: number, type: 'mlf_edit_url' | 'mlf_detail_url', record: any = {}) => {
    if (record.uuid) {
      this.preview(record);
    } else {
      api
        .toMlf(type, { id })
        .then((r: any) => {
          window.open(r.data.url);
        })
        .catch();
    }
  };

  exchangeOrder = (id: string, sortFlag: number) => {
    this.setLoading(true);
    api
      .updateCommentRecommendSort({ id, sort_flag: sortFlag })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setLoading(false);
      })
      .catch(() => {
        this.setLoading(false);
      });
  };

  deleteRecommend = (id: string) => {
    const isSubject = this.state.articleId.startsWith('subject:')
    Modal.confirm({
      title: `是否确定要将该条评论从${isSubject ? '两会弹幕' : '潮客潮语推荐位'}中删除？`,
      onOk: () => {
        this.setLoading(true);
        api
          .deleteCommentRecommend({ id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            this.setLoading(false);
          })
          .catch(() => {
            this.setLoading(false);
          });
      },
    });
  };

  createArticle = () => {
    if (this.props.tableList.total >= 15) {
      message.error('最多只能添加15条评论');
      return;
    }
    this.setState({
      form: {
        key: Date.now(),
        visible: true,
      },
    });
  };

  backToList = () => {
    // this.props.history.push(`/view/releaselist?channel_id=${this.state.channelId}`);
    this.props.history.goBack()
  };

  submitEnd = () => {
    this.getData();
    this.closeDrawer();
  };

  closeDrawer = () => {
    this.setState({
      form: {
        key: this.state.form.key,
        visible: false,
      },
    });
  };

  preview = (record: any) => {
    this.setState({
      preview: {
        visible: true,
        skey: Date.now(),
        data: record
      },
    });
  };

  closePreview = () => {
    this.setState({
      preview: { ...this.state.preview, visible: false },
    });
  };

  render() {
    const { form, ids, channelId, preview, articleId } = this.state;
    const isSubject = articleId.startsWith('subject:')
    const bread = ['签发内容管理', this.state.channelName, isSubject ? '两会弹幕' : '潮客潮语'];
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            <Button style={{ marginRight: 8 }} onClick={this.backToList}>
              <Icon type="left-circle-o" /> 已签发列表
            </Button>
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(bread)}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <Col span={16}>
              {this.requirePerm(isSubject ? `subject_recommend:${channelId}:create_comment` : `channel_recommend:${channelId}:create_comment`)(
                <Button style={{ marginRight: 8 }} onClick={this.createArticle}>
                  <Icon type="plus-circle-o" /> 添加评论
                </Button>
              )}
            </Col>
            <Col span={8} style={{ textAlign: 'right' }}>
              {isSubject ? '分组位置:': '展示位置:'}&nbsp;{this.state.displayPos}&nbsp;
              {this.requirePerm(isSubject ? `subject_recommend:${channelId}:update_position` :  `channel_recommend:${channelId}:update_position:12`)(
                <A onClick={this.showPosChangeModal}>
                  <Icon type="form" />
                </A>
              )}
            </Col>
          </Row>
          <Table
            func="getCommentRecommendList"
            pagination={false}
            index="pg"
            columns={this.getColumns()}
            filter={{ channel_id: channelId, size: 100 }}
            rowKey="id"
          />
          <Drawer
            visible={form.visible}
            skey={form.key}
            title="添加评论"
            onClose={this.closeDrawer}
            onOk={this.handleSubmitForm.bind(this, 'form')}
          >
            <Form
              channelId={isSubject ? articleId : channelId}
              disabledIds={ids}
              wrappedComponentRef={this.setFormRef.bind(this, 'form')}
              onEnd={this.submitEnd}
            />
          </Drawer>
          <PreviewMCN {...preview} onClose={this.closePreview} />
        </div>
      </>
    );
  }
}

export default withRouter(connect<any, any>()(CommentRecommendManager));
