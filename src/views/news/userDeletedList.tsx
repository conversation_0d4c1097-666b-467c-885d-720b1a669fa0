/* eslint-disable array-callback-return */
import { releaseListApi as api } from '@app/api';
import { ITableProps, IBaseProps, CommonObject } from '@app/types';
import { A, Drawer, Table, BaseComponent, PreviewMCN } from '@components/common';
import { connectTable as connect } from '@utils/connect';
import { Button, Col, DatePicker, Icon, message, Modal, Row, Select, Input } from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import { ColumnProps } from 'antd/es/table';
import { RangePickerValue } from 'antd/es/date-picker/interface';
import { resolveNewsType, searchToObject } from '@app/utils/utils';
import { TUserDeletedListFilter, TUserDeletedNewsResData, TUserDeletedNewsRecord } from './news';

type Record = TUserDeletedNewsRecord;

type State = {
  filters: TUserDeletedListFilter;
  preview: {
    visible: boolean;
    key: number;
    data: CommonObject;
  };
  currentType: TUserDeletedListFilter['search_type'];
  currentKeyword: string;
  id: string;
  name: string;
};

type Props = IBaseProps<
  ITableProps<TUserDeletedNewsRecord, TUserDeletedNewsResData>,
  { id: string; name: string }
>;

class UserDeletedList extends BaseComponent<
  ITableProps<TUserDeletedNewsRecord, TUserDeletedNewsResData>,
  State,
  { id: string; name: string }
> {
  constructor(props: Props) {
    super(props);
    const { id, name } = props.match.params
    const { doc_type } = searchToObject()
    this.state = {
      filters: {
        begin: '',
        end: '',
        search_type: 1,
        keyword: '',
        doc_type: doc_type ? parseInt(doc_type) : 0,
      },
      preview: {
        visible: false,
        key: Date.now(),
        data: {}
      },
      currentType: 1,
      currentKeyword: '',
      id,
      name,
    };
  }

  componentDidMount() {
    this.getData({ current: 1 });
  }

  getData = (overlap: TUserDeletedListFilter = {}, filters = this.getFilters()) => {
    this.dispatchTable('getUserDeletedList', 'list', { ...filters, ...overlap });
  };

  getFilters = (): TUserDeletedListFilter => {
    const { current, size } = this.props.tableList;
    let filters: TUserDeletedListFilter = { current, size, channel_id: this.state.id };
    (Object.keys(this.state.filters) as (keyof TUserDeletedListFilter)[]).map(
      (k: keyof TUserDeletedListFilter) => {
        if (this.state.filters[k]) {
          filters = { ...filters, [k]: this.state.filters[k] };
        }
      }
    );
    return filters;
  };

  getColumns = (): ColumnProps<Record>[] => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: undefined, record: Record, index: number) => <span>{getSeq(index)}</span>,
        width: 70,
      },
      {
        title: '潮新闻ID',
        key: 'id',
        dataIndex: 'id',
        width: 110,
      },
      {
        title: '标题',
        key: 'list_title',
        dataIndex: 'list_title',
        render: (text: string, record: Record) => (
          <a onClick={this.preview.bind(this, record)}>{text || '-'}</a>
        ),
      },
      {
        title: '类型',
        dataIndex: 'doc_type',
        render: (text: number) => resolveNewsType(text, 1),
        width: 120,
      },
      {
        title: '作者',
        key: 'author',
        dataIndex: 'author',
        width: 180,
      },
      {
        title: '小潮号',
        key: 'chao_id',
        dataIndex: 'chao_id',
        width: 120
      },
      {
        title: '发布时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: number) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 200,
      },
      // {
      //   title: '操作',
      //   key: 'op',
      //   render: (text: undefined, record: Record) =>
      //     this.requirePerm('user_video:delete')(
      //       <A onClick={this.deleteRecord.bind(this, record)}>删除</A>,
      //     ),
      //   width: 70,
      // },
    ];
  };

  preview = (record: any) => {
    this.setState({
      preview: {
        visible: true,
        key: Date.now(),
        data: record
      },
    });
  };

  deleteRecord = (record: Record) => {
    Modal.confirm({
      title: '是否确认删除？',
      content: '删除后，将清空该条数据',
      onOk: () => {
        this.setLoading(true);
        api
          .deleteUserDeleted({ id: record.id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            this.setLoading(false);
          })
          .catch(() => {
            this.setLoading(false);
          });
      },
    });
  };

  handleRangePickerChange = (dates: RangePickerValue) => {
    if (dates.length === 0) {
      this.setState(
        {
          filters: { ...this.state.filters, begin: '', end: '' },
        },
        () => this.getData({ current: 1 })
      );
    } else {
      this.setState(
        {
          filters: {
            ...this.state.filters,
            begin: dates[0] ? dates[0].format('YYYY-MM-DD') : '',
            end: dates[1] ? dates[1].format('YYYY-MM-DD') : '',
          },
        },
        () => this.getData({ current: 1 })
      );
    }
  };

  typeChange = (value: TUserDeletedListFilter['search_type']) => {
    this.setState({
      currentType: value,
    });
  };

  handleDocTypeChange = (value: number) => {
    this.setState(
      {
        filters: { ...this.state.filters, doc_type: value },
      },
      () => this.getData()
    );
  }

  keywordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    this.setState({
      currentKeyword: e.target.value,
    });
  };

  doSearch = () => {
    this.setState(
      {
        filters: {
          ...this.state.filters,
          search_type: this.state.currentType,
          keyword: this.state.currentKeyword,
        },
      },
      () => this.getData({ current: 1 })
    );
  };

  closePreview = () => {
    this.setState({
      preview: { ...this.state.preview, visible: false },
    });
  };

  backToList = () => {
    this.props.history.go(-1)
    // if (this.state.name === '潮鸣号' || this.state.name === '潮新闻号') {
    //   this.changeRoute(`/view/tmh?channel_id=${this.state.id}`);
    // } else {
    //   this.changeRoute(`/view/chaoke?channel_id=${this.state.id}`);
    // }
  };

  render() {
    const { filters, currentType, currentKeyword, preview } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            <Button onClick={this.backToList}>
              <Icon type="left-circle" /> 返回
            </Button>
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {this.getCrumb(['用户内容管理', this.state.name, '用户发起的删除内容'])}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <Col span={12}>
              <DatePicker.RangePicker
                format="YYYY-MM-DD"
                onChange={this.handleRangePickerChange}
                value={filters.begin ? [moment(filters.begin), moment(filters.end)] : []}
              />
              <Select
                value={filters.doc_type}
                onChange={this.handleDocTypeChange}
                style={{ width: 120, marginLeft: 8 }}
              >
                <Select.Option value={0}>全部类型</Select.Option>
                <Select.Option value={12}>短图文</Select.Option>
                <Select.Option value={10}>视频</Select.Option>
                <Select.Option value={13}>长文章</Select.Option>
              </Select>
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <Select
                value={currentType}
                onChange={this.typeChange}
                style={{ width: 120, marginRight: 8 }}
              >
                <Select.Option value={1}>标题</Select.Option>
                <Select.Option value={2}>作者</Select.Option>
                <Select.Option value={3}>用户手机号</Select.Option>
                <Select.Option value={4}>用户ID</Select.Option>
                <Select.Option value={5}>小潮号</Select.Option>
              </Select>
              <Input
                value={currentKeyword}
                onChange={this.keywordChange}
                style={{ width: 180, marginRight: 8 }}
                onKeyPress={this.handleKey}
                placeholder="请输入搜索内容"
              />
              <Button onClick={this.doSearch}>
                <Icon type="search" />
                搜索
              </Button>
            </Col>
          </Row>
          <Table
            filter={this.getFilters()}
            index="list"
            func="getUserDeletedList"
            columns={this.getColumns()}
            rowKey="id"
            pagination={true}
          />
          <PreviewMCN skey={preview.key} {...preview} onClose={this.closePreview} />
        </div>
      </>
    );
  }
}

export default withRouter(
  connect<TUserDeletedNewsRecord, TUserDeletedNewsResData>()(UserDeletedList)
);
