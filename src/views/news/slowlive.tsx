import { setConfig } from '@app/action/config';
import { getTableList } from '@app/action/tableList';
import { releaseListApi } from '@app/api';
import { CommonObject, IResReleaseListAllData, IResReleaseListRecordsData } from '@app/types';
import { DOC_TYPE } from '@app/utils/constants';
import { A, Table, Drawer, ImageUploader } from '@components/common';
import { connectTable as connect } from '@utils/connect';
import ReactClipboard from 'react-clipboardjs-copy';
import SuperLuckyBagForm from '@app/components/business/SuperLuckyBagForm';
import AnswerGiftForm from '@app/components/business/AnswerGiftForm';
import LiveForm from '@app/components/business/LiveForm';
import FloatingAdvertisementForm from '@app/components/business/FloatingAdvertisementForm';
import LiveType from '@app/components/business/liveTypeForm';
import PushChannelRecordModal from '@app/components/business/PushChannelRecordModal';
import PushChannelDrawer from '@app/components/business/PushChannelDrawer';
import SlowLiveDrawer from '@app/components/business/SlowLiveDrawer';
import LoopArticleDrawer from '@app/components/business/LoopArticleDrawer';
import DataStore from '@app/utils/DataStore';
import ChannelListOperateLogModal from '@views/news/channelListOperateLogModal'

import {
  getCrumb,
  getRecommendHideMenuList,
  getSupportRecommend,
  recommendTypeText,
  requirePerm,
  resolveNewsType,
  resolveTopPushed,
  searchToObject,
} from '@utils/utils';
import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Dropdown,
  Icon,
  InputNumber,
  Menu,
  message,
  Modal,
  Row,
  Select,
  Tag,
  Switch,
  Popover,
  TreeSelect,
  Tooltip
} from 'antd';
import Radio from 'antd/es/radio';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import NewsBaseComponent, { INewsBaseProps } from './baseComponent';

import './index.scss';
import ContentRecommendDrawer from '@app/components/business/recommend/ContentRecommendDrawer';
import { PermA } from '@app/components/permItems';
import SortableColumn from '@app/components/common/sortableColumn';

type Props = INewsBaseProps<{}, IResReleaseListRecordsData, IResReleaseListAllData>;

type LinkForm = {
  visible: boolean;
  key: number;
  id: number;
  recommend_url: string;
  recommend_text: string;
};

type LiveMoveForm = {
  show: boolean,
  key: number;
  record: object
}

function IdCopy(props: any) {
  return (
    <Tooltip title="复制">
      <ReactClipboard
        action="copy"
        text={String(props.id)}
        onSuccess={() => message.success('id复制成功')}
        onError={() => message.error('id复制失败')}>
        <span style={{ cursor: 'pointer' }}><Icon type="copy" /></span>
      </ReactClipboard>
    </Tooltip>
  )
}

class ReleaseList extends NewsBaseComponent<
  {},
  IResReleaseListRecordsData,
  IResReleaseListAllData,
  {
    linkForm: LinkForm,
    SlowLiveDrawerForm: LiveMoveForm
  }
> {
  formRef: any; //超级福袋
  formRef1: any; //答题有礼
  formRef2: any; //直播广告
  formRef3: any; //浮窗广告
  formRef4: any; //直播类型

  constructor(props: Props) {
    super(props);
    const initialFilter = {
      current: 1,
      size: DataStore.pageSize,
      search_type: '3',
      doc_type: '',
      keyword: '',
      begin: false,
      end: false,
      channel_id: this.props.match.params.id,
      recommend_enabled: '2',
      top_pushed: '',
      original: '',
      to_channel_ids: '',
      slow_live: '1',
      sort_by: 0,
      sort_asc: -1,
    };
    this.state = {
      filter: initialFilter,
      pageInput: initialFilter,
      channel: {
        category_id: 1,
        focus_carousel: false,
        id: '1',
        mode: 0,
        name: '',
        tou_tiao: false,
        focus_position: 0,
      } as any,
      operateLog: {
        visible: false,
        scId: 0,
        mlfId: 0,
        articleTitle: '',
        logs: [],
        key: Date.now(),
      } as any,
      newOperateLog: {
        visible: false,
        key: Date.now(),
        record: {},
      },
      LiveTypeForm: {
        visible: false, //内容连接弹窗是否显示
        key: Date.now() + 4, //唯一值
        type: null,
        id: null,
      },
      PVForm: {
        visible: false,
        key: Date.now() + 2,
        article_id: 0,
        type: 0,
        show_pv: 0,
        base_pv: 0,
        min_factor: 0,
        max_factor: 0,
      },
      showDragSorter: false,
      fixedCount: 0,
      total: 0,
      toChannelList: [],
      toChannelCheckedList: [],
      loopArticleDrawerShow: false,
      linkForm: {
        visible: false,
        key: Date.now() + 3,
        id: 0,
        recommend_text: '',
        recommend_url: '',
      },
      LuckBagForm: {
        visible: false, //内容连接弹窗是否显示
        key: Date.now() + 4, //唯一值
        channel_id: '', //channel_id频道id
        article_id: '', //稿件id
        luckBagList: [], //列表
      },
      AnswerRewardForm: {
        visible: false, //内容连接弹窗是否显示
        key: Date.now() + 5, //唯一值
        channel_id: '', //channel_id频道id
        article_id: '', //稿件id
        answerList: [], //列表
      },
      LivePlayForm: {
        visible: false, //内容连接弹窗是否显示
        key: Date.now() + 6, //唯一值
        channel_id: '', //channel_id频道id
        article_id: '', //稿件id
        id: '',
        content: '',
        status: 1,
      },
      FloatingForm: {
        visible: false, //内容连接弹窗是否显示
        key: Date.now() + 7, //唯一值
        channel_id: '', //channel_id频道id
        article_id: '', //稿件id
        id: '',
        shrink_img_url: '',
        expand_img_url: '',
        url: '',
        status: 1,
      },
      PushChannelRecordForm: {
        show: false,
        key: Date.now() + 8,
        record: {}
      },
      PushChannelDrawerForm: {
        show: false,
        key: Date.now() + 9,
        record: {}
      },
      SlowLiveDrawerForm: {
        show: false,
        key: Date.now() + 10,
        record: {}
      },
      ContentRecommendDrawerForm: {
        show: false,
        key: Date.now() + 10,
        record: null
      },
    } as any;
    this.props.dispatch(
      setConfig({ selectKeys: [`/view/live?channel_id=${this.props.match.params.id}`] })
    );
  }

  componentDidMount() {
    this.getData();
    this.getChannelList();
    this.props.dispatch(setConfig({ openKeys: this.props.openKeys }));
    this.props.dispatch(
      setConfig({ selectKeys: [`/view/live?channel_id=${this.props.match.params.id}`] })
    );
  }

  componentDidUpdate(prevProps: Props) {
    const channel_id = this.props.match.params.id
    if (prevProps.tableList.timestamp === this.props.tableList.timestamp &&
      channel_id === this.state.filter.channel_id &&
      this.props.location === prevProps.location
    ) {
      return;
    }
    if (channel_id !== this.state.filter.channel_id ||
      this.props.location !== prevProps.location
    ) {
      const initialFilter = {
        current: 1,
        size: DataStore.pageSize,
        search_type: '3',
        doc_type: '',
        keyword: '',
        begin: false,
        end: false,
        channel_id,
        recommend_enabled: '2',
        top_pushed: '',
        original: '',
        to_channel_ids: '',
        slow_live: '1',
        sort_by: 0,
        sort_asc: -1
      };
      this.props.dispatch(
        setConfig({ selectKeys: [`/view/live?channel_id=${channel_id}`] })
      );
      this.setState(
        {
          filter: initialFilter,
          pageInput: initialFilter,
          toChannelCheckedList: [],
          toChannelList: this.mapToChannelListTreeData(this.state.originToChannelList, channel_id)
        },
        () => this.getData()
      );
    }
    if (
      prevProps.tableList.timestamp !== this.props.tableList.timestamp &&
      Boolean(this.props.tableList.allData.release_list)
    ) {
      const { channel, release_list } = this.props.tableList.allData;
      this.setState({
        channel,
        filter: { ...this.state.filter, current: release_list.current, size: release_list.size },
        pageInput: {
          ...this.state.pageInput,
          current: release_list.current,
          size: release_list.size,
        },
        fixedCount: release_list.fixed_count || 0,
        total: release_list.total,
      });
    }
  }

  handleRangePickerChange = (dates: any) => {
    if (dates.length === 0) {
      this.setState(
        {
          filter: { ...this.state.filter, begin: false, end: false, sort_asc: -1, sort_by: 0 },
          pageInput: { ...this.state.pageInput, begin: false, end: false, sort_asc: -1, sort_by: 0 },
        },
        () => this.getData()
      );
    } else {
      if (this.state.filter.sort_by > 0 &&
        dates[1].diff(dates[0], 'days') > 31) {
        switch (this.state.filter.sort_by) {
          case 1:
            message.error('阅读数排序选择日期，请勿超过31天')
            break
          case 2:
            message.error('发布时间排序选择日期，请勿超过31天')
            break
          case 3:
            message.error('全网传播数排序选择日期，请勿超过31天')
            break
          default:
            break;
        }
        return
      }
      this.setState(
        {
          filter: {
            ...this.state.filter,
            begin: dates[0].format('YYYY-MM-DD'),
            end: dates[1].format('YYYY-MM-DD'),
          },
          pageInput: {
            ...this.state.pageInput,
            begin: dates[0].format('YYYY-MM-DD'),
            end: dates[1].format('YYYY-MM-DD'),
          },
        },
        () => this.getData()
      );
    }
  };

  // 修改阅读数排序
  changeReadCountOrder = (sort_asc: number, sort_by: number) => {
    let { filter: { sort_by: old_sort_by, begin, end }, openDatePickerPanel } = this.state
    if (sort_asc === -1) {
      // 当前选中其他排序的时候 另外一个的重置不生效
      if (old_sort_by != sort_by) { return }
      openDatePickerPanel = false
      begin = false
      end = false
      sort_by = 0
      sort_asc = -1
    } else if (old_sort_by != sort_by) {
      // 进入排序模式，显示时间框，默认选中今天
      openDatePickerPanel = true
      const today = moment()
      begin = today.format('YYYY-MM-DD')
      end = today.format('YYYY-MM-DD')
    }
    this.setState(
      {
        filter: { ...this.state.filter, sort_asc, begin, end, sort_by },
        pageInput: { ...this.state.pageInput, sort_asc, begin, end, sort_by },
        openDatePickerPanel
      },
      () => this.getData()
    );
  }

  handleSlowLiveChange = (e: any) => {
    const slow_live = e.target.value
    const isFilterRecommend = this.state.filter.doc_type === '-1'
    const params: any = { slow_live }
    if (slow_live === '1') {
      params.current = 1
      params.recommend_enabled = '2'
      if (isFilterRecommend) {
        params.doc_type = ''
      }
    }
    const state: any = {
      filter: { ...this.state.filter, ...params },
      pageInput: { ...this.state.pageInput, ...params },
    }
    if (slow_live === '1') {
      state.showDragSorter = false
    }
    this.setState(
      state,
      () => this.getData()
    );
  }

  handleDocTypeChange = (value: any) => {
    this.setState(
      {
        filter: { ...this.state.filter, doc_type: value },
        pageInput: { ...this.state.pageInput, doc_type: value },
      },
      () => this.getData()
    );
  };

  exchangeOrder = (record: any, seq: number, offset: number) => {
    if (record.fixed_number > 0 && seq - offset > 100) {
      message.error('固定位置必须为1~100');
      return;
    }
    const id = record.id
    const isSlowLive = this.state.filter.slow_live === '1'
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi[isSlowLive ? 'slowLiveExchangeOrder' : 'releaseExchangeOrder']({ id, offset, current: seq })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };
  changeOrderInline = (record: any) => {
    if (this.state.fixedCount >= 40) {
      message.error('最多只能固定40条稿件或推荐位');
      return;
    }
    const body: any = {
      position: record.seq,
      id:
        this.state.channel.code === 'zhuanti' && record.related_article_id
          ? record.related_article_id
          : record.id,
      fixed: true,
    };
    this.changeOrderRequest(body, null);
  };
  changeOrderRequest = (body: any, callbacks: any | null) => {
    const WAIT_TIME = 1000;
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .releaseChangeOrder(body)
      .then(() => {
        message.success('操作成功');
        setTimeout(() => {
          this.getData();
          this.props.dispatch(setConfig({ loading: false }));
        }, WAIT_TIME);
        if (typeof callbacks == 'function') {
          callbacks();
        }
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };
  changeOrder = (record: any) => {
    let position = record.seq;
    let sticky = record.fixed_number > 0;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      position = v;
    };
    const stickyChange = (e: any) => {
      sticky = e.target.checked;
    };
    const searchStatus =
      this.state.filter.keyword ||
      this.state.filter.doc_type ||
      this.state.filter.begin ||
      this.state.filter.top_pushed ||
      this.state.filter.original ||
      this.state.filter.to_channel_ids ||
      this.state.filter.recommend_enabled === '1' ||
      this.state.filter.sort_by > 0;
    Modal.confirm({
      title: <p>排序：《{record.list_title}》</p>,
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber
            min={1}
            max={searchStatus ? 999999999999 : this.state.total + this.state.fixedCount}
            defaultValue={position}
            onChange={positionChange}
          />
          <br key={1} />
          <Checkbox
            disabled={!record.visible}
            style={{ marginTop: 8 }}
            defaultChecked={sticky}
            onChange={stickyChange}
            key={2}
          >
            固定位置
          </Checkbox>
          <p style={{ color: '#ccc' }} key={3}>
            此选项只对1~100位置有效
            <br />
            最多只能固定40条稿件或推荐位
          </p>
        </div>
      ),
      onOk: (closeFunc: Function) => {
        const MAX_STICKY_POSITION = 100;
        if (!position) {
          message.error('请填写位置');
          return;
        }
        if (sticky && position > MAX_STICKY_POSITION) {
          message.error('固定位置必须为1~100');
          return;
        }
        if (sticky && this.state.fixedCount >= 40) {
          message.error('最多只能固定40条稿件或推荐位');
          return;
        }
        if (this.state.total + this.state.fixedCount < position && !searchStatus) {
          message.error('稿件位置不能大于稿件总数');
          return;
        }
        const data: any = { position, id: record.id, fixed: sticky }
        this.props.dispatch(setConfig({ loading: true }));
        releaseListApi
          .releaseChangeOrder(data)
          .then(() => {
            message.success('操作成功');
            setTimeout(() => {
              this.getData();
              this.props.dispatch(setConfig({ loading: false }));
            }, WAIT_TIME);
            closeFunc();
          })
          .catch(() => {
            this.props.dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  cancelFix = (id: string | number, title: string) => {
    const WAIT_TIME = 1000;
    Modal.confirm({
      title: <p>确认取消固定《{title}》</p>,
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        releaseListApi
          .releaseCancelFix({ id })
          .then(() => {
            message.success('操作成功');
            setTimeout(() => {
              this.getData();
              this.props.dispatch(setConfig({ loading: false }));
            }, WAIT_TIME);
          })
          .catch(() => {
            this.props.dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  changeVisible = (id: string | number, visible: boolean) => {
    const WAIT_TIME = 1000;
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .releaseChangeVisible({ id, visible: visible ? 0 : 1 })
      .then(() => {
        message.success('操作成功');
        setTimeout(() => {
          this.getData();
          this.props.dispatch(setConfig({ loading: false }));
        }, WAIT_TIME);
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };

  toggleSlowLiveState = (record: any) => {
    const WAIT_TIME = 1000;
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .toggleSlowLive({ id: record.id, slow_live: record.slow_live ? 0 : 1 })
      .then(() => {
        message.success('操作成功');
        setTimeout(() => {
          this.getData();
          this.props.dispatch(setConfig({ loading: false }));
        }, WAIT_TIME);
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };

  showOperateLog = (record: any) => {
    if (
      this.props.session.permissions.indexOf(
        `channel_article:${this.state.channel.id}:view_log`
      ) === -1
    ) {
      return;
    }
    this.setState({
      newOperateLog: {
        visible: true,
        key: Date.now(),
        record: record
      }
    })
  }

  getColumns = () => {
    const channelId = this.state.channel.id;
    const recommendEnabled = this.state.filter.recommend_enabled === '1'
    const isSlowLive = this.state.filter.slow_live === '1'
    const { permissions } = this.props.session;
    const recommendCanEdit = permissions.indexOf(`content_recommend:${channelId}:update`) >= 0;
    const searchStatus =
      this.state.filter.keyword ||
      this.state.filter.doc_type ||
      this.state.filter.begin ||
      this.state.filter.top_pushed ||
      this.state.filter.original ||
      this.state.filter.to_channel_ids ||
      recommendEnabled ||
      this.state.filter.sort_by > 0;

    const getDropDown = (record: any) => {
      const menu = (
        <Menu>
          {record.doc_type > 0 ? requirePerm(
            this,
            `channel_article:${channelId}:edit`
          )(<Menu.Item onClick={this.handleArticleClick.bind(this, record, 'mlf_edit_url', channelId)}>编辑</Menu.Item>) :
            <Menu.Item disabled={!recommendCanEdit} onClick={this.handleArticleClick.bind(this, record, 'mlf_edit_url', channelId)}>编辑</Menu.Item>}

          {record.doc_type > 0 && <Menu.Item
            onClick={this.changeRoute.bind(
              this,
              `/view/pushNotify?channel_id=${record.source_channel_id || channelId}&tmId=${record.original_id || record.id}`
            )}
          >
            快捷推送
          </Menu.Item>}

          {!isSlowLive && requirePerm(
            this,
            `channel_article:${channelId}:sort`
          )(
            // record.fixed_number > 0 ? (
            //   <Menu.Item onClick={this.cancelFix.bind(this, record.id, record.list_title)}>
            //     取消固定
            //   </Menu.Item>
            // ) : 
            (
              <Menu.Item disabled={recommendEnabled || !record.visible} onClick={this.changeOrder.bind(this, record)}>
                排序
              </Menu.Item>
            )
          )}
          {record.doc_type > 0 &&
            requirePerm(
              this,
              `channel_article:${channelId}:push_top`
            )(
              <Menu.Item
                disabled={Boolean(record.source_channel_name)}
                // onClick={this.pushTop.bind(this, record.id, record.list_title)}
                onClick={this.togglePushChannelDrawerState.bind(this, true, record)}
              >
                推至多频道
              </Menu.Item>
            )}
          {requirePerm(
            this,
            `channel_article:${channelId}:display`
          )(
            <Menu.Item
              disabled={record.fixed_number > 0}
              onClick={this.changeVisible.bind(this, record.id, record.visible)}
            >
              {record.visible === true ? '隐藏' : '取消隐藏'}
            </Menu.Item>
          )}
          {record.doc_type > 0 && requirePerm(
            this,
            `comment:view:${channelId}`
          )(
            <Menu.Item
              disabled={record.doc_type === DOC_TYPE.SUBJECT.code}
              onClick={this.toCommentSystem.bind(
                this,
                record.original_id ? record.original_id : record.id
              )}
            >
              评论运营
            </Menu.Item>
          )}
          {record.doc_type > 0 && requirePerm(
            this,
            `channel_article:${channelId}:cancel_release`
          )(
            <Menu.Item onClick={this.revokeNews.bind(this, record.id, record.list_title, record)}>
              取消签发
            </Menu.Item>
          )}
          {record.doc_type > 0 &&
            <Menu.Item disabled={record.url === ''}>
              <a href={record.url} target='_blank'>查看链接</a>
            </Menu.Item>}
          {record.doc_type > 0 &&
            <Menu.Item disabled={record.url === ''}>
              {record.url === '' ? (
                '复制分享链接'
              ) : (
                <ReactClipboard
                  action="copy"
                  text={record.url}
                  onSuccess={this.copySuccess}
                  onError={this.copyFail}
                >
                  <a>复制分享链接</a>
                </ReactClipboard>
              )}
            </Menu.Item>
          }
          {record.doc_type > 0 && requirePerm(
            this,
            'article_metadata:change_base_pv'
          )(<Menu.Item onClick={() => this.getPVDetail(record.id, 0)}>观看人数配置</Menu.Item>)}
          {/* {requirePerm(
            this,
            'channel_article:update_live_info'
          )(<Menu.Item onClick={() => this.showLinkEdit(record)}>购物链接配置</Menu.Item>)} */}
          {record.doc_type === 8 && !record.url.includes('isVertical=1') ? (
            <Menu.Item onClick={() => this.showLiveType(record)}>直播间管理</Menu.Item>
          ) : (
            ''
          )}
          {record.doc_type === 8
            ? requirePerm(
              this,
              `channel_article:${channelId}:live_market:${record.doc_type}:1`
            )(<Menu.Item onClick={() => this.showSuperLuckyBag(record)}>超级福袋</Menu.Item>)
            : ''}
          {record.doc_type == 8
            ? requirePerm(
              this,
              `channel_article:${channelId}:live_market:${record.doc_type}:2`
            )(<Menu.Item onClick={() => this.showAnswerGift(record)}>答题有礼</Menu.Item>)
            : ''}
          {record.doc_type == 8
            ? requirePerm(
              this,
              `channel_article:${channelId}:live_propaganda:${record.doc_type}:1`
            )(<Menu.Item onClick={() => this.showLivePlay(record)}>直播广播</Menu.Item>)
            : ''}
          {record.doc_type == 8
            ? requirePerm(
              this,
              `channel_article:${channelId}:live_propaganda:${record.doc_type}:2`
            )(
              <Menu.Item onClick={() => this.showFloatingAdvertisement(record)}>
                浮窗广告位
              </Menu.Item>
            )
            : ''}
          {record.doc_type > 0 && requirePerm(
            this,
            `channel_article:${channelId}:update_proposal_hidden`
          )(
            <Menu.Item onClick={this.toggleRecommend.bind(this, record.id, record.proposal_hidden)}>
              {record.proposal_hidden === 0 ? '取消推荐' : '设置推荐'}
            </Menu.Item>
          )}
          {/* {record.doc_type > 0 && requirePerm(
            this,
            `channel_article:${channelId}:rss`
          )(<Menu.Item onClick={this.withoutRevokeNews.bind(this, record)}>外部撤稿</Menu.Item>)} */}

          {record.doc_type > 0 && requirePerm(this,
            `channel_article:${channelId}:set_slow_live`
          )(<Menu.Item onClick={this.toggleSlowLiveState.bind(this, record)}>{record.slow_live ? '取消设慢直播' : '设为慢直播'}</Menu.Item>)}

          {isSlowLive && requirePerm(this,
            `channel_article:${channelId}:config_slow_live`
          )(<Menu.Item onClick={this.configSlowLive.bind(this, true, record)}>慢直播设置</Menu.Item>)}

          {record.doc_type < 0 &&
            requirePerm(this, `channel_article:${channelId}:cancel_release`)(
              <Menu.Item onClick={this.normalRevokeNews.bind(this, record.id, record.list_title, true, record)}>
                删除
              </Menu.Item>)}
        </Menu>
      );
      return (
        <Dropdown overlay={menu} overlayClassName={'live_drop_down'}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };

    const { sort_by } = this.state.pageInput
    const { total } = this.props.tableList;

    const column = [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any) => (
          <span>
            {requirePerm(
              this,
              `channel_article:${channelId}:sort`
            )(
              <A
                disabled={record.seq === 1 ||
                  // record.fixed_number > 0 || 
                  searchStatus}
                className="sort-up"
                onClick={this.exchangeOrder.bind(this, record, record.seq, 1)}
              >
                <Icon type="up-circle" theme="filled" />
              </A>
            )}{' '}
            {requirePerm(
              this,
              `channel_article:${channelId}:sort`
            )(
              <A
                disabled={
                  record.seq === total + this.state.fixedCount ||
                  // record.fixed_number > 0 ||
                  searchStatus
                }
                className="sort-down"
                onClick={this.exchangeOrder.bind(this, record, record.seq, -1)}
              >
                <Icon type="down-circle" theme="filled" />
              </A>
            )}
          </span>
        ),
        width: 70,
      },
      {
        title: '序号',
        key: 'seq',
        dataIndex: 'seq',
        width: 70,
        render: (text: any, record: any) => {
          const max = 101;
          const allowPin = record.seq < max;
          return (
            <>
              <span style={{ marginRight: 8 }}>{text}</span>
              {
                !isSlowLive && <>
                  {allowPin && record.fixed_number > 0 && (
                    <PermA
                      perm={`channel_article:${channelId}:sort`}
                      disabled={this.state.filter.recommend_enabled === '1'}
                      onClick={() =>
                        this.cancelFix(
                          this.state.channel.code !== 'zhuanti' ? record.id : record.related_article_id,
                          record.list_title
                        )
                      }
                    >
                      <Icon type="pushpin" theme="filled" />
                    </PermA>
                  )}
                  {allowPin && record.visible && record.fixed_number == 0 && (
                    <PermA
                      perm={`channel_article:${channelId}:sort`}
                      disabled={this.state.filter.recommend_enabled === '1'}
                      onClick={() => this.changeOrderInline(record)}
                    >
                      <Icon type="pushpin" />
                    </PermA>
                  )}
                </>
              }
            </>
          );
        },
      },
      {
        title: '基本信息',
        key: 'id',
        dataIndex: 'id',
        width: 160,
        render: (text: any, record: any) => (
          <div>
            潮新闻ID:{record.id} <IdCopy id={record.id}></IdCopy> <br />
            {record.doc_type != -1 && (<>媒立方ID:{record.doc_type === -1 ? '' : record.metadata_id} <IdCopy id={record.metadata_id}></IdCopy><br /></>)}
            稿件类型:{resolveNewsType(record.doc_type)}
          </div>
        ),
      },
      // {
      //   title: '潮新闻ID',
      //   key: 'id',
      //   dataIndex: 'id',
      //   width: 100,
      // },
      // {
      //   title: '媒立方ID',
      //   key: 'metadata_id',
      //   dataIndex: 'metadata_id',
      //   width: 100,
      //   render: (text: any, record: any) => (
      //     <span>{record.doc_type === -1 ? '' : text}</span>
      //   ),
      // },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
        render: (text: any, record: any) => (
          <div style={{ minWidth: '150px', maxWidth: '400px' }}>
            {record.original == 1 && (
              <Tag color="#1890ff">原创</Tag>
            )}
            {Boolean(record.source_channel_name) && (
              <Tag color="#f1797a">{record.source_channel_name}</Tag>
            )}
            {record.doc_type < 0 && <Tag color="#e99d42">{recommendTypeText(record.ref_type)}</Tag>}
            <a
              style={{ pointerEvents: record.doc_type < 0 && !recommendCanEdit ? 'none' : 'auto' }}
              onClick={this.handleArticleClick.bind(this, record, 'mlf_detail_url')}
              className={`list-title ${record.visible ? '' : 'hide-title'} ${record.fixed_number > 0 ? 'fixed-title' : ''
                }`}
            >
              {text}
              {record.visible ? '' : '（隐藏）'}
              {record.fixed_number > 0 ? '（固定）' : ''}
            </a>
            {Boolean(record.source_channel_name) && <Popover content={this.sourceChannelInfo(record)} >
              <img style={{ marginLeft: '8px' }} src='/assets/sourceChannel.png' />
            </Popover>}
          </div>
        ),
      },
      {
        title: '直播状态',
        key: 'live_status',
        dataIndex: 'live_status',
        width: 90,
        render: (text: any, record: any) => {
          return <span>{["回放", "直播中", "预告"][text]}</span>
        }
      },
      // {
      //   title: (
      //     <SortableColumn title="全网传播数" sort_by={sort_by} currentSortBy={3} pointerEvents={!this.state.filter.keyword} onChange={this.changeReadCountOrder} />
      //   ),
      //   key: 'propagation_read_count',
      //   dataIndex: 'propagation_read_count',
      //   width: 120,
      //   align: 'center',
      //   render: (text: any, record: any) => (
      //     <span>{record.doc_type === -1 ? '' : text}</span>
      //   ),
      // },
      {
        title: (
          <SortableColumn title="阅读数" sort_by={sort_by} currentSortBy={1} pointerEvents={!this.state.filter.keyword} onChange={this.changeReadCountOrder} />
        ),
        key: 'fake_count',
        dataIndex: 'fake_count',
        width: 120,
        align: 'center',
        render: (text: any, record: any) => (
          <span>{record.doc_type === -1 ? '' : text}</span>
        ),
      },
      // {
      //   title: this.state.channel.tou_tiao ? '' : '头条状态',
      //   key: 'top_pushed',
      //   dataIndex: 'top_pushed',
      //   render: (text: any) => (
      //     <span>{this.state.channel.tou_tiao ? '' : resolveTopPushed(text)}</span>
      //   ),
      //   width: this.state.channel.tou_tiao ? 0 : 80,
      // },
      // {
      //   title: '类型',
      //   key: 'type',
      //   dataIndex: 'doc_type',
      //   render: (text: any) => <span>{resolveNewsType(text)}</span>,
      //   width: 70,
      // },
      {
        title: '推至频道',
        key: '',
        dataIndex: '',
        render: (text: any, record: any) => {
          return <div style={{
            width: 90,
            overflow: 'hidden',
            textAlign: 'left'
          }}>
            {record.to_article_list?.map((item: any) => {
              return <div className='line-max'>{`${item.channel_name}${item.pushed == 2 ? '已用' : '未用'}`}</div>
            })}
            {record.to_channel_count > 0 && <a onClick={this.togglePushChannelRecordModalState.bind(this, true, record)}>查看详情</a>}
          </div>
        },
        width: 90,
        // align: 'center',
      },
      {
        title: '城市',
        key: 'city',
        dataIndex: 'city',
        width: 70,
      },
      {
        title: '发稿人',
        key: 'creator',
        dataIndex: 'creator',
        width: 90,
      },
      {
        title: (<SortableColumn title="发布时间" sort_by={sort_by} currentSortBy={2} pointerEvents={!this.state.filter.keyword} onChange={this.changeReadCountOrder} />),
        key: 'published_at',
        dataIndex: 'published_at',
        render: (text: any, record: any) => (
          <span
            style={{ cursor: 'pointer' }}
            onClick={this.showOperateLog.bind(this, record)}
          >
            {moment(text).format('YYYY-MM-DD HH:mm:ss')}
          </span>
        ),
        width: 95,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => getDropDown(record),
        width: 80,
        // fixed: 'right',
      },
    ];
    // 根据阅读PV权限过滤菜单
    const filterTitles: any = []
    const filterReading = permissions.indexOf('pv:show') === -1;
    if (filterReading) {
      filterTitles.push('fake_count') // 阅读数
      filterTitles.push('propagation_read_count') // 全网传播数
    }
    if (!isSlowLive) {
      filterTitles.push('city') // 城市
    }
    return column.filter(v => !filterTitles.includes(v.key))
  };

  getFilter = (filter = this.state.filter) => {
    const result: CommonObject = {};
    const isSlowLive = filter.slow_live === '1'
    Object.keys(filter).forEach((k: string) => {
      if (filter[k]) {
        if (k === 'recommend_enabled') {
          if (!isSlowLive) {
            result.recommend_enabled = filter[k] === '1';
          }
        } else {
          result[k] = filter[k];
        }
      }
    });
    if (!result["sort_by"]) {
      delete result['sort_asc']
    }
    return result;
  };

  handleTopPushedChange = (value: any) => {
    this.setState(
      {
        filter: {
          ...this.state.filter,
          top_pushed: value,
        },
        pageInput: { ...this.state.pageInput, top_pushed: value },
      },
      () => this.getData()
    );
  };

  handleOriginalChange = (value: any) => {
    this.setState(
      {
        filter: {
          ...this.state.filter,
          original: value,
        },
        pageInput: { ...this.state.pageInput, original: value },
      },
      () => this.getData()
    );
  };

  handleToChannelChange = (value: any) => {
    if (value.length > 5) {
      message.error('最多可选择5个')
      return
    }
    const to_channel_ids = value.join(',')
    this.setState(
      {
        toChannelCheckedList: value,
        filter: {
          ...this.state.filter,
          to_channel_ids,
          keyword: ''
        },
        pageInput: { ...this.state.pageInput, to_channel_ids, keyword: '' },
      },
      () => this.getData()
    );
  };

  getData = (filter: CommonObject = this.getFilter()) => {
    console.log('filter', filter)
    this.props.dispatch(getTableList('getReleaseList', 'release_list', filter));
  };

  handleRecommendChange = (value: any) => {
    this.setState(
      {
        filter: {
          ...this.state.filter,
          recommend_enabled: value,
        },
        pageInput: { ...this.state.pageInput, recommend_enabled: value },
      },
      () => this.getData()
    );
  };

  showLinkEdit = (record: any) => {
    releaseListApi
      .getLiveInfo({
        id: record.id,
      })
      .then((res: any) => {
        this.setState({
          linkForm: {
            visible: true,
            key: Date.now(),
            recommend_text: res.data ? res.data.live_info.recommend_text : '',
            recommend_url: res.data ? res.data.live_info.recommend_url : '',
            id: record.id,
          },
        });
      });
  };
  // 直播类型设置
  showLiveType = (record: any) => {
    releaseListApi
      .getLiveType({
        channel_id: record.channel_id,
        article_id: record.original_id > 0 ? record.original_id : record.id,
      })
      .then((res: any) => {
        this.setState({
          LiveTypeForm: {
            visible: true,
            key: Date.now(),
            type: res.data.live_room_type || 0,
            id: record.original_id > 0 ? record.original_id : record.id,
          },
        });
      })
      .catch(() => { });
  };
  // 关闭弹窗
  closeLog4 = () => {
    this.setState({
      LiveTypeForm: {
        ...this.state.LiveTypeForm,
        visible: false,
      },
    });
  };
  // 超级福袋
  showSuperLuckyBag = (record: any) => {
    releaseListApi
      .getLiveList({
        channel_id: record.channel_id,
        article_id: record.original_id > 0 ? record.original_id : record.id,
        type: 1,
      })
      .then((res: any) => {
        this.setState({
          LuckBagForm: {
            visible: true,
            key: Date.now(),
            channel_id: record.channel_id, //channel_id频道id
            article_id: record.original_id > 0 ? record.original_id : record.id,
            luckBagList: res.data.list,
          },
        });
      })
      .catch(() => { });
  };

  // 答题有礼
  showAnswerGift = (record: any) => {
    releaseListApi
      .getLiveList({
        channel_id: record.channel_id,
        article_id: record.original_id > 0 ? record.original_id : record.id,
        type: 2,
      })
      .then((res: any) => {
        this.setState({
          AnswerRewardForm: {
            visible: true,
            key: Date.now(),
            channel_id: record.channel_id, //channel_id频道id
            article_id: record.original_id > 0 ? record.original_id : record.id,
            answerList: res.data.list,
          },
        });
      })
      .catch(() => { });
  };
  // 直播广播
  showLivePlay = (record: any) => {
    releaseListApi
      .getBroadcastList({
        article_id: record.original_id > 0 ? record.original_id : record.id,
        channel_id: record.channel_id,
      })
      .then((res: any) => {
        this.setState({
          LivePlayForm: {
            visible: true,
            key: Date.now(),
            channel_id: record.channel_id,
            article_id: record.original_id > 0 ? record.original_id : record.id,
            id: res.data != undefined ? res.data.live_broadcast.id : null,
            content: res.data != undefined ? res.data.live_broadcast.content : null,
            status: res.data != undefined ? res.data.live_broadcast.status : 0,
          },
        });
      })
      .catch(() => { });
  };
  // 浮窗广告
  showFloatingAdvertisement = (record: any) => {
    releaseListApi
      .getSuspendedAdDetail({
        channel_id: record.channel_id,
        article_id: record.original_id > 0 ? record.original_id : record.id,
      })
      .then((res: any) => {
        this.setState({
          FloatingForm: {
            visible: true,
            key: Date.now(),
            channel_id: record.channel_id,
            article_id: record.original_id > 0 ? record.original_id : record.id,
            id: res.data.result.id,
            shrink_img_url: res.data.result.shrink_img_url,
            expand_img_url: res.data.result.expand_img_url,
            url: res.data.result.url,
            status: res.data.result.status == 1 && res.data.result.status != undefined ? 1 : 0,
          },
        });
      })
      .catch(() => { });
  };

  toggleRecommend = (id: string | number, proposalHidden: number) => {
    const WAIT_TIME = 1000;
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .releaseChangeRecommend({ id, proposal_hidden: proposalHidden === 0 ? 1 : 0 })
      .then(() => {
        message.success('操作成功');
        setTimeout(() => {
          this.getData();
          this.props.dispatch(setConfig({ loading: false }));
        }, WAIT_TIME);
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };

  configSlowLive = (show: boolean, record: any = null) => {
    this.setState({ SlowLiveDrawerForm: { ...this.state.SlowLiveDrawerForm, show, record } })
  };

  editSlowLiveTitlePic = () => {
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi.getSlowLiveTitle()
      .then((data: any) => {
        this.props.dispatch(setConfig({ loading: false }));
        let title = data.data.title || ''
        let modal: any;

        const showModalLoading = (loading: boolean) => {
          modal.update({ okButtonProps: { loading, } })
        }

        const picChange = (u: string) => {
          title = u;
          modal.update({
            content: (
              <>
                <ImageUploader ratio={8 / 1} value={title} onChange={picChange} />
              </>
            ),
          });
        };

        modal = Modal.confirm({
          width: 502,
          title: '城市慢直播标题设置',
          content: (
            <>
              <ImageUploader ratio={8 / 1} value={title} onChange={picChange} />
            </>
          ),
          onOk: (destroy: Function) => {
            if (!title) {
              message.error('请上传标题图片');
              return;
            }
            showModalLoading(true)
            releaseListApi.setSlowLiveTitle({ live_title: title })
              .then(() => {
                showModalLoading(false)
                destroy()
                message.success('操作成功')
              }).catch(() => {
                showModalLoading(false)
              })

          },
        });
      }).catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      })
  }

  submitLinkEdit = () => {
    const { id, recommend_text, recommend_url } = this.state.linkForm;
    const regex = /^https?:\/\//;
    if (!!recommend_url && !regex.test(recommend_url)) {
      message.error('请正确填写url');
      return;
    }
    if (recommend_text.length > 30) {
      message.error('文字不能多于30个字');
      return;
    }
    releaseListApi
      .updateLiveInfo({
        id,
        recommend_text: recommend_text || '',
        recommend_url: recommend_url || '',
      })
      .then(() => {
        message.success('操作成功');
        this.linkFormChange('visible', false);
      });
  };

  linkFormChange = (key: any, value: any) => {
    this.setState({
      linkForm: {
        ...this.state.linkForm,
        [key]: value,
      },
    });
  };
  // 关闭弹窗
  closeLog = () => {
    this.setState({
      LuckBagForm: {
        ...this.state.LuckBagForm,
        visible: false,
      },
    });
  };
  // 弹窗数据提交
  onSubmitEnd = () => {
    this.getData();
    this.closeLog();
  };
  setRef = (ref: 'formRef', instance: any) => {
    this[ref] = instance;
  };

  // 关闭弹窗
  closeLog1 = () => {
    this.setState({
      AnswerRewardForm: {
        ...this.state.AnswerRewardForm,
        visible: false,
      },
    });
  };
  // 弹窗数据提交
  onSubmitEnd1 = () => {
    this.getData();
    this.closeLog1();
  };
  setRef1 = (ref: 'formRef1', instance: any) => {
    this[ref] = instance;
  };

  // 关闭弹窗
  closeLog2 = () => {
    this.setState({
      LivePlayForm: {
        ...this.state.LivePlayForm,
        visible: false,
      },
    });
  };
  // 弹窗数据提交
  onSubmitEnd2 = () => {
    this.getData();
    this.closeLog2();
  };
  setRef2 = (ref: 'formRef2', instance: any) => {
    this[ref] = instance;
  };

  // 关闭弹窗
  closeLog3 = () => {
    this.setState({
      FloatingForm: {
        ...this.state.FloatingForm,
        visible: false,
      },
    });
  };
  // 弹窗数据提交
  onSubmitEnd3 = () => {
    this.getData();
    this.closeLog3();
  };
  setRef3 = (ref: 'formRef3', instance: any) => {
    this[ref] = instance;
  };

  onDragEnd = (oldIndex: number, newIndex: number) => {
    const target = this.props.tableList.records[newIndex];
    const source = this.props.tableList.records[oldIndex];
    const WAIT_TIME = 1000;
    if (target.fixed_number) {
      message.error('当前位置已有固定位置稿件');
      return;
    }
    const body: any = {
      position: target.seq,
      id:
        this.state.channel.code === 'zhuanti' && source.related_article_id
          ? source.related_article_id
          : source.id,
      fixed: false,
    };
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .releaseChangeOrder(body)
      .then(() => {
        message.success('操作成功');
        setTimeout(() => {
          this.getData();
          this.props.dispatch(setConfig({ loading: false }));
        }, WAIT_TIME);
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };
  // 弹窗数据提交
  onSubmitEnd4 = () => {
    this.getData();
    this.closeLog4();
  };
  setRef4 = (ref: 'formRef4', instance: any) => {
    this[ref] = instance;
  };
  toggleLoopArticleDrawer = (show: boolean) => {
    this.setState({ loopArticleDrawerShow: show })
  }
  hideNewOperateLog = () => {
    this.setState({
      newOperateLog: { ...this.state.newOperateLog, visible: false },
    });
  };

  render() {
    // const bread = ['签发内容管理', this.state.channel.name];
    const { LuckBagForm, AnswerRewardForm, LivePlayForm, FloatingForm, LiveTypeForm, PushChannelRecordForm,
      PushChannelDrawerForm, SlowLiveDrawerForm, ContentRecommendDrawerForm, loopArticleDrawerShow } = this.state;
    const isSlowLive = this.state.filter.slow_live === '1'
    const canSetSlowLiveTitle = this.props.session.permissions.indexOf(`channel_article:${this.state.channel.id}:set_title_slow_live`) >= 0
    const searchStatus =
      this.state.filter.keyword ||
      this.state.filter.doc_type ||
      this.state.filter.begin ||
      this.state.filter.recommend_enabled === '1';
    const supportRecommend = getSupportRecommend(this.state.channel)
    const recommendHideMenuList = getRecommendHideMenuList(supportRecommend)
    return (
      <>
        <Row className="layout-infobar">
          <Col span={16}>
            <Button style={{ marginRight: 8 }} onClick={() => this.props.history.goBack()}>
              <Icon type="left-circle" />
              返回
            </Button>

            <span>
              <span>城市慢直播标题设置</span>
              <Icon type="edit"
                style={canSetSlowLiveTitle ? { color: '#40a9ff', cursor: 'pointer' } : { color: '#ccc', pointerEvents: 'none' }}
                onClick={this.editSlowLiveTitlePic.bind(this)} />
            </span>
          </Col>
          <Col span={8} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>

        <div className="component-content news-pages">
          <div style={{ display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap' }}>
            <div style={{ marginBottom: 16 }}>
              <DatePicker.RangePicker
                format="YYYY-MM-DD"
                onChange={this.handleRangePickerChange}
                ranges={this.state.filter.sort_by > 0 ? {
                  '当日': [moment(), moment()],
                  '近3天': [moment().subtract(2, 'days'), moment()],
                  '近一周': [moment().subtract(6, 'days'), moment()],
                } : {}}
                value={
                  this.state.filter.begin
                    ? [moment(this.state.filter.begin), moment(this.state.filter.end)]
                    : []
                }
                style={{ width: 210 }}
              />
              <Select
                value={this.state.filter.doc_type.toString()}
                style={{ width: 100, marginLeft: 8 }}
                onChange={this.handleDocTypeChange}
              >
                <Select.Option value="">稿件类型</Select.Option>
                <Select.Option value={DOC_TYPE.NORMAL.code.toString()}>
                  {DOC_TYPE.NORMAL.name}
                </Select.Option>
                <Select.Option value={DOC_TYPE.SUBJECT.code.toString()}>
                  {DOC_TYPE.SUBJECT.name}
                </Select.Option>
                <Select.Option value={DOC_TYPE.LINK.code.toString()}>
                  {DOC_TYPE.LINK.name}
                </Select.Option>
                <Select.Option value={DOC_TYPE.VIDEO.code.toString()}>
                  {DOC_TYPE.VIDEO.name}
                </Select.Option>
                <Select.Option value={DOC_TYPE.LIVE.code.toString()}>
                  {DOC_TYPE.LIVE.name}
                </Select.Option>
                {
                  !isSlowLive && <Select.Option value={DOC_TYPE.Recommend.code.toString()}>
                    {DOC_TYPE.Recommend.name}
                  </Select.Option>
                }
              </Select>
              {
                !isSlowLive && <Select
                  value={this.state.filter.recommend_enabled}
                  style={{ width: 150, marginLeft: 8 }}
                  onChange={this.handleRecommendChange}
                >
                  <Select.Option value="2">非直播推荐位稿件</Select.Option>
                  <Select.Option value="1">直播推荐位稿件</Select.Option>
                </Select>
              }
              <Select
                value={this.state.filter.top_pushed}
                style={{ width: 100, marginLeft: 8 }}
                onChange={this.handleTopPushedChange}>
                <Select.Option value="">头条状态</Select.Option>
                <Select.Option value="1">头条未用</Select.Option>
                <Select.Option value="2">头条已用</Select.Option>
              </Select>
              <Select
                value={this.state.filter.original}
                style={{ width: 100, marginLeft: 8 }}
                onChange={this.handleOriginalChange}>
                <Select.Option value="">原创类型</Select.Option>
                <Select.Option value="1">原创</Select.Option>
                <Select.Option value="0">非原创</Select.Option>
              </Select>
              <TreeSelect
                value={this.state.toChannelCheckedList}
                style={{ width: 220, marginLeft: 8 }}
                treeData={this.state.toChannelList}
                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                placeholder="推至频道"
                treeDefaultExpandAll={false}
                treeCheckable={true}
                onChange={this.handleToChannelChange}
                allowClear
              />
            </div>

            <div style={{ marginBottom: 16 }}>{this.renderSearch()}</div>
          </div>
          <Table
            filter={this.getFilter()}
            index="release_list"
            func="getReleaseList"
            columns={this.getColumns()}
            rowKey="id"
            pagination={true}
            draggable={!searchStatus && this.state.showDragSorter}
            onDragEnd={this.onDragEnd}
            getRecordDraggable={(record: IResReleaseListRecordsData) => !record.fixed_number}
            total={this.props.tableList.total + this.state.fixedCount}
            sizeChange={(size: number) => DataStore.pageSize = size}
          // tableProps={{
          //   scroll: {
          //     x: true
          //   }
          // }}
          />

          <PushChannelRecordModal
            record={PushChannelRecordForm.record}
            visible={PushChannelRecordForm.show}
            onCancel={this.togglePushChannelRecordModalState.bind(this, false, {})}
            skey={PushChannelRecordForm.key} />

          <PushChannelDrawer
            record={PushChannelDrawerForm.record}
            visible={PushChannelDrawerForm.show}
            onClose={this.togglePushChannelDrawerState.bind(this, false, {})}
            onOk={(id: number, channel_ids: string, complete: () => void) => {
              if (channel_ids.length > 0) {
                this.pushTop(id, channel_ids, (success) => {
                  complete()
                  if (success) {
                    this.togglePushChannelDrawerState(false, {})
                  }
                })
              } else {
                complete()
                this.togglePushChannelDrawerState(false, {})
              }
            }}
            skey={PushChannelDrawerForm.key} />


          <SlowLiveDrawer
            skey={SlowLiveDrawerForm.key}
            record={SlowLiveDrawerForm.record}
            visible={SlowLiveDrawerForm.show}
            onClose={() => this.configSlowLive(false)}
            onEnd={() => {
              this.getData()
              this.configSlowLive(false)
            }} />

          <ContentRecommendDrawer
            skey={ContentRecommendDrawerForm.key}
            maxPosition={searchStatus ? 999999999999 : this.state.total + this.state.fixedCount}
            record={ContentRecommendDrawerForm.record}
            visible={ContentRecommendDrawerForm.show}
            supportTypes={getSupportRecommend(this.state.channel)}
            onEnd={() => {
              this.getData()
              this.toggleContentRecommendDrawerState(false)
            }}
            onClose={() => {
              this.toggleContentRecommendDrawerState(false)
            }}
          />

          <Modal
            visible={this.state.newOperateLog.visible}
            title="操作日志"
            key={this.state.newOperateLog.key}
            cancelText={null}
            onCancel={this.hideNewOperateLog.bind(this)}
            onOk={this.hideNewOperateLog.bind(this)}
          >
            <ChannelListOperateLogModal record={this.state.newOperateLog.record}></ChannelListOperateLogModal>
          </Modal>
          {this.renderAltPV()}
          {/* <Modal
            visible={this.state.linkForm.visible}
            key={this.state.linkForm.key}
            title="购物链接配置"
            onOk={this.submitLinkEdit}
            onCancel={() => this.linkFormChange('visible', false)}
          >
            <Form labelCol={{ span: 4 }} wrapperCol={{ span: 19 }}>
              <Form.Item label="文字">
                <Input
                  value={this.state.linkForm.recommend_text}
                  placeholder="请输入文字"
                  onChange={(e: any) => this.linkFormChange('recommend_text', e.target.value)}
                />
              </Form.Item>
              <Form.Item label="链接">
                <Input
                  value={this.state.linkForm.recommend_url}
                  placeholder="请输入链接"
                  onChange={(e: any) => this.linkFormChange('recommend_url', e.target.value)}
                />
              </Form.Item>
            </Form>
          </Modal> */}
          <Drawer
            visible={LiveTypeForm.visible}
            title={`直播间管理`}
            skey={LiveTypeForm.key}
            onClose={this.closeLog4}
            onOk={() => this.formRef4.doSubmit()}
          >
            <LiveType
              type={LiveTypeForm.type}
              id={LiveTypeForm.id}
              onEnd={this.onSubmitEnd4}
              formContent={LuckBagForm}
              wrappedComponentRef={this.setRef4.bind(this, 'formRef4')}
            />
          </Drawer>
          <Drawer
            visible={LuckBagForm.visible}
            title={`超级福袋`}
            skey={LuckBagForm.key}
            onClose={this.closeLog}
            // onOk={() => this.formRef.doSubmit()}
            closeText={'关闭'}
          >
            <SuperLuckyBagForm
              onEnd={this.onSubmitEnd}
              formContent={LuckBagForm}
              wrappedComponentRef={this.setRef.bind(this, 'formRef')}
            />
          </Drawer>

          <Drawer
            visible={AnswerRewardForm.visible}
            title={`答题有礼`}
            skey={AnswerRewardForm.key}
            onClose={this.closeLog1}
            // onOk={() => this.formRef1.doSubmit()}
            closeText={'关闭'}
          >
            <AnswerGiftForm
              onEnd={this.onSubmitEnd1}
              formContent={AnswerRewardForm}
              wrappedComponentRef={this.setRef1.bind(this, 'formRef1')}
            />
          </Drawer>

          <Drawer
            visible={LivePlayForm.visible}
            title={`直播广播`}
            skey={LivePlayForm.key}
            onClose={this.closeLog2}
            onOk={() => this.formRef2.doSubmit()}
          >
            <LiveForm
              onEnd={this.onSubmitEnd2}
              formContent={LivePlayForm}
              wrappedComponentRef={this.setRef2.bind(this, 'formRef2')}
            />
          </Drawer>

          <Drawer
            visible={FloatingForm.visible}
            title={`浮窗广告`}
            skey={FloatingForm.key}
            onClose={this.closeLog3}
            onOk={() => this.formRef3.doSubmit()}
          >
            <FloatingAdvertisementForm
              onEnd={this.onSubmitEnd3}
              formContent={FloatingForm}
              wrappedComponentRef={this.setRef3.bind(this, 'formRef3')}
            />
          </Drawer>

          <LoopArticleDrawer
            visible={loopArticleDrawerShow}
            onEnd={() => {
              this.toggleLoopArticleDrawer(false)
            }}
            onClose={() => {
              this.toggleLoopArticleDrawer(false)
            }} />
        </div>
      </>
    );
  }
}

export default withRouter(
  connect<IResReleaseListRecordsData, IResReleaseListAllData>()(ReleaseList)
);
