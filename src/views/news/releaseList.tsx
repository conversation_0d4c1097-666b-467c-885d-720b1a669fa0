import { setConfig } from '@app/action/config';
import { getTableList } from '@app/action/tableList';
import { releaseListApi } from '@app/api';
import { CommonObject, IResReleaseListAllData, IResReleaseListRecordsData } from '@app/types';
import { DOC_TYPE } from '@app/utils/constants';
import { A, Table, Drawer } from '@components/common';
import { connectTable as connect } from '@utils/connect';
import ReactClipboard from 'react-clipboardjs-copy';
import RecommendUserVideoForm from '@app/components/business/RecommendUserVideoForm';
import SuperLuckyBagForm from '@app/components/business/SuperLuckyBagForm';
import AnswerGiftForm from '@app/components/business/AnswerGiftForm';
import LiveForm from '@app/components/business/LiveForm';
import LiveType from '@app/components/business/liveTypeForm';
import { PermA, PermButton } from '@app/components/permItems';
import FloatingAdvertisementForm from '@app/components/business/FloatingAdvertisementForm';
import PushChannelRecordModal from '@app/components/business/PushChannelRecordModal';
import PushChannelDrawer from '@app/components/business/PushChannelDrawer';
import HideForm from '@app/components/business/HideForm';
import DataStore from '@app/utils/DataStore';
import ContentRecommendDrawer from '@app/components/business/recommend/ContentRecommendDrawer';
import { recommendTypeText, getSupportRecommend, getRecommendHideMenuList } from '@utils/utils';
import ChannelListOperateLogModal from '@views/news/channelListOperateLogModal';

import {
  getCrumb,
  requirePerm,
  resolveNewsType,
  resolveTopPushed,
  searchToObject,
} from '@utils/utils';
import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Dropdown,
  Icon,
  Input,
  InputNumber,
  Menu,
  message,
  Modal,
  Row,
  Select,
  Tag,
  Table as ATable,
  Form as AForm,
  Switch,
  Popover,
  TreeSelect,
  Tooltip,
} from 'antd';
import moment from 'moment';
import React, { useState } from 'react';
import { withRouter } from 'react-router';
import NewsBaseComponent, { INewsBaseProps } from './baseComponent';
import YaYunBannerDrawer from '@app/components/business/YaYunBannerDrawer';
import LoopArticleDrawer from '@app/components/business/LoopArticleDrawer';
import './index.scss';
import SortableColumn from '@app/components/common/sortableColumn';
import { flushSync } from 'react-dom';
import BatchSort from './batchSort';
import { size } from 'lodash';
import RelatedServiceModal from '@app/components/common/relatedServiceModal';
import PushSubjectRecordModal from '@app/components/business/PushSubjectRecordModal';

type Props = INewsBaseProps<{}, IResReleaseListRecordsData, IResReleaseListAllData>;

function IdCopy(props: any) {
  return (
    <Tooltip title="复制">
      <ReactClipboard
        action="copy"
        text={String(props.id)}
        onSuccess={() => message.success('id复制成功')}
        onError={() => message.error('id复制失败')}
      >
        <span style={{ cursor: 'pointer' }}>
          <Icon type="copy" />
        </span>
      </ReactClipboard>
    </Tooltip>
  );
}

class SelectedActivity extends React.Component<any, any> {
  static getDerivedStateFromProps(nextProps: any) {
    if ('value' in nextProps) {
      return { value: nextProps.value || [] };
    }
    return null;
  }

  constructor(props: any) {
    super(props);
    this.state = {
      value: props.value || [],
    };
  }
  render() {
    return <ATable dataSource={this.state.value} pagination={false} columns={this.props.columns} />;
  }
}
class ReleaseList extends NewsBaseComponent<
  {},
  IResReleaseListRecordsData,
  IResReleaseListAllData
> {
  formRef: any; //超级福袋
  formRef1: any; //答题有礼
  formRef2: any; //直播广告
  formRef3: any; //浮窗广告
  formRef4: any; //直播类型
  formRef5: any; //隐藏显示
  constructor(props: Props) {
    super(props);
    const initialFilter = {
      current: 1,
      size: DataStore.pageSize,
      search_type: '3',
      doc_type: '',
      keyword: '',
      begin: false,
      end: false,
      channel_id: searchToObject().channel_id,
      recommend_enabled: '2',
      top_pushed: '',
      original: '',
      to_channel_ids: '',
      sort_by: 0,
      sort_asc: -1,
    };
    // console.log(props, 'props+++++++++++++++');
    this.state = {
      filter: initialFilter,
      pageInput: initialFilter,
      channel: {
        category_id: 1,
        focus_carousel: false,
        id: '1',
        mode: 0,
        name: '',
        tou_tiao: false,
        focus_position: 1,
        code: '',
      },
      operateLog: {
        visible: false,
        scId: 0,
        mlfId: 0,
        articleTitle: '',
        logs: [],
        key: Date.now(),
        isRecommend: false,
      },
      newOperateLog: {
        visible: false,
        key: Date.now(),
        record: {},
      },
      PVForm: {
        visible: false,
        key: Date.now() + 2,
        article_id: 0,
        type: 0,
        show_pv: 0,
        base_pv: 0,
        min_factor: 0,
        max_factor: 0,
      },
      RecommendVideoForm: {
        show: false,
        key: Date.now() + 11,
        id: 0,
        title: '潮客视频推荐',
        data: {
          enabled: false,
          video_type: 1,
          channelArticles: [],
        },
      },
      ActiviryForm: {
        show: false,
        key: Date.now() + 11,
        articleId: null,
        activityId: null,
      },
      RelatedServiceForm: {
        show: false,
        checked: false,
        key: Date.now() + 12,
        articleId: null,
        activityId: null,
      },
      LiveTypeForm: {
        visible: false, //内容连接弹窗是否显示
        key: Date.now() + 4, //唯一值
        type: null,
        id: null,
      },
      LuckBagForm: {
        visible: false, //内容连接弹窗是否显示
        key: Date.now() + 4, //唯一值
        channel_id: '', //channel_id频道id
        article_id: '', //稿件id
        luckBagList: [], //列表
      },
      AnswerRewardForm: {
        visible: false, //内容连接弹窗是否显示
        key: Date.now() + 5, //唯一值
        channel_id: '', //channel_id频道id
        article_id: '', //稿件id
        answerList: [], //列表
      },
      LivePlayForm: {
        visible: false, //内容连接弹窗是否显示
        key: Date.now() + 6, //唯一值
        channel_id: '', //channel_id频道id
        article_id: '', //稿件id
        id: '',
        content: '',
        status: 1,
      },
      FloatingForm: {
        visible: false, //内容连接弹窗是否显示
        key: Date.now() + 7, //唯一值
        channel_id: '', //channel_id频道id
        article_id: '', //稿件id
        id: '',
        shrink_img_url: '',
        expand_img_url: '',
        url: '',
        status: 1,
      },
      HideForm: {
        visibleShow: false, //内容连接弹窗是否显示
        key: Date.now() + 100, //唯一值
        visible: false,
        is_timing: false,
        id: '',
        start_show_time: Date.now(),
        end_show_time: Date.now() + 1000,
      },
      PushChannelRecordForm: {
        show: false,
        key: Date.now() + 8,
        record: {},
      },
      PushSubjectRecordForm: {
        show: false,
        key: Date.now() + 11,
        record: {},
      },
      PushChannelDrawerForm: {
        show: false,
        key: Date.now() + 9,
        record: {},
      },
      ContentRecommendDrawerForm: {
        show: false,
        key: Date.now() + 10,
        record: null,
      },
      YaYunBannerDrawerShow: false,
      loopArticleDrawerShow: false,
      selectValue: undefined,
      activityList: [],
      showActivity: [],
      showDragSorter: false,
      fixedCount: 0,
      total: 0,
      toChannelList: [],
      toChannelCheckedList: [],
      openDatePickerPanel: false,
      batchSortDialog: {
        visible: false,
        key: null,
      },
    };
    this.setSelectKeys();
  }

  componentDidMount() {
    this.getData();
    this.getChannelList();
    this.props.dispatch(setConfig({ openKeys: this.props.openKeys }));
    this.setSelectKeys();
  }

  setSelectKeys() {
    const url = !!searchToObject().nav_target_type
      ? `/view/releaselist?channel_id=${searchToObject().channel_id}&nav_target_type=${
          searchToObject().nav_target_type
        }`
      : `/view/releaselist?channel_id=${searchToObject().channel_id}`;

    this.props.dispatch(setConfig({ selectKeys: [url] }));
  }

  componentDidUpdate(prevProps: Props) {
    const channel_id = searchToObject().channel_id;
    if (
      prevProps.tableList.timestamp === this.props.tableList.timestamp &&
      channel_id === this.state.filter.channel_id &&
      this.props.location === prevProps.location
    ) {
      return;
    }
    if (channel_id !== this.state.filter.channel_id || this.props.location !== prevProps.location) {
      const initialFilter = {
        current: 1,
        size: DataStore.pageSize,
        search_type: '3',
        doc_type: '',
        keyword: '',
        begin: false,
        end: false,
        channel_id,
        recommend_enabled: '2',
        top_pushed: '',
        original: '',
        to_channel_ids: '',
        sort_by: 0,
        sort_asc: -1,
      };
      this.setSelectKeys();
      this.setState(
        {
          filter: initialFilter,
          pageInput: initialFilter,
          toChannelCheckedList: [],
          toChannelList: this.mapToChannelListTreeData(this.state.originToChannelList, channel_id),
          openDatePickerPanel: false,
        },
        () => this.getData()
      );
    }
    if (
      prevProps.tableList.timestamp !== this.props.tableList.timestamp &&
      Boolean(this.props.tableList.allData.release_list)
    ) {
      const { channel, release_list } = this.props.tableList.allData;
      this.setState({
        channel,
        filter: { ...this.state.filter, current: release_list.current, size: release_list.size },
        pageInput: {
          ...this.state.pageInput,
          current: release_list.current,
          size: release_list.size,
        },
        fixedCount: release_list.fixed_count,
        total: release_list.total,
      });
    }
  }

  handleRangePickerChange = (dates: any) => {
    console.log('filter', dates);
    if (dates.length === 0) {
      this.setState(
        {
          filter: { ...this.state.filter, begin: false, end: false, sort_asc: -1, sort_by: 0 },
          pageInput: {
            ...this.state.pageInput,
            begin: false,
            end: false,
            sort_asc: -1,
            sort_by: 0,
          },
          openDatePickerPanel: false,
        },
        () => this.getData()
      );
    } else {
      if (this.state.filter.sort_by > 0 && dates[1].diff(dates[0], 'days') > 31) {
        switch (this.state.filter.sort_by) {
          case 1:
            message.error('阅读数排序选择日期，请勿超过31天');
            break;
          case 2:
            message.error('发布时间排序选择日期，请勿超过31天');
            break;
          case 3:
            message.error('全网传播数排序选择日期，请勿超过31天');
            break;
          default:
            break;
        }
        return;
      }
      this.setState(
        {
          filter: {
            ...this.state.filter,
            begin: dates[0].format('YYYY-MM-DD'),
            end: dates[1].format('YYYY-MM-DD'),
          },
          pageInput: {
            ...this.state.pageInput,
            begin: dates[0].format('YYYY-MM-DD'),
            end: dates[1].format('YYYY-MM-DD'),
          },
        },
        () => this.getData()
      );
    }
  };

  handleDocTypeChange = (value: any) => {
    this.setState(
      {
        filter: { ...this.state.filter, doc_type: value },
        pageInput: { ...this.state.pageInput, doc_type: value },
      },
      () => this.getData()
    );
  };
  changeOrderInline = (record: any) => {
    if (this.state.fixedCount >= 40) {
      message.error('最多只能固定40条稿件或推荐位');
      return;
    }
    const body: any = {
      position: record.seq,
      id:
        this.state.channel.code === 'zhuanti' && record.related_article_id
          ? record.related_article_id
          : record.id,
      fixed: true,
    };
    this.changeOrderRequest(body, null);
  };
  changeOrderRequest = (body: any, callbacks: any | null) => {
    const WAIT_TIME = 1000;
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .releaseChangeOrder(body)
      .then(() => {
        message.success('操作成功');
        setTimeout(() => {
          this.getData();
          this.props.dispatch(setConfig({ loading: false }));
        }, WAIT_TIME);
        if (typeof callbacks == 'function') {
          callbacks();
        }
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };
  exchangeOrder = (record: any | number, seq: number, offset: number) => {
    console.log(seq, 'seq++++++++++++');
    if (record.fixed_number > 0 && seq - offset > 100) {
      message.error('固定位置必须为1~100');
      return;
    }
    const WAIT_TIME = 1000;
    const id = record.id;
    this.props.dispatch(setConfig({ loading: true }));
    // 普通稿件
    releaseListApi
      .releaseExchangeOrder({ id, offset, current: seq })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };

  changeOrder = (record: any) => {
    let position = record.seq;
    let sticky = record.fixed_number > 0;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      position = v;
    };
    const stickyChange = (e: any) => {
      sticky = e.target.checked;
    };
    const searchStatus =
      this.state.filter.keyword ||
      this.state.filter.doc_type ||
      this.state.filter.begin ||
      this.state.filter.top_pushed ||
      this.state.filter.original ||
      this.state.filter.to_channel_ids ||
      this.state.filter.recommend_enabled === '1' ||
      this.state.filter.sort_asc >= 0;
    Modal.confirm({
      title: <p>排序：《{record.list_title}》</p>,
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber
            min={1}
            max={searchStatus ? 999999999999 : this.state.total + this.state.fixedCount}
            defaultValue={position}
            onChange={positionChange}
          />
          <br key={1} />
          <Checkbox
            disabled={!record.visible}
            style={{ marginTop: 8 }}
            defaultChecked={sticky}
            onChange={stickyChange}
            key={2}
          >
            固定位置
          </Checkbox>
          <p style={{ color: '#ccc' }} key={3}>
            此选项只对1~100位置有效
            <br />
            最多只能固定40条稿件或推荐位
          </p>
        </div>
      ),
      onOk: (closeFunc: Function) => {
        const MAX_STICKY_POSITION = 100;
        if (!position) {
          message.error('请填写位置');
          return;
        }
        if (sticky && position > MAX_STICKY_POSITION) {
          message.error('固定位置必须为1~100');
          return;
        }
        if (sticky && this.state.fixedCount >= 40) {
          message.error('最多只能固定40条稿件或推荐位');
          return;
        }
        if (this.state.total + this.state.fixedCount < position && !searchStatus) {
          message.error('稿件位置不能大于稿件总数');
          return;
        }
        const data: any = { position, id: record.id, fixed: sticky };
        this.props.dispatch(setConfig({ loading: true }));
        releaseListApi
          .releaseChangeOrder(data)
          .then(() => {
            message.success('操作成功');
            setTimeout(() => {
              this.getData();
              this.props.dispatch(setConfig({ loading: false }));
            }, WAIT_TIME);
            closeFunc();
          })
          .catch(() => {
            this.props.dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  cancelFix = (id: string | number, title: string) => {
    const WAIT_TIME = 1000;
    Modal.confirm({
      title: <p>确认取消固定《{title}》</p>,
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        releaseListApi
          .releaseCancelFix({ id })
          .then(() => {
            message.success('操作成功');
            setTimeout(() => {
              this.getData();
              this.props.dispatch(setConfig({ loading: false }));
            }, WAIT_TIME);
          })
          .catch(() => {
            this.props.dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  // 活动组件
  activityComponent = (record: any) => {
    let inputUrl = record.h5_activity_url;
    const toc_type = record;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      inputUrl = v.target.value;
    };
    Modal.confirm({
      title: <p>活动组件</p>,
      icon: <Icon type="info-circle" style={{ color: '#1890ff' }} />,
      content: (
        <div>
          <span>活动组件：</span>
          <Input
            placeholder="请填写活动组件地址"
            defaultValue={inputUrl}
            onChange={positionChange}
          />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        this.props.dispatch(setConfig({ loading: true }));
        releaseListApi
          .getCommentUpdataH5ActivityUrl({ id: record.id, h5ActivityUrl: inputUrl })
          .then(() => {
            message.success('操作成功');
            setTimeout(() => {
              this.getData();
              this.props.dispatch(setConfig({ loading: false }));
            }, WAIT_TIME);
            closeFunc();
          });
      },
    });
  };

  changeVisible = (record: any | number, visible: boolean) => {
    let data = {
      article_id: record.id,
      // channel_id:this.state.channel.id
    };
    releaseListApi.getVisibleConfig(data).then((res: any) => {
      this.setState({
        HideForm: {
          ...this.state.HideForm,
          visibleShow: true,
          visible: record.visible,
          is_timing: !!res.data.detail.type,
          start_show_time: res.data.detail.start_show_time,
          end_show_time: res.data.detail.end_show_time,
          id: record.id,
        },
      });
    });

    // const WAIT_TIME = 1000;
    // this.props.dispatch(setConfig({ loading: true }));
    // releaseListApi
    //   .releaseChangeVisible({ id, visible: visible ? 0 : 1 })
    //   .then(() => {
    //     message.success('操作成功');
    //     setTimeout(() => {
    //       this.getData();
    //       this.props.dispatch(setConfig({ loading: false }));
    //     }, WAIT_TIME);
    //   })
    //   .catch(() => {
    //     this.props.dispatch(setConfig({ loading: false }));
    //   });
  };
  // 3.5.0新增
  // 活动列表
  getActivityColumns = (hideGive: boolean = false) => {
    return [
      {
        title: '活动标题',
        key: 'title',
        dataIndex: 'title',
      },
      {
        title: '状态',
        dataIndex: 'status',
        width: 50,
        render: (text: any, record: any) => {
          return record.status == 1 ? '上架' : '下架';
        },
      },
      {
        title: '操作',
        key: 'op',
        width: 50,
        render: (text: any, record: any) => {
          return <A onClick={() => this.delSelected()}>删除</A>;
        },
      },
    ];
  };
  // 删除已选择活动
  delSelected = () => {
    this.setState({
      showActivity: [],
      ActiviryForm: {
        ...this.state.ActiviryForm,
        activityId: 0,
      },
    });
  };
  // 选择关联的活动
  cChange = (index: any) => {
    let data = this.state.activityList.filter((v: any) => {
      return v.id == index;
    });
    data[0].status = 1;
    this.setState({
      showActivity: data,
      ActiviryForm: {
        ...this.state.ActiviryForm,
        activityId: data[0].id,
      },
    });
  };
  // 关联活动提交
  submitActivity = () => {
    const channelId = this.state.channel.id;
    if (
      !this.state.ActiviryForm.articleId ||
      (!this.state.ActiviryForm.activityId && this.state.ActiviryForm.activityId != 0)
    ) {
      message.error('请选择需要关联的活动');
      return;
    }
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .saveActivity({
        articleId: this.state.ActiviryForm.articleId,
        activityId: this.state.ActiviryForm.activityId,
        channelId: channelId,
      })
      .then((res: any) => {
        this.setState({
          selectValue: undefined,
          showActivity: [],
          ActiviryForm: {
            ...this.state.ActiviryForm,
            articleId: null,
            activityId: 0,
            show: false,
          },
        });
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch((res: any) => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };
  // // 关联活动
  // aboutAct = () => {
  //   this.setState({
  //     ActiviryForm: {
  //       ...this.state.ActiviryForm,
  //       show: true,
  //     },
  //   });
  // };

  // 相关服务
  relatedService = (record: any) => {
    this.setState({
      RelatedServiceForm: {
        ...this.state.RelatedServiceForm,
        articleId:
          this.state.channel.code === 'zhuanti' && record.related_article_id
            ? record.related_article_id
            : record.id,
        show: true,
      },
    });
  };

  // 修改阅读数排序
  changeReadCountOrder = (sort_asc: number, sort_by: number) => {
    let {
      filter: { sort_by: old_sort_by, begin, end },
      openDatePickerPanel,
    } = this.state;
    if (sort_asc === -1) {
      // 当前选中其他排序的时候 另外一个的重置不生效
      if (old_sort_by != sort_by) {
        return;
      }
      openDatePickerPanel = false;
      begin = false;
      end = false;
      sort_by = 0;
      sort_asc = -1;
    } else if (old_sort_by != sort_by) {
      // 进入排序模式，显示时间框，默认选中今天
      openDatePickerPanel = true;
      const today = moment();
      begin = today.format('YYYY-MM-DD');
      end = today.format('YYYY-MM-DD');
    }
    this.setState(
      {
        filter: { ...this.state.filter, sort_asc, begin, end, sort_by },
        pageInput: { ...this.state.pageInput, sort_asc, begin, end, sort_by },
        openDatePickerPanel,
      },
      () => this.getData()
    );
  };

  getColumns = () => {
    const channelId = this.state.channel.id;
    const recommendEnabled = this.state.filter.recommend_enabled === '1';
    const { permissions } = this.props.session;
    const recommendCanEdit = permissions.indexOf(`content_recommend:${channelId}:update`) >= 0;

    const showReadingCount =
      this.state.channel.code == 'daguan' &&
      permissions.indexOf(`channel_article:${channelId}:read_count`) >= 0;

    const searchStatus =
      this.state.filter.keyword ||
      this.state.filter.doc_type ||
      this.state.filter.begin ||
      this.state.filter.top_pushed ||
      this.state.filter.original ||
      this.state.filter.to_channel_ids ||
      recommendEnabled ||
      this.state.filter.sort_by > 0;
    const getDropDown = (record: any) => {
      const menu = (
        <Menu>
          {record.doc_type > 0 ? (
            requirePerm(
              this,
              `channel_article:${channelId}:edit`
            )(
              <Menu.Item
                onClick={this.handleArticleClick.bind(this, record, 'mlf_edit_url', channelId)}
              >
                编辑
              </Menu.Item>
            )
          ) : (
            <Menu.Item
              disabled={!recommendCanEdit}
              onClick={this.handleArticleClick.bind(this, record, 'mlf_edit_url', channelId)}
            >
              编辑
            </Menu.Item>
          )}

          {requirePerm(
            this,
            `channel_article:${channelId}:sort`
          )(
            <Menu.Item
              disabled={recommendEnabled || !record.visible}
              onClick={this.changeOrder.bind(this, record)}
            >
              排序
            </Menu.Item>
            // record.fixed_number > 0 ? (
            //   <Menu.Item
            //     onClick={this.cancelFix.bind(this, record.id, record.list_title)}
            //   >
            //     取消固定
            //   </Menu.Item>
            // ) : (
            //   <Menu.Item disabled={recommendEnabled || !record.visible} onClick={this.changeOrder.bind(this, record)}>
            //     排序
            //   </Menu.Item>
            // )
          )}
          {record.doc_type > 0 &&
            requirePerm(
              this,
              `channel_article:${channelId}:push_top`
            )(
              <Menu.Item
                disabled={Boolean(record.source_channel_name)}
                // onClick={this.pushTop.bind(this, record.id, record.list_title)}
                onClick={this.togglePushChannelDrawerState.bind(this, true, record)}
              >
                推至多频道
              </Menu.Item>
            )}

          {/* 新增 活动组件 */}
          {record.doc_type === 9 &&
            requirePerm(
              this,
              `channel_article:${channelId}:activiti_url`
            )(<Menu.Item onClick={this.activityComponent.bind(this, record)}>活动组件</Menu.Item>)}

          {requirePerm(
            this,
            `channel_article:${channelId}:display`
          )(
            <Menu.Item
              disabled={record.fixed_number > 0}
              onClick={this.changeVisible.bind(this, record, record.visible)}
            >
              {record.visible === true ? '隐藏' : '取消隐藏'}
            </Menu.Item>
          )}
          {record.doc_type > 0 &&
            requirePerm(
              this,
              `comment:view:${channelId}`
            )(
              <Menu.Item
                disabled={record.doc_type === DOC_TYPE.SUBJECT.code}
                onClick={this.toCommentSystem.bind(
                  this,
                  record.original_id ? record.original_id : record.id
                )}
              >
                评论运营
              </Menu.Item>
            )}
          {record.doc_type > 0 &&
            requirePerm(
              this,
              `channel_article:${channelId}:cancel_release`
            )(
              <Menu.Item onClick={this.revokeNews.bind(this, record.id, record.list_title, record)}>
                取消签发
              </Menu.Item>
            )}
          {record.doc_type > 0 && (
            <Menu.Item disabled={record.url === ''}>
              <a href={record.url} target="_blank">
                查看链接
              </a>
            </Menu.Item>
          )}
          {record.doc_type > 0 && (
            <Menu.Item disabled={record.url === ''}>
              {record.url === '' ? (
                '复制分享链接'
              ) : (
                <ReactClipboard
                  action="copy"
                  text={record.url}
                  onSuccess={this.copySuccess}
                  onError={this.copyFail}
                >
                  <a>复制分享链接</a>
                </ReactClipboard>
              )}
            </Menu.Item>
          )}

          {record.doc_type > 0 && (
            <Menu.Item
              onClick={this.changeRoute.bind(
                this,
                `/view/pushNotify?channel_id=${record.source_channel_id || channelId}&tmId=${
                  record.original_id || record.id
                }`
              )}
            >
              快捷推送
            </Menu.Item>
          )}
          {record.doc_type === 2
            ? requirePerm(
                this,
                `channel_article:${channelId}:associated_activity`
              )(<Menu.Item onClick={() => this.aboutAct(record.id)}>关联活动</Menu.Item>)
            : ''}
          {record.doc_type === 8 && !record.url.includes('isVertical=1') ? (
            <Menu.Item onClick={() => this.showLiveType(record)}>直播间管理</Menu.Item>
          ) : (
            ''
          )}
          {record.doc_type === 8
            ? requirePerm(
                this,
                `channel_article:${channelId}:live_market:${record.doc_type}:1`
              )(<Menu.Item onClick={() => this.showSuperLuckyBag(record)}>超级福袋</Menu.Item>)
            : ''}
          {record.doc_type == 8
            ? requirePerm(
                this,
                `channel_article:${channelId}:live_market:${record.doc_type}:2`
              )(<Menu.Item onClick={() => this.showAnswerGift(record)}>答题有礼</Menu.Item>)
            : ''}
          {record.doc_type == 8
            ? requirePerm(
                this,
                `channel_article:${channelId}:live_propaganda:${record.doc_type}:1`
              )(<Menu.Item onClick={() => this.showLivePlay(record)}>直播广播</Menu.Item>)
            : ''}
          {record.doc_type == 8
            ? requirePerm(
                this,
                `channel_article:${channelId}:live_propaganda:${record.doc_type}:2`
              )(
                <Menu.Item onClick={() => this.showFloatingAdvertisement(record)}>
                  浮窗广告位
                </Menu.Item>
              )
            : ''}

          {record.doc_type > 0 &&
            requirePerm(
              this,
              `channel_article:${channelId}:update_proposal_hidden`
            )(
              <Menu.Item
                onClick={this.toggleRecommend.bind(this, record.id, record.proposal_hidden)}
              >
                {record.proposal_hidden === 0 ? '取消推荐' : '设置推荐'}
              </Menu.Item>
            )}
          {record.doc_type === 5
            ? requirePerm(
                this,
                `subject_recommend:${this.state.channel.id}:list_comment_recommend`
              )(
                <Menu.Item
                  onClick={this.changeRoute.bind(
                    this,
                    `/view/commentRecommend/${channelId}/${this.state.channel.name}/subject:${record.id}`
                  )}
                >
                  两会弹幕
                </Menu.Item>
              )
            : ''}

          {record.pushed_shu_cang_qg >= 0 && record.visible
            ? requirePerm(
                this,
                `channel_article:${channelId}:push_to_qg`
              )(
                <Menu.Item
                  onClick={this.togglePushShuCangStatus.bind(
                    this,
                    record.id,
                    record.pushed_shu_cang_qg
                  )}
                >
                  {record.pushed_shu_cang_qg === 0 ? '推至学习强国' : '取消推学习强国'}
                </Menu.Item>
              )
            : ''}

          {(record.original_id === null ||
            record.original_id === undefined ||
            record.original_id === '' ||
            record.original_id === 0) &&
            (record.doc_type === 2 || record.doc_type === 9) &&
            requirePerm(
              this,
              `channel_article:${channelId}:push_to_qg`
            )(<Menu.Item onClick={() => this.relatedService(record)}>服务和划重点</Menu.Item>)}

          {/* {record.doc_type > 0 &&
            requirePerm(
              this,
              `channel_article:${this.state.channel.id}:rss`
            )(<Menu.Item onClick={this.withoutRevokeNews.bind(this, record)}>外部撤稿</Menu.Item>)} */}

          {record.doc_type < 0 &&
            requirePerm(
              this,
              `channel_article:${channelId}:cancel_release`
            )(
              <Menu.Item
                onClick={this.normalRevokeNews.bind(
                  this,
                  record.id,
                  record.list_title,
                  true,
                  record
                )}
              >
                删除
              </Menu.Item>
            )}
        </Menu>
      );
      return (
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };

    const getXXQGDropDown = (record: any) => {
      const menu = (
        <Menu>
          {requirePerm(
            this,
            `channel_article:${channelId}:sort`
          )(
            <Menu.Item
              disabled={recommendEnabled || !record.visible}
              onClick={this.changeOrder.bind(this, record)}
            >
              排序
            </Menu.Item>
          )}

          {requirePerm(
            this,
            `channel_article:${channelId}:display`
          )(
            <Menu.Item
              disabled={record.fixed_number > 0}
              onClick={this.changeVisible.bind(this, record, record.visible)}
            >
              {record.visible === true ? '隐藏' : '取消隐藏'}
            </Menu.Item>
          )}
          {record.doc_type > 0 &&
            requirePerm(
              this,
              `comment:view:${channelId}`
            )(
              <Menu.Item
                disabled={record.doc_type === DOC_TYPE.SUBJECT.code}
                onClick={this.toCommentSystem.bind(
                  this,
                  record.original_id ? record.original_id : record.id
                )}
              >
                评论运营
              </Menu.Item>
            )}
          {record.doc_type > 0 && (
            <Menu.Item disabled={record.url === ''}>
              <a href={record.url} target="_blank">
                查看链接
              </a>
            </Menu.Item>
          )}
          {record.doc_type > 0 && (
            <Menu.Item disabled={record.url === ''}>
              {record.url === '' ? (
                '复制分享链接'
              ) : (
                <ReactClipboard
                  action="copy"
                  text={record.url}
                  onSuccess={this.copySuccess}
                  onError={this.copyFail}
                >
                  <a>复制分享链接</a>
                </ReactClipboard>
              )}
            </Menu.Item>
          )}
        </Menu>
      );
      return (
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };

    const { sort_by } = this.state.pageInput;
    const { total } = this.props.tableList;
    const isToutiao = this.state.channel.tou_tiao;
    const column = [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any) => (
          <span>
            {requirePerm(
              this,
              `channel_article:${channelId}:sort`
            )(
              <A
                disabled={
                  record.seq === 1 ||
                  // record.fixed_number > 0 ||
                  searchStatus
                }
                className="sort-up"
                onClick={this.exchangeOrder.bind(this, record, record.seq, 1)}
              >
                <Icon type="up-circle" theme="filled" />
              </A>
            )}{' '}
            {requirePerm(
              this,
              `channel_article:${channelId}:sort`
            )(
              <A
                disabled={
                  record.seq === total + this.state.fixedCount ||
                  // record.fixed_number > 0 ||
                  searchStatus
                }
                className="sort-down"
                onClick={this.exchangeOrder.bind(this, record, record.seq, -1)}
              >
                <Icon type="down-circle" theme="filled" />
              </A>
            )}
          </span>
        ),
        width: 60,
        className: 'drag-invisible',
      },
      {
        title: '序号',
        key: 'seq',
        dataIndex: 'seq',
        // render: (text: any, record: any, index: number) =>
        // searchStatus ? (current - 1) * size + index + 1 : text,
        width: 60,
        render: (text: any, record: any) => {
          const max = 101;
          const allowPin = record.seq < max;
          return (
            <>
              <span style={{ marginRight: 8 }}>
                {text}
                {allowPin}
              </span>
              {allowPin && record.fixed_number > 0 && (
                <PermA
                  perm={`channel_article:${channelId}:sort`}
                  disabled={this.state.filter.recommend_enabled === '1'}
                  onClick={() =>
                    this.cancelFix(
                      this.state.channel.code !== 'zhuanti' ? record.id : record.related_article_id,
                      record.list_title
                    )
                  }
                >
                  <Icon type="pushpin" theme="filled" />
                </PermA>
              )}
              {allowPin && record.visible && record.fixed_number == 0 && (
                <PermA
                  perm={`channel_article:${channelId}:sort`}
                  disabled={this.state.filter.recommend_enabled === '1'}
                  onClick={() => this.changeOrderInline(record)}
                >
                  <Icon type="pushpin" />
                </PermA>
              )}
            </>
          );
        },
      },
      {
        title: '基本信息',
        key: 'id',
        dataIndex: 'id',
        width: 160,
        render: (text: any, record: any) => (
          <div>
            潮新闻ID:{record.id} <IdCopy id={record.id}></IdCopy> <br />
            {record.doc_type != -1 && (
              <>
                {this.state.channel.code != 'xxqg' ? '媒立方ID' : '学习强国ID'}:
                {record.doc_type === -1 ? '' : record.metadata_id}{' '}
                <IdCopy id={record.metadata_id}></IdCopy>
                <br />
              </>
            )}
            稿件类型:{resolveNewsType(record.doc_type)}
          </div>
        ),
      },
      // {
      //   title: '媒立方ID',
      //   key: 'metadata_id',
      //   dataIndex: 'metadata_id',
      //   width: 100,
      //   render: (text: any, record: any) => (
      //     <span>{record.doc_type === -1 ? '' : text}</span>
      //   ),
      // },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
        render: (text: any, record: any) => {
          return (
            <div style={{ minWidth: '150px', maxWidth: '400px' }}>
              {record.original == 1 && <Tag color="#1890ff">原创</Tag>}
              {Boolean(record.source_channel_name) && (
                <Tag color="#f1797a">{record.source_channel_name}</Tag>
              )}
              {record.doc_type < 0 && (
                <Tag color="#e99d42">{recommendTypeText(record.ref_type)}</Tag>
              )}
              <a
                style={{
                  pointerEvents: record.doc_type < 0 && !recommendCanEdit ? 'none' : 'auto',
                }}
                onClick={this.handleArticleClick.bind(this, record, 'mlf_detail_url')}
                className={`list-title ${record.visible ? '' : 'hide-title'} ${
                  record.fixed_number > 0 ? 'fixed-title' : ''
                }`}
              >
                {text}
                {record.visible ? '' : '（隐藏）'}
                {record.fixed_number > 0 ? '（固定）' : ''}
              </a>
              {Boolean(record.source_channel_name) && (
                <Popover content={this.sourceChannelInfo(record)}>
                  <img style={{ marginLeft: '8px' }} src="/assets/sourceChannel.png" />
                </Popover>
              )}
            </div>
          );
        },
      },
      ...(this.state.channel.code != 'xxqg'
        ? [
            {
              title: (
                <SortableColumn
                  title="全网传播"
                  sort_by={sort_by}
                  currentSortBy={3}
                  pointerEvents={!this.state.filter.keyword}
                  onChange={this.changeReadCountOrder}
                />
              ),
              key: 'propagation_read_count',
              dataIndex: 'propagation_read_count',
              width: 110,
              align: 'center',
              render: (text: any, record: any) => (
                <span>{Boolean(record.doc_type === -1 || record.doc_type == 8) ? '' : text}</span>
              ),
            },
          ]
        : []),
      {
        // isToutiao ? xx : '阅读数',
        title: (
          <SortableColumn
            title="阅读数"
            sort_by={sort_by}
            currentSortBy={1}
            pointerEvents={!this.state.filter.keyword}
            onChange={this.changeReadCountOrder}
          />
          // <div style={{ display: 'flex', alignItems: 'center', pointerEvents: this.state.filter.keyword ? 'none' : 'auto' }} >
          //   <Icon type="undo"
          //     style={{ transform: 'rotateY(180deg) rotateZ(120deg)', cursor: 'pointer', marginRight: 3 }}
          //     onClick={this.changeReadCountOrder.bind(this, -1)} />
          //   <div style={{ position: 'relative', cursor: 'pointer' }} onClick={() => {
          //     this.changeReadCountOrder(sort_asc === 0 ? 1 : 0)
          //   }}>阅读数
          //     <Icon type="caret-up" style={{ position: 'absolute', color: !this.state.filter.keyword && sort_asc === 1 ? '#1890ff' : '#999' }} />
          //     <Icon type="caret-down" style={{ position: 'absolute', bottom: 0, color: !this.state.filter.keyword && sort_asc === 0 ? '#1890ff' : '#999' }} />
          //   </div>
          // </div>
        ),
        key: 'fake_count',
        dataIndex: 'fake_count',
        width: 120,
        align: 'center',
        render: (text: any, record: any) => <span>{record.doc_type === -1 ? '' : text}</span>,
      },
      ...(showReadingCount
        ? [
            {
              title: (
                <SortableColumn
                  title="真实阅读"
                  sort_by={sort_by}
                  currentSortBy={4}
                  pointerEvents={!this.state.filter.keyword}
                  onChange={this.changeReadCountOrder}
                />
              ),
              key: 'reading_count',
              dataIndex: 'reading_count',
              width: 90,
              align: 'center',
              render: (text: any, record: any) => (
                <span>
                  {record.reading_count > record.fake_count
                    ? record.fake_count
                    : record.reading_count}
                </span>
              ),
            },
          ]
        : []),

      // {
      //   title: isToutiao ? '' : '头条状态',
      //   key: 'top_pushed',
      //   dataIndex: 'top_pushed',
      //   render: (text: any) => (
      //     <span>{isToutiao ? '' : resolveTopPushed(text)}</span>
      //   ),
      //   width: isToutiao ? 0 : 80,
      //   className: 'drag-invisible',
      // },
      // {
      //   title: '类型',
      //   key: 'type',
      //   dataIndex: 'doc_type',
      //   render: (text: any) => <span>{resolveNewsType(text)}</span>,
      //   width: 90,
      // },
      ...(this.state.channel.code != 'xxqg'
        ? [
            {
              title: '推至频道',
              key: 'to_channel_list',
              dataIndex: 'to_channel_list',
              render: (text: any, record: any) => {
                return (
                  <div
                    style={{
                      width: 90,
                      overflow: 'hidden',
                      textAlign: 'left',
                    }}
                  >
                    {record.to_article_list?.map((item: any) => {
                      return (
                        <div className="line-max">{`${item.channel_name}${
                          item.pushed == 2 ? '已用' : '未用'
                        }`}</div>
                      );
                    })}
                    {record.to_channel_count > 0 && (
                      <a onClick={this.togglePushChannelRecordModalState.bind(this, true, record)}>
                        查看详情
                      </a>
                    )}
                  </div>
                );
              },
              width: 80,
              // align: 'center',
            },
            // {
            //   title: '相关专题',
            //   key: 'ref_subjects',
            //   dataIndex: 'ref_subjects',
            //   render: (text: any, record: any) => {
            //     return (
            //       <div
            //         style={{
            //           width: 90,
            //           overflow: 'hidden',
            //           textAlign: 'left',
            //         }}
            //       >
            //         {record.ref_subjects?.map((item: any) => {
            //           return <div className="line-max">{`${item.channel_name}-专题已用`}</div>;
            //         })}
            //         {record.ref_subjects?.length > 0 && (
            //           <a onClick={this.togglePushSubjectRecordModalState.bind(this, true, record)}>
            //             查看详情
            //           </a>
            //         )}
            //       </div>
            //     );
            //   },
            //   width: 80,
            //   // align: 'center',
            // },
          ]
        : []),
      {
        title: '发稿人',
        key: 'creator',
        dataIndex: 'creator',
        width: 90,
      },
      {
        title: (
          <SortableColumn
            title="发布时间"
            sort_by={sort_by}
            currentSortBy={2}
            pointerEvents={!this.state.filter.keyword}
            onChange={this.changeReadCountOrder}
          />
        ),
        key: 'published_at',
        dataIndex: 'published_at',
        render: (text: any, record: any) => (
          <span style={{ cursor: 'pointer' }} onClick={this.showOperateLog.bind(this, record)}>
            {moment(text).format('YYYY-MM-DD HH:mm:ss')}
          </span>
        ),
        width: 95,
      },
      {
        title: '操作',
        // fixed: 'right',
        key: 'op',
        render: (text: any, record: any) =>
          this.state.channel.code == 'xxqg' && record.doc_type != -1
            ? getXXQGDropDown(record)
            : getDropDown(record),
        width: 80,
        className: 'drag-invisible',
        // fixed: 'right',
      },
    ];
    // 根据阅读PV权限过滤菜单
    const showReading = permissions.indexOf('pv:show') === -1;
    if (showReading) {
      return column.filter((v) => {
        return v.key !== 'fake_count' && v.key !== 'propagation_read_count';
      });
    } else {
      return column;
    }
  };

  getFilter = (filter = this.state.filter) => {
    const result: CommonObject = {};
    Object.keys(filter).forEach((k: string) => {
      if (filter[k]) {
        if (k === 'recommend_enabled') {
          result.recommend_enabled = filter[k] === '1';
        } else {
          result[k] = filter[k];
        }
      }
    });
    // if (k === 'sort_asc') {
    //   if (filter[k] >= 0) {
    //     result[k] = filter[k];
    //     result['sort_by'] = 1
    //   }
    //   return
    // }
    if (!result['sort_by']) {
      delete result['sort_asc'];
    }
    return result;
  };

  handleRecommendChange = (value: any) => {
    this.setState(
      {
        filter: {
          ...this.state.filter,
          recommend_enabled: value,
        },
        pageInput: { ...this.state.pageInput, recommend_enabled: value },
      },
      () => this.getData()
    );
  };

  handleTopPushedChange = (value: any) => {
    this.setState(
      {
        filter: {
          ...this.state.filter,
          top_pushed: value,
        },
        pageInput: { ...this.state.pageInput, top_pushed: value },
      },
      () => this.getData()
    );
  };

  handleOriginalChange = (value: any) => {
    this.setState(
      {
        filter: {
          ...this.state.filter,
          original: value,
        },
        pageInput: { ...this.state.pageInput, original: value },
      },
      () => this.getData()
    );
  };

  handleToChannelChange = (value: any) => {
    if (value.length > 5) {
      message.error('最多可选择5个');
      return;
    }
    const to_channel_ids = value.join(',');
    this.setState(
      {
        toChannelCheckedList: value,
        filter: {
          ...this.state.filter,
          to_channel_ids,
          keyword: '',
        },
        pageInput: { ...this.state.pageInput, to_channel_ids, keyword: '' },
      },
      () => this.getData()
    );
  };

  getData = (filter: CommonObject = this.getFilter()) => {
    console.log('filter:', filter);
    this.props.dispatch(getTableList('getReleaseList', 'release_list', filter));
  };

  // 提示 打开新建设置
  openRecommendVideoFormDrawer = () => {
    releaseListApi.getUgcVideoRecommendlist().then((res: any) => {
      const recommendVideo = res.data.recommendVideo[0];
      const channelArticles: any = [];
      if (recommendVideo) {
        if (recommendVideo.video_id) {
          channelArticles.push({
            id: recommendVideo.video_id,
            list_title: recommendVideo.video_title,
            channel_name: '潮客',
          });
        }
        this.setState({
          RecommendVideoForm: {
            ...this.state.RecommendVideoForm,
            show: true,
            data: {
              enabled: !!recommendVideo.enabled,
              video_type: recommendVideo.video_type,
              channelArticles,
            },
          },
        });
      } else {
        this.setState({
          RecommendVideoForm: {
            ...this.state.RecommendVideoForm,
            show: true,
          },
        });
      }
    });
  };

  // 提示关闭
  RecommendVideoFormDrawer = () => {
    this.setState({
      RecommendVideoForm: { ...this.state.RecommendVideoForm, show: false, data: {} },
    });
  };

  // 提示-提交
  RecommendVideoFormSubmitEnd = () => {
    this.RecommendVideoFormDrawer();
    this.getData();
  };
  setHideForm = (val: any) => {
    this.setState(
      {
        HideForm: {
          ...this.state.HideForm,
          visibleShow: false,
        },
      },
      () => this.getData()
    );
  };
  // 直播类型设置
  showLiveType = (record: any) => {
    releaseListApi
      .getLiveType({
        channel_id: record.channel_id,
        article_id: record.original_id > 0 ? record.original_id : record.id,
      })
      .then((res: any) => {
        this.setState({
          LiveTypeForm: {
            visible: true,
            key: Date.now(),
            type: res.data.live_room_type || 0,
            id: record.original_id > 0 ? record.original_id : record.id,
          },
        });
      })
      .catch(() => {});
  };
  // 超级福袋
  showSuperLuckyBag = (record: any) => {
    releaseListApi
      .getLiveList({
        channel_id: record.channel_id,
        article_id: record.original_id > 0 ? record.original_id : record.id,
        type: 1,
      })
      .then((res: any) => {
        this.setState({
          LuckBagForm: {
            visible: true,
            key: Date.now(),
            channel_id: record.channel_id, //channel_id频道id
            article_id: record.original_id > 0 ? record.original_id : record.id,
            luckBagList: res.data.list,
          },
        });
      })
      .catch(() => {});
  };

  // 答题有礼
  showAnswerGift = (record: any) => {
    releaseListApi
      .getLiveList({
        channel_id: record.channel_id,
        article_id: record.original_id > 0 ? record.original_id : record.id,
        type: 2,
      })
      .then((res: any) => {
        this.setState({
          AnswerRewardForm: {
            visible: true,
            key: Date.now(),
            channel_id: record.channel_id, //channel_id频道id
            article_id: record.original_id > 0 ? record.original_id : record.id,
            answerList: res.data.list,
          },
        });
      })
      .catch(() => {});
  };
  // 直播广播
  showLivePlay = (record: any) => {
    releaseListApi
      .getBroadcastList({
        article_id: record.original_id > 0 ? record.original_id : record.id,
        channel_id: record.channel_id,
      })
      .then((res: any) => {
        this.setState({
          LivePlayForm: {
            visible: true,
            key: Date.now(),
            channel_id: record.channel_id,
            article_id: record.original_id > 0 ? record.original_id : record.id,
            id: res.data != undefined ? res.data.live_broadcast.id : null,
            content: res.data != undefined ? res.data.live_broadcast.content : null,
            status: res.data != undefined ? res.data.live_broadcast.status : 0,
          },
        });
      })
      .catch(() => {});
  };
  // 浮窗广告
  showFloatingAdvertisement = (record: any) => {
    releaseListApi
      .getSuspendedAdDetail({
        channel_id: record.channel_id,
        article_id: record.original_id > 0 ? record.original_id : record.id,
      })
      .then((res: any) => {
        this.setState({
          FloatingForm: {
            visible: true,
            key: Date.now(),
            channel_id: record.channel_id,
            article_id: record.original_id > 0 ? record.original_id : record.id,
            id: res.data.result.id,
            shrink_img_url: res.data.result.shrink_img_url,
            expand_img_url: res.data.result.expand_img_url,
            url: res.data.result.url,
            status: res.data.result.status == 1 && res.data.result.status != undefined ? 1 : 0,
          },
        });
      })
      .catch(() => {});
  };

  toggleRecommend = (id: string | number, proposalHidden: number) => {
    const WAIT_TIME = 1000;
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .releaseChangeRecommend({ id, proposal_hidden: proposalHidden === 0 ? 1 : 0 })
      .then(() => {
        message.success('操作成功');
        setTimeout(() => {
          this.getData();
          this.props.dispatch(setConfig({ loading: false }));
        }, WAIT_TIME);
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };

  togglePushShuCangStatus = (id: string | number, status: number) => {
    const WAIT_TIME = 1000;
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .changePushShuCangStatus({ id, status: status === 0 ? 1 : 0 })
      .then(() => {
        message.success('操作成功');
        setTimeout(() => {
          this.getData();
          this.props.dispatch(setConfig({ loading: false }));
        }, WAIT_TIME);
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };

  // 关闭弹窗
  closeLog = () => {
    this.setState({
      LuckBagForm: {
        ...this.state.LuckBagForm,
        visible: false,
      },
    });
  };
  // 关闭弹窗
  closeLog4 = () => {
    this.setState({
      LiveTypeForm: {
        ...this.state.LiveTypeForm,
        visible: false,
      },
    });
  };
  // 弹窗数据提交
  onSubmitEnd4 = () => {
    this.getData();
    this.closeLog4();
  };
  // 弹窗数据提交
  onSubmitEnd = () => {
    this.getData();
    this.closeLog();
  };
  setRef = (ref: 'formRef', instance: any) => {
    this[ref] = instance;
  };
  setRef4 = (ref: 'formRef4', instance: any) => {
    this[ref] = instance;
  };
  // 关闭弹窗
  closeLog1 = () => {
    this.setState({
      AnswerRewardForm: {
        ...this.state.AnswerRewardForm,
        visible: false,
      },
    });
  };
  // 弹窗数据提交
  onSubmitEnd1 = () => {
    this.getData();
    this.closeLog1();
  };
  setRef1 = (ref: 'formRef1', instance: any) => {
    this[ref] = instance;
  };

  // 关闭弹窗
  closeLog2 = () => {
    this.setState({
      LivePlayForm: {
        ...this.state.LivePlayForm,
        visible: false,
      },
    });
  };
  // 弹窗数据提交
  onSubmitEnd2 = () => {
    this.getData();
    this.closeLog2();
  };
  setRef2 = (ref: 'formRef2', instance: any) => {
    this[ref] = instance;
  };

  // 关闭弹窗
  closeLog3 = () => {
    this.setState({
      FloatingForm: {
        ...this.state.FloatingForm,
        visible: false,
      },
      HideForm: {
        ...this.state.HideForm,
        visibleShow: false,
      },
    });
  };
  setRef5 = (ref: 'formRef5', instance: any) => {
    this[ref] = instance;
  };
  // 弹窗数据提交
  onSubmitEnd3 = () => {
    this.getData();
    this.closeLog3();
  };
  setRef3 = (ref: 'formRef3', instance: any) => {
    this[ref] = instance;
  };

  onDragEnd = (oldIndex: number, newIndex: number) => {
    const target = this.props.tableList.records[newIndex];
    const source = this.props.tableList.records[oldIndex];
    const WAIT_TIME = 1000;
    if (target.fixed_number) {
      message.error('当前位置已有固定位置稿件');
      return;
    }
    const body: any = {
      position: target.seq,
      id:
        this.state.channel.code === 'zhuanti' && source.related_article_id
          ? source.related_article_id
          : source.id,
      fixed: false,
    };
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .releaseChangeOrder(body)
      .then(() => {
        message.success('操作成功');
        setTimeout(() => {
          this.getData();
          this.props.dispatch(setConfig({ loading: false }));
        }, WAIT_TIME);
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };

  toggleYaYunBannerDrawer = (show: boolean) => {
    this.setState({ YaYunBannerDrawerShow: show });
  };

  toggleLoopArticleDrawer = (show: boolean) => {
    this.setState({ loopArticleDrawerShow: show });
  };

  showOperateLog = (record: any) => {
    if (
      this.props.session.permissions.indexOf(
        `channel_article:${this.state.channel.id}:view_log`
      ) === -1
    ) {
      return;
    }
    this.setState({
      newOperateLog: {
        visible: true,
        key: Date.now(),
        record: record,
      },
    });
  };

  hideNewOperateLog = () => {
    this.setState({
      newOperateLog: { ...this.state.newOperateLog, visible: false },
    });
  };

  showBatchSort = () => {
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .getReleaseList({ channel_id: searchToObject().channel_id, size: 60, batch_move: 1 })
      .then((res) => {
        this.props.dispatch(setConfig({ loading: false }));
        this.setState({
          ...this.state,
          batchSortDialog: {
            visible: true,
            key: Date.now(),
            data: res.data,
          },
        });
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };

  render() {
    const layout = {
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
    };
    const halfspan = 12;
    const isToutiao = this.state.channel.tou_tiao;
    const bread = ['签发内容管理', this.state.channel.name];
    const searchStatus =
      this.state.filter.keyword ||
      this.state.filter.doc_type ||
      this.state.filter.begin ||
      this.state.filter.top_pushed ||
      this.state.filter.original ||
      this.state.filter.to_channel_ids ||
      this.state.filter.recommend_enabled === '1' ||
      this.state.filter.sort_by > 0;
    const {
      RecommendVideoForm,
      LuckBagForm,
      AnswerRewardForm,
      LivePlayForm,
      FloatingForm,
      LiveTypeForm,
      PushChannelRecordForm,
      PushSubjectRecordForm,
      PushChannelDrawerForm,
      ContentRecommendDrawerForm,
      YaYunBannerDrawerShow,
      loopArticleDrawerShow,
    } = this.state;

    const supportRecommend = getSupportRecommend(
      this.state.channel,
      searchToObject().nav_target_type
    );
    const recommendHideMenuList = getRecommendHideMenuList(supportRecommend);
    return (
      <>
        <Row className="layout-infobar">
          <Col span={19}>
            {requirePerm(
              this,
              `content_recommend:${this.state.channel.id}:focus_detail:37`
            )(
              <Button style={{ marginRight: 8 }} onClick={() => this.toggleLoopArticleDrawer(true)}>
                走马灯管理
              </Button>
            )}
            {requirePerm(
              this,
              `channel_article:${this.state.channel.id}:list_focus`
            )(
              <Button
                style={{ marginRight: 8 }}
                onClick={this.changeRoute.bind(
                  this,
                  `/view/focuslist/${this.state.channel.id}/releaselist`
                )}
              >
                焦点图管理
              </Button>
            )}
            {(this.state.channel.name === '亚运' || searchToObject().nav_target_type == 'event') &&
              requirePerm(
                this,
                `content_recommend:${this.state.channel.id}:focus_detail:36`
              )(
                <Button
                  style={{ marginRight: 8 }}
                  onClick={() => this.toggleYaYunBannerDrawer(true)}
                >
                  赛事焦点图管理
                </Button>
              )}
            {requirePerm(
              this,
              `content_recommend:${this.state.channel.id}:create`
            )(
              <Select
                value="创建"
                style={{ marginRight: 8, width: 120 }}
                onChange={this.handleRecommendCreate}
              >
                <Select.Option value={'all'}>内容推荐位</Select.Option>
                {recommendHideMenuList.map((item: any) => (
                  <Select.Option key={item.code} value={item.code}>
                    {item.name}
                  </Select.Option>
                ))}
              </Select>
            )}
            {requirePerm(
              this,
              `channel_win_recommend:${this.state.channel.id}:view`
            )(
              <Button
                style={{ marginRight: 8 }}
                onClick={this.changeRoute.bind(
                  this,
                  `/view/floatWindow/${this.state.channel.id}/releaselist`
                )}
              >
                浮窗管理
              </Button>
            )}
            {
              <span
                style={{
                  display:
                    this.props.session.permissions.indexOf(
                      `channel_article:${this.state.channel.id}:sort`
                    ) === -1
                      ? 'none'
                      : 'inline',
                  marginRight: 8,
                }}
              >
                拖拽排序&nbsp;
                <Switch
                  checked={this.state.showDragSorter}
                  checkedChildren="开"
                  unCheckedChildren="关"
                  onChange={(checked) => this.setState({ showDragSorter: checked })}
                />
              </span>
            }
            {searchToObject().is_tou_tiao == 1 && (
              <PermButton
                perm={`channel_article:${this.state.channel.id}:sort`}
                type="primary"
                onClick={() => this.showBatchSort()}
              >
                批量排序
              </PermButton>
            )}

            {/* {requirePerm(
              this,
              `ugc_video_recommend:list`
            )(
              <Button
                style={{ marginRight: 8, display: recommend ? 'none' : 'inline-block' }}
                onClick={this.openRecommendVideoFormDrawer}
              >
                潮客视频推荐
              </Button>
            )} */}
          </Col>
          <Col span={5} className="layout-breadcrumb">
            {getCrumb(bread)}
          </Col>
        </Row>

        <div className="component-content news-pages">
          <Row style={{ marginBottom: 16 }}>
            <Col span={halfspan + 4}>
              <DatePicker.RangePicker
                key={`${this.state.filter.begin}-${this.state.filter.end}`}
                style={{ width: 210 }}
                format="YYYY-MM-DD"
                value={
                  this.state.filter.begin
                    ? [moment(this.state.filter.begin), moment(this.state.filter.end)]
                    : []
                }
                open={this.state.openDatePickerPanel}
                onOpenChange={(openDatePickerPanel) => {
                  this.setState({ openDatePickerPanel });
                }}
                onChange={this.handleRangePickerChange}
                ranges={
                  this.state.filter.sort_by > 0
                    ? {
                        当日: [moment(), moment()],
                        近3天: [moment().subtract(2, 'days'), moment()],
                        近一周: [moment().subtract(6, 'days'), moment()],
                      }
                    : {}
                }
              />
              <Select
                value={this.state.filter.doc_type.toString()}
                style={{ width: 100, marginLeft: 8 }}
                onChange={this.handleDocTypeChange}
              >
                <Select.Option value="">稿件类型</Select.Option>
                <Select.Option value={DOC_TYPE.NORMAL.code.toString()}>
                  {DOC_TYPE.NORMAL.name}
                </Select.Option>
                <Select.Option value={DOC_TYPE.SUBJECT.code.toString()}>
                  {DOC_TYPE.SUBJECT.name}
                </Select.Option>
                <Select.Option value={DOC_TYPE.LINK.code.toString()}>
                  {DOC_TYPE.LINK.name}
                </Select.Option>
                <Select.Option value={DOC_TYPE.VIDEO.code.toString()}>
                  {DOC_TYPE.VIDEO.name}
                </Select.Option>
                <Select.Option value={DOC_TYPE.LIVE.code.toString()}>
                  {DOC_TYPE.LIVE.name}
                </Select.Option>
                <Select.Option value={DOC_TYPE.ALBUM.code.toString()}>
                  {DOC_TYPE.ALBUM.name}
                </Select.Option>
                <Select.Option value={DOC_TYPE.Recommend.code.toString()}>
                  {DOC_TYPE.Recommend.name}
                </Select.Option>
              </Select>
              <Select
                value={this.state.filter.recommend_enabled}
                style={{ width: 130, marginLeft: 8 }}
                onChange={this.handleRecommendChange}
              >
                <Select.Option value="2">非推荐位稿件</Select.Option>
                <Select.Option value="1">推荐位稿件</Select.Option>
              </Select>
              {!this.state.channel.tou_tiao &&
                ['推荐', '潮鸣号', '潮客', '学习强国'].indexOf(this.state.channel.name) === -1 && (
                  <Select
                    value={this.state.filter.top_pushed}
                    style={{ width: 100, marginLeft: 8 }}
                    onChange={this.handleTopPushedChange}
                  >
                    <Select.Option value="">头条状态</Select.Option>
                    <Select.Option value="1">头条未用</Select.Option>
                    <Select.Option value="2">头条已用</Select.Option>
                  </Select>
                )}
              {['学习强国'].indexOf(this.state.channel.name) === -1 && (
                <Select
                  value={this.state.filter.original}
                  style={{ width: 100, marginLeft: 8 }}
                  onChange={this.handleOriginalChange}
                >
                  <Select.Option value="">原创类型</Select.Option>
                  <Select.Option value="1">原创</Select.Option>
                  <Select.Option value="0">非原创</Select.Option>
                </Select>
              )}
              {['学习强国'].indexOf(this.state.channel.name) === -1 && (
                <TreeSelect
                  value={this.state.toChannelCheckedList}
                  style={{ width: 210, marginLeft: 8 }}
                  treeData={this.state.toChannelList}
                  dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                  placeholder="推至频道"
                  treeDefaultExpandAll={false}
                  treeCheckable={true}
                  onChange={this.handleToChannelChange}
                  allowClear
                />
              )}
            </Col>
            <Col span={halfspan - 4}>{this.renderSearch()}</Col>
          </Row>
          <Table
            filter={this.getFilter()}
            index="release_list"
            func="getReleaseList"
            columns={this.getColumns()}
            rowKey="id"
            pagination={true}
            draggable={!searchStatus && this.state.showDragSorter}
            onDragEnd={this.onDragEnd}
            getRecordDraggable={(record: IResReleaseListRecordsData) => !record.fixed_number}
            total={this.props.tableList.total + this.state.fixedCount}
            sizeChange={(size: number) => (DataStore.pageSize = size)}
            // tableProps={{
            //   scroll: {
            //     x: true,
            //   },
            // }}
          />
          <Drawer
            visible={RecommendVideoForm.show}
            skey={RecommendVideoForm.key}
            title={RecommendVideoForm.title}
            onClose={this.RecommendVideoFormDrawer}
            onOk={this.handleSubmitForm.bind(this, 'RecommendVideoForm')}
          >
            <RecommendUserVideoForm
              formContent={RecommendVideoForm.data}
              wrappedComponentRef={this.setFormRef.bind(this, 'RecommendVideoForm')}
              onEnd={this.RecommendVideoFormSubmitEnd}
              onClose={this.RecommendVideoFormDrawer}
            />
          </Drawer>
          <Modal
            title="关联活动"
            visible={this.state.ActiviryForm.show}
            key={this.state.ActiviryForm.key}
            width="600px"
            onCancel={() =>
              this.setState({ ActiviryForm: { ...this.state.ActiviryForm, show: false } })
            }
            onOk={this.submitActivity}
          >
            <AForm {...layout}>
              <AForm.Item required={true} label="选择活动">
                {this.state.ActiviryForm.show ? (
                  <Select
                    ref="sel"
                    allowClear={true}
                    placeholder="请选择需要关联的活动"
                    onChange={this.cChange.bind(this)}
                  >
                    {this.state.activityList.map((v: any) => (
                      <Select.Option key={v.id} value={v.id.toString()}>
                        {v.title}
                      </Select.Option>
                    ))}
                  </Select>
                ) : (
                  ''
                )}

                <SelectedActivity
                  columns={this.getActivityColumns(true)}
                  rowKey="id"
                  value={this.state.showActivity}
                />
              </AForm.Item>
            </AForm>
          </Modal>

          <RelatedServiceModal
            channel_article_id={this.state.RelatedServiceForm.articleId}
            visible={this.state.RelatedServiceForm.show}
            key={this.state.RelatedServiceForm.key}
            addApi={releaseListApi.channelArticleRelatedServicesSave}
            onCancel={() => {
              this.setState({
                RelatedServiceForm: {
                  ...this.state.RelatedServiceForm,
                  show: false,
                },
              });
            }}
            onEnd={() => {
              this.setState({
                RelatedServiceForm: {
                  ...this.state.RelatedServiceForm,
                  show: false,
                },
              });
            }}
          ></RelatedServiceModal>

          <Drawer
            visible={LiveTypeForm.visible}
            title={`直播间管理`}
            skey={LiveTypeForm.key}
            onClose={this.closeLog4}
            onOk={() => this.formRef4.doSubmit()}
          >
            <LiveType
              type={LiveTypeForm.type}
              id={LiveTypeForm.id}
              onEnd={this.onSubmitEnd4}
              formContent={LuckBagForm}
              wrappedComponentRef={this.setRef4.bind(this, 'formRef4')}
            />
          </Drawer>
          <Drawer
            visible={LuckBagForm.visible}
            title={`超级福袋`}
            skey={LuckBagForm.key}
            onClose={this.closeLog}
            // onOk={() => this.formRef.doSubmit()}
            closeText={'关闭'}
          >
            <SuperLuckyBagForm
              onEnd={this.onSubmitEnd}
              formContent={LuckBagForm}
              wrappedComponentRef={this.setRef.bind(this, 'formRef')}
            />
          </Drawer>

          <Drawer
            visible={AnswerRewardForm.visible}
            title={`答题有礼`}
            skey={AnswerRewardForm.key}
            onClose={this.closeLog1}
            // onOk={() => this.formRef1.doSubmit()}
            closeText={'关闭'}
          >
            <AnswerGiftForm
              onEnd={this.onSubmitEnd1}
              formContent={AnswerRewardForm}
              wrappedComponentRef={this.setRef1.bind(this, 'formRef1')}
            />
          </Drawer>

          <Drawer
            visible={LivePlayForm.visible}
            title={`直播广播`}
            skey={LivePlayForm.key}
            onClose={this.closeLog2}
            onOk={() => this.formRef2.doSubmit()}
          >
            <LiveForm
              onEnd={this.onSubmitEnd2}
              formContent={LivePlayForm}
              wrappedComponentRef={this.setRef2.bind(this, 'formRef2')}
            />
          </Drawer>

          <Drawer
            visible={FloatingForm.visible}
            title={`浮窗广告`}
            skey={FloatingForm.key}
            onClose={this.closeLog3}
            onOk={() => this.formRef3.doSubmit()}
          >
            <FloatingAdvertisementForm
              onEnd={this.onSubmitEnd3}
              formContent={FloatingForm}
              wrappedComponentRef={this.setRef3.bind(this, 'formRef3')}
            />
          </Drawer>
          <Drawer
            visible={this.state.HideForm.visibleShow}
            title={`隐藏设置`}
            skey={this.state.HideForm.key}
            onClose={this.closeLog3}
            // onOk={this.hideSetOk}
            onOk={() => this.formRef5.doSubmit()}
          >
            <HideForm
              wrappedComponentRef={this.setRef5.bind(this, 'formRef5')}
              onEnd={this.setHideForm}
              HideForm={this.state.HideForm}
            />
          </Drawer>

          <PushChannelRecordModal
            record={PushChannelRecordForm.record}
            visible={PushChannelRecordForm.show}
            onCancel={this.togglePushChannelRecordModalState.bind(this, false, {})}
            skey={PushChannelRecordForm.key}
          />

          <PushSubjectRecordModal
            record={PushSubjectRecordForm.record}
            visible={PushSubjectRecordForm.show}
            onCancel={this.togglePushSubjectRecordModalState.bind(this, false, {})}
            skey={PushSubjectRecordForm.key}
          />

          <PushChannelDrawer
            record={PushChannelDrawerForm.record}
            visible={PushChannelDrawerForm.show}
            onClose={this.togglePushChannelDrawerState.bind(this, false, {})}
            onOk={(id: number, channel_ids: string, complete: () => void) => {
              if (channel_ids.length > 0) {
                this.pushTop(id, channel_ids, (success) => {
                  complete();
                  if (success) {
                    this.togglePushChannelDrawerState(false, {});
                  }
                });
              } else {
                complete();
                this.togglePushChannelDrawerState(false, {});
              }
            }}
            skey={PushChannelDrawerForm.key}
          />

          <ContentRecommendDrawer
            skey={ContentRecommendDrawerForm.key}
            maxPosition={searchStatus ? 999999999999 : this.state.total + this.state.fixedCount}
            record={ContentRecommendDrawerForm.record}
            visible={ContentRecommendDrawerForm.show}
            supportTypes={supportRecommend}
            onEnd={() => {
              this.getData();
              this.toggleContentRecommendDrawerState(false);
            }}
            onClose={() => {
              this.toggleContentRecommendDrawerState(false);
            }}
          />

          <YaYunBannerDrawer
            visible={YaYunBannerDrawerShow}
            onEnd={() => {
              this.toggleYaYunBannerDrawer(false);
            }}
            onClose={() => {
              this.toggleYaYunBannerDrawer(false);
            }}
          />

          <LoopArticleDrawer
            visible={loopArticleDrawerShow}
            onEnd={() => {
              this.toggleLoopArticleDrawer(false);
            }}
            onClose={() => {
              this.toggleLoopArticleDrawer(false);
            }}
          />

          <Modal
            visible={this.state.newOperateLog.visible}
            title="操作日志"
            key={this.state.newOperateLog.key}
            cancelText={null}
            onCancel={this.hideNewOperateLog.bind(this)}
            onOk={this.hideNewOperateLog.bind(this)}
          >
            <ChannelListOperateLogModal
              record={this.state.newOperateLog.record}
              isXXQG={this.state.channel.code == 'xxqg'}
            ></ChannelListOperateLogModal>
          </Modal>

          <BatchSort
            {...this.state.batchSortDialog}
            onCancel={() =>
              this.setState({ ...this.state, batchSortDialog: { visible: false, key: null } })
            }
            onOk={() => {
              this.setState({ ...this.state, batchSortDialog: { visible: false, key: null } });
              this.getData();
            }}
          ></BatchSort>

          {this.renderAltPV()}
        </div>
      </>
    );
  }
}

export default withRouter(
  connect<IResReleaseListRecordsData, IResReleaseListAllData>()(ReleaseList)
);
