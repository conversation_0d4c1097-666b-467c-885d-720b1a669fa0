.news-pages{
  .list-title {
    word-break: break-all;
    vertical-align: bottom;
  }
  .fixed-title {
    color: red
  }
  .hide-title {
    color: #ccc;
  }

}

.timeline-dot, .timeline-dot-big {
  margin-left: 106px!important;
}

.timeline-dot-big:before, .timeline-dot:before{
  margin-left: -106px;
  margin-top: -6px;
  content: attr(data-show);
  text-align: right;
  width: 100px;
  position: absolute;
}

.timeline-dot-big:before{
  font-size: 16px;
}

.news-card-tooltip-content {
  width: 400px;
  .ant-tooltip-content {
    width: 400px!important;
  }
}
.thm_new_title{
  max-height: 24px;
  min-width: 48px;
  padding: 5px 10px ;
  background-color: aquamarine;
  border-radius: 2px;
  margin-right: 5px;
}

.live_drop_down {
  width: 300px;
  ul {
    width: 300px;
    display: flex;
    flex-wrap: wrap;
  
    li {
      width: 150px;
    }
  }  
}