import React, { useState } from 'react';
import {
  SortableContainer,
  SortableElement,
  SortableHandle,
  SortEnd,
  arrayMove,
} from 'react-sortable-hoc';
import { Icon, message, Modal, Spin } from 'antd';
import './batch_sort.scss';
import { releaseListApi } from '@app/api';
import { searchToObject } from '@app/utils/utils';
import { getArticleItem } from './sortArticleItem';

const DragHandle = SortableHandle(() => (
  <div className="drag-handle" style={{ cursor: 'move' }}>
    <Icon type="menu" style={{ color: '#999' }} />
  </div>
));

const SortableItem = SortableElement((props: any) => {
  const article = props.value.article;
  const index = props.value.index;
  const children = getArticleItem(article);

  if (!children) {
    return <div></div>;
  }
  return (
    <div className="bs_sortable_item">
      <div className="bs_sortable_index">{index}</div>
      <DragHandle></DragHandle>
      <div className="bs_sortable_article_wrapper">{children}</div>
    </div>
  );
});
const CustomSortableContainer = SortableContainer((props: any) => <div {...props} />);

export const BatchSort = (props: any) => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState(props.data?.release_dto_list?.slice(0, 60) || []);
  const dragEnd = (sort: SortEnd) => {
    setDataSource(arrayMove([...dataSource], sort.oldIndex, sort.newIndex));
  };

  const handleBatchSort = () => {
    setLoading(true);
    const ids = dataSource.map((item: any, index: number) => {
      return {
        id: item.id,
        position: index + 1,
      };
    });
    const url = `channel_article/batch_move_position?channel_id=${
      searchToObject().channel_id
    }&current_time=${props.data?.current_time}`;
    releaseListApi
      .channelBatchSort(url, ids)
      .then((res: any) => {
        setLoading(false);
        message.success('排序成功');
        props.onOk?.();
      })
      .catch(() => {
        setLoading(false);
      });
  };

  return (
    <Modal
      width={520}
      destroyOnClose
      maskClosable={false}
      visible={props.visible}
      title="批量排序"
      key={props.key}
      onCancel={() => !loading && props?.onCancel?.()}
      onOk={() => !loading && handleBatchSort()}
      confirmLoading={loading}
    >
      <Spin tip="正在加载..." spinning={loading}>
        <p>可对列表Top60内容拖拽排序，内容展示顺序与App一致，<br />隐藏内容不展示。</p>
        <div
          style={{
            position: 'relative',
            height: '500px',
            overflow: 'hidden',
          }}
        >
          <CustomSortableContainer
            style={{ height: '100%', overflow: 'auto' }}
            useDragHandle
            // disableAutoscroll
            helperClass="batch_sort_row_dragging"
            onSortEnd={dragEnd}
          >
            {dataSource.map((item: any, index: number) => {
              return (
                <SortableItem key={`${item.id}-${index}`} index={index} value={{
                  article: item,
                  index: index + 1
                }}></SortableItem>
              );
            })}
          </CustomSortableContainer>
        </div>
      </Spin>
    </Modal>
  );
};

export default BatchSort;
