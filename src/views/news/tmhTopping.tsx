import { getTableList } from '@action/tableList';
import { releaseListApi as api } from '@app/api';
import { CommonObject } from '@app/types';
import { A, Table, OrderColumn, Drawer, PreviewMCN } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setLoading, setMenu, searchToObject } from '@utils/utils';
import Form from '@components/business/tmhRecommendForm';

import { Button, Col, Divider, Icon, Input, message, DatePicker, Row, Select, Modal } from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import { RangePickerValue } from 'antd/es/date-picker/interface';

@(withRouter as any)
@connect
class tmhTopping extends React.Component<any, any> {
  formRef: any;
  constructor(props: any) {
    super(props);
    this.state = {
      cType: 1,
      cKeyword: '',
      listSelectedKeys: [],
      visible: false,
      channel_id: props.match.params.id,
      preview: {
        visible: false,
        skey: Date.now() + 1,
        data: {}
      },
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    // setMenu(this);
  }

  componentDidUpdate(prevProps: any) {
    if (this.props.tableList.timestamp !== prevProps.tableList.timestamp) {
      this.setState({ listSelectedKeys: [] });
    }
  }

  getData = (overlap: CommonObject = {}, filter = this.getFilter()) => {
    this.props.dispatch(
      getTableList('getTmhToppingList', 'article_list', { ...filter, ...overlap })
    );
  };
  // 列表请求参数
  getFilter = () => {
    const { current, size } = this.props.tableList;
    const { channel_id } = this.state;
    const filters: CommonObject = { current, size, channel_id: channel_id };
    return filters;
  };
  // 排序
  exchangeOrder = (record: any, sort_flag: number) => {
    setLoading(this, true);
    api
      .sortTmhTopping({
        sort_flag,
        id: record.article_top_id,
      })
      .then(() => {
        message.success('操作成功');
        this.getData();
        setLoading(this, false);
      })
      .catch(() => {
        setLoading(this, false);
      });
  };
  getColumns = () => {
    const { current, size, total } = this.props.tableList;
    const { channel_id } = this.state;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) => (
          <span>
            {requirePerm(
              this,
              `article_video_top:${channel_id}:update_sort`
            )(
              <A
                disabled={i === 0}
                className="sort-up"
                onClick={() => this.exchangeOrder(record, 0)}
              >
                <Icon type="up-circle" theme="filled" />
              </A>
            )}{' '}
            {requirePerm(
              this,
              `article_video_top:${channel_id}:update_sort`
            )(
              <A
                disabled={i === total - 1}
                className="sort-down"
                onClick={() => this.exchangeOrder(record, 1)}
              >
                <Icon type="down-circle" theme="filled" />
              </A>
            )}
          </span>
        ),
        width: 80,
      },
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 80,
      },
      {
        title: '潮新闻ID',
        key: 'id',
        dataIndex: 'id',
        width: 100,
      },
      {
        title: '创作者平台ID',
        key: 'metadata_id',
        dataIndex: 'metadata_id',
        width: 115,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
        render: (text: any, record: any) => (
          <>
            <a onClick={this.preview.bind(this, record)} className="list-title">
              {text}
            </a>
          </>
        ),
      },
      {
        title: '作者',
        key: 'creator',
        dataIndex: 'creator',
        width: 150,
      },
      {
        title: '发布时间',
        key: 'published_at',
        dataIndex: 'published_at',
        render: (text: number) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 200,
      },
      {
        title: '操作',
        key: 'op',
        dataIndex: 'article_top_id',
        render: (text: any) => (
          <span>
            {requirePerm(
              this,
              `article_video_top:${channel_id}:cancel`
            )(<A onClick={this.deleteRecord.bind(this, text)}>取消置顶</A>)}
          </span>
        ),
        width: 100,
      },
    ];
  };
  // 预览;
  preview = (record: any) => {
    this.setState({
      preview: {
        visible: true,
        skey: Date.now(),
        data: record
      },
    });
  };
  // 关闭预览
  closePreview = () => {
    this.setState({
      preview: { ...this.state.preview, visible: false },
    });
  };
  // 添加新闻
  addNews = () => {
    this.setState({
      visible: true,
    });
  };
  // 删除新闻
  deleteRecord = (id: number) => {
    Modal.confirm({
      title: '是否确定要将该稿件取消置顶',
      onOk: () => {
        setLoading(this, true);
        api
          .cancelTmhTopping({ id: id })
          .then(() => {
            message.success('操作成功');
            setLoading(this, false);
            this.getData();
          })
          .catch(() => setLoading(this, false));
      },
    });
  };
  // 关闭弹窗
  closeDrawer = () => {
    this.setState({
      visible: false,
    });
  };
  // 提交成功
  submitEnd = () => {
    this.closeDrawer();
    this.getData();
  };
  backToTopic = () => {
    this.props.history.go(-1);
  };
  submitForm = () => {
    this['formRef'].doSubmit();
  };
  setRef = (ref: 'formRef', instance: any) => {
    this[ref] = instance;
  };
  render() {
    const { preview, channel_id } = this.state;
    return (
      <React.Fragment>
        <Row className="layout-infobar">
          <Col span={16}>
            <Button onClick={this.backToTopic} style={{ marginRight: 8 }}>
              <Icon type="left-circle-o" />
              返回
            </Button>
            {requirePerm(
              this,
              `article_video_top:${channel_id}:save`
            )(
              <Button onClick={this.addNews} style={{ marginRight: 8 }}>
                添加新闻
              </Button>
            )}
          </Col>
          <Col span={8} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getTmhToppingList"
            index="article_list"
            filter={this.getFilter()}
            columns={this.getColumns()}
            rowKey="id"
          />
        </div>
        <PreviewMCN {...preview} onClose={this.closePreview} />
        <Drawer
          visible={this.state.visible}
          skey={this.state.key}
          title="添加话题"
          onClose={this.closeDrawer}
          onOk={this.submitForm.bind('form')}
        >
          <Form
            id={channel_id}
            wrappedComponentRef={this.setRef.bind(this, 'formRef')}
            onEnd={this.submitEnd}
          />
        </Drawer>
      </React.Fragment>
    );
  }
}

export default tmhTopping;
