import { CommonResponse, IOperationActionData, IOperationLogRes } from '@app/types';
import { Modal, Tabs, Timeline, Tooltip } from 'antd';
import React, { forwardRef, useEffect, useState } from 'react'
import { releaseListApi } from '@app/api';

const OperateLogList = (props: any, ref: any) => {
  const isRecommend = props.record && props.record.doc_type === -1
  const id = props.type == '1' ? props.record.id : props.record.original_id
  const isXXQG = props.isXXQG ?? false
  const [metaId, setMetaId] = useState(props.type == '1' ? props.record.metadata_id : '')
  const [title, setTitle] = useState(props.type == '1' ? props.record.list_title : '')

  const [logs, setLogs] = useState<any>([])

  useEffect(() => {
    getOperateLog()
  }, [])

  const getOperateLog = () => {
    releaseListApi[isRecommend ? 'getRecommendOperateLog' : 'getOperateLog']({ id })
      .then((r: CommonResponse<IOperationLogRes>) => {
        setLogs(r.data.logs)
        setMetaId(r.data?.channel_article?.metadata_id || '')
        setTitle(r.data?.channel_article?.list_title || '')
      })
      .catch();
  };



  return (
    <div>
      <p>
        新闻ID：{id}&emsp;&emsp;&emsp;&emsp;{isRecommend ? '' : `${isXXQG ? '学习强国' : '媒立方'}ID：${metaId}`}
      </p>
      <p>标题：{title}</p>
      <br />
      <div>
        <Timeline>
          {logs.map((v: any, i: number) => [
            <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
              &nbsp;
            </Timeline.Item>,
            v.actions.map((action: IOperationActionData, index: number) => (
              <Timeline.Item
                className="timeline-dot"
                data-show={action.time}
                key={`time${i}-action${index}`}
              >
                {action.user}&emsp;&emsp;{action.action}&emsp;&emsp;
                {action.opinion && (
                  <Tooltip title={(<p>
                    送审意见：
                    <br />
                    {action.opinion}
                  </p>)}>
                    <a>送审意见</a>
                  </Tooltip>
                )}
              </Timeline.Item>
            )),
          ])}
        </Timeline>
      </div>
    </div>
  )
}

const ChannelListOperateLogModal = (props: any, ref: any) => {

  const callback = (key: any) => {

  }

  return (
    <>
      {!props.record?.original_id && <OperateLogList isXXQG={props.isXXQG} key={"1"} record={props.record} type={'1'}></OperateLogList>}
      {
        !!props.record?.original_id && (<Tabs defaultActiveKey="此稿件日志" onChange={callback}>
          <Tabs.TabPane tab="此稿件日志" key="1">
            <OperateLogList key={"1"} record={props.record} isXXQG={props.isXXQG} type={'1'}></OperateLogList>
          </Tabs.TabPane>
          <Tabs.TabPane tab="源稿件日志" key="2">
            <OperateLogList key={"2"} record={props.record} isXXQG={props.isXXQG} type={'2'}></OperateLogList>
          </Tabs.TabPane>
        </Tabs>)
      }

    </>
  );

}

export default forwardRef<any, any>(ChannelListOperateLogModal)