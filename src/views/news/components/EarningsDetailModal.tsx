import React, { useEffect, useState } from 'react';
import { Modal, Button, Icon, message, Descriptions } from 'antd';
import { releaseListApi } from '@app/api';
import moment from 'moment';

interface EarningsDetailModalProps {
  visible: boolean;
  articleId: string | number;
  onClose: () => void;
  loading?: boolean;
}

interface EarningsDetailData {
  earnings_type: string;
  article_earnings_level: string;
  earnings_money: number;
  article_earnings_state: string;
  earnings_given_time: string;
}

const EarningsDetailModal: React.FC<EarningsDetailModalProps> = ({
  visible,
  articleId,
  onClose,
  loading: externalLoading = false,
}) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<EarningsDetailData | null>(null);

  // 获取收益详情数据
  const fetchEarningsDetail = async () => {
    if (!articleId || articleId === '') return;

    setLoading(true);
    try {
      const response:any = await releaseListApi.getEarningsArticleDetail({
        article_id: articleId,
      });
        setData(response.data);
    } catch (error) {
      console.error('获取收益详情失败：', error);
      setData(null);
    } finally {
      setLoading(false);
    }
  };

  // 弹窗打开时获取数据
  useEffect(() => {
    if (visible && articleId) {
      fetchEarningsDetail();
    }
  }, [visible, articleId]);

  // 弹窗关闭时清空数据
  useEffect(() => {
    if (!visible) {
      setData(null);
    }
  }, [visible]);

  // 格式化收益金额
  const formatMoney = (money: number) => {
    if (typeof money !== 'number') return '-';
    return `${money}元`;
  };

  // 格式化时间
  const formatTime = (time: string) => {
    if (!time) return '-';
    return moment(time).format('YYYY-MM-DD HH:mm:ss');
  };

  return (
    <Modal
      visible={visible}
      title="创作收益详情"
      width={600}
      onCancel={onClose}
      footer={[
        <Button key="close" type="primary" onClick={onClose}>
          确定
        </Button>,
      ]}
      destroyOnClose={true}
    >
      {loading || externalLoading ? (
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Icon type="loading" />
          <div style={{ marginTop: 16 }}>加载中...</div>
        </div>
      ) : data ? (
        <div style={{ padding: '20px 0' }}>
          <Descriptions column={1} bordered>
            <Descriptions.Item label="收益类型">
              {data.earnings_type || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="稿件等级">
              {data.article_earnings_level || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="金额">
              {data.earnings_money}
            </Descriptions.Item>
            <Descriptions.Item label="状态">
              {data.article_earnings_state || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="收益发放时间">
              {formatTime(data.earnings_given_time)}
            </Descriptions.Item>
          </Descriptions>
        </div>
      ) : (
        <div style={{ textAlign: 'center', color: '#999', padding: '40px 0' }}>
          暂无收益详情
        </div>
      )}
    </Modal>
  );
};

export default EarningsDetailModal;
