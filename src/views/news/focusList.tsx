/* eslint-disable array-callback-return */
import { setConfig } from '@app/action/config';
import { getTableList } from '@app/action/tableList';
import { releaseListApi } from '@app/api';
import { CommonObject, IResFocusListRecordsData, IResFocusListAllData } from '@app/types';
import { DOC_TYPE } from '@app/utils/constants';
import A from '@components/common/a';
import Table from '@components/common/table';
import { connectTable as connect } from '@utils/connect';
import { getCrumb, requirePerm, resolveNewsType, resolveTopPushed } from '@utils/utils';
import {
  Button,
  Col,
  Dropdown,
  Icon,
  Menu,
  message,
  Row,
  Switch,
  Tag,
  Modal,
  InputNumber,
  Tooltip,
} from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import ReactClipboard from 'react-clipboardjs-copy';
import NewsBaseComponent, { INewsBaseProps } from './baseComponent';

import './index.scss';

interface IMatchProps {
  id: string;
  from: string;
}

type Props = INewsBaseProps<IMatchProps, IResFocusListRecordsData, IResFocusListAllData>;

class FocusList extends NewsBaseComponent<
  IMatchProps,
  IResFocusListRecordsData,
  IResFocusListAllData
> {
  constructor(props: Props) {
    super(props);
    const initialFilter = {
      current: 1,
      size: 10,
      search_type: '3',
      keyword: '',
      channel_id: this.props.match.params.id,
    };
    this.state = {
      filter: initialFilter,
      pageInput: initialFilter,
      channel: {
        category_id: 1,
        focus_carousel: false,
        focus_pic_mode: false,
        id: '1',
        mode: 0,
        name: '',
        tou_tiao: false,
        focus_position: 1,
      } as any,
      operateLog: {
        visible: false,
        scId: 0,
        mlfId: 0,
        articleTitle: '',
        logs: [],
        key: Date.now(),
      } as any,
      PVForm: {
        visible: false,
        key: Date.now() + 2,
        article_id: 0,
        type: 0,
        show_pv: 0,
        base_pv: 0,
        min_factor: 0,
        max_factor: 0,
      },
      total: 0,
      onCount: 0,
      displayPos: 1,
      positionId: 1,
    } as any;
  }

  componentDidMount() {
    this.getData();
    const { from, id } = this.props.match.params;
    this.props.dispatch(
      setConfig({ openKeys: this.props.openKeys, selectKeys: [`/view/${from}?channel_id=${id}`] })
    );
  }

  componentDidUpdate(prevProps: Props) {
    if (prevProps.tableList.timestamp === this.props.tableList.timestamp) {
      return;
    }
    if (
      prevProps.tableList.timestamp !== this.props.tableList.timestamp &&
      Boolean(this.props.tableList.allData.focus_list)
    ) {
      const { channel, focus_list } = this.props.tableList.allData;
      let onCount = 0;
      focus_list.records.map((v: any) => {
        if (v.displayed) {
          onCount += 1;
        }
      });
      this.setState({
        channel,
        onCount,
        filter: { ...this.state.filter, current: focus_list.current, size: focus_list.size },
        pageInput: { ...this.state.pageInput, current: focus_list.current, size: focus_list.size },
        total: focus_list.total,
        positionId: this.props.tableList.allData.position_id,
        displayPos: this.props.tableList.allData.channel.focus_position,
      });
    }
  }

  exchangeOrder = (id: number, seq: number, offset: number) => {
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .focusExchangeOrder({ offset, id, current: seq })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };

  changeDisplayed = (id: number, displayed: boolean, record: any) => {
    if (record.visible === false) {
      message.error('隐藏状态的稿件无法设置成上架！');
      return;
    }
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .focusChangeDisplayed({ id, displayed: !displayed })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };

  onCarouselChange = (checked: boolean) => {
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .focusChangeCarousel({ id: this.state.channel.id, focus_carousel: checked ? '1' : '0' })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };

  onFocusPicModeChange = (checked: boolean) => {
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .focusChangePicMode({ id: this.state.channel.id, focus_pic_mode: checked ? '1' : '0' })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };

  showPosChangeModal = () => {
    let pos = this.state.displayPos;
    const posChange = (value: number | undefined) => {
      pos = value;
    };
    Modal.confirm({
      title: '修改位置序号',
      content: (
        <div>
          <InputNumber
            placeholder="请输入修改序号"
            min={1}
            onChange={posChange}
            defaultValue={pos}
          />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!pos) {
          message.error('请填写序号');
          return;
        }
        releaseListApi
          .updateFocusPosition({
            id: this.state.channel.id,
            position: pos,
          })
          .then(() => {
            message.success('操作成功');
            this.getData();
            closeFunc();
          });
      },
    });
  };

  changeVisible = (id: string | number, visible: boolean) => {
    const WAIT_TIME = 1000;
    this.props.dispatch(setConfig({ loading: true }));
    releaseListApi
      .releaseChangeVisible({ id, visible: visible ? 0 : 1 })
      .then(() => {
        message.success('操作成功');
        setTimeout(() => {
          this.getData();
          this.props.dispatch(setConfig({ loading: false }));
        }, WAIT_TIME);
      })
      .catch(() => {
        this.props.dispatch(setConfig({ loading: false }));
      });
  };

  getColumns = () => {
    const channelId = this.state.channel.id;
    const searchStatus = Boolean(this.state.filter.keyword);
    const getDropDown = (record: any) => {
      const menu = (
        <Menu>
          {requirePerm(
            this,
            `channel_article:${channelId}:edit`
          )(
            <Menu.Item
              onClick={this.toMlf.bind(
                this,
                record.channel_article_id,
                'mlf_edit_url',
                record,
                channelId
              )}
            >
              编辑
            </Menu.Item>
          )}
          {requirePerm(
            this,
            `channel_article:${channelId}:update_displayed`
          )(
            <Menu.Item
              onClick={this.changeDisplayed.bind(this, record.id, record.displayed, record)}
            >
              {record.displayed ? '下架' : '上架'}
            </Menu.Item>
          )}
          {requirePerm(
            this,
            `channel_article:${channelId}:display`
          )(
            <Menu.Item
              disabled={record.visible === true}
              onClick={this.changeVisible.bind(this, record.channel_article_id, record.visible)}
            >
              {record.visible === true ? '隐藏' : '取消隐藏'}
            </Menu.Item>
          )}
          {requirePerm(
            this,
            `comment:view:${channelId}`
          )(
            <Menu.Item
              disabled={record.doc_type === DOC_TYPE.SUBJECT.code}
              onClick={this.toCommentSystem.bind(
                this,
                record.original_id ? record.original_id : record.channel_article_id
              )}
            >
              评论运营
            </Menu.Item>
          )}
          {requirePerm(
            this,
            `channel_article:${channelId}:cancel_release`
          )(
            <Menu.Item
              onClick={this.revokeNews.bind(this, record.channel_article_id, record.title, record)}
            >
              取消签发
            </Menu.Item>
          )}
          <Menu.Item disabled={record.url === ''}>
            <a href={record.url} target="_blank">
              查看链接
            </a>
          </Menu.Item>
          <Menu.Item disabled={record.url === ''}>
            {record.url === '' ? (
              '复制分享链接'
            ) : (
              <ReactClipboard
                action="copy"
                text={record.url}
                onSuccess={this.copySuccess}
                onError={this.copyFail}
              >
                <a>复制分享链接</a>
              </ReactClipboard>
            )}
          </Menu.Item>
        </Menu>
      );
      return (
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };

    const getSeq = (i: number) => (this.state.filter.current - 1) * this.state.filter.size + i + 1;

    const column = [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) => (
          <span>
            {requirePerm(
              this,
              `channel_article:${channelId}:exchange_order`
            )(
              <A
                disabled={getSeq(i) === 1 || getSeq(i) > this.state.onCount || searchStatus}
                className="sort-up"
                onClick={this.exchangeOrder.bind(this, record.id, getSeq(i), 1)}
              >
                <Icon type="up-circle" theme="filled" />
              </A>
            )}{' '}
            {requirePerm(
              this,
              `channel_article:${channelId}:exchange_order`
            )(
              <A
                disabled={
                  getSeq(i) === this.state.onCount || getSeq(i) > this.state.onCount || searchStatus
                }
                className="sort-down"
                onClick={this.exchangeOrder.bind(this, record.id, getSeq(i), -1)}
              >
                <Icon type="down-circle" theme="filled" />
              </A>
            )}
          </span>
        ),
        width: 70,
      },
      {
        title: '潮新闻ID',
        key: 'article_id',
        dataIndex: 'channel_article_id',
        width: 100,
      },
      {
        title: '媒立方ID',
        key: 'metadata_id',
        dataIndex: 'metadata_id',
        width: 100,
      },
      {
        title: '新闻标题',
        key: 'title',
        dataIndex: 'title',
        render: (text: any, record: any) => (
          <span>
            {Boolean(record.source_channel_name) && (
              <Tag color="#f1797a">{record.source_channel_name}</Tag>
            )}
            <a
              onClick={this.toMlf.bind(this, record.channel_article_id, 'mlf_detail_url')}
              className={`list-title ${record.visible ? '' : 'hide-title'}`}
            >
              {text}
              {record.visible ? '' : '（隐藏）'}
            </a>
          </span>
        ),
      },
      // {
      //   title: this.state.channel.tou_tiao ? '' : '头条状态',
      //   key: 'top_pushed',
      //   dataIndex: 'top_pushed',
      //   render: (text: any) => <span>{resolveTopPushed(text)}</span>,
      //   width: 90,
      // },
      {
        title: '全网传播数',
        key: 'propagation_read_count',
        dataIndex: 'propagation_read_count',
        width: 100,
        align: 'center',
        render: (text: any, record: any) =>
          Boolean(record.doc_type === -1 || record.doc_type == 8) ? '' : text,
      },
      {
        title: '阅读数',
        key: 'fake_count',
        dataIndex: 'fake_count',
        align: 'center',
        width: 90,
      },
      {
        title: '类型',
        key: 'type',
        dataIndex: 'doc_type',
        render: (text: any) => <span>{resolveNewsType(text)}</span>,
        width: 70,
      },
      {
        title: '发稿人',
        key: 'creator',
        dataIndex: 'creator',
        width: 90,
      },
      {
        title: '发布时间',
        key: 'published_at',
        dataIndex: 'published_at',
        render: (text: any, record: any) => (
          <span
            onClick={this.getOperateLog.bind(
              this,
              record.channel_article_id,
              record.metadata_id,
              record.title
            )}
          >
            {moment(text).format('YYYY-MM-DD HH:mm:ss')}
          </span>
        ),
        width: 95,
      },
      {
        title: '状态',
        key: 'displayed',
        dataIndex: 'displayed',
        render: (text: any) => <span>{text ? '展示中' : '待展示'}</span>,
        width: 85,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => getDropDown(record),
        width: 70,
      },
    ];

    const { permissions } = this.props.session;
    // 根据阅读PV权限过滤菜单
    const showReading = permissions.indexOf('pv:show') === -1;
    if (showReading) {
      return column.filter((v) => {
        return v.key !== 'fake_count' && v.key !== 'propagation_read_count';
      });
    }
    return column;
  };

  getFilter = (filter = this.state.filter) => {
    const result: CommonObject = {};
    Object.keys(filter).forEach((k: string) => {
      if (filter[k]) {
        result[k] = filter[k];
      }
    });
    return result;
  };

  getData = (filter: CommonObject = this.getFilter()) => {
    this.props.dispatch(getTableList('getFocusList', 'focus_list', filter));
  };

  render() {
    const halfspan = 12;
    const channelName = this.state.channel.name;
    const bread = ['签发内容管理', channelName, '焦点图管理'];
    const hasCarousel = ['公告'].indexOf(channelName) === -1;
    const { from, id } = this.props.match.params;
    const isLive = from === 'live' || channelName === '直播';
    return (
      <>
        <Row className="layout-infobar">
          <Col span={halfspan}>
            <Button
              // onClick={this.changeRoute.bind(
              //   this,
              //   `/view/${from}?channel_id=${id}`
              // )}
              onClick={() => this.props.history.goBack()}
            >
              <Icon type="left-circle" /> 已签发列表
            </Button>
          </Col>
          <Col span={halfspan} className="layout-breadcrumb">
            {getCrumb(bread)}
          </Col>
        </Row>

        <div className="component-content news-pages">
          <Row style={{ marginBottom: 16 }}>
            <Col span={halfspan}>
              {hasCarousel && (
                <Row>
                  自动轮播{' '}
                  {requirePerm(
                    this,
                    `channel:${this.state.channel.id}:carousel`
                  )(
                    <Switch
                      style={{ marginLeft: 8, marginRight: 8 }}
                      checked={this.state.channel.focus_carousel}
                      onChange={this.onCarouselChange}
                    />
                  )}
                  <Tooltip title={'请确保焦点图所有图片比例一致！'}>
                    <Icon type="question-circle" />
                  </Tooltip>
                  <span>大图模式</span>
                  {requirePerm(
                    this,
                    `channel:${this.state.channel.id}:carousel`
                  )(
                    <Switch
                      style={{ marginLeft: 8, marginRight: 8 }}
                      checked={this.state.channel.focus_pic_mode}
                      onChange={this.onFocusPicModeChange}
                    />
                  )}
                  <span style={{ marginLeft: 15 }}>
                    展示位置：焦点图显示在走马灯稿件下方。
                    <Tooltip title={<img src="/assets/banner_tip.png" width={150} height={135} />}>
                      <Icon type="question-circle" />
                    </Tooltip>
                  </span>
                </Row>
              )}
            </Col>
            <Col span={halfspan}>{this.renderSearch()}</Col>
          </Row>
          <Table
            filter={this.getFilter()}
            index="focus_list"
            func="getFocusList"
            columns={this.getColumns()}
            rowKey="id"
            pagination={true}
          />
          {this.renderOperateLog()}
        </div>
      </>
    );
  }
}

export default withRouter(connect<IResFocusListRecordsData, IResFocusListAllData>()(FocusList));
