import { <PERSON><PERSON>, Col, Divider, Icon, Input, Modal, Row, Select, Tooltip, message } from "antd"
import { getCrumb } from "@app/utils/utils"
import { useHistory } from 'react-router';
import { useSelector, useDispatch } from 'react-redux';
import { getTableList } from '@app/action/tableList';
import { A, Table } from '@components/common';
import React, { useEffect, useState } from "react"
import AddCircleAuthorityDrawer from "./component/AddCircleAuthorityDrawer";
import { communityApi as api } from "@app/api";
import { PermA, PermButton } from "@app/components/permItems";
import { setConfig } from "@app/action/config";
import moment from "moment";

export default function CircleAuthorityMgr(props: any) {
  const dispatch = useDispatch();
  const history = useHistory()
  const { total, current, size, records = [] } = useSelector((state: any) => state.tableList)
  const [filter, setFilter] = useState({
    circle_id: '',
    search_type: 1,
    keyword: ''
  })
  const [searchState, setSearchState] = useState({
    search_type: 1,
    keyword: ''
  })
  const [circleList, setCircleList] = useState<any>([])
  const [authorityDrawerVisiable, setAuthorityDrawerVisiable] = useState(false)
  const [editRecord, setEditRecord] = useState(null)
  const getSeq = (i: number) => (current - 1) * size + i + 1;
  const columns: any = [
    {
      title: '序号',
      key: 'id',
      render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
      width: 90,
    },
    {
      title: '8531账号',
      key: 'user_name',
      dataIndex: 'user_name',
      width: 120,
    },
    {
      title: '真实姓名',
      key: 'name',
      dataIndex: 'name',
      width: 120,
    },
    {
      title: '审核范围',
      key: 'circle_list',
      render: (_: any, record: any) => record.circle_list ? record.circle_list.map((item: any) => item.name).join(',') : ''
    },
    {
      title: '最后操作时间',
      key: 'updated_at',
      dataIndex: 'updated_at',
      render: (text: number) => moment(text).format('YYYY-MM-DD HH:mm'),
      width: 150,
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any) => (
        <span>
          <PermA perm="ugc_permission:edit" onClick={() => handleEditAuthorityBlock(record)}>编辑</PermA>
          <Divider type="vertical" />
          <PermA perm="ugc_permission:edit" onClick={() => handleDelete(record)}>删除</PermA>
        </span>
      ),
      width: 120,
    },
  ]


  const handleEditAuthorityBlock = (record: any) => {
    setEditRecord(record)
    setAuthorityDrawerVisiable(true)
  }

  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '确定删除该账号及对应权限？',
      onOk: () => {
        dispatch(setConfig({ loading: true }))
        api.editUGCPermission({ user_name: record.user_name })
          .then(() => {
            message.success('操作成功')
            dispatch(setConfig({ loading: false }))
            getData()
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }))
          })
      },
    });
  }

  const changeFilter = (key: string, val: any, goToFirstPage = false) => {
    const newFilter = {
      ...filter,
      [key]: val
    }
    setFilter(newFilter)
    getData(goToFirstPage, newFilter)
  }

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      const newFilter = {
        ...filter,
        ...searchState
      }
      setFilter(newFilter)
      getData(true, newFilter)
    }
  };

  const getData = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current
    dispatch(getTableList('getUGCPermissionList', 'list', { current: cur, size, ...newFilter }));
  }

  const getCircleList = () => {
    api.getCircleList({ current: 1, size: 100 })
      .then((res) => {
        const { list = [] } = res.data as any
        setCircleList(list.records)
      }).catch(() => {

      })
  }

  const submitEnd = (goToFirstPage: boolean) => {
    getData(goToFirstPage)
    setAuthorityDrawerVisiable(false)
  }

  useEffect(() => {
    const { selectKeys, openKeys } = props
    dispatch(
      setConfig({ selectKeys, openKeys })
    );
    getData(true)
    // 获取圈子列表
    getCircleList()
  }, [])

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <>
            <PermButton perm="ugc_permission:edit" onClick={() => handleEditAuthorityBlock(null)}>添加账号权限</PermButton>&nbsp;
            <Tooltip title="配置后台编辑账号的内容审核范围，只能查看并审核指定圈子的内容" placement="top">
              <Icon type="question-circle" />
            </Tooltip>
          </>
          {/* <Button
            style={{ marginLeft: 12 }}
            onClick={() => {
              window.open('https://mlf3.8531.cn/mediacube/edit-center/content-review')
            }}>前往审核后台</Button> */}
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={14}>
            <Select style={{ width: 110 }} value={filter.circle_id} onChange={(val) => changeFilter('circle_id', val)}>
              <Select.Option value="">审核范围</Select.Option>
              <Select.Option value={-1}>全部内容</Select.Option>
              <Select.Option value={0}>无圈子</Select.Option>
              {circleList.map((item: any) => <Select.Option key={item.id} value={item.id}>{item.name}</Select.Option>)}
            </Select>
          </Col>
          <Col span={10} style={{ textAlign: 'right' }}>
            <Select
              value={searchState.search_type}
              style={{ width: 110, marginRight: 8 }}
              onChange={(search_type: any) => setSearchState({ ...searchState, search_type })}
            >
              <Select.Option value={1}>真实姓名</Select.Option>
              <Select.Option value={2}>8531账号</Select.Option>
            </Select>
            <Input
              value={searchState.keyword}
              style={{ marginRight: 8, width: 160 }}
              onChange={(e: any) => setSearchState({ ...searchState, keyword: e.target.value })}
              onKeyPress={handleKey}
              placeholder="输入搜索内容"
            />
            <Button onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" /> 搜索
            </Button>
          </Col>
        </Row>
        <Table
          func="getUGCPermissionList"
          index="list"
          pagination={true}
          rowKey="id"
          columns={columns}
        />
        <AddCircleAuthorityDrawer
          record={editRecord}
          circleList={circleList}
          visible={authorityDrawerVisiable}
          onClose={() => setAuthorityDrawerVisiable(false)}
          onEnd={submitEnd} />
      </div>
    </>
  )
}