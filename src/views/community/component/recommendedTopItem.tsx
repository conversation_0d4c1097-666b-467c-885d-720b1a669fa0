import React, { useState, useEffect } from 'react'
import {
    Input,
    Tooltip,
    Icon,
} from 'antd';
// @connectSession
// @(Form.create({ name: 'activityForm' }) as any)
export default function RecommendedTopItem(props: any) {
    const getFieldDecorator = props.getFieldDecorator
    const getFieldsValue = props.getFieldsValue
    const setFieldsValue = props.setFieldsValue
    let Form = props.Form
    const [stateList, setStateList] = useState({ ...props.json })
    const [v, setV] = useState(props.v)
    useEffect(() => {
        setStateList({ ...props.json })
        setV(props.v)
    }, [props.json])
    const onChange = () => {

    }
    const onSearch = () => {

    }
    const changeRecommendName = (e: any) => {
        let values = getFieldsValue()
        values.recommend_name = e.target.value
        setFieldsValue({
            ...values
        })
    }
    const getTooltipTitle = () => {
        return (
            <>
                <div>
                    <div>输入说明：</div>
                    <div>直接输入阿拉伯数字代表位置序号，例如要在信息流第8个位置展示，则直接输入“8”即可。</div>
                    <div>最大输入数字为99</div>
                    <div>注：</div>
                    <div>直接在输入的指定位置上显示，不参与内容排序，且优先于内容展示。</div>
                </div>
            </>
        )
    }
    return (
        <>
            <Form.Item label="推荐位名称">
                {getFieldDecorator(`recommend_name`, {
                    initialValue: props.bannerObj.recommend_name,
                    rules: [
                        {
                            required: true,
                            message: '请填写推荐位名称',
                        },
                    ],
                })(<Input style={{ width: 400 }} maxLength={15} onChange={(e) => changeRecommendName(e)} placeholder='请输入名称，不超过15字' />)}
            </Form.Item>
            <Form.Item label="显示位置">
                {getFieldDecorator(`position`, {
                    initialValue: props.bannerObj.position,
                    rules: [
                        {
                            required: true,
                            message: '请输入显示位置',
                        },
                        {
                            pattern: /^[1-9][0-9]{0,1}$/,
                            message: "请输入1-99的数字"
                        }
                    ],
                })(
                    <Input style={{ width: 400 }} placeholder='请输入要显示在列表中的位置序号，1~99' />
                )}
                <Tooltip className='' title={getTooltipTitle()} placement="topRight">
                    <Icon className='ant-tooltip_icon' type="question-circle-o" />
                </Tooltip>
            </Form.Item>
        </>
    )
}
// export default index1
