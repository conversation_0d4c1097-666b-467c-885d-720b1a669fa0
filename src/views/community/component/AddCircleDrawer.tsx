import React, { forwardRef, useEffect, useState } from 'react';
import { Drawer, ImageUploader } from '@components/common';
import { Form, Icon, Input, Switch, Tooltip, message } from 'antd';
import connect from '@utils/connectSession';
import { communityApi as api } from '@app/api';
import { setMLoading } from '@app/utils/utils';
import Radio from 'antd/es/radio';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';

const AddCircleDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const [circleClassList, setCircleClassList] = useState([]);

  const { getFieldDecorator, getFieldsValue, setFieldsValue } = props.form;

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 19 },
  };

  const getCircleClassList = () => {
    api.getCircleClassList({}).then((res: any) => {
      setCircleClassList(res.data?.list || []);
    });
  };

  const handleOkClick = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        dispatch(setConfig({ mLoading: true }));

        const params = { ...values };
        if (props.record) {
          params.id = props.record.id;
        }
        params.content_name = params.content_name ? params.content_name?.trim() : '';
        params.circle_fans_name = params.circle_fans_name ? params.circle_fans_name?.trim() : '';
        params.notify_standard = params.enable_notify_standard
          ? params.notify_standard?.trim()
          : '';

        api
          .editCircle(params)
          .then(() => {
            dispatch(setConfig({ mLoading: false }));
            message.success(props.record ? '修改成功' : '添加成功');
            props.onEnd(false);
          })
          .catch(() => {
            dispatch(setConfig({ mLoading: false }));
          });
      }
    });
  };

  const handleDescKeydown = (e: any) => {
    if (e.key === 'Enter') {
      e.preventDefault();
    }
  };

  useEffect(() => {
    if (props.visible) {
      getCircleClassList();
    }
  }, [props.visible]);

  const {
    name = '',
    logo_url = '',
    background_url = '',
    description = '',
    type = 0,
    show_style = 0,
    select_show_style = 0,
    content_name = '',
    circle_fans_name = '',
    sort_type = 0,
    style_url = '',
    enable_notify_standard = false,
    notify_standard = '',
    show_comment = true,
    select_show_comment = true,
    circle_class_id = 0,
  } = props.record || {};
  return (
    <Drawer
      title={`${props.record ? '编辑' : '新增'}圈子`}
      onClose={props.onClose}
      visible={props.visible}
      onOk={handleOkClick}
      skey={props.key}
    >
      <Form {...formLayout}>
        <Form.Item label="圈子名称">
          {getFieldDecorator('name', {
            initialValue: name,
            rules: [
              {
                required: true,
                message: '请输入圈子名称',
              },
            ],
          })(<Input placeholder="请输入圈子名称，最多15个字" maxLength={15} />)}
        </Form.Item>
        <Form.Item label="圈子头像" extra="支持jpg,jpeg,png,gif图片格式，比例为 1:1">
          {getFieldDecorator('logo_url', {
            initialValue: logo_url,
            rules: [
              {
                required: true,
                message: '请上传圈子头像',
              },
            ],
          })(
            <ImageUploader
              ratio={1 / 1}
              accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
            />
          )}
        </Form.Item>
        <Form.Item label="背景图" extra="支持jpg,jpeg,png图片格式，比例为 25:18">
          {getFieldDecorator('background_url', {
            initialValue: background_url,
          })(<ImageUploader ratio={25 / 18} />)}
        </Form.Item>
        <Form.Item label="圈子简介">
          {getFieldDecorator('description', {
            initialValue: description,
            rules: [
              {
                required: true,
                message: '请输入圈子简介',
              },
            ],
          })(
            <Input.TextArea
              placeholder="请输入圈子简介，最多30字"
              rows={3}
              maxLength={30}
              onKeyDown={handleDescKeydown}
            />
          )}
        </Form.Item>
        <Form.Item label="全部列表样式">
          {getFieldDecorator('show_style', {
            initialValue: show_style,
            rules: [
              {
                required: true,
                message: '请选择展示样式',
              },
            ],
          })(
            <Radio.Group>
              <Radio value={0}>
                单列
                <Tooltip
                  title={<img src="/assets/circle_content_tip0.png" width={220} height={198}></img>}
                >
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
              <Radio value={1}>
                双列
                <Tooltip
                  title={<img src="/assets/circle_content_tip1.png" width={220} height={199}></img>}
                >
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
              <Radio value={2}>
                极简
                <Tooltip
                  title={<img src="/assets/circle_content_tip2.png" width={220} height={200}></img>}
                >
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {getFieldsValue().show_style == 0 && (
          <Form.Item label="外显跟帖">
            {getFieldDecorator('show_comment', {
              initialValue: show_comment,
              valuePropName: 'checked',
              rules: [
                {
                  required: true,
                  // message: '请选择默认排序',
                },
              ],
            })(<Switch></Switch>)}
            &nbsp;
            <Tooltip title="将第一条热评/最新评论显示在圈子内容列表中">
              <Icon type="question-circle" />
            </Tooltip>
          </Form.Item>
        )}
        <Form.Item label="精选列表样式">
          {getFieldDecorator('select_show_style', {
            initialValue: select_show_style,
            rules: [
              {
                required: true,
                message: '请选择展示样式',
              },
            ],
          })(
            <Radio.Group>
              <Radio value={0}>
                单列
                <Tooltip
                  title={<img src="/assets/circle_content_tip0.png" width={220} height={198}></img>}
                >
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
              <Radio value={1}>
                双列
                <Tooltip
                  title={<img src="/assets/circle_content_tip1.png" width={220} height={199}></img>}
                >
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
              <Radio value={2}>
                极简
                <Tooltip
                  title={<img src="/assets/circle_content_tip2.png" width={220} height={200}></img>}
                >
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item label="圈子分类">
          {getFieldDecorator('circle_class_id', {
            initialValue: circle_class_id,
            rules: [
              {
                required: true,
                message: '请选择圈子分类',
              },
            ],
          })(
            <Radio.Group>
              <Radio value={0}>无分类</Radio>
              {circleClassList.map((item: any) => (
                <Radio key={item.id} value={item.id}>
                  {item.name}
                </Radio>
              ))}
            </Radio.Group>
          )}
        </Form.Item>

        {getFieldsValue().select_show_style == 0 && (
          <Form.Item label="外显跟帖">
            {getFieldDecorator('select_show_comment', {
              initialValue: select_show_comment,
              valuePropName: 'checked',
              rules: [
                {
                  required: true,
                  // message: '请选择默认排序',
                },
              ],
            })(<Switch></Switch>)}
            &nbsp;
            <Tooltip title="将第一条热评/最新评论显示在圈子内容列表中">
              <Icon type="question-circle" />
            </Tooltip>
          </Form.Item>
        )}
        <Form.Item label="内容名称">
          {getFieldDecorator('content_name', {
            initialValue: content_name,
            rules: [
              {
                message: '请输入内容名称',
                whitespace: true,
              },
              {
                max: 4,
                message: '内容名称最多4个字',
              },
            ],
          })(
            <Input placeholder="本圈内容的统一名称，例如作品、帖子等；选填，最多4个字，未自定义时默认显示为“内容”"></Input>
          )}
        </Form.Item>
        <Form.Item label="圈友名称">
          {getFieldDecorator('circle_fans_name', {
            initialValue: circle_fans_name,
            rules: [
              {
                message: '请输入圈友名称',
                whitespace: true,
              },
              {
                max: 4,
                message: '圈友名称最多4个字',
              },
            ],
          })(
            <Input placeholder="加入本圈圈友的统一名称，例如成员、粉丝等；选填，最多4个字，未自定义时默认显示为“圈友”"></Input>
          )}
        </Form.Item>

        <Form.Item label="默认排序">
          {getFieldDecorator('sort_type', {
            initialValue: sort_type,
            rules: [
              {
                required: true,
                message: '请选择默认排序',
              },
            ],
          })(
            <Radio.Group>
              <Radio value={0}>最新</Radio>
              <Radio value={1}>最热</Radio>
            </Radio.Group>
          )}
          <Tooltip
            title={
              <>
                1、最新，按发布时间从新到旧排（支持后台手动调整内容排序、插入推荐位）
                <br />
                2、最热，按累计赞数从多到少排
              </>
            }
          >
            <Icon type="question-circle" />
          </Tooltip>
        </Form.Item>
        <Form.Item
          label="内容卡片样式"
          extra={
            <>
              支持jpg,jpeg,png图片格式，比例为 375:96&nbsp;
              <Tooltip
                title={
                  <>
                    该自定义样式用于潮圈频道首页【圈友正在聊】列表中，让本圈内容更醒目，点击图片可导流至圈子。
                    <br />
                    设计图片时请在底部预留出安全区域，保证与作者信息重叠的部分可看清文字。
                    效果示意：
                    <img width={200} height={172} src="/assets/circle_content_tip.png"></img>
                  </>
                }
              >
                <Icon type="question-circle" />
              </Tooltip>
            </>
          }
        >
          {getFieldDecorator('style_url', {
            initialValue: style_url,
          })(<ImageUploader ratio={375 / 96} />)}
        </Form.Item>
        <Form.Item label="内容规范">
          {getFieldDecorator('enable_notify_standard', {
            initialValue: enable_notify_standard,
            valuePropName: 'checked',
            // rules: [{
            //   required: true,
            //   message: '请选择默认排序',
            // }],
          })(<Switch></Switch>)}
          &nbsp;
          <Tooltip title="打开后，用户首次发布内容到圈子时将出现提示，告知当前圈子可以/不可以发哪些内容">
            <Icon type="question-circle" />
          </Tooltip>
        </Form.Item>
        {getFieldsValue().enable_notify_standard && (
          <Form.Item label="规范说明">
            {getFieldDecorator('notify_standard', {
              initialValue: notify_standard,
              rules: [
                {
                  required: true,
                  message: '请输入规范说明',
                  whitespace: true,
                },
              ],
            })(
              <Input.TextArea
                placeholder="请说明当前圈子可以发布哪些内容、不可以发布哪些内容，支持换行，最多200字"
                rows={3}
                maxLength={200}
              />
            )}
          </Form.Item>
        )}
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'AddCircleDrawer' })(forwardRef<any, any>(AddCircleDrawer));
