import React, { useState, useEffect } from 'react'
import {
    Input,
    Tooltip,
    Icon,
    Select,
    Col,
    message,
} from 'antd';
import _ from 'lodash'
import { communityApi } from '@app/api';
import { ColorSelectButton } from '@app/components/business/ColorSelectModal';
import './community.scss'

const { Option } = Select;

function preview(colorData: any, picUrl: any, nickName: any, reason: any) {
    const { bottom_color } = colorData
    return (
        <div className='user_recommend_preview'>
            <div className='user_recommend_preview_head'>
                <img src={picUrl} />
                <div className='user_recommend_preview_head_mask' style={{ background: `linear-gradient(182deg, ${bottom_color}00 53%, ${bottom_color}3B 70%, ${bottom_color}FF 100%)` }}></div>
            </div>
            <div className='user_recommend_preview_content' style={{ backgroundColor: bottom_color }}>
                <h5>{nickName}</h5>
                <p>{reason}</p>
            </div>
            <div className='user_recommend_review_focus'>关注</div>
        </div>
    )
}

class HeadIcon extends React.Component<any> {
    render(): React.ReactNode {
        const { value = '' } = this.props
        return <div style={{
            position: 'relative',
            width: 100,
            height: 100,
            backgroundColor: '#eee',
            border: '1px dashed #ccc',
            textAlign: 'center'
        }}>
            <p style={{ margin: '28px 0', lineHeight: 1.5 }}>请先输入<br />用户昵称</p>
            {value && <img style={{ position: 'absolute', left: 0, top: 0, width: '100%', height: '100%', objectFit: 'cover' }} src={value} />}
        </div>
    }
}

// @connectSession
// @(Form.create({ name: 'activityForm' }) as any)
export default function CommunityFormItem(props: any) {
    // console.log(props,'props======');
    const getFieldDecorator = props.getFieldDecorator
    const getFieldsValue = props.getFieldsValue
    const setFieldsValue = props.setFieldsValue
    const v = props.v
    const Form = props.Form
    const [stateList, setStateList] = useState({ ...props.json })
    const [optionData, setOptionData]: any = useState([])
    const [refIds, setRefIds]: any = useState([...props.ref_ids])
    useEffect(() => {
        setStateList({ ...props.json })
        // setRefIds(props.ref_ids )
    }, [props.json])
    //选择稿件
    const chooseSelect = (e: any, v: any) => {
        const values = getFieldsValue();
        let optionText = JSON.parse(e)
        let ids = [...props.ref_ids]
        if (ids.findIndex((el: any) => el == optionText.id) >= 0) {
            values['json'][v].nick_name = ''
            values['json'][v].chao_id = ''
            setFieldsValue({
                ...values
            })
            return message.error('该用户已添加推荐');
        }
        if (v < ids.length) {
            ids[v] = optionText.id
        } else {
            ids.push(optionText.id)
        }
        setRefIds([...ids])
        // values['json'][v].nick_name = `${optionText.id} - 【${optionText.channel_name}】- ${optionText.list_title} `
        values['json'][v].nick_name = `${optionText.nick_name}`
        values['json'][v].chao_id = `${optionText.chao_id}`
        values['json'][v].id = `${optionText.id}`
        values['json'][v].image_url = `${optionText.image_url}`
        const json = { ...stateList.json, ...(values.json ? values.json : {}) };
        setFieldsValue({ ...values })
        props.chooseSelectItem(values, ids)
        setStateList({ json })
    }
    const handleSearch = _.debounce((val: any, v: number) => {
        let data = { keyword: val.toString()}
        data.need_head_image = true
        if (!val)
            return
        communityApi.recommendAccount_Search(data).then((res: any) => {
            let records = [...res.data.list]
            setOptionData(records)
        })
    }, 500)
    //更改标题
    const changeManuscript = (e: any, v: number, val: any) => {
        let ref_ids = props.ref_ids
        let id = val.id.trim()
        let ids = ref_ids.findIndex((el: string) => {
            return el == id
        })
        if (ids >= 0) {
            ref_ids.splice(ids, 1)
        }
        setRefIds([...ref_ids])
        const values = getFieldsValue();
        const json = { ...stateList.json, ...(values.json ? values.json : {}) };
        json[v]['chao_id'] = ''
        json[v]['image_url'] = ''
        json[v]['colorInfo'] = {
            color_type: 1,
            auto_color: null,
            bottom_color: null
        }
        props.chooseSelectItem(values, ref_ids)
        setStateList({
            ...json
        })
    }

    const { style, json } = getFieldsValue()
    const { nick_name,chao_id, reason, image_url } = stateList[v] || {}
    const dynamicReason = reason || (json && json[v] ? json[v].reason : '')
console.log('stateList[v]::::',stateList[v])
    return (
        <div className='community'>
            <Col key={v} className='edit_flex' style={{ display: chao_id ? '' : 'none' }}>
                <div className='ant-form-item-label' style={{ minWidth: '16.66667%' }}>
                    <label className="ant-form-item-required">推荐用户</label>
                </div>
                <div className='title_or_change'>
                    <div className='title_show_hide' style={{minWidth:'70%'}}>
                        <Form.Item style={{width:'100%'}}>
                            {getFieldDecorator(`json[${v}].nick_name`, {
                                initialValue: nick_name ? nick_name : '',
                                rules: [
                                    {
                                        required:false,
                                    },
                                ],
                            })(<Input disabled={true} style={{ width: 'calc(100% + 80px)' }} maxLength={15} />)}
                        </Form.Item>
                    </div>
                    <span className='choose_change' style={{}} onClick={(e) => changeManuscript(e, v, stateList[v])}>更改</span>
                </div>
            </Col>
            <Form.Item label="推荐用户" style={{ display: chao_id ? 'none' : '' }} >
                {getFieldDecorator(`json[${v}].chao_id`, {
                    initialValue: chao_id ? chao_id : undefined,
                    rules: [
                        {
                            required: true,
                            message: '请输入用户昵称/小潮号',
                        },
                    ],
                })(
                    <Select style={{ width: '80%' }}
                        onSelect={(e) => chooseSelect(e, v)}
                        placeholder="请输入用户昵称或小潮号"
                        onSearch={(e) => handleSearch(e, v)}
                        defaultActiveFirstOption={false} showSearch>
                        {optionData.map((d: any) => <Option key={d.id} value={JSON.stringify(d)}>{`${d.nick_name} | 小潮号：${d.chao_id} `}</Option>)}
                    </Select>
                )}
            </Form.Item>
            <Form.Item label="推荐理由：" >
                {getFieldDecorator(`json[${v}].reason`, {
                    initialValue: reason,
                    rules: [
                        {
                            required: style === 91,
                            message: '请输入推荐理由，最多15字',
                        },
                    ],
                })(<Input style={{ width: '80%' }} maxLength={15} placeholder="请输入推荐理由，最多15字" />)}
                {style === 0 && <Tooltip className='' title="用于向其他用户介绍该用户。如未填写，认证用户将展示认证信息；未认证用户将展示个性签名；以上信息均无，则显示空白。" placement="topLeft">
                    <Icon className='ant-tooltip_icon' type="question-circle-o" />
                </Tooltip>}
            </Form.Item>

            <div style={{ display: style === 91 ? 'block' : 'none' }}>
                <Form.Item label="头像">
                    {getFieldDecorator(`json[${v}].image_url`, {
                        initialValue: image_url,
                    })(<HeadIcon />)}
                </Form.Item>

                <Form.Item label="背景色" required>
                    {getFieldDecorator(`json[${v}].colorInfo`, {
                        initialValue: stateList[v]?.colorInfo,
                        rules: style === 91 ? [{
                            validator(_: any, { bottom_color }: any, callback: any) {
                                if (bottom_color) {
                                    callback()
                                } else {
                                    callback(new Error('请设置背景色'))
                                }
                            }
                        }] : undefined,
                    })(
                        <ColorSelectButton picUrl={image_url} preview={(colorData: any) => preview(colorData, image_url, chao_id, dynamicReason)} />
                    )}
                </Form.Item>
            </div>

            <Form.Item label="推荐理由：" style={{ display: 'none' }} >
                {getFieldDecorator(`json[${v}].id`, {
                    initialValue: stateList[v]?.id,
                    rules: [
                        {
                            message: '请输入推荐理由，最多15字',
                        },
                    ],
                })(<Input style={{ width: '80%' }} maxLength={15} placeholder="请输入推荐理由，最多15字" />)}
            </Form.Item>
        </div>
    )
}
