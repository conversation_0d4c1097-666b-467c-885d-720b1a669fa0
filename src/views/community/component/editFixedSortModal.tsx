import {
  Button,
  Checkbox,
  Form,
  Icon,
  InputNumber,
  Modal,
  Radio,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { releaseListApi, reportApi } from '@app/api';

const EditFixedSortModal = (props: any, ref: any) => {
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator } = props.form;
  const [init, setInit] = useState<any>({});
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  const formLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        if (values.globalPosition == undefined && values.boardPosition == undefined) {
          message.error('请输入排序值');
          return;
        }
        setLoading(true);
        const parmas = [];

        if (values.globalPosition != undefined) {
          parmas.push({
            circle_id: props.record.circle_id,
            article_id: props.record.article_id,
            board_id: 0,
            position: values.globalPosition,
            fixed: values.globalFixed,
          });
        }

        if (!!props.record?.board_id && values.boardPosition != undefined) {
          parmas.push({
            circle_id: props.record.circle_id,
            article_id: props.record.article_id,
            board_id: props.record.board_id,
            position: values.boardPosition,
            fixed: values.boardFixed,
          });
        }

        releaseListApi
          .editCircleArticleFixDetail({
            data: JSON.stringify(parmas),
          })
          .then((res: any) => {
            message.success('修改成功');
            setLoading(false);
            props.onOk && props.onOk(props.type);
          })
          .catch(() => {
            // message.error('添加失败');
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
        setLoading(false);
      }
    });
  };

  useEffect(() => {
    if (props.visible) {
      const globalFixedNumber =
        props.sort?.find((item: any) => item.board_id == 0)?.fixed_number || 0;
      const globalFixed = globalFixedNumber > 0;
      const globalPosition = globalFixedNumber > 0 ? globalFixedNumber : undefined;

      const boardFixedNumber =
        props.sort?.find((item: any) => item.board_id == props.record?.board_id)?.fixed_number || 0;
      const boardFixed = boardFixedNumber > 0;
      const boardPosition = boardFixedNumber > 0 ? boardFixedNumber : undefined;

      setInit({
        globalFixed,
        globalPosition,
        boardFixed,
        boardPosition,
      });
    }
  }, [props.visible]);

  return (
    <Modal
      width={500}
      visible={props.visible}
      title={'排序'}
      key={props.skey}
      onCancel={() => {
        if (!loading) {
          props.onCancel && props.onCancel();
        }
      }}
      onOk={handleSubmit}
      maskClosable={false}
      destroyOnClose={true}
    >
      <Spin spinning={false}>
        <Form {...formLayout}>
          <h3>在全部列表下</h3>

          <Form.Item label="排序值">
            {getFieldDecorator('globalPosition', {
              initialValue: init.globalPosition,
              // rules: [
              //   {
              //     required: true,
              //     message: '请输入排序值',
              //   },
              // ],
            })(
              <InputNumber
                placeholder="请输入1～100的数字"
                min={1}
                max={100}
                style={{ width: '100%' }}
                precision={0}
              />
            )}
          </Form.Item>
          {props.record?.content_level != 1 && (
            <Form.Item
              label="固定位置"
              extra={
                <>
                  {' '}
                  此选项只对1~100位置有效
                  <br />
                  最多只能固定40条稿件或推荐位
                </>
              }
            >
              {getFieldDecorator('globalFixed', {
                initialValue: init.globalFixed,
                valuePropName: 'checked',
              })(<Checkbox></Checkbox>)}
            </Form.Item>
          )}

          {!!props.record?.board_id && (
            <>
              <h3>在{props.record?.board_name}列表下</h3>

              <Form.Item label="排序值">
                {getFieldDecorator('boardPosition', {
                  initialValue: init.boardPosition,
                  // rules: [
                  //   {
                  //     required: true,
                  //     message: '请输入排序值',
                  //   },
                  // ],
                })(
                  <InputNumber
                    placeholder="请输入1～100的数字"
                    min={1}
                    max={100}
                    style={{ width: '100%' }}
                    precision={0}
                  />
                )}
              </Form.Item>

              {props.record?.content_level != 1 && (
                <Form.Item
                  label="固定位置"
                  extra={
                    <>
                      {' '}
                      此选项只对1~100位置有效
                      <br />
                      最多只能固定40条稿件或推荐位
                    </>
                  }
                >
                  {getFieldDecorator('boardFixed', {
                    initialValue: init.boardFixed,
                    valuePropName: 'checked',
                  })(<Checkbox></Checkbox>)}
                </Form.Item>
              )}
            </>
          )}
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<any>({ name: 'EditFixedSortModal' })(
  forwardRef<any, any>(EditFixedSortModal)
);
