import React, { useState, useEffect } from 'react';
import { Input, Select, Col, message } from 'antd';
import _ from 'lodash';
import { communityApi } from '@app/api';
import './community.scss';
const { Option } = Select;

// @connectSession
// @(Form.create({ name: 'activityForm' }) as any)
export default function CircleFormItem(props: any) {
  // console.log(props, 'props======');
  const getFieldDecorator = props.getFieldDecorator;
  const getFieldsValue = props.getFieldsValue;
  const setFieldsValue = props.setFieldsValue;
  const v = props.v;
  const Form = props.Form;
  const [stateList, setStateList] = useState({ ...props.json });
  const [optionData, setOptionData]: any = useState([]);
  const [refIds, setRefIds]: any = useState([...props.ref_ids]);
  const style = props.form.getFieldValue(`style`);
  useEffect(() => {
    setStateList({ ...props.json });
    // setRefIds(props.ref_ids )
  }, [props.json]);
  //选择稿件
  const chooseSelect = (e: any, v: any) => {
    const values = getFieldsValue();
    let optionText = JSON.parse(e);
    let ids = [...props.ref_ids];
    if (ids.findIndex((el: any) => el == optionText.id) >= 0) {
      values['json'][v].name = '';
      setFieldsValue({
        ...values,
      });
      return message.error('该圈子已添加推荐');
    }
    if (v < ids.length) {
      ids[v] = optionText.id;
    } else {
      ids.push(optionText.id);
    }
    setRefIds([...ids]);
    // values['json'][v].name = `${optionText.id} - 【${optionText.channel_name}】- ${optionText.list_title} `
    values['json'][v].name = `${optionText.name}`;
    values['json'][v].id = `${optionText.id}`;
    const json = { ...stateList.json, ...(values.json ? values.json : {}) };
    setFieldsValue({
      ...values,
    });
    props.chooseSelectItem(values, ids);
    setStateList({
      json,
    });
  };
  const handleSearch = _.debounce((val: any, v: number) => {
    const keyword = val.toString();
    if (!keyword) {
      setOptionData([]);
      return;
    }
    const params = { current: 1, size: 100, enabled: true, search_type: 1, keyword };
    communityApi.getCircleList(params).then((res: any) => {
      const records = [...res.data.list.records];
      setOptionData(records);
    });
  }, 500);
  //更改标题
  const changeManuscript = (e: any, v: number, val: any) => {
    let ref_ids = props.ref_ids;
    let id = val.id;
    let ids = ref_ids.findIndex((el: string) => {
      return el == id;
    });
    if (ids >= 0) {
      ref_ids.splice(ids, 1);
    }
    setRefIds([...ref_ids]);
    const values = getFieldsValue();
    const json = { ...stateList.json, ...(values.json ? values.json : {}) };
    json[v]['name'] = '';
    props.chooseSelectItem(values, ref_ids);
    setStateList({
      ...json,
    });
  };
  return (
    <div className="community">
      <Col key={v} className="edit_flex" style={{ display: `${stateList[v]?.name ? '' : 'none'}` }}>
        <div className="ant-form-item-label" style={{ minWidth: '16.66667%' }}>
          <label className="ant-form-item-required">推荐圈子</label>
        </div>
        <div className="title_or_change">
          <div className="title_show_hide">{stateList[v]?.name}</div> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <span className="choose_change" onClick={(e) => changeManuscript(e, v, stateList[v])}>
            更改
          </span>
        </div>
      </Col>
      <Form.Item label="推荐圈子" style={{ display: `${stateList[v]?.name ? 'none' : ''}` }}>
        {getFieldDecorator(`json[${v}].name`, {
          initialValue: props.json[v]?.name ? props.json[v]?.name : undefined,
          rules: [
            {
              required: true,
              message: '请输入圈子名称',
            },
          ],
        })(
          <Select
            style={{ width: '80%' }}
            showSearch
            onSelect={(e) => chooseSelect(e, v)}
            placeholder="请输入圈子名称"
            onSearch={(e) => handleSearch(e, v)}
            defaultActiveFirstOption={false}
          >
            {optionData.map((d: any) => (
              <Option key={d.id} value={JSON.stringify(d)}>{`${d.name} `}</Option>
            ))}
          </Select>
        )}
      </Form.Item>
      {style == 0 && (
        <Form.Item label="推荐理由：">
          {getFieldDecorator(`json[${v}].reason`, {
            initialValue: stateList[v]?.reason,
            rules: [
              {
                message: '请输入推荐理由，最多15字',
              },
            ],
          })(
            <Input style={{ width: '80%' }} maxLength={15} placeholder="请输入推荐理由，最多15字" />
          )}
        </Form.Item>
      )}
      <Form.Item label="推荐理由：" style={{ display: 'none' }}>
        {getFieldDecorator(`json[${v}].id`, {
          initialValue: stateList[v]?.id,
        })(<Input style={{ width: '80%' }} />)}
      </Form.Item>
    </div>
  );
}
