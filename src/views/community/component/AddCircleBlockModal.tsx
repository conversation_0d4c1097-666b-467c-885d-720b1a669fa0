import React from 'react';
import { Form, message, Modal, Input, Radio, Tooltip, Icon, Switch } from 'antd';
import connect from '@utils/connectSession';
import { communityApi as api } from '@app/api';

@connect
@(Form.create({ name: 'AddCircleBlockModal' }) as any)
export default class AddCircleBlockModal extends React.Component<any, any> {
  state = {
    loading: false,
  };

  handleOkClick = () => {
    const {
      form: { validateFields },
      onEnd,
      record,
    } = this.props;
    validateFields((err: any, values: any) => {
      if (!err) {
        this.setState({ loading: true });
        const params = { ...values, circle_id: this.props.circleId };
        if (record) {
          params.id = record.id;
        }
        api
          .editCircleBoard(params)
          .then(() => {
            this.setState({ loading: false });
            message.success(record ? '修改成功' : '添加成功');
            onEnd(false);
          })
          .catch(() => {
            this.setState({ loading: false });
          });
      }
    });
  };

  render() {
    const {
      record,
      visible,
      onCancel,
      form: { getFieldDecorator },
    } = this.props;
    const dataSrc = record || {};
    const { name = '', show_style = -1, show_comment = true } = dataSrc;
    const formLayout = {
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
    };
    return (
      <Modal
        visible={visible}
        key="AddCircleBlockModal"
        title={`${record ? '编辑' : '添加'}圈子版块`}
        confirmLoading={this.state.loading}
        width="500px"
        maskClosable={false}
        onCancel={onCancel}
        onOk={this.handleOkClick}
        destroyOnClose
      >
        <Form {...formLayout}>
          <Form.Item label="版块名称">
            {getFieldDecorator('name', {
              initialValue: name,
              rules: [
                {
                  required: true,
                  message: '请输入版块名称',
                },
              ],
            })(<Input maxLength={10} placeholder="请输入版块名称，最多10个字" />)}
          </Form.Item>
          <Form.Item label="列表样式">
            {getFieldDecorator('show_style', {
              initialValue: show_style,
              rules: [
                {
                  required: true,
                  message: '请选择展示样式',
                },
              ],
            })(
              <Radio.Group>
                <Radio value={-1}>
                  默认
                  <Tooltip title="跟随「全部」列表的样式">
                    <Icon type="question-circle" />
                  </Tooltip>
                </Radio>
                <Radio value={0}>
                  单列
                  <Tooltip
                    title={
                      <img src="/assets/circle_content_tip0.png" width={220} height={198}></img>
                    }
                  >
                    <Icon type="question-circle" />
                  </Tooltip>
                </Radio>
                <Radio value={1}>
                  双列
                  <Tooltip
                    title={
                      <img src="/assets/circle_content_tip1.png" width={220} height={199}></img>
                    }
                  >
                    <Icon type="question-circle" />
                  </Tooltip>
                </Radio>
                <Radio value={2}>
                  极简
                  <Tooltip
                    title={
                      <img src="/assets/circle_content_tip2.png" width={220} height={200}></img>
                    }
                  >
                    <Icon type="question-circle" />
                  </Tooltip>
                </Radio>
              </Radio.Group>
            )}
          </Form.Item>
          {this.props.form.getFieldsValue().show_style == 0 && (
            <Form.Item label="外显跟帖">
              {getFieldDecorator('show_comment', {
                initialValue: show_comment,
                valuePropName: 'checked',
                rules: [
                  {
                    required: true,
                    // message: '请选择默认排序',
                  },
                ],
              })(<Switch></Switch>)}
              &nbsp;
              <Tooltip title="将第一条热评/最新评论显示在圈子内容列表中">
                <Icon type="question-circle" />
              </Tooltip>
            </Form.Item>
          )}
        </Form>
      </Modal>
    );
  }
}
