import React from "react";
import { Drawer } from '@components/common';
import { Checkbox, Form, Icon, Select, Spin, Tooltip, message } from "antd";
import connect from '@utils/connectSession';
import { communityApi as api } from '@app/api';
import { setMLoading } from "@app/utils/utils";
import Radio from "antd/es/radio";
import _ from "lodash";

@connect
@(Form.create({ name: 'AddCircleAuthorityDrawer' }) as any)
export default class AddCircleAuthorityDrawer extends React.Component<any, any> {

  state = {
    loading: false,
    accountOptions: [],
    circleListOptions: [],
    range: 0,
    detail: { name: '', circle_ids: '', user_name: '' }
  }

  handleOkClick = () => {
    const { form: { validateFields }, onEnd, record } = this.props
    validateFields((err: any, values: any) => {
      if (!err) {
        const { accountInfo, circleList = '' } = values
        let user_name
        let apiKey: 'editUGCPermission' | 'addUGCPermission'
        if (record) {
          user_name = this.state.detail.user_name
          apiKey = 'editUGCPermission'
        } else {
          user_name = accountInfo.split('|')[1]
          apiKey = 'addUGCPermission'
        }
        const circle_ids = this.state.range === 0 ? '-1' : (circleList ? circleList.join(',') : circleList)
        setMLoading(this, true);
        api[apiKey]({ user_name, circle_ids })
          .then(() => {
            setMLoading(this, false);
            message.success(record ? '修改成功' : '添加成功');
            onEnd(false)
          })
          .catch(() => {
            setMLoading(this, false);
          })
      }
    })
  }

  handleAccountSearch = _.debounce((val: any) => {
    if (!val) {
      this.setState({ accountOptions: [] })
      return
    }
    api.searchAdminUser({ keyword: val })
      .then(({ data }) => {
        const { user_list } = data as any
        this.setState({ accountOptions: user_list })
      })
      .catch(() => {

      })
  }, 500)

  componentDidUpdate(prevProps: Readonly<any>, prevState: Readonly<any>, snapshot?: any): void {
    const { circleList, record } = this.props
    if (prevProps.circleList !== circleList) {
      const circleListOptions = [{ label: '无圈子', value: 0 }]
      circleList.forEach((item: any) => {
        circleListOptions.push({ label: item.name + (item.enabled ? '' : '（已下线）'), value: item.id })
      });
      this.setState({
        circleListOptions
      })
    }
    if (!prevProps.visible && this.props.visible) {
      const isEdit = !!record
      this.setState({
        accountOptions: [],
        range: 0,
        loading: isEdit,
        detail: { name: '', circle_ids: '', user_name: '' }
      })
      if (isEdit) {
        api.getUGCPermissionDetail({ user_name: record.user_name })
          .then(({ data }) => {
            const { data: detail } = data as any
            const { name, user_name, circle_list = [{ id: -1 }] } = detail
            this.setState({
              loading: false,
              range: circle_list[0].id === -1 ? 0 : 1,
              detail: {
                name: `${name}（${user_name}）`,
                user_name,
                circle_ids: circle_list[0].id === -1 ? [] : circle_list.map((item: any) => item.id)
              }
            })
          })
          .catch(() => {
            this.setState({
              loading: false
            })
          })
      }
    }
  }

  render() {
    const { record, visible, onClose, form: { getFieldDecorator } } = this.props
    const isEdit = !!record
    const { detail } = this.state
    const formLayout = {
      labelCol: { span: 3 },
      wrapperCol: { span: 19 },
    };
    return <Drawer
      title={`${record ? '编辑' : '新增'}账号权限`}
      onClose={onClose}
      visible={visible}
      onOk={this.handleOkClick}
      skey={'AddCircleAuthorityDrawer'}
    >
      <Spin
        tip="正在加载..."
        spinning={this.state.loading}>
        <Form {...formLayout}>
          <Form.Item label="真实姓名">
            {!isEdit && getFieldDecorator('accountInfo', {
              rules: [{
                required: true,
                message: '请输入真实姓名',
              }],
            })(
              <Select style={{ width: 400, display: record ? 'none' : '' }}
                placeholder="请输入姓名或8531账号"
                onSearch={this.handleAccountSearch}
                showSearch>
                {this.state.accountOptions.map((item: any) => <Select.Option key={item.id} value={`${item.name}|${item.user_name}`}>{item.name}（{item.user_name}）</Select.Option>)}
              </Select>
            )}
            {isEdit && <div>{detail.name}</div>}
          </Form.Item>
          <Form.Item label="审核范围" required>
            <Radio.Group value={this.state.range} onChange={(e) => this.setState({ range: e.target.value })}>
              <Radio value={0}>全部内容&nbsp;<Tooltip title="包括所有属于圈子或无圈子的内容" placement="top">
                <Icon type="question-circle" />
              </Tooltip></Radio>
              <Radio value={1}>部分内容</Radio>
            </Radio.Group>
            {/* style={{ marginLeft: 114 }} */}
            {
              this.state.range === 1 && <div>
                {getFieldDecorator('circleList', {
                  initialValue: detail.circle_ids,
                  rules: [{
                    required: true,
                    message: '请选择审核范围',
                  }],
                })(
                  <Checkbox.Group options={this.state.circleListOptions} />
                )}
              </div>
            }
          </Form.Item>
        </Form>
      </Spin>
    </Drawer>
  }
}