import React, { useState, useEffect } from 'react';
import { Button, Form, Input } from 'antd';
import _ from 'lodash';
import Radio from 'antd/es/radio';
import { ImageUploader, SearchAndInput } from '@components/common';
import NewSearchAndInput from '@components/common/newNewsSearchAndInput';
import moment from 'moment';
import { ColorSelectButton } from '@app/components/business/ColorSelectModal';
import './community.scss';
import RangeInput from '@app/components/common/RangeInput';
import { relative } from 'path';

function preview(colorData: any, picUrl: any, title: any, summary: any) {
  const { bottom_color } = colorData;
  return (
    <div className="generate_recommend3_preview">
      <img src={picUrl} />
      <div
        className="generate_recommend3_preview_mask"
        style={{
          background: `linear-gradient(180deg, ${bottom_color}00 0%, ${bottom_color}FF 100%)`,
        }}
      >
        {title && <h3>{title}</h3>}
        {summary && <p>{summary}</p>}
      </div>
    </div>
  );
}

// @connectSession
// @(Form.create({ name: 'activityForm' }) as any)
export default function AggregationFormItem(props: any) {
  // console.log(props,'props======');
  const { getFieldDecorator, getFieldsValue, setFieldsValue } = props;
  const v = props.v;
  // const Form = props.Form
  const [stateList, setStateList] = useState({ ...props.json });
  const [showCount, setShowCount] = useState(props.json[v]?.articles?.length == 2 ? 1 : 0);
  useEffect(() => {
    const newJson = { ...props.json };
    setStateList(newJson);
    setShowCount(newJson[v]?.articles?.length == 2 ? 1 : 0);
  }, [props.json]);

  const maxLength = showCount == 0 ? 20 : 40;

  const validator = (rule: any, value: any, callback: any) => {
    if (!!value?.list_title?.trim() && !!value?.url?.trim()) {
      if (value?.list_title?.trim()?.length > maxLength) {
        console.log('validator', showCount);
        if (showCount == 0) {
          callback('标题不能超过' + maxLength + '字');
        } else {
          callback('标题不能超过' + maxLength + '字' + ',最好大于20字');
        }
        return;
      }
      callback();
    } else if (!value?.list_title?.trim() && !value?.url?.trim()) {
      callback('内容不能为空');
    } else {
      if (!value?.list_title?.trim()) {
        callback('请输入标题');
      } else {
        callback('请输入跳转链接');
      }
    }
  };
  const handleChangeShowCount = (e: any) => {
    setShowCount(e.target.value);
    setTimeout(() => {
      props.form.validateFields([
        `json[${v}].articles[0]`,
        `json[${v}].articles[1]`,
        `json[${v}].articles[2]`,
      ]);
    }, 0);
  };

  const downMove = (index: number) => {
    const value = getFieldsValue();
    const current = value.json[props.index];
    const currentArtcile = current.articles[index];
    const nextArticle = current.articles[index + 1];
    current.articles[index] = nextArticle;
    current.articles[index + 1] = currentArtcile;
    setFieldsValue({
      ...value,
    });
  };

  const upMove = (index: number) => {
    const value = getFieldsValue();
    const current = value.json[props.index];
    const currentArtcile = current.articles[index];
    const nextArticle = current.articles[index - 1];
    current.articles[index] = nextArticle;
    current.articles[index - 1] = currentArtcile;
    setFieldsValue({
      ...value,
    });
  };

  return (
    <div className="community">
      {/* <Form.Item label="分组名称">
        {getFieldDecorator(`json[${v}].group_name`, {
          initialValue: stateList[v]?.group_name,
          rules: [
            {
              required: true,
              message: '请输入分组名称',
            }, {
              validator: (rule: any, value: any, callback: any) => {
                if (!!value?.trim()) {
                  callback()
                }
                callback('内容不能为空')
              }
            }
          ],
        })(
          <Input style={{ width: '80%' }} placeholder='请输入名称，不超过6个字' maxLength={6} />
        )}
      </Form.Item> */}
      <Form.Item
        key={'selected_pic_url'}
        label="分组选中图标"
        extra={`支持jpg,jpeg,png图片格式，比例为8:7`}
      >
        {getFieldDecorator(`json[${v}].selected_pic_url`, {
          initialValue: stateList[v]?.selected_pic_url,
          rules: [
            {
              required: true,
              message: '请选择图片',
            },
          ],
        })(<ImageUploader ratio={8 / 7} accept={['image/png', 'image/jpeg', 'image/jpg']} />)}
      </Form.Item>
      <Form.Item
        key={'unselected_pic_url'}
        label="分组未选中图标"
        extra={`支持jpg,jpeg,png图片格式，比例为8:7`}
      >
        {getFieldDecorator(`json[${v}].unselected_pic_url`, {
          initialValue: stateList[v]?.unselected_pic_url,
          rules: [
            {
              required: true,
              message: '请选择图片',
            },
          ],
        })(<ImageUploader ratio={8 / 7} accept={['image/png', 'image/jpeg', 'image/jpg']} />)}
      </Form.Item>
      <Form.Item label="分组URL">
        {getFieldDecorator(`json[${v}].group_url`, {
          initialValue: stateList[v]?.group_url,
          rules: [
            {
              pattern: /^https?:\/\//,
              message: '请填写正确的URL地址',
            },
          ],
        })(<Input style={{ width: '80%' }} placeholder="请输入跳转链接" />)}
      </Form.Item>
      <Form.Item label="展示内容数">
        {getFieldDecorator(`json[${v}].show_count`, {
          initialValue: stateList[v]?.articles?.length == 2 ? 1 : 0,
          rules: [
            {
              required: true,
              message: '请选择样式',
            },
          ],
        })(
          <Radio.Group onChange={handleChangeShowCount}>
            <Radio value={0}>3条</Radio>
            <Radio value={1}>2条</Radio>
          </Radio.Group>
        )}
      </Form.Item>
      <Form.Item
        key={1}
        label={
          <span style={{ position: 'relative' }}>
            内容1：
            <div style={{ position: 'absolute', left: -90, top: -14 }}>
              <Button
                // onClick={this.upMove.bind(this, v)}
                className="btn_mar"
                disabled
                type="primary"
                icon="up"
              />
              <Button onClick={() => downMove(0)} className="btn_mar" type="primary" icon="down" />
            </div>
          </span>
        }
      >
        {getFieldDecorator(`json[${v}].articles[${0}]`, {
          initialValue: stateList[v]?.articles?.[0],
          rules: [
            {
              required: true,
              validator: validator,
            },
          ],
        })(
          <RangeInput
            compact={false}
            fisrtInputProps={{
              placeholder:
                showCount == 0
                  ? `请输入标题，不超过${maxLength}字`
                  : `请输入标题，不超过${maxLength}字，最好大于20字`,
            }}
            secondInputProps={{
              placeholder: '请输入跳转链接',
            }}
            first={'list_title'}
            second={'url'}
          ></RangeInput>
        )}
      </Form.Item>
      <Form.Item
        key={2}
        label={
          <span style={{ position: 'relative' }}>
            内容2：
            <div style={{ position: 'absolute', left: -90, top: -14 }}>
              <Button onClick={() => upMove(1)} className="btn_mar" type="primary" icon="up" />
              <Button
                onClick={() => downMove(1)}
                disabled={showCount != 0}
                className="btn_mar"
                type="primary"
                icon="down"
              />
            </div>
          </span>
        }
      >
        {getFieldDecorator(`json[${v}].articles[${1}]`, {
          initialValue: stateList[v]?.articles?.[1],
          rules: [
            {
              required: true,
              validator: validator,
            },
          ],
        })(
          <RangeInput
            compact={false}
            fisrtInputProps={{
              placeholder:
                showCount == 0
                  ? `请输入标题，不超过${maxLength}字`
                  : `请输入标题，不超过${maxLength}字，最好大于20字`,
            }}
            secondInputProps={{
              placeholder: '请输入跳转链接',
            }}
            first={'list_title'}
            second={'url'}
          ></RangeInput>
        )}
      </Form.Item>
      {showCount == 0 && (
        <Form.Item
          key={3}
          label={
            <span style={{ position: 'relative' }}>
              内容3：
              <div style={{ position: 'absolute', left: -90, top: -14 }}>
                <Button onClick={() => upMove(2)} className="btn_mar" type="primary" icon="up" />
                <Button
                  // onClick={this.upMove.bind(this, v)}
                  className="btn_mar"
                  disabled
                  type="primary"
                  icon="down"
                />
              </div>
            </span>
          }
        >
          {getFieldDecorator(`json[${v}].articles[${2}]`, {
            initialValue: stateList[v]?.articles?.[2],
            rules: [
              {
                required: true,
                validator: validator,
              },
            ],
          })(
            <RangeInput
              compact={false}
              fisrtInputProps={{
                placeholder:
                  showCount == 0
                    ? `请输入标题，不超过${maxLength}字`
                    : `请输入标题，不超过${maxLength}字，最好大于20字`,
              }}
              secondInputProps={{
                placeholder: '请输入跳转链接',
              }}
              first={'list_title'}
              second={'url'}
            ></RangeInput>
          )}
        </Form.Item>
      )}
      {/* <Input maxLength={6}></Input> */}
    </div>
  );
}
