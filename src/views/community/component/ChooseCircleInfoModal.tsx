import { But<PERSON>, Mo<PERSON>, Spin } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { communityApi as api } from '@app/api';
import './community.scss';

export default function ChooseCircleInfoModal(props: any) {
  const { visible, onCancel, record, onEnd, idKey = 'article_id' } = props;
  const [okLoading, setOkLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [circleList, setCircleList] = useState([]);
  const [blockList, setBlockList] = useState([]);
  const [selectedCircleId, setSelectedCircleId] = useState<any>(null);
  const [selectedBlockId, setSelectedBlockId] = useState<any>(null);
  const leftListRef = useRef<HTMLUListElement>(null);
  const rightListRef = useRef<HTMLUListElement>(null);

  useEffect(() => {
    if (visible) {
      // 显示请求最新的圈子与版块
      setCircleList([]);
      setSelectedCircleId(null);
      setBlockList([]);
      setSelectedBlockId(null);
      setLoading(true);
      const apiList = [api.getCircleList({ current: 1, size: 100, enabled: true })];
      if (record?.circle_id) {
        apiList.push(
          api.getCircleBoardList({ circle_id: record?.circle_id, current: 1, size: 20 })
        );
      }
      Promise.all(apiList)
        .then(([res1, res2]) => {
          setLoading(false);
          const { list: res1Data } = res1.data as any;
          const { records: list1 = [] } = res1Data;
          setCircleList(list1);
          const { list: list2 = [] } = res2.data as any;
          setBlockList(list2);
          const circleIndex = list1.findIndex((item: any) => item.id === record?.circle_id);
          if (circleIndex >= 0) {
            setSelectedCircleId({
              id: record?.circle_id,
              name: record?.circle_name,
            });
            const leftList = leftListRef.current;
            if (leftList) {
              leftList.scrollTop = 35 * (circleIndex - 1);
            }
          }
          const boardIndex = list2.findIndex((item: any) => item.id === record?.board_id);
          if (boardIndex >= 0) {
            setSelectedBlockId({
              id: record?.board_id,
              name: record?.board_name,
            });
            const rightList = rightListRef.current;
            if (rightList) {
              rightList.scrollTop = 35 * (boardIndex - 1);
            }
          }
        })
        .catch(() => {
          setLoading(false);
        });
    }
  }, [visible]);

  const handleOk = () => {
    onEnd({
      circle_id: selectedCircleId?.id,
      circle_name: selectedCircleId?.name,
      board_id: selectedBlockId?.id,
      board_name: selectedBlockId?.name,
    });
  };

  const handleCircleIdChange = (circle: any) => {
    setSelectedCircleId(circle);
    if (!circle) {
      setBlockList([]);
      setSelectedBlockId(null);
      return;
    }
    setLoading(true);
    api
      .getCircleBoardList({ circle_id: circle.id, current: 1, size: 20 })
      .then((res) => {
        setLoading(false);
        const { list = [] } = res.data as any;
        setBlockList(list);
        setSelectedBlockId(null);
      })
      .catch(() => {
        setLoading(false);
        setBlockList([]);
      });
  };

  const handleBlockIdChange = (block: any) => {
    setSelectedBlockId(block);
  };

  return (
    <Modal
      visible={visible}
      key={props.key}
      title="选择圈子"
      width="500px"
      maskClosable={false}
      onCancel={onCancel}
      destroyOnClose={true}
      bodyStyle={{ height: 200 }}
      footer={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ flex: '1' }}></div>
          <Button key="back" onClick={onCancel}>
            取消
          </Button>
          <Button key="submit" type="primary" loading={okLoading} onClick={handleOk}>
            确定
          </Button>
        </div>
      }
    >
      <Spin tip="正在加载..." spinning={loading}>
        <div className="edit_circle_info">
          <ul ref={leftListRef}>
            <li
              key={0}
              className={!selectedCircleId ? 'selected' : ''}
              onClick={() => handleCircleIdChange(null)}
            >
              不选择圈子
            </li>
            {circleList.map((item: any) => (
              <li
                key={item.id}
                className={selectedCircleId?.id == item.id ? 'selected' : ''}
                onClick={() => handleCircleIdChange(item)}
              >
                {item.name}
              </li>
            ))}
          </ul>
          <ul ref={rightListRef}>
            <li
              key={0}
              className={!selectedBlockId ? 'selected' : ''}
              onClick={() => handleBlockIdChange(null)}
            >
              不选择版块
            </li>
            {blockList.map((item: any) => (
              <li
                key={item.id}
                className={selectedBlockId?.id == item.id ? 'selected' : ''}
                onClick={() => handleBlockIdChange(item)}
              >
                {item.name}
              </li>
            ))}
          </ul>
        </div>
      </Spin>
    </Modal>
  );
}
