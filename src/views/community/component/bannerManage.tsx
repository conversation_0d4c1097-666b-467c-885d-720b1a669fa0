import React, { useMemo, useState, useRef, createRef, forwardRef, useImperativeHandle, useEffect } from 'react'
import { Row, Col, Select, DatePicker, Input, Button, Divider, Icon, Drawer, message, Modal, Tooltip } from 'antd'
import Form, { FormComponentProps } from "antd/lib/form/Form";
import { Table, OrderColumn } from '@components/common';
import { getCrumb, setMenuHook, searchToObject } from '@app/utils/utils';
import { useHistory } from 'react-router-dom';
import { PermA, PermButton } from '@components/permItems';
import { recommendApi, releaseListApi, communityApi } from '@app/api';
import { ImageUploader } from '@components/common';
import { useSelector, useDispatch } from 'react-redux';
import { getTableList } from '@app/action/tableList';
import moment from 'moment';
const { Option } = Select
const { Search } = Input;
const { MonthPicker, RangePicker, WeekPicker } = DatePicker;

interface AddBannerFormProps extends FormComponentProps {
    submit: () => void;
    // articlesChanges: (a :any) => void;
    list: any;
    // position:() => String;
}
type Ref = FormComponentProps;
const AddForm = forwardRef<Ref, AddBannerFormProps>(
    ({ form, list, submit }: AddBannerFormProps, ref) => {
        useImperativeHandle(ref, () => ({
            form
        }));
        const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
            e.preventDefault();
            submit();
        };
        const getToolTip = () => {
            return (<div>名称不会展示在客户端的轮播图上，仅用于后台标识</div>)
        }
        return (
            <Form
                onSubmit={handleSubmit}
                labelCol={{ span: 4 }}
                wrapperCol={{ span: 20 }}
                labelAlign="left"
            >
                <Form.Item label="推荐位名称">
                    {form.getFieldDecorator("title", {
                        initialValue: list.title,
                        rules: [
                            {
                                required: true,
                                message: "请填写推荐位名称"
                            },
                            // {
                            //     pattern: /^[1-9]$/,
                            //     message: "请输入1-9的数字"
                            // }
                        ]
                    })(<Input style={{ width: '40%' }} maxLength={20} placeholder='请输入名称，最多20字' /> )}
                    <Tooltip className='' title={getToolTip()} placement="topLeft">
                        <Icon className='ant-tooltip_icon' type="question-circle-o" style={{ marginLeft: 8 }} />
                    </Tooltip>
                </Form.Item>
                <Form.Item label="上传图片"  extra="支持上传jpg,jpeg,png图片格式">
                    {form.getFieldDecorator("pic_url", {
                        initialValue: list.pic_url,
                        rules: [
                            {
                                required: true,
                                message: "请上传图片"
                            },
                        ]
                    })(
                        <ImageUploader ratio={2 / 1} accept={['image/png', 'image/jpeg', 'image/jpg']} />
                    )}
                </Form.Item>
                <Form.Item label="跳转链接">
                    {form.getFieldDecorator("url", {
                        initialValue: list.url,
                        rules: [
                            {
                                required: true,
                                message: "请输入跳转链接"
                            },
                            {
                                pattern: /^https?:\/\//,
                                message: "请输入正确的链接格式"
                            }
                        ]
                    })(<Input style={{ width: '40%' }} placeholder='请输入跳转链接' />)}
                </Form.Item>
            </Form>
        );
    }
);

const EnhancedForm = Form.create<AddBannerFormProps>()(AddForm);
export default function BannerManage(props: any) {
    const { total, current, size, records = [] ,allData} = useSelector((state: any) => state.tableList)
    const [iconLoading, setIconLoading] = useState(false)
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    const [visibleShow, setVisibleShow] = useState(false)
    const [bannerData, setBannerData] = useState({})
    const [bannerId, setBannerId] = useState('')
    const [drawerTitle, setDrawerTitle] = useState('')
    const [bannerapi, setBannerapi] = useState('recommendBannerCreate' as any)
    const [filter, setFilter] = useState(() => {
        return {
            type: '32',
            size: '10',
            current: '1',
        }
    })
    const formRef = createRef<FormComponentProps>();
    const history = useHistory()
    const dispatch = useDispatch();
    useEffect(() => {
        getData()
    }, [])
    const deleteAd = (id: string | number) => {
        Modal.confirm({
            title: <p>确认删除吗？</p>,
            onOk: () => {
                communityApi
                    .recommendBannerDeleted({ id })
                    .then(() => {
                        message.success('操作成功');
                        getData()
                    })
                    .catch(() => message.error('操作失败'))
            },
        });
    }
    const getData = (data = filter) => {
        dispatch(getTableList('recommendBannerList', 'recommend_list', data));
    };
    const editBanner = (record: any) => {
        setBannerData({ ...record })
        setBannerId(record.id)
        setVisibleShow(true)
        setBannerapi('recommendBannerUpdate')
        setDrawerTitle('编辑轮播图')
    }
    const moveStatus = (record: any ,type: any) => {
        if(allData.on_show_count>=5 && type == '上架') return message.error('最多展示5张轮播图')
        
        let data = {
            id: record.id,
            status: record.status ? '0' : '1'
        }
        communityApi.recommendBannerUpdateStatus(data).then((res: any) => {
            message.success('操作成功')
            getData()
        })
    }
    const getColumns = useMemo(() => {
        return [
            {
                title: '排序',
                key: 'order',
                render: (text: any, record: any, i: number) => {
                    const pos = getSeq(i)
                    return <OrderColumn
                        pos={i + 1}
                        start={1}
                        end={records.length}
                        perm="community:sort"
                        disableUp={!!record.fixed_number || !record.status}
                        disableDown={!!record.fixed_number || !record.status}
                        onUp={() => listSort(record, 0)}
                        onDown={() => listSort(record, 1)}
                    />
                },
                width: 70,
            },
            {
                title: '序号',
                key: 'seq',
                width: 80,
                render: (text: any, record: any, i: number) => (<> <span style={{ marginRight: 8 }}>{getSeq(i)}</span></>)
            },
            {
                title: '名称',
                dataIndex: 'title',
                width: 90,
            },
            {
                title: '图片',
                dataIndex: 'pic_url',
                width: 150,
                render: (text: any) => (<img src={text} className="list-pic" />)
            },
            {
                title: '链接',
                dataIndex: 'url',
            },
            {
                title: '状态',
                dataIndex: 'status',
                width: 90,
                render:(text: any)=>( <span>{text ? '展示中' : '未展示'}</span> )
            },
            {
                title: '创建人',
                dataIndex: 'created_by',
                width: 150,
            },
            {
                title: '最后操作时间',
                dataIndex: 'updated_at',
                render: (text: any) => (<span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>),
                width:200
            },
            {
                title: '操作',
                key: 'op',
                render: (text: any, record: any) => (
                    <>
                        <PermA
                            perm={`type_recommend:32:update`}
                            onClick={() => editBanner(record)}
                            style={{ marginRight: 5 }}
                        >
                            编辑
                        </PermA>
                        <PermA
                            perm={`type_recommend:32:update_status`}
                            onClick={() => moveStatus(record, record.status ? '下架' : '上架')}
                            style={{ marginRight: 5 }}
                        >
                            { record.status ? '下架' : '上架'}
                        </PermA>
                        <PermA
                            perm={`type_recommend:32:delete`}
                            onClick={() => deleteAd(record.id)}
                            style={{ marginRight: 5 }}
                        >
                            删除
                        </PermA>
                    </>
                ),
                width: 140,
            },
        ];
    }, [records]);
    const listSort = (record: any, i: number) => {
        let data = {
            id: record.id,
            sort_flag: i,
        }
        communityApi.recommendBannerSort(data).then((res: any) => {
            message.success('操作成功')
            getData()
        })
    }
    const onClose = () => {
        setVisibleShow(false)
    }
    const onOk = (e: any) => {
        setIconLoading(true)
        formRef.current.form.validateFieldsAndScroll((err: any, values: any) => {
            if (!err) {
                console.log(values, 'values=========');
                let data = {
                    type: '32',
                    id: bannerId,
                    ...values
                }
                communityApi[bannerapi](data).then((res: any) => {
                    console.log(res, 'res===========');
                    message.success('操作成功')
                    setIconLoading(false)
                    setVisibleShow(false)
                    setBannerId('')
                    getData()
                    setBannerapi('')
                })
                //   setconLoading(true)
                //   let data={
                //     recommend_name: props.initialValues?.recommend_detail?.title,
                //     id: props.initialValues.position_id,
                //     ref_ids: selectData.join(),
                //     position: values.position,
                //     channel_id: channelId
                //   }
                //   recommendApi.saveTopList(data).then(res=>{
                //     message.success('操作成功');
                //     props.changeVisible(false)
                //     setconLoading(false)

                //   }).catch(()=>{
                //     setconLoading(false)
                //   })
            } else {
                setIconLoading(false)
                message.error('请检查表单内容');
            }
        });
        // formRef.current.handleSubmit(e)
    }
    //取消提交
    const cancleSubmit = () => {
        setVisibleShow(false)
    }
    //添加推荐用户
    const addRecommendedUser = () => {
        setVisibleShow(true)
        setBannerData({})
        setBannerId('')
        setBannerapi('recommendBannerCreate')
        setDrawerTitle('添加轮播图')
    }
    return (
        <>
            <Row>
                <Col span={12} >
                    <Button onClick={() => history.go(-1)} style={{ margin: '8px 8px 0 0 ' }}>
                        <Icon type="left-circle-o" />
                        返回
                    </Button>
                    <PermButton perm={'type_recommend:32:create'} onClick={() => addRecommendedUser()}>添加轮播图</PermButton>
                </Col>
                <Col span={12} className="layout-breadcrumb">
                    {getCrumb(props.breadCrumb)}
                </Col>
            </Row>
            <div className="component-content">
                <Row>
                    <Col>
                        <Table
                            func="recommendBannerList"
                            index="recommend_list"
                            columns={getColumns}
                            rowKey="id"
                            pagination={true}
                            filter={filter}
                        />
                    </Col>
                </Row>
                <Drawer
                    title={drawerTitle}
                    width={'60%'}
                    maskClosable={false}
                    onClose={onClose}
                    visible={visibleShow}
                    bodyStyle={{ paddingBottom: 80 }}
                    destroyOnClose
                >
                    <div className='box'>
                        <EnhancedForm submit={() => onOk} wrappedComponentRef={formRef} list={bannerData} />
                    </div>
                    <div
                        style={{
                            position: 'absolute',
                            right: 0,
                            bottom: 0,
                            width: '100%',
                            borderTop: '1px solid #e9e9e9',
                            padding: '10px 16px',
                            background: '#fff',
                            textAlign: 'right',
                        }}
                    >
                        <Button onClick={() => cancleSubmit()} style={{ marginRight: 8 }}>
                            取消
                        </Button>
                        <PermButton
                            perm={''}
                            type="primary" loading={iconLoading}
                            onClick={(e) => onOk(e)}
                        >
                            确定
                        </PermButton>
                    </div>
                </Drawer>
            </div>
        </>
    )
}
