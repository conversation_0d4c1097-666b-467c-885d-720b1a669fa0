import React, { useState, useEffect } from 'react';
import { Input } from 'antd';
import _ from 'lodash';
import Radio from 'antd/es/radio';
import { ImageUploader, SearchAndInput } from '@components/common';
import NewSearchAndInput from '@components/common/newNewsSearchAndInput';
import moment from 'moment';
import { ColorSelectButton } from '@app/components/business/ColorSelectModal';
import './community.scss';
import NewImageUploader from '@app/components/common/newImageUploader';
import useSkipFirstEffect from '@app/utils/useSkipFirstEffect';

function preview(colorData: any, picUrl: any, title: any, summary: any) {
  const { bottom_color } = colorData;
  return (
    <div className="generate_recommend3_preview">
      <img src={picUrl} />
      <div
        className="generate_recommend3_preview_mask"
        style={{
          background: `linear-gradient(180deg, ${bottom_color}00 0%, ${bottom_color}FF 100%)`,
        }}
      >
        {title && <h3>{title}</h3>}
        {summary && <p>{summary}</p>}
      </div>
    </div>
  );
}

// @connectSession
// @(Form.create({ name: 'activityForm' }) as any)
export default function GenerateFormItem(props: any) {
  console.log(props, 'props======');
  const getFieldDecorator = props.getFieldDecorator;
  const getFieldsValue = props.getFieldsValue;
  const v = props.v;
  const Form = props.Form;
  const [link_type, setLink_type] = useState(props.json[v].link_type);
  const style: 71 | 72 | 73 = getFieldsValue().style;

  const fixJson = (json: any) => {
    const newJson = { ...json };
    Object.keys(newJson).forEach((key) => {
      if (!newJson[key].pic) {
        newJson[key].pic = json[key].pic_url && {
          url: json[key].pic_url,
          ratio: ({ 72: 1, 73: 3 / 4 } as any)[style],
        };
      }
    });
    return newJson;
  };

  const [stateList, setStateList] = useState({ ...fixJson(props.json) });

  useEffect(() => {
    const newJson = { ...fixJson(props.json) };
    setStateList(newJson);
    setLink_type(newJson[props.v].link_type || 0);
  }, [props.json]);

  useSkipFirstEffect(() => {
    props.form?.validateFields();
  }, [style]);

  const columns1 = [
    { title: '栏目名称', dataIndex: 'name' },
    { title: '创建人', dataIndex: 'created_user_name' },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      render: (text: number) => moment(text).format('YYYY-MM-DD'),
    },
  ];
  const columns2 = [
    {
      title: '潮新闻ID',
      dataIndex: 'id',
      width: 80,
      render: (_: any, v: any) => v.uuid || v.id,
    },
    {
      title: '新闻频道',
      key: 'type',
      dataIndex: 'channel_name',
      width: 90,
    },
    {
      title: '新闻标题',
      key: 'list_title',
      dataIndex: 'list_title',
    },
  ];

  const { json } = getFieldsValue();
  const { title, summary, pic_url } = stateList[v] || {};
  const dynamicTitle = title || (json && json[v] ? json[v].title : '');
  const dynamicSummary = summary || (json && json[v] ? json[v].summary : '');

  const titleLenght = style == 72 ? 10 : 6;
  return (
    <div className="community">
      <Form.Item label="标题">
        {getFieldDecorator(`json[${v}].title`, {
          initialValue: stateList[v]?.title,
          rules: [
            {
              required: true,
              message: '请输入标题',
            },
            {
              max: titleLenght,
              message: `活动标题不能超过${titleLenght}个字`,
            },
          ],
        })(
          <Input
            style={{ width: '80%' }}
            placeholder={`最多可输入${titleLenght}字`}
            maxLength={titleLenght}
          />
        )}
      </Form.Item>
      <Form.Item label="简介" style={{ display: style === 73 ? 'block' : 'none' }}>
        {getFieldDecorator(`json[${v}].summary`, {
          initialValue: stateList[v]?.summary,
        })(<Input style={{ width: '80%' }} placeholder="最多可输入8字" maxLength={8} />)}
      </Form.Item>
      {(style === 72 || style === 73) && (
        <Form.Item
          label="上传图片"
          extra={`支持jpg,jpeg,png图片格式，比例为${({ 72: '1:1', 73: '3:4' } as any)[style]}`}
        >
          {getFieldDecorator(`json[${v}].pic`, {
            initialValue: stateList[v]?.pic,
            rules: [
              {
                required: style === 72 || style === 73,
                message: '请选择图片',
              },
              {
                validator: (rule: any, value: any, callback: any) => {
                  if (
                    value &&
                    !!value.url &&
                    value?.ratio != ({ 72: 1, 73: 3 / 4 } as any)[style]
                  ) {
                    callback('图片比例不正确,请重新上传');
                  } else {
                    callback();
                  }
                  callback();
                },
              },
            ],
          })(
            <NewImageUploader
              ratio={({ 72: 1, 73: 3 / 4 } as any)[style]}
              accept={['image/png', 'image/jpeg', 'image/jpg']}
            />
          )}
        </Form.Item>
      )}
      {style === 73 && (
        <Form.Item label="背景色" required>
          {getFieldDecorator(`json[${v}].colorInfo`, {
            initialValue: stateList[v]?.colorInfo,
            rules:
              style === 73
                ? [
                    {
                      validator(_: any, value: any, callback: any) {
                        if (value?.bottom_color) {
                          callback();
                        } else {
                          callback(new Error('请设置背景色'));
                        }
                      },
                    },
                  ]
                : undefined,
          })(
            <ColorSelectButton
              picUrl={getFieldsValue()?.json?.[v]?.pic?.url}
              preview={(colorData: any) =>
                preview(
                  colorData,
                  getFieldsValue()?.json?.[v]?.pic?.url,
                  dynamicTitle,
                  dynamicSummary
                )
              }
            />
          )}
        </Form.Item>
      )}

      <Form.Item label="跳转">
        {getFieldDecorator(`json[${v}].link_type`, {
          initialValue: stateList[v]?.link_type || 0,
          rules: [
            {
              required: true,
              message: '请选择样式',
            },
          ],
        })(
          <Radio.Group onChange={(e) => setLink_type(e.target.value)}>
            <Radio value={0}>链接</Radio>
            <Radio value={1}>栏目</Radio>
            <Radio value={2}>稿件</Radio>
          </Radio.Group>
        )}
      </Form.Item>
      {link_type === 0 && (
        <Form.Item label="跳转链接：">
          {getFieldDecorator(`json[${v}].link_url`, {
            initialValue: stateList[v]?.link_url,
            rules: [
              {
                required: true,
                message: '请输入跳转链接',
              },
              {
                pattern: /^https?:\/\//,
                message: '请填写正确的URL地址',
              },
            ],
          })(<Input style={{ width: '80%' }} placeholder="请输入跳转链接" />)}
        </Form.Item>
      )}
      {link_type === 1 && (
        <Form.Item label="关联栏目">
          {getFieldDecorator(`json[${v}].columnList`, {
            initialValue: stateList[v]?.columnList,
            rules: [
              {
                required: true,
                message: '请关联栏目',
                type: 'array',
              },
              {
                max: 1,
                message: '最多关联1条栏目',
                type: 'array',
              },
            ],
          })(
            <SearchAndInput
              max={1}
              func="searchUGCTopic"
              columns={columns1}
              placeholder="输入名称搜索栏目"
              initialValues={{ list: stateList[v]?.columnList }}
              body={{ type: 0 }}
              order={true}
              addOnTop={true}
              searchKey="name"
              funcIndex="list"
              apiWithPagination={true}
              selectOptionDisplay={(record: any) => `#${record.name}#`}
              detailMode
            />
          )}
        </Form.Item>
      )}
      {link_type === 2 && (
        <Form.Item label="关联稿件">
          {getFieldDecorator(`json[${v}].channelArticles`, {
            initialValue: stateList[v]?.channelArticles,
            rules: [
              {
                required: true,
                message: '请关联稿件',
                type: 'array',
              },
              {
                max: 1,
                message: '最多关联1条稿件',
                type: 'array',
              },
            ],
          })(
            <NewSearchAndInput
              max={1}
              func="listArticleRecommendSearch"
              columns={columns2}
              placeholder="输入ID或标题关联稿件"
              body={{ doc_types: '2,3,4,5,8,9,10,11,12,13' }}
              order={true}
              addOnTop={true}
            />
          )}
        </Form.Item>
      )}
      <Form.Item label="推荐理由：" style={{ display: 'none' }}>
        {getFieldDecorator(`json[${v}].id`, {
          initialValue: stateList[v]?.id,
          rules: [{ message: '请输入推荐理由，最多15字' }],
        })(
          <Input style={{ width: '80%' }} maxLength={15} placeholder="请输入推荐理由，最多15字" />
        )}
      </Form.Item>
    </div>
  );
}
