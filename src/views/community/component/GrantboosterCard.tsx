import React, {useEffect, useState, useRef, useCallback} from 'react';
import UserSelect  from '@components/business/useSelect';
import {Form, message, Radio} from 'antd';
import {A, Table, Drawer} from '@components/common';
import {sysApi} from "@app/api";
import {setMLoading} from "@utils/utils";

const GrantboosterCard: React.FC<{ selectKeys: string; openKeys: string; breadCrumb: any }> = (
    props
) => {
    const formRef = useRef({} as any);
    const selectRef = useRef(null);
    const {getFieldDecorator,validateFields} = props.form;
    const handleOkClick = () => {
        validateFields((err: any, values: any) => {
            if (!err) {
                sysApi.grantCard(values).then(res=>{
                    message.success('发放成功');
                    props.getData()
                    props.onEnd()
                })
            }
        })
    }
    return (
        <>
            <Drawer
                title={'发放助推卡'}
                onClose={props.onClose}
                visible={props.visible}
                skey={'grantboosterCard'}
                onOk={() => {handleOkClick()}}
            >
                <Form ref={formRef} labelCol={{span: 6}} wrapperCol={{span: 16}}>
                    <Form.Item label="归属用户">
                        {getFieldDecorator('account_id', {
                            initialValue: '',
                            rules: [
                                {
                                    required: true,
                                    message: '请选择归属用户',
                                },
                            ],
                        })(<UserSelect ref={selectRef}/>)}
                    </Form.Item>
                    <Form.Item label="目标次数">
                        {getFieldDecorator('target_number', {
                            rules: [
                                {
                                    required: true,
                                    message: '请选择目标次数',
                                },
                            ],
                        })(
                            <Radio.Group>
                                <Radio value={100}>100次展示</Radio>
                                <Radio value={200}>200次展示</Radio>
                                <Radio value={500}>500次展示</Radio>
                            </Radio.Group>
                        )}
                    </Form.Item>
                </Form>
            </Drawer>
        </>
    );
};

export default Form.create({})(GrantboosterCard);
