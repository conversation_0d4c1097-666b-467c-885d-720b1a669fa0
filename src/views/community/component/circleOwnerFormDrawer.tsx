import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import {
  Button,
  Checkbox,
  Dropdown,
  Form,
  Icon,
  Input,
  InputNumber,
  Menu,
  Modal,
  Radio,
  Select,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { communityApi } from '@app/api';
import '@components/business/styles/business.scss';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { Drawer, VideoUploader } from '@app/components/common';
import ImgUploadList from '@app/components/common/imgUploadList';
import _, { set } from 'lodash';
import PermsCheckbox from '@app/components/common/PermsCheckbox';

const CircleOwnerFormDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();

  const {
    biz_type = 0,
    report_type = 0,
    title = '',
    content = '',
    medias = [],
    status = 0,
    remark = '',
  } = props.formContent || {};

  // const [perms, setPerms] = useState([])
  const [accountOptions, setAccountOptions] = useState([]);
  const { getFieldDecorator, getFieldValue } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
      doSubmit,
    }),
    []
  );

  useEffect(() => {
    if (!!props.formContent && !!props.formContent.nick_name) {
      handleAccountSearch(props.formContent.nick_name);
    } else {
      handleAccountSearch('');
    }
    // setPerms(props.perms)
  }, [props.formContent]);

  const doSubmit = () => {
    console.log('doSubmitdoSubmit');
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        console.log('xxxxx');
        const permission = values.permission;

        const ids = permission.map((item: any) => {
          return (
            item.children
              ?.map((cItem: any) => {
                if (cItem.checked) {
                  return cItem.id;
                } else {
                  return '';
                }
              })
              ?.filter((id: any) => !!id) ?? []
          );
        });

        var permission_ids = [].concat(...ids);
        // console.log('permission_ids', permission_ids)

        const body: any = {
          account_id: values.account_id,
          circle_id: props.circle_id,
          permission_ids: permission_ids,
        };
        if (values.dataType == 1) {
          body.permission_board_ids = -1;
        } else {
          body.permission_board_ids = values.permission_board_ids.join(',');
        }

        if (props.formContent?.id) {
          body.id = props.formContent?.id;
        }

        dispatch(setConfig({ mLoading: true }));
        communityApi
          .editCircleOwner(body)
          .then(() => {
            dispatch(setConfig({ mLoading: false }));
            message.success('操作成功');
            props.onEnd();
          })
          .catch(() => {
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val?.trim()) {
      setAccountOptions([]);
      return;
    }

    communityApi
      .recommendAccount_Search({ keyword: val })
      .then(({ data }) => {
        const { list } = data as any;
        setAccountOptions(list || []);
      })
      .catch(() => {});
  }, 500);

  // const changePerms = (val: any) => {
  //   setPerms(val)
  // }

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  return (
    <Drawer
      title={!!props.formContent ? '编辑主理人' : '添加主理人'}
      visible={props.visible}
      skey={props.skey}
      onClose={props.onClose}
      onOk={doSubmit}
      okText="保存"
      // footer={((<><Button style={{ width: 100, marginRight: 8 }} onClick={props.onClose}>取消</Button>
      //   <Dropdown trigger={['hover']} overlay={menu} placement="topCenter">
      //     <PermButton style={{ width: 100 }} type="primary" perm='' onClick={props.onOk}>
      //       保存<Icon type="up" />
      //     </PermButton>
      //   </Dropdown>

      // </>))}
    >
      <Form {...formLayout}>
        <Form.Item label="用户昵称">
          {getFieldDecorator('account_id', {
            initialValue: !!props.formContent ? props.formContent.account_id : undefined,
            rules: [
              {
                required: true,
                message: '请输入用户昵称',
              },
            ],
          })(
            <Select
              style={{ width: '100%' }}
              placeholder="请输入昵称或小潮号"
              onSearch={handleAccountSearch}
              showSearch
              disabled={!!props.formContent}
              filterOption={false}
            >
              {accountOptions.map((item: any) => (
                <Select.Option key={item.id} value={item.id}>
                  {item.nick_name}|小潮号：{item.chao_id}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>

        <Form.Item label="功能权限">
          {getFieldDecorator('permission', {
            initialValue: props.perms,
          })(<PermsCheckbox></PermsCheckbox>)}
        </Form.Item>

        <Form.Item label="数据权限">
          {getFieldDecorator('dataType', {
            initialValue: !props.formContent?.permission_board_ids
              ? 1
              : props.formContent?.permission_board_ids == '-1'
              ? 1
              : 2,
          })(
            <Radio.Group>
              <Radio value={1}>
                全部内容&nbsp;
                <Tooltip title="包括选择了版块或未选择版块的">
                  <Icon type="question-circle" />
                </Tooltip>
              </Radio>
              <Radio value={2}>部分内容</Radio>
            </Radio.Group>
          )}
        </Form.Item>

        {getFieldValue('dataType') == 2 && (
          <Form.Item label=" " colon={false} style={{ marginTop: -20 }}>
            {getFieldDecorator('permission_board_ids', {
              initialValue:
                props.formContent?.permission_board_ids?.length > 0
                  ? props.formContent?.permission_board_ids?.split(',') || []
                  : [],
              rules: [
                {
                  validator: (rule: any, value: any, callback: any) => {
                    if (value?.length > 0) {
                      callback();
                      return;
                    }
                    callback('请选择数据权限');
                    return;
                  },
                },
              ],
            })(
              <Checkbox.Group>
                {props.boards?.map((board: any) => {
                  return board.id == -1 ? null : (
                    <Checkbox key={board.id} value={`${board.id}`}>
                      {board.name}
                    </Checkbox>
                  );
                })}
              </Checkbox.Group>
            )}
          </Form.Item>
        )}
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'CircleOwnerFormDrawer' })(
  forwardRef<any, any>(CircleOwnerFormDrawer)
);
