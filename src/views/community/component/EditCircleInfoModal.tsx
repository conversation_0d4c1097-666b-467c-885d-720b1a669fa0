import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "antd";
import React, { useEffect, useRef, useState } from "react";
import { communityApi as api } from "@app/api";
import "./community.scss"

export default function EditCircleInfoModal(props: any) {
  const { visible, onCancel, record, onEnd, idKey = 'article_id' } = props
  const [okLoading, setOkLoading] = useState(false)
  const [loading, setLoading] = useState(false)
  const [circleList, setCircleList] = useState([])
  const [blockList, setBlockList] = useState([])
  const [selectedCircleId, setSelectedCircleId] = useState(0)
  const [selectedBlockId, setSelectedBlockId] = useState(0)
  const leftListRef = useRef<HTMLUListElement>(null)
  const rightListRef = useRef<HTMLUListElement>(null)

  useEffect(() => {
    if (visible) {
      // 显示请求最新的圈子与版块
      setCircleList([])
      setSelectedCircleId(0)
      setBlockList([])
      setSelectedBlockId(0)
      setLoading(true)
      const apiList = [api.getCircleList({ current: 1, size: 100, enabled: true })]
      if (record.circle_id) {
        apiList.push(api.getCircleBoardList({ circle_id: record.circle_id, current: 1, size: 20 }))
      }
      Promise.all(apiList)
        .then(([res1, res2]) => {
          setLoading(false)
          const { list: res1Data } = res1.data as any
          const { records: list1 = [] } = res1Data
          setCircleList(list1)
          const { list: list2 = [] } = res2.data as any
          setBlockList(list2)
          const circleIndex = list1.findIndex((item: any) => item.id === record.circle_id)
          if (circleIndex >= 0) {
            setSelectedCircleId(record.circle_id)
            const leftList = leftListRef.current
            if (leftList) {
              leftList.scrollTop = 35 * (circleIndex - 1)
            }
          }
          const boardIndex = list2.findIndex((item: any) => item.id === record.board_id)
          if (boardIndex >= 0) {
            setSelectedBlockId(record.board_id)
            const rightList = rightListRef.current
            if (rightList) {
              rightList.scrollTop = 35 * (boardIndex - 1)
            }
          }
        })
        .catch(() => {
          setLoading(false)
        })
    }
  }, [visible])

  const handleOk = () => {
    setOkLoading(true)
    api.updateCircleBlock({ article_id: record[idKey], circle_id: selectedCircleId, board_id: selectedBlockId })
      .then(() => {
        setOkLoading(false)
        onEnd()
      })
      .catch(() => {
        setOkLoading(false)
      })
  }

  const handleCircleIdChange = (circle_id: any) => {
    setSelectedCircleId(circle_id)
    if (circle_id === 0) {
      setBlockList([])
      setSelectedBlockId(0)
      return
    }
    setLoading(true)
    api.getCircleBoardList({ circle_id, current: 1, size: 20 })
      .then((res) => {
        setLoading(false)
        const { list = [] } = res.data as any
        setBlockList(list)
        setSelectedBlockId(0)
      }).catch(() => {
        setLoading(false)
        setBlockList([])
      })
  }

  const handleBlockIdChange = (blockId: any) => {
    setSelectedBlockId(blockId)
  }

  return (
    <Modal
      visible={visible}
      key={props.key}
      title="修改圈子信息"
      width="500px"
      maskClosable={false}
      onCancel={onCancel}
      destroyOnClose={true}
      bodyStyle={{ height: 200 }}
      footer={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ flex: 1, fontSize: 13, color: '#aaa', textAlign: 'left' }}>无需重新审核，可直接生效</span>
          <Button key="back" onClick={onCancel}>取消</Button>
          <Button key="submit" type="primary" loading={okLoading} onClick={handleOk}>确定</Button>
        </div>
      }
    >
      <Spin
        tip="正在加载..."
        spinning={loading}>
        <div className="edit_circle_info">
          <ul ref={leftListRef}>
            <li key={0} className={selectedCircleId === 0 ? 'selected' : ''} onClick={() => handleCircleIdChange(0)}>不选择圈子</li>
            {circleList.map((item: any) => <li key={item.id} className={selectedCircleId === item.id ? 'selected' : ''} onClick={() => handleCircleIdChange(item.id)}>{item.name}</li>)}
          </ul>
          <ul ref={rightListRef}>
            <li key={0} className={selectedBlockId === 0 ? 'selected' : ''} onClick={() => handleBlockIdChange(0)}>不选择版块</li>
            {blockList.map((item: any) => <li key={item.id} className={selectedBlockId === item.id ? 'selected' : ''} onClick={() => handleBlockIdChange(item.id)}>{item.name}</li>)}
          </ul>
        </div>
      </Spin>
    </Modal>
  )
}