import React, { useMemo, useState, useEffect, useRef, forwardRef, useImperativeHandle, createRef } from 'react'
import { Row, Col, Select, DatePicker, Input, Button, Divider, Icon, Drawer, Tooltip, message ,Modal} from 'antd'
import { Table } from '@components/common';
import Form, { FormComponentProps } from "antd/lib/form/Form";
import { getCrumb, setMenuHook, searchToObject } from '@app/utils/utils';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { PermA, PermButton } from '@components/permItems';
import SearchAndInput from '@components/common/newsSearchAndInput';
import { communityApi, recommendApi, releaseListApi } from '@app/api';
import { CommonObject, RequestBody } from '@app/types';
import { useSelector, useDispatch } from 'react-redux';
import { getTableList } from '@app/action/tableList';
import moment from 'moment';
const { Option } = Select
const { Search } = Input;
const { MonthPicker, RangePicker, WeekPicker } = DatePicker;
interface TopicFormProps extends FormComponentProps {
    submit: () => void;
    articlesChanges: (a: any) => void;
    // list: any;
    listDetails: any
    // position:() => String;
}
type Ref = FormComponentProps;
const TopicForm = forwardRef<Ref, TopicFormProps>(
    // , onSubmit , list , articlesChanges , position
    ({ form, submit, articlesChanges, listDetails }: TopicFormProps, ref) => {
        useImperativeHandle(ref, () => ({
            form
        }));
        const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
            e.preventDefault();
            // debugger
            submit();
        };
        const articlesChange = (data: any) => {
            articlesChanges(data)
        }
        const getDoc_types = () => {
            return { type: '10' }
        }
        const getToolTip = () => {
            return (<><div>
                <div>输入说明：</div>
                <div>直接输入阿拉伯数字代表位置序号，例如要在信息流第8个位置展示，则直接输入“8”即可。</div>
                <div>最大输入数字为99</div>
                <div>注：</div>
                <div>直接在输入的指定位置上显示，不参与内容排序，且优先于内容展示。</div>
            </div></>)
        }
        return (
            <Form
                onSubmit={handleSubmit}
                labelCol={{ span: 4 }}
                wrapperCol={{ span: 20 }}
                labelAlign="left"
            >
                <Form.Item label="推荐位名称">
                    {form.getFieldDecorator("name", {
                        initialValue: listDetails.title,
                        rules: [
                            {
                                required: true,
                                message: "请填写推荐位名称"
                            },
                        ]
                    })(<Input style={{ width: '350px' }} maxLength={15} placeholder="请输入名称，不超过15字" />)}
                </Form.Item>
                <Form.Item label="显示位置">
                    {form.getFieldDecorator("position", {
                        initialValue: listDetails.position,
                        rules: [
                            {
                                required: true,
                                message: "请输入显示位置"
                            },
                            {
                                pattern: /^[1-9][0-9]{0,1}$/,
                                message: "请输入1-99的数字"
                            }
                        ]
                    })(<Input style={{ width: '350px' }} maxLength={20} placeholder="请输入要显示在列表中的位置序号，1~99" />)}
                    <Tooltip className='' title={getToolTip()} placement="topLeft">
                        <Icon className='ant-tooltip_icon' type="question-circle-o" style={{ marginLeft: 8 }} />
                    </Tooltip>
                </Form.Item>
                <Form.Item label="推荐话题">
                    {form.getFieldDecorator("position_id", {
                        initialValue: listDetails?.topic_list?.length || '',
                        rules: [
                            {
                                required: true,
                                message: "请选择推荐话题"
                            },
                        ]
                    })(
                        <SearchAndInput
                            max={10}
                            func="recommendTopicSearchList"
                            funcIndex='list'
                            columns={[
                                {
                                    title: '话题名称',
                                    key: 'name',
                                    dataIndex: 'name',
                                },
                            ]}
                            order={true}
                            placeholder="请输入话题名称"
                            initialValues={{ list: listDetails.topic_list }}
                            triggerInitialValueChange={articlesChange}
                            body={getDoc_types()}
                            searchKey={'name'}
                            apiWithPagination={true}
                            displayChannel={'name'}
                            selectOptionDisplay={(record: any) => `${record.name}`}
                        />
                    )}
                </Form.Item>
            </Form>
        );
    }
);

const EnhancedForm = Form.create<TopicFormProps>()(TopicForm);
export default function TopicRecommendation(props: any) {
    const { id: channelId, name: channelName } = useRouteMatch<any>().params;
    const { total, current, size, records = [], allData } = useSelector((state: any) => state.tableList)
    const [iconLoading, setIconLoading] = useState(false)
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    const [visibleShow, setVisibleShow] = useState(false)
    const [joggleType, setJoggleType] = useState(true)
    let joggleCreate = communityApi.recommendTopicCreate
    let joggleUpdate = communityApi.recommendTopicUpdate
    const [classId, setClassId] = useState([])
    const [listDetails, setListDetails] = useState({})
    const [listId, setListId] = useState('')
    const dispatch = useDispatch();
    //   const formRef = useRef({} as any);
    const formRef = createRef<FormComponentProps>();
    const history = useHistory()
    const deleteAd = (id: string | number) => {
        Modal.confirm({
            title: <p>确认删除吗？</p>,
            onOk: () => {
                let data = {
                    id,
                    channel_id: searchToObject().channel_id || channelId,
                }
                communityApi.recommendTopicListDelete(data).then((res: any) => {
                    message.success('操作成功')
                    getData()
                })
            },
        });

    }
    const initialFilter = {
        current: 1,
        size: 10,
        type: '33',
        // search_type: '1',
        // doc_type: '',
        // keyword: '',
        // begin: false,
        // end: false,
        channel_id: searchToObject().channel_id || channelId,
        // community_pushed: 1
    };
    const [filter, setFilter] = useState<CommonObject>(initialFilter)
    const getFilter = () => {
        const result: CommonObject = {};
        Object.keys(filter).forEach((k: string) => {
            if (filter[k]) {
                result[k] = filter[k];
            }
        });
        return result;
    };
    useEffect(() => {
        getData()
    }, [])
    const getData = (filter = getFilter()) => {
        dispatch(getTableList('recommendTopicLabelListByPage', 'recommend_list', filter));
    };
    const tooltipText = () => {
        return (
            <>
                <div>
                    运营位将直接在输入的指定位置上显示，不参与内容排序，且优先于内容展示
                </div>
            </>
        )
    }
    const getColumns = useMemo(() => {
        return [
            {
                title: '序号',
                key: 'seq',
                render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
                width: 70,
            },
            {
                title: '推荐位名称',
                dataIndex: 'title',
                key: 'title',
                
            },
            {
                title: <span>显示位置<Tooltip placement="topLeft" title={tooltipText()}>
                    <Icon style={{ paddingLeft: '5px' }} type="question-circle" />
                </Tooltip></span>,
                dataIndex: 'position',
                width: 120,
            },
            {
                title: '推荐话题数',
                dataIndex: 'topic_number',
                key: 'topic_number',
                width: 150,
            },
            {
                title: '状态',
                dataIndex: 'status',
                key: 'status',
                render: (text: any) => (<span>{text ? '展示中' : '待展示'}</span>),
                width: 90,
            },
            {
                title: '创建人',
                dataIndex: 'creator',
                key: 'creator',
                width: 150,
            },
            {
                title: '最后操作时间',
                dataIndex: 'published_at',
                key: 'published_at',
                render: (text: any) => (<span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>),
                width: 200,
            },
            {
                title: '操作',
                key: 'op',
                render: (text: any, record: any) => (
                    <>
                        <PermA
                            perm={`topic_recommend:${searchToObject().channel_id || channelId}:update`}
                            onClick={() => editBanner(record)}
                            style={{ marginRight: 5 }}
                        >
                            编辑
                        </PermA>
                        <PermA
                            perm={`topic_recommend:${searchToObject().channel_id || channelId}:update_status`}
                            onClick={() => moveStatus(record, record.status ? '下线' : '上线')}
                            style={{ marginRight: 5 }}
                        >
                            {record.status ? '下线' : '上线'}
                        </PermA>
                        <PermA
                            perm={`topic_recommend:${searchToObject().channel_id || channelId}:delete`}
                            onClick={() => deleteAd(record.id)}
                            style={{ marginRight: 5 }}
                        >
                            删除
                        </PermA>
                    </>
                ),
                width: 140,
            },
        ];
    }, [records]);
    const moveStatus = (record: any, type: string) => {
        if (allData.on_show_count >= 5 && type == '上架') return message.error('最多展示5组推荐')
        let data = {
            channel_id: searchToObject().channel_id,
            id: record.id,
            status: record.status ? '0' : '1'
        }
        communityApi.recommendTopicUpdateStatus(data).then((res: any) => {
            message.success('操作成功')
            getData()
        })
    }
    const editBanner = (record: any) => {
        setListId(record.id)
        communityApi.recommendTopicListDetail({ id: record.id }).then((res: any) => {
            setListDetails(res.data.recommend_detail)
            setVisibleShow(true)
            setJoggleType(false)
            let ids: any = []
            res.data.recommend_detail.topic_list.forEach((el: any) => {
                ids.push(el.id)
            })
            setClassId(ids)

        })
    }
    const onOk = (e: any) => {
        if (classId.length < 2) return message.error('请推荐2个及以上话题')
        setIconLoading(true)
        // setconLoading(false)
        formRef.current.form.validateFieldsAndScroll((err: any, values: any) => {
            if (!err) {
                let data = {
                    channel_id: searchToObject().channel_id || channelId,
                    recommend_name: values.name,
                    position: values.position,
                    ref_ids: classId,
                    cycle_carousel: true,
                    column_style: '1',
                    topic_type: 10,
                    id: listId

                }
                let promise = joggleType ? joggleCreate(data) : joggleUpdate(data)
                promise.then(res => {
                    message.success('操作成功')
                    setIconLoading(false)
                    setVisibleShow(false)
                    getData()
                }).catch((err) => {
                    setIconLoading(false)
                })
            } else {
                setIconLoading(false)
                message.error('请检查表单内容');
            }
        });
    }
    //取消提交
    const cancleSubmit = () => {
        setVisibleShow(false)
        setIconLoading(false)
    }
    //添加推荐用户
    const addRecommendedTopics = () => {
        setVisibleShow(true)
        setJoggleType(true)
        setListId('')
        setListDetails({})
    }
    const articlesChanges = (data: any) => {
        setClassId(data.channelArticleIds)
    }
    return (
        <>
            <Row>
                <Col span={12} >
                    <Button onClick={() => history.go(-1)} style={{ margin: '8px 8px 0 0 ' }}>
                        <Icon type="left-circle-o" />
                        返回
                    </Button>
                    <PermButton perm={`topic_recommend:${searchToObject().channel_id || channelId}:create`} onClick={() => addRecommendedTopics()}>添加推荐话题</PermButton>
                </Col>
                <Col span={12} className="layout-breadcrumb">
                    {getCrumb(props.breadCrumb)}
                </Col>
            </Row>
            <div className="component-content">
                <Row>
                    <Col>
                        <Table
                            func="recommendTopicLabelListByPage"
                            index="recommend_list"
                            filter={getFilter()}
                            columns={getColumns}
                            rowKey="id"
                            pagination={true}
                            total={total}
                        />
                    </Col>
                </Row>
            </div>
            <Drawer
                title="话题推荐位"
                width={'60%'}
                maskClosable={false}
                onClose={() => setVisibleShow(false)}
                visible={visibleShow}
                bodyStyle={{ paddingBottom: 80 }}
                destroyOnClose
            >
                <div className='box'>
                    <EnhancedForm submit={() => onOk} wrappedComponentRef={formRef} listDetails={listDetails} articlesChanges={(e) => articlesChanges(e)} />
                </div>
                <div
                    style={{
                        position: 'absolute',
                        right: 0,
                        bottom: 0,
                        width: '100%',
                        borderTop: '1px solid #e9e9e9',
                        padding: '10px 16px',
                        background: '#fff',
                        textAlign: 'right',
                    }}
                >
                    <Button onClick={() => cancleSubmit()} style={{ marginRight: 8 }}>
                        取消
                    </Button>
                    <PermButton
                        perm={''}
                        type="primary" loading={iconLoading}
                        onClick={(e) => onOk(e)}
                    >
                        确定
                    </PermButton>
                    {/* <Button onClick={(e)=>onOk(e)} type="primary" loading={iconLoading}>
                确定
                </Button> */}
                </div>
            </Drawer>

        </>
    )
}
