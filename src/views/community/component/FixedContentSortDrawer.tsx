import React from "react";
import { A, Drawer } from '@components/common';
import { Form, InputNumber, Select, message, Table, Spin } from "antd";
import connect from '@utils/connectSession';
import { communityApi as api } from '@app/api';
import { setMLoading } from "@app/utils/utils";
import SearchAndInput from '@components/common/newsSearchAndInput';

@connect
@(Form.create({ name: 'FixedContentSortDrawer' }) as any)
export default class FixedContentSortDrawer extends React.Component<any, any> {

  state = {
    loading: false,
    list: null,
    ids: null
  }

  getItemTitle = (item: any) => (item.list_title || item.content || '-').slice(0, 30) // item[item.doc_type === 12 ? 'content' : 'list_title']

  handleOkClick = () => {
    const { form: { validateFields }, onEnd, circle_id } = this.props
    validateFields((err: any, values: any) => {
      if (!err) {
        const { ids, pos } = values
        setMLoading(this, true);
        api.editCircleFixList({
          circle_id,
          list: ids && ids.length ? ids.map((id: any) => id + '_' + pos[id]).join(',') : ''
        })
          .then(() => {
            setMLoading(this, false);
            message.success('设置成功');
            onEnd()
          }).catch(() => {
            setMLoading(this, false);
          })
      }
    })
  }

  validatorNumer = (rule: any, value: any, callback: any) => {
    const { form: { getFieldsValue, setFields }, max } = this.props
    const { pos } = getFieldsValue()
    const values: any = {}
    for (const id in pos) {
      const val = pos[id]
      if (!values[val]) {
        values[val] = []
      }
      values[val].push(id)
    }
    let isValidate = true
    for (const val in values) {
      const ids = values[val]
      if (val && val !== 'null' && val !== 'undefined') {
        // 已存在相同序号
        if (ids.length > 1) {
          ids.forEach((id: any) => {
            const gl = pos[id] > max
            setFields({ [`pos[${id}]`]: { value: pos[id], errors: [new Error(gl ? '已超过圈子内容总数' : '已存在相同序号')] } })
          });
          isValidate = false
        } else {
          ids.forEach((id: any) => {
            const gl = pos[id] > max
            if (gl) {
              isValidate = false
            }
            setFields({ [`pos[${id}]`]: { value: pos[id], errors: gl ? [new Error('已超过圈子内容总数')] : null } })
          });
        }
      } else {
        ids.forEach((id: any) => {
          setFields({ [`pos[${id}]`]: { value: pos[id], errors: [new Error('显示位置不能为空')] } })
        });
        isValidate = false
      }
    }
    if (isValidate) {
      callback()
    }
  }

  componentDidUpdate(prevProps: Readonly<any>, prevState: Readonly<any>, snapshot?: any): void {
    if (!prevProps.visible && this.props.visible) {
      // 显示请求详情接口
      const { circle_id } = this.props
      this.setState({ loading: true, list: null, ids: null })
      api.getCircleFixList({ circle_id })
        .then(({ data }) => {
          this.setState({ loading: false })
          const { list: serverList = [] } = data as any
          const list: any = []
          const ids: any = []
          for (let i = 0; i < serverList.length; i++) {
            const { article_id: id, title: list_title, fixed_number } = serverList[i]
            list.push({ id, list_title, fixed_number })
            ids.push(id)
          }
          this.setState({ list, ids })
        })
        .catch(() => {
          this.setState({ loading: false })
        })
    }
  }

  render() {
    const { visible, onClose, form: { getFieldDecorator }, circle_id } = this.props
    const { list, ids } = this.state
    const formLayout = {
      labelCol: { span: 3 },
      wrapperCol: { span: 19 },
    };
    const columns: any = [
      {
        title: '潮新闻id',
        dataIndex: 'id',
        width: 100,
      },
      {
        title: '内容标题',
        dataIndex: 'list_title',
        render: (text: any, record: any) => this.getItemTitle(record)
      },
      {
        title: '显示位置',
        width: 160,
        render: (text: any, record: any, index: number) => {
          return (
            <Form.Item style={{ marginBottom: 0 }}>
              {getFieldDecorator(`pos[${record.id}]`, {
                initialValue: record.fixed_number,
                rules: [{
                  validator: this.validatorNumer
                }],
              })(
                <InputNumber style={{ width: '100%' }} min={1} max={99} placeholder="请输入1～99数字" />
              )}
            </Form.Item>
          )
        }
      }
    ]
    return (
      <Drawer
        title="固定内容排序"
        onClose={onClose}
        visible={visible}
        onOk={this.handleOkClick}
        okPerm={'circle_article_fix:edit'}
        skey={'FixedContentSortDrawer'}
      >
        <Spin
          tip="正在加载..."
          spinning={this.state.loading}>
          <Form {...formLayout}>
            {
              list && <Form.Item label="选择内容">
                {getFieldDecorator('ids', {
                  initialValue: ids,
                })(
                  <SearchAndInput
                    idField="id"
                    max={10}
                    func="searchCircleArticle"
                    funcIndex='list'
                    columns={columns}
                    placeholder="请输入潮新闻id或标题，仅限本圈内容"
                    initialValues={{ list }}
                    body={{ circle_id }}
                    searchKey={'keyword'}
                    selectOptionDisplay={(record: any) => `${record.id}-${this.getItemTitle(record)}`}
                  />
                )}
              </Form.Item>
            }
          </Form>
        </Spin>
      </Drawer>
    )
  }
}