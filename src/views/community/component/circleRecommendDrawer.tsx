import React, { forwardRef, useEffect, useRef, useState } from 'react';
import { Drawer, NewTable } from '@components/common';
import { Form, Input, Select, Spin, message, DatePicker, Button, Modal } from 'antd';
import connect from '@utils/connectSession';
import { communityApi as api, releaseListApi, searchApi } from '@app/api';
import Radio from 'antd/es/radio';
import _ from 'lodash';
import moment, { Moment } from 'moment';
import CircleArticleRecommend from './circleArticleRecommend';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import CircleUserRecommendForm from './circleUserRecommendForm';

function CircleRecommendDrawer(props: any, ref: any) {
  const [editData, setEditData] = useState<any>(null);
  const formRef = useRef<any>(null);
  const dispatch = useDispatch();
  const handleOkClick = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        dispatch(setConfig({ mLoading: true }));
        const formData = formRef.current?.getFormData(values) || {};
        const params = {
          ...formData,
          article_category: 3,
          channel_id: `${props.circle_id},${values.board_id ?? 0}`,
        };
        if (props.record) {
          params.id = props.record.recommend_id || props.record.id;
        }

        delete params.board_id;
        console.log(params);

        const api = props.record
          ? releaseListApi.updateContentRecommend
          : releaseListApi.createContentRecommend;
        api(params)
          .then((res: any) => {
            message.success('操作成功');
            dispatch(setConfig({ mLoading: false }));
            props.onEnd();
          })
          .catch(() => {
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 20 },
  };

  useEffect(() => {
    if (props.visible) {
      if (props.record) {
        getDetail();
      }
    }
  }, [props.visible]);

  const getDetail = async () => {
    dispatch(setConfig({ mLoading: true }));
    releaseListApi
      .getContentRecommend({ id: props.record.recommend_id || props.record.id })
      .then(({ data }) => {
        dispatch(setConfig({ mLoading: false }));
        setEditData(data as any);
      })
      .catch(() => {
        dispatch(setConfig({ mLoading: false }));
        setEditData(null);
      });
  };

  const type = editData?.recommend?.ref_type || props.type;

  const title = props.record ? '编辑推荐位' : `创建${type == 42 ? '稿件' : '用户'}推荐位`;

  return (
    <Drawer
      title={title}
      onClose={props.onClose}
      visible={props.visible}
      onOk={handleOkClick}
      skey={props.key}
    >
      <Form {...formLayout}>
        {type == '42' && (
          <CircleArticleRecommend ref={formRef} {...props} data={editData}></CircleArticleRecommend>
        )}
        {type == '23' && (
          <CircleUserRecommendForm
            ref={formRef}
            {...props}
            data={editData}
          ></CircleUserRecommendForm>
        )}
      </Form>
    </Drawer>
  );
}

export default Form.create<any>({ name: 'CircleRecommendDrawer' })(
  forwardRef<any, any>(CircleRecommendDrawer)
);
