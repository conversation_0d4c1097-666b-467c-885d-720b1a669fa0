import { communityApi } from '@app/api';
import { Form, Icon, Input, InputNumber, message, Modal, Select, Tooltip } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import SearchAndInput from '@components/common/newNewsSearchAndInput';
import { A, ImageUploader } from '@app/components/common';
import { getImageRatio } from '@app/utils/utils';

const CircleArticleRecommend = forwardRef<any, any>((props: any, ref: any) => {
  const { getFieldDecorator } = props.form;
  const [articleList, setArticleList] = useState([]);
  useImperativeHandle(
    ref,
    () => ({
      getFormData: (formData: any) => {
        const article_list = formData.article_list.map((item: any) => {
          const result: any = {
            article_id: item.id,
            list_pic: item.new_list_pic,
          };
          if (item.new_list_title) {
            result.list_title = item.new_list_title;
          }

          return result;
        });

        const result = {
          ...formData,
          title_show: true,
          title_style: 0,
          ref_ids: article_list.map((item: any) => item.article_id),
          ref_extensions: JSON.stringify(article_list),
          ref_type: 42,
        };
        delete result.article_list;
        return result;
      },
    }),
    []
  );

  useEffect(() => {
    if (props.data) {
      const ref_extensions = JSON.parse(props.data?.recommend?.ref_extensions || '[]') || [];
      setArticleList(
        props.data?.article_list?.map((item: any) => {
          const extension = ref_extensions?.find((i: any) => i.article_id == item.id);

          return {
            ...item,
            new_list_pic: extension?.list_pic,
            new_list_title: extension?.list_title,
          };
        }) || []
      );
    }
  }, [props.data]);

  const editPic = async (url: string, index: number) => {
    let pic: string = url;
    let modal: any;

    let proportion: any = (4 / 3).toFixed(2);
    let consistentProportions = true;
    if (url) {
      const afterCalculation: any = await getImageRatio(url);
      if (Math.abs(afterCalculation - proportion) <= 0.05) {
        consistentProportions = false;
      } else {
        consistentProportions = true;
      }
    }

    const picChange = (u: string) => {
      pic = u;
      modal.update({
        content: (
          <>
            <ImageUploader value={pic} onChange={picChange} ratio={4 / 3} />
            <p>比例4:3</p>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '编辑图片',
      width: 500,
      content: (
        <>
          <ImageUploader value={pic} onChange={picChange} imgMaxWidth={400} ratio={4 / 3} />
          {consistentProportions && url ? (
            <span style={{ color: 'red' }}>
              该图片比例非{4}:{3}，图片将自动居中截取，建议重新上传。
            </span>
          ) : (
            <p>建议比例4:3</p>
          )}
        </>
      ),
      onOk: async (destroy: Function) => {
        const articles = [...props.form.getFieldsValue().article_list];
        articles[index].new_list_pic = pic;
        props.form.setFieldsValue({ article_list: articles });
        destroy();
      },
    });
  };

  const editTitle = (value: string, index: number) => {
    let title: string = value || '';
    let modal: any;
    const titleChange = (e: any) => {
      title = e.target.value;
      modal.update({
        content: (
          <>
            <Input.TextArea
              placeholder="最多输入40字"
              value={title}
              maxLength={40}
              onPressEnter={(e) => e.preventDefault()}
              onChange={titleChange}
            ></Input.TextArea>
            <div style={{ textAlign: 'right' }}>{title?.length}/40</div>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '自定义标题',
      content: (
        <>
          <Input.TextArea
            placeholder="最多输入40字"
            value={title}
            maxLength={40}
            onPressEnter={(e) => e.preventDefault()}
            onChange={titleChange}
          ></Input.TextArea>
          <div style={{ textAlign: 'right' }}>{title?.length}/40</div>
        </>
      ),
      onOk: (destroy: Function) => {
        // if (!title?.trim()) {
        //   message.error('请输入自定义标题');
        //   return;
        // }
        const articles = [...props.form.getFieldsValue().article_list];
        articles[index].new_list_title = title?.trim();
        props.form.setFieldsValue({ article_list: articles });
        destroy();
      },
    });
  };
  const columns = [
    {
      title: '潮新闻ID',
      dataIndex: 'id',
      width: 80,
      render: (_: any, v: any) => v.uuid || v.id,
    },
    {
      title: '新闻频道',
      key: 'type',
      dataIndex: 'channel_name',
      width: 90,
    },
    {
      title: '新闻标题',
      key: 'list_title',
      dataIndex: 'list_title',
      width: 150,
    },
    {
      // width: 90,
      title: '自定义标题',
      key: 'new_list_title',
      dataIndex: 'new_list_title',
      render: (text: string, record: any, index: number) => {
        return (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <div>{record.new_list_title || record.list_title}</div>
            <a
              style={{ flex: 'none', marginLeft: '5px' }}
              onClick={() => editTitle(record.new_list_title || record.list_title, index)}
            >
              修改
            </a>
          </div>
        );
      },
    },
    {
      title: '列表图',
      align: 'center',
      // title: '列表图',
      dataIndex: 'new_list_pic',
      width: 100,
      render: (text: string, record: any, index: number) => {
        const img = text || record.list_pics?.split(',')?.[0];
        return (
          <div style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
            {img && <img src={img} style={{ height: 40, maxWidth: 70 }} />}
            <A onClick={() => editPic(img, index)}>{img ? '修改' : '上传'}</A>
          </div>
        );
      },
    },
  ];

  return (
    <>
      <Form.Item label="推荐位名称" extra="该名称仅用于后台显示，便于管理，前台只显示推荐的稿件">
        {getFieldDecorator('title', {
          initialValue: props.data?.recommend?.title,
          rules: [
            {
              required: true,
              message: '请输入推荐位名称',
              whitespace: true,
            },
            {
              max: 15,
              message: '推荐位名称最长不能超过15个字',
            },
          ],
        })(<Input placeholder="请输入名称，不超过15个字" />)}
      </Form.Item>

      {!props.data && (
        <Form.Item label="显示页面">
          {getFieldDecorator('board_id', {
            initialValue: `${props.board_id || 0}`,
            rules: [
              {
                required: true,
                message: '请选择显示页面',
              },
            ],
          })(
            <Select placeholder="请选择显示页面" style={{ width: 150 }}>
              {props.blockOptions?.map((item: any) => (
                <Select.Option key={`${item.value}`} value={`${item.value}`}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
          )}
          <span style={{ marginLeft: 10 }}>列表</span>
          <Tooltip title="1、全部是指圈子下的「全部」列表，不是所有标签页；2、全部或版块列表相互独立，需分别配置">
            <Icon style={{ marginLeft: 5 }} type="question-circle" />
          </Tooltip>
        </Form.Item>
      )}

      {!props.data && (
        <Form.Item label="显示位置">
          {getFieldDecorator('position', {
            // initialValue: 1,
            rules: [
              {
                required: true,
                message: '请选择显示位置',
              },
            ],
          })(
            <InputNumber
              placeholder="请输入在列表中显示的位置序号，1~500正整数"
              min={1}
              max={500}
              precision={0}
              style={{ width: 350 }}
            />
          )}
          <Tooltip
            title={
              <>
                直接输入阿拉伯数字代表位置序号，例如要在圈子下某个列表第8个位置插入该推荐位，则直接输入“8”即可。
                <br />
                注：
                直接在输入的指定位置上插入推荐位显示，参与圈子内容列表的排序，且优先于圈子普通内容展示
              </>
            }
          >
            <Icon style={{ marginLeft: 5 }} type="question-circle" />
          </Tooltip>
        </Form.Item>
      )}

      <Form.Item label="关联稿件">
        {getFieldDecorator('article_list', {
          initialValue: articleList,
          validateFirst: true,
          rules: [
            {
              required: true,
              message: '请关联新闻',
              type: 'array',
            },
            {
              max: 1,
              message: '最多关联1条新闻',
              type: 'array',
            },
            {
              validator: (rule: any, val: any, callback: any) => {
                if (val.filter((v: any) => !v.new_list_pic).length > 0) {
                  return callback('请上传列表图');
                } else {
                  return callback();
                }
              },
            },
          ],
        })(
          <SearchAndInput
            // key={style}
            max={1}
            func="listArticleRecommendSearch"
            columns={columns}
            placeholder="输入ID或标题关联稿件"
            body={{ doc_types: '2,3,4,5,8,9' }}
            order={false}
            addOnTop={true}
            map={(v: any) => {
              return {
                ...v,
                new_list_pic: v.list_pics?.split(',')?.[0],
              };
            }}
            afix="仅限媒立方稿件"
          />
        )}
      </Form.Item>
    </>
  );
});

export default CircleArticleRecommend;
