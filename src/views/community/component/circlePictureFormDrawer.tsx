import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import { Button, Dropdown, Form, Icon, Input, InputNumber, Menu, Modal, Radio, Select, Spin, Tooltip, message } from 'antd'
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react'

import { communityApi } from '@app/api';
import '@components/business/styles/business.scss'
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { Drawer, ImageUploader, VideoUploader } from '@app/components/common';

const CirclePictureFormDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();

  const { getFieldDecorator } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
      doSubmit
    }),
    []
  );

  const doSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body: any = { ...values, type: props.type, circle_id: props.circle_id };


        if (props.record) {
          body.id = props.record.id
        }

        dispatch(setConfig({ mLoading: true }));
        (props.record ? communityApi.editCirclePicRecommend : communityApi.createCirclePicRecommend)(body)
          .then(() => {
            dispatch(setConfig({ mLoading: false }))
            message.success('操作成功');
            props.onEnd();
          })
          .catch(() => {
            dispatch(setConfig({ mLoading: false }))
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  }

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  return <Drawer
    title={props.record ? '编辑推荐位' : '添加推荐位'}
    visible={props.visible}
    skey={props.skey}
    onClose={props.onClose}
    onOk={doSubmit}
    okText='确定'
  // footer={((<><Button style={{ width: 100, marginRight: 8 }} onClick={props.onClose}>取消</Button>
  //   <Dropdown trigger={['hover']} overlay={menu} placement="topCenter">
  //     <PermButton style={{ width: 100 }} type="primary" perm='' onClick={props.onOk}>
  //       保存<Icon type="up" />
  //     </PermButton>
  //   </Dropdown>

  // </>))}
  >
    <Form {...formLayout}>

      <Form.Item label="图片" extra={`支持jpg,jpeg,png图片格式，推荐比例${props.type == 1 ? '690:110' : '2:1'}`}>
        {getFieldDecorator('image_url', {
          initialValue: props.record?.image_url,
          rules: [
            {
              required: true,
              message: '请上传图片',
            },
          ],
        })(
          <ImageUploader
            // ratio={1}
            ratio={props.type == 1 ? 690 / 110 : 2 / 1}
            // needMz={true}
            // isCutting={true}
            accept={['image/jpeg', 'image/png', 'image/jpg']}
          />
        )}
      </Form.Item>

      <Form.Item label="链接">
        {getFieldDecorator('url', {
          initialValue: props.record?.url,
          rules: [
            {
              required: true,
              message: '链接不能为空',
              whitespace: true
            },
            {
              pattern: /^https?:\/\//,
              message: "请输入正确的链接格式"
            }
          ],
        })(<Input placeholder='请输入链接'></Input>)}
      </Form.Item>

    </Form>
  </Drawer>
}

export default Form.create<any>({ name: 'CirclePictureFormDrawer' })(forwardRef<any, any>(CirclePictureFormDrawer));
