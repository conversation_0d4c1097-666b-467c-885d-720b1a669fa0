import { communityApi } from '@app/api';
import {
  Button,
  Col,
  Form,
  Icon,
  Input,
  InputNumber,
  message,
  Modal,
  Radio,
  Row,
  Select,
  Tooltip,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import SearchAndInput from '@components/common/newNewsSearchAndInput';
import { A, ImageUploader } from '@app/components/common';
import TMFormList, { FormListTitle, TMFormListRef } from '@app/components/common/TMFormList';
import _ from 'lodash';
import './community.scss';

const initList = [
  {
    account_id: undefined,
    reason: '',
  },
  {
    account_id: undefined,
    reason: '',
  },
];
const CircleUserRecommendForm = forwardRef<any, any>((props: any, ref: any) => {
  const { getFieldDecorator, getFieldsValue, setFieldsValue } = props.form;
  const formListRef = useRef<TMFormListRef>(null);
  const [platformList, setPlatformList] = useState<any[]>(initList);

  const [optionData, setOptionData]: any = useState([]);

  const handleSearch = _.debounce((val: any) => {
    let data = { keyword: val.toString() };
    if (!val) return;
    communityApi.recommendAccount_Search(data).then((res: any) => {
      let records = [...res.data.list];
      setOptionData(records);
    });
  }, 500);

  const handleSelect = (e: any, i: number) => {
    const data = e ? JSON.parse(e) : {};
    const list = getFieldsValue().list;
    const index = list.findIndex((item: any) => item.account_id == data.id);
    if (index == -1 && e) {
      list[i].chao_id = data.chao_id;
      list[i].nick_name = data.nick_name;
      list[i].account_id = data.id;
    } else {
      list[i].account_id = undefined;
      list[i].chao_id = undefined;
      list[i].nick_name = undefined;
      if (index != -1) {
        message.error('该用户已添加推荐');
      }
    }
    setPlatformList(list);
    setFieldsValue({
      list,
    });
  };

  useImperativeHandle(
    ref,
    () => ({
      getFormData: (formData: any) => {
        const title_show = formData.title_style >= 0;

        const result = {
          ...formData,
          title_show,
          title_pic: formData.title_style >= 1 ? formData.title_pic : '',
          ref_type: 23,
          style: 0,
          ref_extensions: JSON.stringify(formData.list),
        };

        if (title_show) {
          result.title_style = formData.title_style;
        } else {
          delete result.title_style;
        }

        delete result.list;
        return result;
      },
    }),
    []
  );

  useEffect(() => {
    const list = props.data?.user_list || initList;
    setPlatformList(list);
  }, [JSON.stringify(props.data)]);

  const editData = props.data?.recommend || {};
  editData.title_style = editData.show_title ? editData.title_style : -1;

  return (
    <>
      <RecommendCommon form={props.form} editData={editData} isEdit={true}></RecommendCommon>

      {!props.data && (
        <Form.Item label="显示页面">
          {getFieldDecorator('board_id', {
            initialValue: `${props.board_id || 0}`,
            rules: [
              {
                required: true,
                message: '请选择显示页面',
              },
            ],
          })(
            <Select placeholder="请选择显示页面" style={{ width: 150 }}>
              {props.blockOptions?.map((item: any) => (
                <Select.Option key={`${item.value}`} value={`${item.value}`}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
          )}
          <span style={{ marginLeft: 10 }}>列表</span>
          <Tooltip title="1、全部是指圈子下的「全部」列表，不是所有标签页；2、全部或版块列表相互独立，需分别配置">
            <Icon style={{ marginLeft: 5 }} type="question-circle" />
          </Tooltip>
        </Form.Item>
      )}

      {!props.data && (
        <Form.Item label="显示位置">
          {getFieldDecorator('position', {
            // initialValue: props.data?.position,
            rules: [
              {
                required: true,
                message: '请选择显示位置',
              },
            ],
          })(
            <InputNumber
              placeholder="请输入在列表中显示的位置序号，1~500正整数"
              min={1}
              max={500}
              precision={0}
              style={{ width: 350 }}
            />
          )}
          <Tooltip
            title={
              <>
                直接输入阿拉伯数字代表位置序号，例如要在圈子下某个列表第8个位置插入该推荐位，则直接输入“8”即可。
                <br />
                注：
                直接在输入的指定位置上插入推荐位显示，参与圈子内容列表的排序，且优先于圈子普通内容展示
              </>
            }
          >
            <Icon style={{ marginLeft: 5 }} type="question-circle" />
          </Tooltip>
        </Form.Item>
      )}

      <TMFormList
        ref={formListRef}
        dataList={platformList}
        form={props.form}
        fromItem={() => {
          return {
            reason: '',
            account_id: undefined,
          };
        }}
        filed="list"
      >
        {(item: any, i: any, total: number) => {
          const { chao_id } = item;
          return (
            <div key={i}>
              <FormListTitle
                allowClear={true}
                total={total}
                i={i}
                title="用户"
                min={2}
                upMove={() => formListRef.current?.upMove(i)}
                downMove={() => formListRef.current?.downMove(i)}
                removeItem={() => formListRef.current?.removeItem(i)}
              ></FormListTitle>
              {i == 0 && (
                <p
                  style={{
                    paddingLeft: '60px',
                  }}
                >
                  最多添加20个用户 *当前圈子为单列或极简样式时，可显示推荐理由；双列样式下，不显示推荐理由
                  <Tooltip
                    title={
                      <>
                        <img width={200} height={333} src="/assets/circle_user_rec_tip1.png"></img>
                      </>
                    }
                  >
                    <Icon type="question-circle-o" />
                  </Tooltip>
                </p>
              )}
              <Form.Item label="推荐用户">
                {getFieldDecorator(`list[${i}].account_id`, {
                  rules: [
                    {
                      required: true,
                      message: '请输入用户昵称/小潮号',
                    },
                  ],
                })(
                  item.account_id ? (
                    <span>
                      <Input
                        disabled
                        style={{ width: '80%' }}
                        value={`${item?.nick_name} | 小潮号：${item?.chao_id}`}
                      ></Input>
                      &nbsp;
                      <a onClick={() => handleSelect(null, i)}>更改</a>
                    </span>
                  ) : (
                    <Select
                      style={{ width: '80%' }}
                      placeholder="请输入用户昵称或小潮号"
                      onSearch={(e) => handleSearch(e)}
                      onSelect={(e) => handleSelect(e, i)}
                      defaultActiveFirstOption={false}
                      showSearch
                    >
                      {optionData.map((d: any) => (
                        <Select.Option
                          key={d.chao_id}
                          value={JSON.stringify(d)}
                        >{`${d.nick_name} | 小潮号：${d.chao_id}`}</Select.Option>
                      ))}
                    </Select>
                  )
                )}
              </Form.Item>
              <Form.Item style={{ display: 'none' }}>
                {getFieldDecorator(`list[${i}].chao_id`, {})(<Input disabled />)}
              </Form.Item>
              <Form.Item style={{ display: 'none' }}>
                {getFieldDecorator(`list[${i}].nick_name`, {})(<Input disabled />)}
              </Form.Item>

              <Form.Item label="推荐理由：">
                {getFieldDecorator(`list[${i}].reason`, {
                  // initialValue: reason,
                  rules: [
                    {
                      message: '请输入推荐理由，最多15字',
                    },
                  ],
                })(
                  <Input
                    style={{ width: '80%' }}
                    maxLength={15}
                    placeholder="请输入推荐理由，最多15字"
                  />
                )}
                <Tooltip
                  className=""
                  title="用于向其他用户介绍该用户。如未填写，认证用户将展示认证信息；未认证用户将展示个性签名；以上信息均无，则显示空白。"
                  placement="topLeft"
                >
                  &nbsp;
                  <Icon className="ant-tooltip_icon" type="question-circle-o" />
                </Tooltip>
              </Form.Item>
            </div>
          );
        }}
      </TMFormList>

      <Row style={{ textAlign: 'center', marginBottom: 16 }} id="add-btn-line">
        <Button
          onClick={() => formListRef.current?.addItem()}
          disabled={(formListRef.current?.total ?? 0) >= 20}
        >
          添加用户
        </Button>
      </Row>
    </>
  );
});

function RecommendCommon(props: any) {
  const {
    form: { getFieldDecorator },
    children,
    editData,
    isEdit,
    hiddenTitleType,
    isReport = false,
  } = props;
  const [title_style, setTitle_style] = useState(-1);
  let { maxPosition } = props;
  if (!maxPosition || maxPosition >= 500) {
    maxPosition = 500;
  } else {
    maxPosition += 1;
  }
  const positionTip = (
    <span>
      <p>输入说明：</p>
      <p>
        直接输入阿拉伯数字代表位置序号，例如要在头条频道信息流第8个位置插入该推荐位，则直接输入“8”即可。
      </p>
      <p>最大输入数字为{maxPosition}</p>
      <br />
      <p>注：</p>
      <p>直接在输入的指定位置上插入推荐位显示，参与稿件列表的排序，且优先于稿件展示</p>
    </span>
  );
  const titleStyleDefaultValue =
    typeof editData.title_style === 'undefined' ? -1 : editData.title_style;

  useEffect(() => {
    if (editData.title_style) {
      setTitle_style(editData.title_style);
    }
  }, [editData.title_style]);

  const titleStyleChange = (e: any) => {
    const val = e.target.value;
    setTitle_style(val);
    if (val === 1 || val === 2) {
      setTimeout(() => {
        const {
          form: { setFieldsValue },
        } = props;
        setFieldsValue({ title_pic: '' });
      }, 0);
    }
  };

  const titleStyleTip = (desc: string, picName: string, width: number, height: number) => {
    return (
      <div>
        <div style={{ marginBottom: 5 }} dangerouslySetInnerHTML={{ __html: desc }}></div>
        <img src={`/assets/${picName}.png`} width={width} height={height} />
      </div>
    );
  };

  return (
    <>
      <Form.Item label="推荐位名称" style={{ display: 'block' }}>
        {getFieldDecorator('title', {
          initialValue: editData.title,
          rules: [
            {
              required: true,
              message: '请输入推荐位名称',
            },
            {
              max: 15,
              message: '推荐位名称最长不能超过15个字',
            },
          ],
        })(<Input placeholder="请输入名称，不超过15个字" />)}
      </Form.Item>
      {!hiddenTitleType && (
        <Form.Item style={{ marginLeft: 130, marginTop: -5, whiteSpace: 'nowrap' }}>
          {getFieldDecorator('title_style', {
            initialValue: titleStyleDefaultValue,
          })(
            <Radio.Group onChange={titleStyleChange}>
              <Radio style={{ fontSize: 13 }} value={-1}>
                不显示标题
              </Radio>
              {!isReport && (
                <Radio style={{ fontSize: 13 }} value={0}>
                  标题为默认样式 &nbsp;
                  <Tooltip
                    title={titleStyleTip(
                      '图标默认为蓝色竖条标题为<br/>推荐位名称',
                      'recommend_title1',
                      200,
                      49
                    )}
                  >
                    <Icon type="question-circle" />
                  </Tooltip>
                </Radio>
              )}
            </Radio.Group>
          )}
        </Form.Item>
      )}
      {!hiddenTitleType && title_style === 1 && (
        <Form.Item
          style={{ marginLeft: 120, marginTop: -20 }}
          extra="支持上传jpg,jpeg,png,gif图片格式, 比例未限制, 建议高度75px"
        >
          {getFieldDecorator('title_pic', {
            initialValue: editData.title_pic,
            rules: [
              {
                required: true,
                message: '请上传图片',
              },
            ],
          })(<ImageUploader isCutting />)}
        </Form.Item>
      )}
      {!hiddenTitleType && title_style === 2 && (
        <Form.Item
          style={{ marginLeft: 120, marginTop: -20 }}
          extra="支持上传jpg,jpeg,png,gif图片格式,比例为1:1,建议尺寸60px*60px"
        >
          {getFieldDecorator('title_pic', {
            initialValue: editData.title_pic,
            rules: [
              {
                required: true,
                message: '请上传图片',
              },
            ],
          })(<ImageUploader ratio={1 / 1} />)}
        </Form.Item>
      )}
      {children}
    </>
  );
}

export default CircleUserRecommendForm;
