import {Button, Col, Icon, Input, Modal, Row, Select, Tooltip,  Form} from "antd"
import { getCrumb,  UserDetail} from "@app/utils/utils"
import { useSelector, useDispatch } from 'react-redux';
import { getTableList } from '@app/action/tableList';
import {PreviewMCN, Table} from '@components/common';
import React, { useEffect, useState } from "react"
import GrantboosterCard from "./component/GrantboosterCard";
import { sysApi, userApi} from "@app/api";
import { PermA, PermButton } from "@app/components/permItems";
import { setConfig } from "@app/action/config";
import moment from "moment";

export default function BoosterCardMgr(props: any) {
  const dispatch = useDispatch();
  const { total, current, size, records = [] } = useSelector((state: any) => state.tableList)
  const [filter, setFilter] = useState({
    status: '',
    grant_type: '',
    search_type: 1,
    keyword: ''
  })
  const [searchState, setSearchState] = useState({
    search_type: 1,
    keyword: ''
  })
  const [circleList, setCircleList] = useState<any>([])
  const [authorityDrawerVisiable, setAuthorityDrawerVisiable] = useState(false)
  const [editRecord, setEditRecord] = useState(null)
  const [user,setUser] = useState({
    key: Date.now(),
    visible: false,
    detail: {}
  })
  const [onState,setOnState] = useState({
    key: new Date(),
    visible: false,
    detail:{}
  })
  const [preview,setPreview] = useState({
    visible: false,
    key: Date.now(),
    data: {}
  })
  const getSeq = (i: number) => (current - 1) * size + i + 1;
  // 显示用户详情
  const showUserDetailModal =(record: any, visible: boolean) => {
    userApi
        .getUserDetail({ accountId: record.account_id })
        .then((r: any) => {
          setUser({
            key: Date.now(),
            visible:true,
            detail: r.data.account,
          })
        })
  }
  // 设为失效
  const setLapse = (record:any) => {
    Modal.confirm({
      title: '确定设为失效?',
      onOk: () => {
        sysApi.setCardInvalid({id:record.id})
            .then((r: any) => {
              getData()
            })
      },
      onCancel() { },
    });
  }
  const openPreview = (record: any) => {
    const data = record
    data.id = record.article_id
     setPreview({
       visible: true,
       key: Date.now(),
       data: data
     })
  };
  const closePreview = () => {
    setPreview({
      ...preview, visible: false
    })
  };
  const getFilter = () => {
    return filter;
  };
  const columns: any = [
    {
      title: '序号',
      key: 'order',
      render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
      width: 90,
    },
    {
      title: '助推卡ID',
      key: 'id',
      dataIndex: 'id',
      width: 70,
    },
    {
      title: '归属用户',
      key: 'nick_name',
      dataIndex: 'nick_name',
      width: 110,
      render: (text: any, record: any) => (<a onClick={() => showUserDetailModal(record, true)}>{text}</a>)
    },
    {
      title: '目标次数',
      key: 'target_number',
      dataIndex: 'target_number',
      width: 80
    },
    {
      title: '使用状态',
      key: 'status',
      dataIndex: 'status',
      render: (text: number,record) => <span
          onClick={()=>{
            if ([1,-1].includes(text)) {
              if (record.status == -1){
                setOnState({
                  key: new Date(),
                  visible: true,
                  detail:record
                })
              }else {
                sysApi.getBoostDetail({id: record.id}).then(res=>{
                  const {detail} = res.data
                  detail.article_id = record.article_id
                  setOnState({
                    key: new Date(),
                    visible: true,
                    detail:detail
                  })
                })
              }

            }
          }}
          style={{color: [1,-1].includes(text) ? '#1890ff' : 'none',cursor:"pointer"}}>
        {['已过期','已失效','未使用','已使用',''][text+2]}</span>,
      width: 80,
    },
    {
      title: '发放时间',
      dataIndex: 'grant_at',
      render: (text: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 130,
    },
    {
      title: '发放方式',
      dataIndex: 'grant_type',
      render: (text: any) => <span>{['系统发放','后台发放'][text]}</span>,
      width: 80,
    },
    {
      title: '发放人',
      key: 'created_by',
      dataIndex: 'created_by',
      width: 110,
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any) => (
        <div style={{textAlign:"center",width:60}}>
          {record.status == 0 ? <PermA perm="ugc:encourage:card:invalid" onClick={() => setLapse(record)}>设为失效</PermA> : '-'}
        </div>
      ),
      width: 100,
    },
  ]


  const handleEditAuthorityBlock = (record: any) => {
    setEditRecord(record)
    setAuthorityDrawerVisiable(true)
  }

  const formLayout = {
    labelCol: { span: 5 },
    wrapperCol: { span: 17 },
  };
  const changeFilter = (key: string, val: any, goToFirstPage = false) => {
    const newFilter = {
      ...filter,
      [key]: val
    }
    setFilter(newFilter)
    getData(true, newFilter)
  }

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      const newFilter = {
        ...filter,
        ...searchState
      }
      setFilter(newFilter)
      getData(true, newFilter)
    }
  };

  const getData = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current
    dispatch(getTableList('getUGCCardList', 'list', { current: cur, size, ...newFilter }));
  }

  const submitEnd = (goToFirstPage: boolean) => {
    getData(goToFirstPage)
    setAuthorityDrawerVisiable(false)
  }

  useEffect(() => {
    const { selectKeys, openKeys } = props
    dispatch(
      setConfig({ selectKeys, openKeys })
    );
    getData(true)
  }, [])

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <>
            <PermButton perm="ugc:encourage:card:grant" onClick={() => handleEditAuthorityBlock(null)}>发放助推卡</PermButton>&nbsp;
            <Tooltip title="向指定账号发放助推卡，作者可任意选择自己近7天内发布的非沉底内容使用，增加展示机会。发放后默认7天有效期。" placement="top">
              <Icon type="question-circle" />
            </Tooltip>
          </>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={14}>
            <Select style={{ width: 110 }} value={filter.status} onChange={(val) => changeFilter('status', val)}>
              <Select.Option value="">使用状态</Select.Option>
              <Select.Option value={1}>已使用</Select.Option>
              <Select.Option value={0}>未使用</Select.Option>
              <Select.Option value={-1}>已失效</Select.Option>
              <Select.Option value={-2}>已过期</Select.Option>
            </Select>
            <Select style={{ width: 110,marginLeft: 8 }} value={filter.grant_type} onChange={(val) => changeFilter('grant_type', val)}>
              <Select.Option value="">发放方式</Select.Option>
              <Select.Option value={0}>系统发放</Select.Option>
              <Select.Option value={1}>后台发放</Select.Option>
            </Select>
          </Col>
          <Col span={10} style={{ textAlign: 'right' }}>
            <Select
              value={searchState.search_type}
              style={{ width: 110, marginRight: 8 }}
              onChange={(search_type: any) => setSearchState({ ...searchState, search_type })}
            >
              <Select.Option value={1}>用户昵称</Select.Option>
              <Select.Option value={2}>小潮号</Select.Option>
            </Select>
            <Input
              value={searchState.keyword}
              style={{ marginRight: 8, width: 160 }}
              onChange={(e: any) => setSearchState({ ...searchState, keyword: e.target.value })}
              onKeyPress={handleKey}
              placeholder="输入搜索内容"
            />
            <Button onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" /> 搜索
            </Button>
          </Col>
        </Row>
        <Table
          func="getUGCCardList"
          filter={getFilter()}
          index="list"
          pagination={true}
          rowKey="id"
          columns={columns}
        />
        <GrantboosterCard
          record={editRecord}
          circleList={circleList}
          visible={authorityDrawerVisiable}
          getData={() => { getData() }}
          onClose={() => setAuthorityDrawerVisiable(false)}
          onEnd={submitEnd} />
        <Modal
            visible={user.visible}
            key={user.key}
            title="用户详情"
            width={800}
            onCancel={() => {setUser({
              key: Date.now(),
              visible:false,
              detail: {},
            })}}
            onOk={() => {setUser({
              key: Date.now(),
              visible:false,
              detail: {},
            })}}
        >
          {/*{user.visible && getUserDetail(user.detail)}*/}
          {user.visible && <UserDetail detail={user.detail}/>}
        </Modal>
        <Modal
            visible={onState.visible}
            key={onState.key}
            title={onState.detail.status == -1 ? '失效情况':'使用情况'}
            width={530}
            onCancel={() => {setOnState({...onState,visible: false})}}
            onOk={() => {setOnState({...onState,visible: false})}}
        >
          {onState.visible && <>
          <Form {...formLayout}>
            {
              onState.detail.status == -1 ? <>
                <Form.Item label="失效时间">{moment(onState.detail.invalid_at).format('YYYY-MM-DD HH:mm:ss')}</Form.Item>
                <Form.Item label="操作人">{onState.detail.invalid_by}</Form.Item>
              </> : <>
                <Form.Item label="助推内容">
                  <a
                      onClick={()=>{openPreview(onState.detail)}}
                      title={onState.detail.list_title}
                      style={{
                        display: '-webkit-box',
                        textOverflow: 'ellipsis',
                        WebkitLineClamp: 1,
                        WebkitBoxOrient: 'vertical',
                        overflow: "hidden",
                      }}
                  >
                    {onState.detail.doc_type == 12 && !onState.detail.list_title ? '图片内容' :
                    onState.detail.list_title }</a></Form.Item>
                <Form.Item label="潮新闻ID">{onState.detail.article_id}</Form.Item>
                <Form.Item label="创作者平台ID">{onState.detail.metadata_id}</Form.Item>
                <Form.Item label="使用时间">{moment(onState.detail.use_at).format('YYYY-MM-DD HH:mm:ss')}</Form.Item>
              </>
            }
          </Form>
          </>}
        </Modal>
        <PreviewMCN skey={preview.key} {...preview} onClose={closePreview} />
      </div>
    </>
  )
}