/* eslint-disable react/no-string-refs */
import { setConfig } from '@action/config';
import { userApi as api } from '@app/api';
import { CardEditor } from '@components/common';
import connect from '@utils/connectSession';
import { getCrumb, requirePerm } from '@utils/utils';
import { Button, Col, Icon, message, Row, Switch } from 'antd';
import React from 'react';
import { withRouter } from 'react-router';

@(withRouter as any)
@connect
class ActiveValueRule extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      editing: false,
      mall: false,
    };
  }

  componentDidMount() {
    this.getMallStatus();
    this.getData();
    this.props.dispatch(
      setConfig({
        selectKeys: this.props.selectKeys,
        openKeys: this.props.openKeys,
      })
    );
  }

  getData = () => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .getHContent(12)
      .then((r: any) => {
        this.props.dispatch(setConfig({ loading: false }));
        this.setState({ content: r.data.content, editing: false });
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  submitData = () => {
    const content = (this.refs.ce as any).getData();
    console.log(content);
    if (content === '') {
      message.error('请填写积分规则');
      return;
    }
    this.props.dispatch(setConfig({ loading: true }));
    api
      .updateHContent({ content, type: 12 })
      .then(() => {
        message.success('操作成功');
        this.getData();
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  getMallStatus = () => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .getMallSwitch()
      .then((res: any) => {
        this.setState({
          mall: res.data.switch,
        });
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  handleMallChange = (checked: boolean) => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .updateMallSwitch({
        status: checked,
      })
      .then(() => {
        message.success('操作成功');
        this.getMallStatus();
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  render() {
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            {this.state.editing ? (
              <>
                <Button key="x" onClick={this.submitData} type="primary" style={{ marginRight: 8 }}>
                  保存
                </Button>
                <Button key="y" onClick={() => this.setState({ editing: false })}>
                  取消
                </Button>
              </>
            ) : (
              <>
                <Button style={{ marginRight: 8 }} onClick={() => this.props.history.goBack()}>
                  返回
                </Button>
                {requirePerm(
                  this,
                  'h5Content:update:12'
                )(
                  <Button key="z" onClick={() => this.setState({ editing: true })}>
                    <Icon type="edit" /> 编辑
                  </Button>
                )}
              </>
            )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <CardEditor
            title="圈友活跃值规则说明"
            value={this.state.content}
            editing={this.state.editing}
            ref="ce"
          />
        </div>
      </>
    );
  }
}

export default ActiveValueRule;
