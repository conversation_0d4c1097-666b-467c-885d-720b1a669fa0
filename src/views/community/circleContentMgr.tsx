import {
  getCrumb,
  jumpToEdit,
  recommendTypeText,
  requirePerm4Function,
  resolveNewsType,
  searchToObject,
  showDataSetModal,
  showIDDetailModal,
  showReadCountDetailModal,
  UserDetail,
} from '@app/utils/utils';
import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Dropdown,
  Icon,
  Input,
  InputNumber,
  Menu,
  Modal,
  Radio,
  Row,
  Select,
  Tag,
  Timeline,
  Tooltip,
  message,
} from 'antd';
import { Table, PreviewMCN, OrderColumn } from '@components/common';
import { useDispatch, useSelector, useStore } from 'react-redux';
import { useHistory, useParams } from 'react-router';
import { getTableList } from '@app/action/tableList';
import ReactClipboard from 'react-clipboardjs-copy';
import React, { useEffect, useState } from 'react';
import _, { values } from 'lodash';
import { communityApi as api, listApi, opApi, releaseListApi, userApi } from '@app/api';
import { setConfig } from '@app/action/config';
import { PermA, PermButton, PermMenuItem, PermSwitch } from '@app/components/permItems';
import moment from 'moment';
import FixedContentSortDrawer from './component/FixedContentSortDrawer';
import CircleContentTopContentDrawer from './component/CircleContentTopContentDrawer';
import EditCircleInfoModal from './component/EditCircleInfoModal';
import EditCircleContentLevelModal from './component/EditCircleContentLevelModal';
import showImagePreviewModal from '@app/components/common/imagePreviewModal';
import ImagePreviewColumn from '@app/components/common/imagePreviewColumn';
import EditFixedSortModal from './component/editFixedSortModal';
import GetVisaModal from '@app/components/business/GetVisaModal';
import CircleRecommendDrawer from './component/circleRecommendDrawer';
import ContentRecommendDrawer from '@app/components/business/recommend/ContentRecommendDrawer';
import '../news/index.scss';

export default function CircleContentMgr(props: any) {
  const dispatch = useDispatch();
  const history = useHistory();
  const { id: circle_id, name } = useParams<any>();
  const { selected = '', board_id = '' } = searchToObject();
  const { session } = useStore().getState();
  const {
    current,
    size,
    records = [],
    allData: { real_total = 999999 },
  } = useSelector((state: any) => state.tableList);
  const [filter, setFilter] = useState({
    content_level: '',
    selected: selected ? parseInt(selected) : '',
    board_id: board_id ? parseInt(board_id) : '',
    doc_type: '',
    publish_time_start: '',
    publish_time_end: '',
    topic_id: '',
    retrieved: '',
    circle_id: circle_id,
  });
  const [searchState, setSearchState] = useState({
    search_type: 1,
    keyword: '',
  });

  const [getVisa, setGetVisa] = useState({
    visible: false,
    record: null,
  });
  const [topicLoading, setTopicLoading] = useState(false);
  const [topicList, setTopicList] = useState([]);
  // const [authorOptions, setAuthorOptions] = useState([]);
  const [blockOptions, setBlockOptions] = useState<any>(null);
  const [levelModalVisiable, setLevelModalVisiable] = useState(false);
  const [circleInfoModalVisiable, setCircleInfoModalVisiable] = useState(false);
  const [editRecord, setEditRecord] = useState(null);
  const [preview, setPreview] = useState({
    visible: false,
    skey: Date.now(),
    data: {},
  });
  const [user, setUser] = useState({
    key: Date.now(),
    visible: false,
    detail: {},
  });

  const [recommendCreate, setRecommendCreate] = useState<any>({
    visible: false,
    key: null,
    record: null,
  });

  const [operateLog, setOperateLog] = useState<any>({
    visible: false,
    // key: uuid(),
    logs: null,
  });

  const [dragSorter, setDragSorter] = useState(false);

  const [topContentDrawerVisiable, setTopContentDrawerVisiable] = useState(false);
  const [fixedContentSortDrawerVisiable, setFixedContentSortDrawerVisiable] = useState(false);
  const canSeeAccount = session.permissions.indexOf('account:detail') > -1;
  const [fixedSort, setFixedSort] = useState<any>({
    visible: false,
    record: null,
  });
  const handleEditContentLevel = (record: any) => {
    setEditRecord(record);
    setLevelModalVisiable(true);
  };

  const handleEditArticle = (record: any) => {
    if (record.doc_type == -1) {
      // 推荐位
      setRecommendCreate({
        visible: true,
        key: Date.now(),
        record: record,
      });
      return;
    }

    dispatch(setConfig({ loading: true }));
    releaseListApi
      .getArticleEditUrl({ id: record.article_id })
      .then((res: any) => {
        dispatch(setConfig({ loading: false }));
        if (res.data.token) {
          jumpToEdit(res.data.token, record.doc_type);
          Modal.confirm({
            title: '是否已经操作完成，需要刷新页面？',
            okText: '确定',
            cancelText: '取消',
            onOk: () => {
              getData();
            },
          });
        }
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  const handleEditCircleInfo = (record: any) => {
    setEditRecord(record);
    setCircleInfoModalVisiable(true);
  };

  const changeVisible = (record: any) => {
    dispatch(setConfig({ loading: true }));
    releaseListApi
      .circleArticleSetDown({
        circle_id,
        bord_id: filter.board_id || 0,
        article_id: record.article_id,
        down: !record.down,
      })
      .then(() => {
        message.success('操作成功');
        setTimeout(() => {
          getData();
          dispatch(setConfig({ loading: false }));
        }, 1000);
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  const handleDeleteRecommend = (record: any) => {
    Modal.confirm({
      title: <p>{`确认删除推荐位《${record.recommend_name}》？`}</p>,
      content: '',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        releaseListApi
          .chaokeRevokeNews({
            id: record.article_id,
          })
          .then(() => {
            message.success('操作成功');
            setTimeout(() => {
              getData();
              dispatch(setConfig({ loading: false }));
            }, 1000);
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const handleRecommendSort = (record: any) => {
    const fixed_position = !!filter.board_id ? record.board_fixed_number : record.fixed_number;

    let position = fixed_position > 0 ? fixed_position : undefined;
    let sticky = fixed_position > 0;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      position = v;
    };
    const stickyChange = (e: any) => {
      sticky = e.target.checked;
    };

    Modal.confirm({
      title: <p>排序：《{record.recommend_name}》</p>,
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber
            style={{ width: 150 }}
            placeholder="请输入位置"
            min={1}
            // max={999999999999}
            defaultValue={position}
            onChange={positionChange}
          />
          <br key={1} />
          {record.content_level != 1 && (
            <Checkbox
              style={{ marginTop: 8 }}
              defaultChecked={sticky}
              onChange={stickyChange}
              key={2}
            >
              固定位置
            </Checkbox>
          )}
          <p style={{ color: '#ccc' }} key={3}>
            此选项只对1~100位置有效
            <br />
            最多只能固定40条稿件或推荐位
          </p>
        </div>
      ),
      onOk: (closeFunc: Function) => {
        const MAX_STICKY_POSITION = 100;
        if (position == undefined || position == null) {
          message.error('请填写位置');
          return;
        }
        if (sticky && position > MAX_STICKY_POSITION) {
          message.error('固定位置必须为1~100');
          return;
        }

        dispatch(setConfig({ loading: true }));
        const params: any = {
          circle_id: record.circle_id,
          article_id: record.article_id,
          position: position,
          fixed: sticky,
          board_id: 0,
        };
        if (!!filter.board_id) {
          params.board_id = filter.board_id;
        }
        releaseListApi
          .editCircleArticleFixDetail({ data: JSON.stringify([params]) })
          .then(() => {
            message.success('操作成功');
            dispatch(setConfig({ loading: false }));
            getData();

            closeFunc();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const handleEditFixedSort = (record: any) => {
    dispatch(setConfig({ loading: true }));
    releaseListApi
      .getCircleArticleFixDetail({ article_id: record.article_id, circle_id: record.circle_id })
      .then((res: any) => {
        dispatch(setConfig({ loading: false }));
        setFixedSort({
          visible: true,
          record,
          skey: Date.now(),
          sort: res.data?.article_list,
        });
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  const toggleWinnowStatus = (record: any) => {
    const { article_id, selected } = record;
    Modal.confirm({
      title: record.selected ? '确定将该内容取消精选？' : '确定将该内容设为精选？',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        api
          .setCircleContentSelected({ article_id, selected: selected ? 0 : 1 })
          .then(() => {
            dispatch(setConfig({ loading: false }));
            getData();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const handleCancelPub = (record: any) => {
    Modal.confirm({
      title: (
        <p>
          确认取消签发《
          {record.title?.length > 30 ? record.title.slice(0, 30) + '...' : record.title}》？
        </p>
      ),
      content: <p>该作品将变为待审核状态，仅作者本人可见</p>,
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        releaseListApi
          .chaokeRevokeNews({ id: record.article_id })
          .then(() => {
            dispatch(setConfig({ loading: false }));
            getData();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const handlePreview = (record: any) => {
    if (record.doc_type == -1) {
      // 推荐位
      setRecommendCreate({
        visible: true,
        key: Date.now(),
        record: record,
      });
      return;
    }

    setPreview({
      visible: true,
      skey: Date.now(),
      data: record,
    });
  };

  const closePreview = () => {
    setPreview({
      ...preview,
      visible: false,
    });
  };

  const showUserDetail = (record: any) => {
    dispatch(setConfig({ loading: true }));
    userApi
      .getUserDetail({ accountId: record.account_id })
      .then((r: any) => {
        setUser({
          visible: true,
          key: Date.now(),
          detail: r.data.account,
        });
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => dispatch(setConfig({ loading: false })));
  };

  const toCommentSystem = (id: number) => {
    const w = window.open();
    releaseListApi
      .getCommentSystemUrl({ article_id: id })
      .then((r: any) => {
        if (w) {
          w.location = r.data.url;
        } else {
          message.error('浏览器可能拦截了新页面');
        }
      })
      .catch(() => {
        w && w.close();
      });
  };

  const getDropDown = (record: any) => {
    const { channel_id, doc_type } = record;
    const menu =
      doc_type == -1 ? (
        <Menu>
          {requirePerm4Function(
            session,
            'content_recommend:circle,0:update'
          )(<Menu.Item onClick={() => handleEditArticle(record)}>编辑</Menu.Item>)}
          {requirePerm4Function(
            session,
            'circle_article_fix:edit'
          )(<Menu.Item onClick={() => handleRecommendSort(record)}>排序</Menu.Item>)}
          <PermMenuItem
            perm="content_recommend:circle,0:down"
            onClick={() => changeVisible(record)}
          >
            {record.content_level == 1 ? '取消隐藏' : '隐藏'}
          </PermMenuItem>

          <PermMenuItem
            perm={`content_recommend:circle,0:delete`}
            onClick={() => handleDeleteRecommend(record)}
          >
            删除
          </PermMenuItem>
        </Menu>
      ) : (
        <Menu>
          {requirePerm4Function(
            session,
            `ugc_article:${channel_id}:set_content_level`
          )(<Menu.Item onClick={() => handleEditContentLevel(record)}>修改内容等级</Menu.Item>)}
          {requirePerm4Function(
            session,
            'circle_article:update_circle'
          )(<Menu.Item onClick={() => handleEditCircleInfo(record)}>修改圈子信息</Menu.Item>)}
          {requirePerm4Function(
            session,
            'circle_article_fix:edit'
          )(<Menu.Item onClick={() => handleEditFixedSort(record)}>排序</Menu.Item>)}
          {requirePerm4Function(
            session,
            'channel_article:ugc:edit_url'
          )(<Menu.Item onClick={() => handleEditArticle(record)}>编辑作品</Menu.Item>)}
          {requirePerm4Function(
            session,
            'circle_article:set_selected'
          )(
            <Menu.Item onClick={() => toggleWinnowStatus(record)}>
              {record.selected ? '取消精选' : '设为精选'}
            </Menu.Item>
          )}
          {requirePerm4Function(
            session,
            `comment:view:${channel_id}`
          )(<Menu.Item onClick={() => toCommentSystem(record.uuid)}>评论运营</Menu.Item>)}
          {requirePerm4Function(
            session,
            `channel_article:${channel_id}:cancel_release`
          )(<Menu.Item onClick={() => handleCancelPub(record)}>取消签发</Menu.Item>)}
          <Menu.Item>
            {record.url ? (
              <ReactClipboard
                action="copy"
                text={record.url}
                onSuccess={() => message.success('链接已复制')}
                onError={() => message.error('复制失败')}
              >
                <a>复制链接</a>
              </ReactClipboard>
            ) : (
              '复制链接'
            )}
          </Menu.Item>
        </Menu>
      );

    return (
      <Dropdown overlay={menu}>
        <a className="ant-dropdown-link">
          操作 <Icon type="down" />
        </a>
      </Dropdown>
    );
  };

  const getOperateLog = (record: any) => {
    opApi
      .getOperateLog({ target_id: record.article_id, type: 166 })
      .then((r: any) => {
        setOperateLog({
          visible: true,
          logs: r.data.admin_log_list,
          // key: uuid(),
          record: record,
        });
      })
      .catch();
  };
  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const isSearch =
    filter.content_level !== '' ||
    filter.selected !== '' ||
    !!filter.doc_type ||
    !!filter.publish_time_start ||
    !!filter.publish_time_end ||
    !!filter.keyword ||
    !!filter.topic_id ||
    !!filter.retrieved;

  const columns: any = [
    {
      title: '排序',
      dataIndex: 'sort',
      width: 90,
      render: (text: any, record: any, index: number) => {
        const realIndex = getSeq(index);
        return (
          <OrderColumn
            perm="circle_article_fix:edit"
            pos={realIndex}
            start={1}
            end={real_total}
            disable={isSearch}
            onUp={() => handleExchangeOrder(record, realIndex, 1)}
            onDown={() => handleExchangeOrder(record, realIndex, -1)}
          />
        );
      },
    },
    {
      title: '序号',
      dataIndex: 'seq',
      width: 90,
      render: (a: any, record: any, index: number) => {
        const fixed_position = !!filter.board_id ? record.board_fixed_number : record.fixed_number;
        const realIndex = getSeq(index);
        return (
          <>
            <span style={{ marginRight: 8 }}>{realIndex}</span>
            {fixed_position > 0 && !isSearch && realIndex <= 100 && (
              <PermA
                perm={`circle_article_fix:edit`}
                // disabled={record.down}
                onClick={() => {
                  fixPos(record, realIndex, false);
                }}
              >
                <Icon type="pushpin" theme="filled" />
              </PermA>
            )}
            {record.content_level != 1 && fixed_position == 0 && !isSearch && realIndex <= 100 && (
              <PermA
                perm={`circle_article_fix:edit`}
                disabled={record.content_level == 1}
                onClick={() => {
                  fixPos(record, realIndex, true);
                }}
              >
                <Icon type="pushpin" />
              </PermA>
            )}
          </>
        );
      },
    },
    {
      title: '潮新闻ID',
      dataIndex: 'article_id',
      width: 80,
      render: (text: any, record: any) => (
        <a
          onClick={() =>
            showIDDetailModal({
              id: record.article_id,
              uuid: record.uuid,
              metadata_id: record.metadata_id,
            })
          }
        >
          {text}
        </a>
      ),
    },
    {
      title: '标题',
      dataIndex: 'title',
      render(text: string, record: any) {
        const fixed_position = !!filter.board_id ? record.board_fixed_number : record.fixed_number;

        if (record.doc_type == -1) {
          return (
            <PermA
              perm="content_recommend:circle,0:update"
              onClick={() => handlePreview(record)}
              className={`list-title ${record.down ? 'hide-title' : ''} ${
                fixed_position > 0 ? 'fixed-title' : ''
              }`}
            >
              {record.doc_type < 0 && (
                <Tag color="#e99d42">{recommendTypeText(record.ref_type)}</Tag>
              )}
              {text}
              {record.content_level == 1 ? '（隐藏）' : ''}
              {fixed_position > 0 ? '（固定）' : ''}
            </PermA>
          );
        }

        const tags = [];
        if (record.selected) {
          tags.push('精选');
        }
        const level = ['', '全局沉底', '推荐', '圈外沉底'][parseInt(record.content_level) || 0];
        if (!!level) {
          tags.push(level);
        }
        const colorMap: any = {
          精选: '#f1797a',
          推荐: '#e99d42',
          全局沉底: 'gray',
          圈外沉底: 'gray',
        };

        let titleColor = '';
        if (record.content_level == 1) {
          titleColor = 'gray';
        } else if (fixed_position > 0) {
          titleColor = 'red';
        }
        return (
          <a onClick={() => handlePreview(record)}>
            {tags.length > 0 &&
              tags.map((tag) => {
                return <Tag color={colorMap[tag]}>{tag}</Tag>;
              })}
            <span style={{ color: titleColor }}>
              {(text?.length > 30 ? text.slice(0, 30) + '...' : text) || '-'}
              {fixed_position > 0 ? '(固定)' : ''}
            </span>
          </a>
        );
      },
    },
    {
      title: '封面图/配图',
      key: 'pic_array',
      dataIndex: 'pic_array',
      width: 150,
      align: 'center',
      render: (text: any, record: any) => (
        <div style={{ height: 60, textAlign: 'center' }}>
          <ImagePreviewColumn text={text?.[0]} imgs={record.pic_array}></ImagePreviewColumn>
          {/* <img src={text?.[0]} className='list-pic' onMouseEnter={() => showImagePreviewModal({ images: record.pic_array })}></img> */}
        </div>
      ),
    },
    {
      title: '内容类型',
      dataIndex: 'doc_type',
      render: (doc_type: any) => resolveNewsType(doc_type, 1),
      width: 90,
    },
    // {
    //   title: '内容等级',
    //   dataIndex: 'content_level',
    //   render: (content_level: number) => ['通过', '沉底', '推荐'][content_level],
    //   width: 90,
    // },
    // {
    //   title: '是否精选',
    //   dataIndex: 'selected',
    //   render: (selected: boolean) => (selected ? '精选' : ''),
    //   width: 90,
    // },
    ...(!filter.board_id
      ? [
          {
            title: '所属版块',
            dataIndex: 'board_name',
            width: 90,
            render: (text: any, record: any) => {
              if (record.doc_type == -1) {
                return '';
              }
              return text;
            },
          },
        ]
      : []),
    {
      title: '作者',
      dataIndex: 'author',
      width: 140,
      render(text: string, record: any) {
        if (record.doc_type == -1) {
          return '';
        }
        return canSeeAccount ? <a onClick={() => showUserDetail(record)}>{text}</a> : text;
      },
    },
    // {
    //   title: '小潮号',
    //   key: 'chao_id',
    //   dataIndex: 'chao_id',
    //   width: 110,
    // },
    {
      title: '阅读数',
      key: 'fake_count',
      dataIndex: 'fake_count',
      width: 110,
      render: (text: any, record: any) =>
        record.doc_type == -1 ? '' : <a onClick={() => showReadCountDetailModal(record)}>{text}</a>,
    },
    {
      title: '点赞数',
      width: 90,
      dataIndex: 'like_count',
      // render: (text: any, record: any) => <a onClick={() => showDataSetModal(record, 0)}>{text || 0}</a>
      render: (text: any, record: any) => (record.doc_type == -1 ? '' : text || 0),
    },
    {
      title: '评论数',
      width: 90,
      dataIndex: 'comment_count',
      render: (text: any, record: any) => (record.doc_type == -1 ? '' : text || 0),
    },
    {
      title: '发布时间',
      dataIndex: 'published_at',
      width: 105,
      render: (text: any, record: any) => (
        <a style={{ width: 80 }} onClick={() => getOperateLog(record)}>
          {moment(text).format('YYYY-MM-DD HH:mm:ss')}
        </a>
      ),
    },
    {
      title: (
        <span>
          取签状态
          <Tooltip
            placement="top"
            title={
              <span>
                已取稿——内容被取稿但未签发；
                <br />
                已取签——内容被取稿且被签发
              </span>
            }
          >
            <Icon style={{ paddingLeft: '5px' }} type="question-circle" />
          </Tooltip>
        </span>
      ),
      dataIndex: 'retrieved',
      width: 100,
      render: (retrieved: number, record: any) =>
        record.doc_type == -1 ? (
          ''
        ) : (
          <a onClick={() => setGetVisa({ record, visible: true })}>
            {['', '已取稿', '已取签'][retrieved]}
          </a>
        ),
    },
    {
      title: '操作',
      key: 'op',
      align: 'center',
      width: 90,
      fixed: 'right',
      render: (text: any, record: any) => getDropDown(record),
    },
  ];

  const handleExchangeOrder = (record: any, pos: number, flag: 1 | -1) => {
    dispatch(setConfig({ loading: true }));
    const params: any = {
      circle_id: record.circle_id,
      article_id: record.article_id,
      current: pos,
      offset: flag,
      board_id: 0,
    };
    if (!!filter.board_id) {
      params.board_id = filter.board_id;
    }
    releaseListApi
      .circleArticleExchangeOrder(params)
      .then(() => {
        message.success('操作成功');
        dispatch(setConfig({ loading: false }));
        getData();
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  const fixPos = (record: any, pos: any, fix: Boolean) => {
    dispatch(setConfig({ loading: true }));
    const params: any = {
      circle_id: record.circle_id,
      article_id: record.article_id,
      position: pos,
      fixed: fix,
      board_id: 0,
    };
    if (!!filter.board_id) {
      params.board_id = filter.board_id;
    }
    releaseListApi
      .editCircleArticleFixDetail({ data: JSON.stringify([params]) })
      .then(() => {
        message.success('操作成功');
        dispatch(setConfig({ loading: false }));
        getData();
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  const changeFilter = (key: string, val: any, goToFirstPage = false) => {
    const newFilter = {
      ...filter,
      [key]: val,
    };
    setFilter(newFilter);
    getData(goToFirstPage, newFilter);
  };

  const timeChange = (dates: any) => {
    const newFilter = {
      ...filter,
      publish_time_start: dates.length === 0 ? '' : dates[0].format('YYYY-MM-DD'),
      publish_time_end: dates.length === 0 ? '' : dates[1].format('YYYY-MM-DD'),
    };
    setFilter(newFilter);
    getData(true, newFilter);
  };

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      const newFilter = {
        ...filter,
        ...searchState,
      };
      setFilter(newFilter);
      getData(true, newFilter);
    }
  };

  // const handleAuthorChange = (val: any) => {
  //   if (!val) {
  //     // 清空了
  //     handleSearchTypeChange(searchState.search_type);
  //   }
  // };

  // const handleAuthorSearch = _.debounce((nick_name: any) => {
  //   if (!nick_name) {
  //     setAuthorOptions([]);
  //     return;
  //   }
  //   opApi
  //     .getUserList({ nick_name, status: 2, current: 1, size: 20, cert_types: '0,1,2' })
  //     .then(({ data }) => {
  //       const {
  //         account_list: { records = [] },
  //       } = data as any;
  //       setAuthorOptions(records);
  //     })
  //     .catch(() => { });
  // }, 500);

  // const handleAuthorSelect = (_: any, option: any) => {
  //   setSearchState({ ...searchState, keyword: option.key });
  // };

  const handleSearchTypeChange = (search_type: any) => {
    setSearchState({ ...searchState, search_type });
    // setAuthorOptions([]);
  };

  const submitContentLevelEnd = (goToFirstPage: boolean) => {
    getData(goToFirstPage);
    setLevelModalVisiable(false);
  };

  const submitCircleBlockEnd = (goToFirstPage: boolean) => {
    getData(goToFirstPage);
    setCircleInfoModalVisiable(false);
  };

  const submitTopContentEnd = (goToFirstPage: boolean) => {
    getData(goToFirstPage);
    setTopContentDrawerVisiable(false);
  };

  const submitFixedSortEnd = (goToFirstPage: boolean) => {
    getData(goToFirstPage);
    setFixedContentSortDrawerVisiable(false);
  };

  const getData = (goToFirstPage = false, newFilter = filter) => {
    const cur = goToFirstPage ? 1 : current;
    dispatch(
      getTableList('getCircleArticleList', 'list', { current: cur, size, circle_id, ...newFilter })
    );
  };

  const onDragEnd = (oldIndex: number, newIndex: number) => {
    console.log('newIndex', newIndex);
    console.log('oldIndex', oldIndex);
    const source: any = records[oldIndex];
    //  newIndex + 1
    const fixed_position = !!filter.board_id ? source.board_fixed_number : source.fixed_number;

    dispatch(setConfig({ loading: true }));
    const params: any = {
      circle_id: circle_id,
      article_id: source.article_id,
      position: newIndex + 1,
      fixed: fixed_position > 0,
      board_id: 0,
    };
    if (!!filter.board_id) {
      params.board_id = filter.board_id;
    }
    releaseListApi
      .editCircleArticleFixDetail({ data: JSON.stringify([params]) })
      .then(() => {
        message.success('操作成功');
        dispatch(setConfig({ loading: false }));
        getData();
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });

    // const target: any = this.props.tableList.records[newIndex];
    // const WAIT_TIME = 1000;
    // if (target.fixed_number) {
    //   message.error('当前位置已有固定位置稿件');
    //   return;
    // }
    // const body: any = {
    //   position: target.seq,
    //   id: source.id,
    //   fixed: false,
    // };
    // this.props.dispatch(setConfig({ loading: true }));
    // communityApi
    //   .recommendBannerListFiexdPosition(body)
    //   .then(() => {
    //     message.success('操作成功');
    //     setTimeout(() => {
    //       this.getData();
    //       this.props.dispatch(setConfig({ loading: false }));
    //     }, WAIT_TIME);
    //   })
    //   .catch(() => {
    //     this.props.dispatch(setConfig({ loading: false }));
    //   });
  };

  const getBlockList = () => {
    api
      .getCircleBoardList({ circle_id, current: 1, size: 20 })
      .then(({ data }) => {
        const { list = [] } = data as any;
        setBlockOptions(list.map((item: any) => ({ value: item.id, label: item.name })));
      })
      .catch(() => {});
  };

  useEffect(() => {
    getData(true);
    getBlockList();
  }, []);

  const handleSearch = _.debounce(async (val: string) => {
    if (!val) {
      setTopicList([]);
      return;
    }
    setTopicLoading(true);
    try {
      opApi.getTopicNameList({ keyword: val }).then((res: any) => {
        let records: any = [...res.data.list.records];
        setTopicList(records);
      });
    } catch (error) {
      console.error('Error fetching data:', error);
    }
    setTopicLoading(false);
  }, 500);

  const handleRecommendCreate = (val: any) => {
    setRecommendCreate({
      visible: true,
      key: Date.now(),
      record: null,
      type: val,
    });
  };

  const exportData = () => {
    Modal.confirm({
      title: '单次最多可导出2000行数据',
      content: '（如果当前列表数量超出上限，仅导出前2000行）',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        api
          .exportCircleArticleList({
            ...filter,
            export_flag: 1,
          })
          .then((res: any) => {
            console.log('res', res);
            const a = document.createElement('a');
            a.href = window.URL.createObjectURL(res.data);
            a.download = `潮新闻-${name}-内容列表${moment().format('YYYYMMDD')}.xlsx`;
            a.click();
            dispatch(setConfig({ loading: false }));
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button onClick={() => history.goBack()}>
            <Icon type="left-circle" />
            返回
          </Button>
          {/* <PermButton
            perm="circle_article_top:list"
            style={{ marginLeft: 8 }}
            onClick={() => setTopContentDrawerVisiable(true)}
          >
            设置置顶内容
          </PermButton> */}
          {/* <>
            <PermButton
              perm="circle_article_fix:list"
              style={{ marginLeft: 8 }}
              onClick={() => setFixedContentSortDrawerVisiable(true)}
            >
              固定内容排序
            </PermButton>
            &nbsp;
            <Tooltip
              title="最多可设置10条固定内容，并指定内容在列表中的显示位置。同时在全部内容的最新/热门排序下生效。"
              placement="top"
            >
              <Icon type="question-circle" />
            </Tooltip>
          </> */}
          <>
            <PermButton
              perm="common_recommend:list:1"
              style={{ marginLeft: 8 }}
              onClick={() => history.push(`/view/circlePictureMgr/${circle_id}/${name}`)}
            >
              图片推荐位
            </PermButton>
            &nbsp;
            <Tooltip
              overlayStyle={{ maxWidth: 516 }}
              title={<img src="/assets/circle_pic_recommend.png" width={500} height={241} />}
              placement="top"
            >
              <Icon type="question-circle" />
            </Tooltip>
          </>
          <PermButton perm="" style={{ marginLeft: 8 }} onClick={() => exportData()}>
            导出数据
          </PermButton>
          {requirePerm4Function(
            session,
            'content_recommend:circle,0:create'
          )(
            <Select
              value="创建列表推荐位"
              style={{ marginLeft: 8, width: 150, marginRight: 8 }}
              onChange={handleRecommendCreate}
            >
              <Select.Option value="42">稿件推荐位</Select.Option>
              <Select.Option value="23">用户推荐位</Select.Option>
            </Select>
          )}

          <span>
            拖拽排序&nbsp;
            <PermSwitch
              disabled={isSearch}
              perm="circle_article_fix:edit"
              checked={dragSorter}
              checkedChildren="开"
              unCheckedChildren="关"
              onChange={(e: any) => {
                setDragSorter(e);
              }}
            />
          </span>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb([...props.breadCrumb, name])}
        </Col>
      </Row>
      <div className="component-content news-pages">
        {blockOptions?.length > 0 && (
          <Row style={{ marginBottom: 16 }}>
            <Radio.Group
              value={filter.board_id}
              buttonStyle="solid"
              onChange={(e) => changeFilter('board_id', e.target.value, true)}
            >
              <Radio.Button value="">全部</Radio.Button>
              {/* <Radio.Button value={0}>无版块</Radio.Button> */}
              {blockOptions?.map((item: any) => (
                <Radio.Button key={item.value} value={item.value}>
                  {item.label}
                </Radio.Button>
              ))}
            </Radio.Group>
          </Row>
        )}

        <Row style={{ marginBottom: 16 }}>
          <Col span={16}>
            <Select
              style={{ width: 94 }}
              value={filter.selected?.toString()}
              onChange={(val) => changeFilter('selected', val)}
            >
              <Select.Option value="">是否精选</Select.Option>
              <Select.Option value={'1'}>精选</Select.Option>
              <Select.Option value={'0'}>非精选</Select.Option>
            </Select>
            <Select
              style={{ width: 94, marginLeft: 8 }}
              value={filter.doc_type}
              onChange={(val) => changeFilter('doc_type', val)}
            >
              <Select.Option value="">内容类型</Select.Option>
              <Select.Option value={10}>小视频</Select.Option>
              <Select.Option value={12}>短图文</Select.Option>
              <Select.Option value={13}>长文章</Select.Option>
              <Select.Option value={-1}>推荐位</Select.Option>
            </Select>
            <Select
              style={{ width: 94, marginLeft: 8 }}
              value={filter.content_level}
              onChange={(val) => changeFilter('content_level', val)}
            >
              <Select.Option value="">内容等级</Select.Option>
              <Select.Option value={2}>推荐</Select.Option>
              <Select.Option value={0}>通过</Select.Option>
              <Select.Option value={3}>圈外沉底</Select.Option>
              <Select.Option value={1}>全局沉底</Select.Option>
            </Select>
            {/* {blockOptions && (
              <Select
                style={{ width: 94, marginLeft: 8 }}
                value={filter.board_id}
                onChange={(val) => changeFilter('board_id', val)}
              >
                <Select.Option value="">所属版块</Select.Option>
                <Select.Option value={0}>无版块</Select.Option>
                {blockOptions.map((item: any) => (
                  <Select.Option key={item.value} value={item.value}>
                    {item.label}
                  </Select.Option>
                ))}
              </Select>
            )} */}
            <DatePicker.RangePicker
              style={{ width: 210, marginLeft: 8 }}
              format="YYYY-MM-DD"
              onChange={timeChange}
            />
            <Select
              style={{ width: 150, marginLeft: 8 }}
              showSearch
              allowClear
              optionFilterProp="children"
              loading={topicLoading}
              placeholder="按话题搜索"
              onSearch={handleSearch}
              notFoundContent={null}
              filterOption={false}
              onChange={(value: string) => {
                if (!value) {
                  setTopicList([]);
                }

                const newFilter = {
                  ...filter,
                  topic_id: value || '',
                };
                setFilter(newFilter);
                getData(true, newFilter);
              }}
              value={filter.topic_id || undefined}
              // ref={ref} // 将 ref 属性传递给 Select 组件
            >
              {topicList.map((item: any) => (
                <Select.Option key={item.id} value={item.id}>
                  {item.name}
                </Select.Option>
              ))}
            </Select>
            <Select
              style={{ width: 94, marginLeft: 8 }}
              value={filter.retrieved}
              onChange={(val) => changeFilter('retrieved', val)}
            >
              <Select.Option value="">取签状态</Select.Option>
              <Select.Option value="0">未取稿</Select.Option>
              <Select.Option value="1">已取稿</Select.Option>
              <Select.Option value="2">已取签</Select.Option>
            </Select>
          </Col>
          <Col span={8} style={{ textAlign: 'right' }}>
            <Select
              value={searchState.search_type}
              style={{ width: 94, marginRight: 8 }}
              onChange={handleSearchTypeChange}
            >
              <Select.Option value={1}>内容标题</Select.Option>
              <Select.Option value={2}>作者</Select.Option>
              <Select.Option value={3}>潮新闻id</Select.Option>
              <Select.Option value={4}>小潮号</Select.Option>
            </Select>
            <Input
              value={searchState.keyword}
              style={{ width: 140 }}
              onChange={(e: any) =>
                setSearchState({ ...searchState, keyword: e.target.value?.trim() })
              }
              onKeyPress={handleKey}
              placeholder="输入搜索内容"
            />
            {/* {searchState.search_type === 2 && (
              <Select
                style={{ width: 140 }}
                placeholder="输入搜索内容"
                onSearch={handleAuthorSearch}
                onSelect={handleAuthorSelect}
                onChange={handleAuthorChange}
                showSearch
                allowClear
              >
                {authorOptions.map((item: any) => (
                  <Select.Option key={item.id} value={item.nick_name}>
                    {item.nick_name}
                  </Select.Option>
                ))}
              </Select>
            )} */}
            <Button style={{ marginLeft: 8 }} onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" /> 搜索
            </Button>
          </Col>
        </Row>
        <Table
          func="getCircleArticleList"
          index="list"
          filter={{ ...filter, circle_id }}
          pagination={true}
          rowKey="article_id"
          columns={columns}
          tableProps={{ scroll: { x: 1500 } }}
          onDragEnd={(oldIndex: number, newIndex: number) => onDragEnd(oldIndex, newIndex)}
          draggable={dragSorter && !isSearch}
          // getRecordDraggable={(record: any) => {
          //   const fixed_position = !!filter.board_id
          //     ? record.board_fixed_number
          //     : record.fixed_number;
          //   return fixed_position == 0;
          // }}
        />
        <EditCircleContentLevelModal
          record={editRecord}
          visible={levelModalVisiable}
          onCancel={() => setLevelModalVisiable(false)}
          onEnd={submitContentLevelEnd}
        />
        <EditCircleInfoModal
          record={editRecord}
          visible={circleInfoModalVisiable}
          onCancel={() => setCircleInfoModalVisiable(false)}
          onEnd={submitCircleBlockEnd}
        />
        <PreviewMCN {...preview} onClose={closePreview} />
        <Modal
          visible={user.visible}
          key={user.key}
          title="用户详情"
          width={800}
          onCancel={() => setUser({ ...user, visible: false })}
          onOk={() => setUser({ ...user, visible: false })}
        >
          {/*{getUserDetail(user.detail)}*/}
          {<UserDetail detail={user.detail} />}
        </Modal>
        <CircleContentTopContentDrawer
          circle_id={circle_id}
          visible={topContentDrawerVisiable}
          onClose={() => setTopContentDrawerVisiable(false)}
          onEnd={submitTopContentEnd}
        />
        <FixedContentSortDrawer
          max={real_total}
          circle_id={circle_id}
          visible={fixedContentSortDrawerVisiable}
          onClose={() => setFixedContentSortDrawerVisiable(false)}
          onEnd={submitFixedSortEnd}
        />

        {/* 操作日志 */}
        <Modal
          visible={operateLog.visible}
          title="操作日志"
          key={operateLog.key}
          cancelText={null}
          onCancel={() => setOperateLog({ ...operateLog, visible: false })}
          onOk={() => setOperateLog({ ...operateLog, visible: false })}
        >
          <div>
            <h3 className="line-max" style={{}}>
              潮新闻ID：{operateLog.record?.article_id}&nbsp;标题：{operateLog.record?.title}
            </h3>
            <Timeline>
              {operateLog.logs?.map((v: any, i: number) => [
                <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                  &nbsp;
                </Timeline.Item>,
                v.log_list?.map((action: any, index: number) => (
                  <Timeline.Item
                    className="timeline-dot"
                    data-show={moment(action.created_at).format('HH:mm:ss')}
                    key={`time${i}-action${index}`}
                  >
                    {action.admin_name}&emsp;{action.remark}
                    {/* {action.user}&emsp;&emsp;{action.action}&emsp;&emsp; */}
                  </Timeline.Item>
                )),
              ])}
            </Timeline>
          </div>
        </Modal>

        <GetVisaModal
          record={getVisa.record}
          visible={getVisa.visible}
          onCancel={() => setGetVisa({ ...getVisa, visible: false })}
        />

        <EditFixedSortModal
          {...fixedSort}
          onCancel={() => setFixedSort({ visible: false })}
          onOk={() => {
            setFixedSort({ visible: false });
            getData();
          }}
        ></EditFixedSortModal>

        <CircleRecommendDrawer
          circle_id={circle_id}
          board_id={filter.board_id}
          blockOptions={[{ value: '0', label: '全部' }, ...(blockOptions || [])]}
          {...recommendCreate}
          onClose={() => setRecommendCreate({ visible: false })}
          onEnd={() => {
            setRecommendCreate({ visible: false });
            getData();
          }}
        ></CircleRecommendDrawer>
      </div>
    </>
  );
}
