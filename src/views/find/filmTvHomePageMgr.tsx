import React, { useMemo, useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector, useStore } from 'react-redux';
import { getTableList, setTableList } from '@app/action/tableList';
import { findApi as api } from '@app/api';
import { Table } from '@components/common';
import { getCrumb, requirePerm, requirePerm4Function } from '@utils/utils';
import {
  Button,
  Col,
  Form,
  Icon,
  Input,
  InputNumber,
  message,
  Modal,
  Radio,
  Row,
  Select,
  Dropdown,
  Menu,
  Tooltip,
  Switch,
} from 'antd';
import moment from 'moment';
import { CommonObject } from '@app/types';
import { PermA, PermButton } from '@app/components/permItems';
import { setTableCache } from '@app/action/tableCache';
import ReactClipboard from 'react-clipboardjs-copy';
import { setMenuHook } from '@app/utils/utils';
import BannerDrawer from './component/bannerDrawer';
import RecommendDrawer from './component/recommendDrawer';
import ZoneDrawer from './component/zoneDrawer';
import { withRouter } from 'react-router';
// ✅ 添加图片预览功能
import { PhotoSlider } from 'react-photo-view';

const { TextArea } = Input;

interface FilmTvHomePageMgrProps {
  dispatch?: any;
  session?: any;
  breadCrumb?: any;
  history?: any;
  location?: any;
  match?: any;
}

const FilmTvHomePageMgr: React.FC<FilmTvHomePageMgrProps> = (props) => {
  const dispatch = useDispatch();

  // 使用Redux的全局tableList状态
  const tableList = useSelector((state: any) => state.tableList);
  const { summary_date = '' } = useSelector((state: any) => state.tableList.allData || {});
  const session = useSelector((state: any) => state.session);
  const { session: storeSession } = useStore().getState();

  const [activeTab, setActiveTab] = useState('recommends');
  const [loading, setLoading] = useState(false);
  const [bannerDrawerVisible, setBannerDrawerVisible] = useState(false);
  const [recommendDrawerVisible, setRecommendDrawerVisible] = useState(false);
  const [zoneDrawerVisible, setZoneDrawerVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<any>(null);
  // ✅ 添加图片预览状态
  const [imagePreview, setImagePreview] = useState({
    visible: false,
    imgs: [] as string[],
    index: 0
  });

  useEffect(() => {
    setMenuHook(dispatch, props);
    // 初始加载推荐位数据
    getData({ current: 1, size: 10 });
  }, []);

  useEffect(() => {
    // 切换Tab时重新加载数据
    getData({ current: 1, size: 10 });
  }, [activeTab]);

  // 获取数据
  const getData = useCallback((overlap: CommonObject = {}) => {
    const apiConfig = getApiConfig(activeTab);
    if (!apiConfig) return;

    const { current, size } = tableList;
    const filters: CommonObject = { current, size };
    
    if (activeTab === 'recommends') {
      filters.channel_id = 'b2ff6033c3c07b5876emedia';
    }

    dispatch(
      getTableList(apiConfig.api, apiConfig.index, { ...filters, ...overlap })
    );
  }, [dispatch, activeTab, tableList.current, tableList.size]);

  // 获取过滤条件（供Table组件使用）
  const getFilter = useCallback(() => {
    const { current, size } = tableList;
    const filters: CommonObject = { current, size };
    
    if (activeTab === 'recommends') {
      filters.channel_id = 'b2ff6033c3c07b5876emedia';
    }
    
    return filters;
  }, [activeTab, tableList.current, tableList.size]);

  // 获取不同tab的API配置
  const getApiConfig = (tabKey: string) => {
    const configs = {
      recommends: { api: 'getRecommendPositionList' as const, index: 'recommends' },
      list: { api: 'getHotFilmList' as const, index: 'list' },
    };
    return configs[tabKey as keyof typeof configs];
  };

  // 推荐位状态变更处理（不需要二次确认）
  const handleRecommendStatusChange = useCallback(
    (record: any) => {
      setLoading(true);
      const newStatus = record.status === 1 ? 0 : 1;
      
      api.updateRecommendPositionStatus({
        id: record.id,
        status: newStatus,
      })
        .then(() => {
          message.success('操作成功');
          getData(); // 保持当前页
        })
        .catch(() => {
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [getData]
  );

  // 热门影视状态变更处理（支持上架和下架操作）
  const handleHotFilmStatusChange = useCallback(
    (record: any) => {
      const isOnline = record.show === true;
      const actionText = isOnline ? '下架' : '上架';
      const confirmTitle = isOnline 
        ? '下架后，在所有页面都不再展示，也不能播放。' 
        : '确定上架？';
      
      Modal.confirm({
        title: confirmTitle,
        onOk: () => {
          setLoading(true);
          api.updateMediaOnline({
            id: record.id,
            online: !isOnline,
          })
            .then(() => {
              message.success(`${actionText}成功`);
              getData(); // 保持当前页
            })
            .catch(() => {
            })
            .finally(() => {
              setLoading(false);
            });
        },
      });
    },
    [getData]
  );

  // 统一的排序处理
  const handleSort = useCallback(
    (record: any, pos: number, tabKey: string) => {
      let newPos = record.position;
      const posChange = (value: number | undefined) => {
        newPos = value || record.position;
      };

      Modal.confirm({
        title: <p>排序：《{record.title}》</p>,
        icon: <Icon type="info-circle" />,
        content: (
          <div>
            <span>请输入位置：</span>
            <InputNumber
              placeholder="请输入1-1000的数字"
              min={1}
              max={1000}
              defaultValue={record.position}
              onChange={posChange}
              style={{ width: '200px', marginLeft: '8px' }}
              precision={0}
            />
          </div>
        ),
        onOk: () => {
          if (!newPos || newPos < 1) {
            message.error('请填写有效的序号');
            return Promise.reject();
          }
          
          setLoading(true);
          return api
            .sortRecommendPosition({
              id: record.id,
              position: newPos,
            })
            .then(() => {
              message.success('操作成功');
              getData(); // 保持当前页
            })
            .catch(() => {
            })
            .finally(() => {
              setLoading(false);
            });
        },
        okText: '确定',
        cancelText: '取消',
      });
    },
    [getData]
  );

  // 智能删除处理（删除后可能需要跳转页面）
  const smartRefreshData = useCallback(() => {
    const { current, size, total, records } = tableList;
    
    // 如果删除后当前页只有一条数据，且不是第一页，则跳转到上一页
    if (records.length === 1 && current > 1) {
      getData({
        current: current - 1,
        size: size,
      });
    } else {
      // 否则刷新当前页
      getData();
    }
  }, [getData, tableList]);

  // 统一的删除处理
  const handleDelete = useCallback(
    (record: any, tabKey: string) => {
      Modal.confirm({
        title: <p>确认删除推荐位《{record.title}》？</p>,
        icon: <Icon type="exclamation-circle" />,
        content: '删除后无法恢复，请谨慎操作。',
        onOk: () => {
          setLoading(true);
          return api
            .deleteRecommendPosition({
              id: record.id,
            })
            .then(() => {
              message.success('删除成功');
              smartRefreshData();
            })
            .catch(() => {
            })
            .finally(() => {
              setLoading(false);
            });
        },
        okText: '确定',
        cancelText: '取消',
      });
    },
    [smartRefreshData]
  );

  // 编辑处理
  const handleEdit = useCallback((record?: any) => {
    console.log('record', record);
    setCurrentRecord(record || null);
    setRecommendDrawerVisible(true);
  }, []);


  // 抽屉相关处理函数
  const handleCloseRecommendDrawer = useCallback(() => {
    setRecommendDrawerVisible(false);
    setCurrentRecord(null);
  }, []);

  const handleRecommendSuccess = useCallback(() => {
    getData(); // 保持当前页
  }, [getData]);

  const handleOpenBannerDrawer = useCallback(() => {
    setBannerDrawerVisible(true);
  }, []);

  const handleCloseBannerDrawer = useCallback(() => {
    setBannerDrawerVisible(false);
  }, []);

  const handleBannerSuccess = useCallback(() => {
    // 如果需要刷新列表数据，可以在这里调用
  }, []);

  const handleOpenZoneDrawer = useCallback(() => {
    setZoneDrawerVisible(true);
  }, []);

  const handleCloseZoneDrawer = useCallback(() => {
    setZoneDrawerVisible(false);
  }, []);

  const handleZoneSuccess = useCallback((values: any) => {
    console.log('保存强国/华数专区数据:', values);
    message.success('保存成功');
    setZoneDrawerVisible(false);
  }, []);

  // 获取推荐位列配置
  const getRecommendPositionColumns = useMemo(() => {
    const { current, size } = tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;

    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 60,
      },
      {
        title: 'ID',
        dataIndex: 'id',
        width: 100,
      },
      {
        title: '推荐位标题',
        dataIndex: 'title',
        render: (text: string, record: any) => {
          const isInactive = record.status !== 1;
          const displayTitle = isInactive ? `${text}（隐藏）` : text;
          return (
            <span
              style={{
                color: isInactive ? '#bfbfbf' : '#1890ff',
                cursor: 'pointer',
              }}
              onClick={() => {
                // 打开编辑推荐位弹窗
                setCurrentRecord(record);
                setRecommendDrawerVisible(true);
              }}
            >
              {displayTitle}
            </span>
          );
        },
      },
      {
        title: '推荐位类型',
        dataIndex: 'ref_type',
        width: 120,
        render: (text: string, record: any) => {
          if (record.ref_type === 45) {
            return '组合影视推荐位';
          } else if (record.ref_type === 46) {
            return '单条影视推荐位';
          }
          return text;
        },
      },
      {
        title: (
          <span>
            列表排序
            <Tooltip title="固定显示在影视专区首页列表的指定位置，与系统计算的影视内容混排">
              <Icon type="question-circle" style={{ marginLeft: 4 }} />
            </Tooltip>
          </span>
        ),
        dataIndex: 'position',
        width: 100,
        render: (text: number) => (
          <Tooltip title="固定显示在影视专区首页列表的指定位置，与系统计算的影视内容混排">
            <span>{text || 1}</span>
          </Tooltip>
        ),
      },
      {
        title: '状态',
        dataIndex: 'status',
        width: 80,
        render: (text: number) => (text === 1 ? '展示中' : '未展示'),
      },
      {
        title: '操作人',
        dataIndex: 'updated_by',
        width: 100,
        render: (text: string) => text,
      },
      {
        title: '操作时间',
        dataIndex: 'updated_at',
        width: 160,
        render: (text: string) =>
          text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '2023-01-01 00:00:00',
      },
      {
        title: '操作',
        key: 'action',
        fixed: 'right',
        width: 120,
        render: (text: any, record: any, i: number) => {
          const menu = (
            <Menu>
              {requirePerm4Function(
                storeSession,
                'media_recommend:edit'
              )(<Menu.Item onClick={() => handleEdit(record)}>编辑</Menu.Item>)}
              {requirePerm4Function(
                storeSession,
                'media_recommend:edit'
              )(<Menu.Item onClick={() => handleSort(record, getSeq(i), 'recommends')}>排序</Menu.Item>)}
              {requirePerm4Function(
                storeSession,
                'media_recommend:edit'
              )(<Menu.Item onClick={() => handleRecommendStatusChange(record)}>{record.status === 1 ? '下架' : '上架'}</Menu.Item>)}
              {requirePerm4Function(
                storeSession,
                'media_recommend:edit'
              )(<Menu.Item onClick={() => handleDelete(record, 'recommends')}>删除</Menu.Item>)}
            </Menu>
          );

          return (
            <Dropdown overlay={menu}>
              <a className="ant-dropdown-link">
                操作 <Icon type="down" />
              </a>
            </Dropdown>
          );
        },
      },
    ];
  }, [tableList.current, tableList.size, handleEdit, handleSort, handleRecommendStatusChange, handleDelete]);

  // 获取热门影视列配置
  const getHotFilmColumns = useMemo(() => {
    const { current, size } = tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;

    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 80,
      },
      {
        title: 'ID',
        dataIndex: 'id',
        width: 100,
      },
      {
        title: '影视名称',
        dataIndex: 'title',
        render: (text: string, record: any) => (
          <PermA 
            perm="media:edit" 
            onClick={() => {
              props.history.push(
                `/view/media_content_list?filmId=${record.id}&filmName=${encodeURIComponent(record.title || text)}`
              );
            }}
          >
            <span style={{ color: record.show ? 'inherit' : '#ccc' }}>
              {text + (record.show ? '' : '（已下架）') || '暂无'}
            </span>
          </PermA>
        ),
      },
      {
        title: '封面图',
        dataIndex: 'list_pic_h',
        width: 120,
        render: (text: string) => (
          <img
            src={text || '/placeholder.png'}
            alt="封面"
            style={{ width: 60, height: 80, objectFit: 'cover', display: 'block', cursor: 'pointer' }}
            onClick={() => {
              const imageUrl = text || '/placeholder.png';
              if (imageUrl && imageUrl !== '/placeholder.png') {
                setImagePreview({
                  visible: true,
                  imgs: [imageUrl],
                  index: 0
                });
              }
            }}
          />
        ),
      },
      {
        title: '分类',
        dataIndex: 'type',
        width: 80,
        render: (text: string | number) => {
          const categoryMap: { [key: string]: string } = {
            '1': '电视剧',
            '2': '电影',
            '3': '短剧',
            '4': '纪录片',
          };
          return categoryMap[String(text)] || '-';
        },
      },
      {
        title: '类型',
        dataIndex: 'theme_names',
        width: 80,
        render: (text: string, record: any) => {
          if (record.themes) {
            const themeMap: { [key: string]: string } = {
              '1': '爱情',
              '2': '战争',
              '3': '动作',
              '4': '都市',
              '5': '公益',
              '6': '家庭',
              '7': '金融',
              '8': '历史',
              '9': '励志',
              '10': '文化',
              '11': '喜剧',
              '12': '乡村',
              '13': '悬疑',
            };

            const themeIds = record.themes.split(',');
            const themeNames = themeIds
              .map((id: string) => themeMap[id.trim()])
              .filter((name: string) => name)
              .join(',');

            return themeNames || '-';
          }
          return text || '-';
        },
      },
      {
        title: (
          <span>
            近24小时播放量
            <Tooltip title="有分集的影视，统计的是所有资源播放量总和">
              <Icon type="question-circle" style={{ marginLeft: 4 }} />
            </Tooltip>
          </span>
        ),
        dataIndex: 'read_count',
        width: 150,
        render: (text: number) => (
          <Tooltip title="有分集的影视，统计的是所有资源播放量总和">
            <span>{text}</span>
          </Tooltip>
        ),
      },
      {
        title: '操作',
        key: 'action',
        fixed: 'right',
        width: 200,
        render: (text: any, record: any) => {
          return (
            <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
              <PermA
                perm="media:edit"
                onClick={() => handleHotFilmStatusChange(record)}
              >
                {record.show === true ? '下架' : '上架'}
              </PermA>
              <ReactClipboard
                action="copy"
                text={record.url}
                onSuccess={() => message.success('链接已复制')}
                onError={() => message.error('复制失败')}
              >
                <a>复制链接</a>
              </ReactClipboard>
            </div>
          );
        },
      },
    ];
  }, [tableList.current, tableList.size, handleHotFilmStatusChange, props.history]);

  // 渲染提示信息
  const renderTips = useMemo(() => {
    if (activeTab === 'list') {
      return (
        <div
          style={{
            marginBottom: 16,
            color: '#333',
          }}
        >
          系统每隔1小时计算一次，当前数据更新时间：{summary_date || '2025-01-01 00:00:00'}
        </div>
      );
    }
    return null;
  }, [activeTab, summary_date]);

  return (
    <>
      <Row className="layout-infobar">
        <Col span={18}>
          <Radio.Group
            value={activeTab}
            buttonStyle="solid"
            onChange={(e) => setActiveTab(e.target.value)}
            style={{ marginRight: 8 }}
          >
            <Radio.Button value="recommends">推荐位</Radio.Button>
            <Radio.Button value="list">热门影视</Radio.Button>
          </Radio.Group>
          {activeTab === 'list' && (
            <Tooltip
              title={
                <div>
                  <div>
                    1、热门影视排序——按最新24小时观看量从多到少排，有分集的影视将统计所有集数总和
                  </div>
                  <div>2、如需移除热门影视，可操作下架</div>
                  <div>3、如需添加热门影视，或将某部影视显示位置前置，可添加影视推荐位-单条</div>
                </div>
              }
              placement="top"
            >
              <Icon type="question-circle" style={{ marginRight: 8 }} />
            </Tooltip>
          )}
          {activeTab === 'recommends' && (
            <>
              <PermButton 
                perm="media_recommend:edit" 
                onClick={() => handleEdit()} 
                style={{ marginRight: 8 }}
              >
                添加推荐位
              </PermButton>
              <PermButton 
                perm="media_recommend:edit" 
                onClick={handleOpenBannerDrawer} 
                style={{ marginRight: 8 }}
              >
                顶部轮播图
              </PermButton>
              <PermButton 
                perm="media_recommend:edit" 
                onClick={handleOpenZoneDrawer}
              >
                强国/华数专区
              </PermButton>
            </>
          )}
        </Col>
        <Col span={6} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>

      <div className="component-content">
        {renderTips}

        {/* 使用统一的表格渲染 */}
        <Table
          func={getApiConfig(activeTab)?.api || 'getRecommendPositionList'}
          index={getApiConfig(activeTab)?.index || 'recommends'}
          rowKey="id"
          columns={activeTab === 'recommends' ? getRecommendPositionColumns : getHotFilmColumns}
          filter={getFilter()}
          pagination={true}
        />
      </div>

      {/* 轮播图管理抽屉 */}
      <BannerDrawer
        visible={bannerDrawerVisible}
        onClose={handleCloseBannerDrawer}
        onSuccess={handleBannerSuccess}
      />

      {/* 推荐位管理抽屉 */}
      <RecommendDrawer
        visible={recommendDrawerVisible}
        onClose={handleCloseRecommendDrawer}
        onSuccess={handleRecommendSuccess}
        editRecord={currentRecord}
      />

      {/* 强国/华数专区管理抽屉 */}
      <ZoneDrawer
        visible={zoneDrawerVisible}
        onClose={handleCloseZoneDrawer}
        onSubmit={handleZoneSuccess}
      />

      {/* ✅ 添加图片预览组件 */}
      <PhotoSlider
        maskOpacity={0.5}
        images={imagePreview.imgs?.map((v: any) => ({ src: v, key: v })) || []}
        visible={imagePreview.visible}
        onClose={() => setImagePreview({
          ...imagePreview,
          visible: false
        })}
        index={imagePreview.index}
        onIndexChange={(index) => setImagePreview({
          ...imagePreview,
          index
        })}
      />
    </>
  );
};

// 使用withRouter包装组件以保持路由功能
const ConnectedFilmTvHomePageMgr = withRouter(FilmTvHomePageMgr);

export default ConnectedFilmTvHomePageMgr;
