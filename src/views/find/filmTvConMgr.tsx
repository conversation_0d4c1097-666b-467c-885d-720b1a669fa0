import React, { useState, useEffect, useMemo, memo } from 'react';
import { useDispatch, useSelector, useStore } from 'react-redux';
import { getTableList, setTableList } from '@app/action/tableList';
import { findApi as api } from '@app/api';
import { OrderColumn, Table } from '@components/common';
import { getCrumb, requirePerm, requirePerm4Function } from '@utils/utils';
import {
  Button,
  Col,
  Form,
  Icon,
  Input,
  InputNumber,
  message,
  Modal,
  Row,
  Select,
  Dropdown,
  Menu,
  Tooltip,
} from 'antd';
import { useHistory, useLocation, useRouteMatch, useParams } from 'react-router-dom';
import moment from 'moment';
import { CommonObject } from '@app/types';
import { PermA, PermButton } from '@app/components/permItems';
import { setTableCache } from '@app/action/tableCache';
import ReactClipboard from 'react-clipboardjs-copy';
import { setMenuHook } from '@app/utils/utils';
import AddFilmResourceDrawer from './component/AddFilmResourceDrawer';
import BatchAddResourceModal from './component/BatchAddResourceModal';
// ✅ 添加图片预览功能
import { PhotoSlider } from 'react-photo-view';

export default function FilmTvConMgr(props: any) {
  const dispatch = useDispatch();
  const history = useHistory();
  const location = useLocation();
  const match = useRouteMatch();
  const { session } = useStore().getState();

  // 从 URL 查询参数中获取 filmId 和 filmName
  const searchParams = new URLSearchParams(location.search);
  const filmId = searchParams.get('filmId');
  const filmName = searchParams.get('filmName');

  // Redux state
  const tableList = useSelector((state: any) => state.tableList);
  const tableCache = useSelector((state: any) => state.tableCache);

  // Local state
  const [filter, setFilter] = useState({
    show: '',
  });
  const [loading, setLoading] = useState(false);
  const [editRecord, setEditRecord] = useState<any>(null);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [drawerKey, setDrawerKey] = useState(Date.now());
  const [recommendPositionDetail, setRecommendPositionDetail] = useState<any>(null);
  const [batchModalVisible, setBatchModalVisible] = useState(false);
  const [batchModalKey, setBatchModalKey] = useState(Date.now());
  // ✅ 添加图片预览状态
  const [imagePreview, setImagePreview] = useState({
    visible: false,
    imgs: [] as string[],
    index: 0,
  });

  // 初始化
  useEffect(() => {
    setMenuHook(dispatch, props);

    if (tableCache?.beforeRoute === match?.path && tableCache.records.length > 0) {
      getData({ current: tableCache.current, size: tableCache.size });
    }
  }, []);

  // 获取数据
  const getData = (overlap: CommonObject = {}) => {
    dispatch(
      getTableList('getMediaItemList', 'list', {
        media_id: filmId,
        ...getFilter(),
        ...overlap,
      })
    );
  };
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  // 获取过滤条件
  const getFilter = () => {
    const { current, size } = tableList;
    const filters: CommonObject = { media_id: filmId, current, size };

    Object.keys(filter).forEach((key) => {
      if (filter[key as keyof typeof filter] !== '') {
        filters[key] = filter[key as keyof typeof filter];
      }
    });

    return filters;
  };

  // 处理过滤条件变化
  const handleFilterChange = (key: string, value: any) => {
    setFilter({
      ...filter,
      [key]: value,
    });
  };

  // 监听 filter 变化
  useEffect(() => {
    if (filter !== undefined) {
      getData({ current: 1 });
    }
  }, [filter.show]);

  // 返回影视列表
  const handleBack = () => {
    history.goBack();
  };

  // 处理添加资源
  const handleAddResource = () => {
    setDrawerVisible(true);
    setEditRecord(null);
    setDrawerKey(Date.now());
  };

  // 处理批量添加资源
  const handleBatchAddResource = () => {
    setBatchModalVisible(true);
    setBatchModalKey(Date.now());
  };

  // 处理批量添加弹窗关闭
  const handleBatchModalClose = () => {
    setBatchModalVisible(false);
  };

  // 处理批量添加确认
  const handleBatchModalOk = (data: any) => {
    console.log('批量添加资源数据:', data);
    // 这里可以处理批量添加的逻辑
    // 例如：循环调用 api.editMediaItem 来添加每个资源
    // 或者将数据传递给其他处理函数

    // 刷新数据
    const currentFilters = getFilter();
    getData({
      current: tableList.current,
      size: tableList.size,
      ...currentFilters,
    });
  };

  // 处理编辑资源
  const handleEdit = async (record: any) => {
    setDrawerVisible(true);
    setEditRecord(record);
    setDrawerKey(Date.now());

    // 直接使用列表数据，不调用接口
    setRecommendPositionDetail(null);
  };

  // 处理抽屉关闭
  const handleDrawerClose = () => {
    setDrawerVisible(false);
    setEditRecord(null);
    setRecommendPositionDetail(null);
  };

  // 处理抽屉确认
  const handleDrawerOk = () => {
    // 刷新数据并更新排序状态
    const currentFilters = getFilter();
    getData({
      current: tableList.current,
      size: tableList.size,
      ...currentFilters,
    });
  };

  // 处理排序
  const handleSort = (record: any, pos: number) => {
    let newPos = pos;
    const posChange = (value: number | undefined) => {
      newPos = value || pos;
    };

    Modal.confirm({
      title: <p>排序：《{record.title}》</p>,
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber
            placeholder="请输入数字"
            min={1}
            onChange={posChange}
            defaultValue={pos}
            style={{ width: '200px', marginLeft: '8px' }}
            precision={0}
          />
        </div>
      ),
      onOk: () => {
        if (!newPos || newPos < 1) {
          message.error('请填写有效的序号');
          return Promise.reject();
        }

        setLoading(true);
        return api
          .updateMediaItemOrder({
            id: record.id,
            sort_flag: 2, // 指定位置
            number: newPos,
          })
          .then((res: any) => {
            message.success('操作成功');
            // 保持当前页面和过滤条件刷新数据，确保排序状态更新
            const currentFilters = getFilter();
            getData({
              current: tableList.current,
              size: tableList.size,
              ...currentFilters,
            });
          })
          .catch((error: any) => {
            console.error('排序失败:', error);
          })
          .finally(() => {
            setLoading(false);
          });
      },
      okText: '确定',
      cancelText: '取消',
    });
  };

  // 处理状态变更
  const handleStatusChange = (record: any) => {
    const isOnline = record.show === true;
    Modal.confirm({
      title: isOnline ? '下架后，在所有页面都不再展示，也不能播放' : '确定上架？',
      onOk: () => {
        setLoading(true);
        api
          .updateMediaItemOnline({
            id: record.id,
            online: !isOnline,
          })
          .then((res: any) => {
            message.success('操作成功');
            // 刷新数据并更新排序状态
            const currentFilters = getFilter();
            getData({
              current: tableList.current,
              size: tableList.size,
              ...currentFilters,
            });
          })
          .catch((error: any) => {
            console.error('状态变更失败:', error);
          })
          .finally(() => {
            setLoading(false);
          });
      },
    });
  };

  // 处理删除
  const handleDelete = (record: any) => {
    Modal.confirm({
      content: '确定删除资源？',
      onOk: () => {
        setLoading(true);
        api
          .deleteMediaItem({
            id: record.id,
          })
          .then((res: any) => {
            message.success('删除成功');
            // 刷新数据并更新排序状态
            const currentFilters = getFilter();
            getData({
              current: tableList.current,
              size: tableList.size,
              ...currentFilters,
            });
          })
          .catch((error: any) => {
            console.error('删除失败:', error);
          })
          .finally(() => {
            setLoading(false);
          });
      },
    });
  };

  // 处理预览
  const handlePreview = (record: any) => {
    // 预留预览功能，打开播放器
    message.info(`预览资源: ${record.title}`);
  };

  // 处理排序操作
  const order = (record: any, current: number, type: number) => {
    setLoading(true);
    api
      .updateMediaItemOrder({
        id: record.id,
        sort_flag: type === -1 ? 0 : 1, // 0: 向上, 1: 向下
      })
      .then((res: any) => {
        message.success('操作成功');
        // 保持当前页面和过滤条件刷新数据，确保排序状态更新
        const currentFilters = getFilter();
        getData({
          current: tableList.current,
          size: tableList.size,
          ...currentFilters,
        });
      })
      .catch((error: any) => {
        console.error('排序失败:', error);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 获取列配置
  const getColumns = useMemo(() => {
    const { current, size } = tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;

    // 检查是否有筛选条件
    const hasFilter = filter.show !== '';

    // 获取展示中的数据列表
    const showingRecords = tableList.records?.filter((record: any) => record.show === true) || [];

    return [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) => {
          // 当前记录不是展示状态时，禁用排序
          const isNotShowing = record.show !== true;

          // 当有筛选条件时，禁用排序功能
          const disableByFilter = hasFilter;

          // 判断是否是最后一条展示中的数据
          // 需要在当前页面的展示中数据中找到最后一条
          const currentPageShowingRecords =
            tableList.records?.filter((r: any, index: number) => r.show === true) || [];
          const isLastShowingRecord =
            currentPageShowingRecords.length > 0 &&
            record.show === true &&
            record.id === currentPageShowingRecords[currentPageShowingRecords.length - 1]?.id;

          // 向上排序禁用条件：不是展示状态 或 有筛选条件
          const disableUp = isNotShowing || disableByFilter;

          // 向下排序禁用条件：不是展示状态 或 有筛选条件 或 是最后一条展示中的记录
          const disableDown = isNotShowing || disableByFilter || isLastShowingRecord;

          return (
            <OrderColumn
              pos={getSeq(i)}
              start={1}
              end={allData.on_show_count}
              perm="media:edit"
              disable={!record.show}
              onUp={() => order(record, getSeq(i), -1)}
              onDown={() => order(record, getSeq(i), 1)}
            />
          );
        },
        width: 60,
      },
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 60,
      },
      {
        title: '影视ID',
        dataIndex: 'article_id',
        width: 80,
      },
      {
        title: '封面图',
        dataIndex: 'cover_url',
        width: 100,
        render: (text: string) => (
          <img
            src={text}
            alt="封面"
            style={{ height: '80px', objectFit: 'cover', display: 'block', cursor: 'pointer' }}
            onClick={() => {
              setImagePreview({
                visible: true,
                imgs: [text],
                index: 0,
              });
            }}
          />
        ),
      },
      {
        title: '标题',
        dataIndex: 'title',
        render: (text: string, record: any) =>
          text ? (
            <a href={record.video_url} target="_blank" rel="noopener noreferrer">
              {text}
            </a>
          ) : (
            '-'
          ),
      },
      {
        title: (
          <div>
            播放量
            {/* <Tooltip
              title="有分集的影视，统计的是所有资源播放量总和（包括下架状态的）"
              placement="top"
            >
              <Icon type="question-circle" />
            </Tooltip> */}
          </div>
        ),
        dataIndex: 'read_count',
        width: 100,
        render: (text: number) => (text || 0).toLocaleString(),
      },
      {
        title: '状态',
        dataIndex: 'show',
        width: 80,
        render: (text: boolean) => (text ? '展示中' : '未展示'),
      },
      {
        title: '操作人',
        dataIndex: 'updated_by',
        width: 100,
      },
      {
        title: '最后操作时间',
        dataIndex: 'updated_at',
        width: 100,
        render: (text: number) => (
          <span style={{ whiteSpace: 'pre-wrap' }}>
            {text ? moment(text).format('YYYY-MM-DD \n HH:mm:ss') : '-'}
          </span>
        ),
      },
      {
        title: '操作',
        key: 'action',
        width: 100,
        render: (text: any, record: any, i: number) => {
          const menu = (
            <Menu>
              {requirePerm4Function(
                session,
                'media:edit'
              )(<Menu.Item onClick={() => handleEdit(record)}>编辑资源</Menu.Item>)}
              {record.show === true &&
                requirePerm4Function(
                  session,
                  'media:edit'
                )(<Menu.Item onClick={() => handleSort(record, getSeq(i))}>排序</Menu.Item>)}
              {requirePerm4Function(
                session,
                'media:online'
              )(
                <Menu.Item onClick={() => handleStatusChange(record)}>
                  {record.show === true ? '下架' : '上架'}
                </Menu.Item>
              )}
              {requirePerm4Function(
                session,
                'media:edit'
              )(<Menu.Item onClick={() => handleDelete(record)}>删除</Menu.Item>)}
              {record.url && (
                <Menu.Item>
                  <ReactClipboard
                    action="copy"
                    text={record.url}
                    onSuccess={() => message.success('链接已复制')}
                    onError={() => message.error('复制失败')}
                  >
                    <span>复制链接</span>
                  </ReactClipboard>
                </Menu.Item>
              )}
            </Menu>
          );

          return (
            <Dropdown overlay={menu}>
              <a className="ant-dropdown-link">
                操作 <Icon type="down" />
              </a>
            </Dropdown>
          );
        },
      },
    ];
  }, [tableList.current, tableList.size, tableList.total, tableList.records, filter.show]);

  // 获取动态面包屑
  const getBreadcrumb = () => {
    const baseBreadcrumb = ['运营管理', '影视内容管理'];
    if (filmName) {
      const decodedFilmName = decodeURIComponent(filmName);
      const truncatedName =
        decodedFilmName.length > 10 ? decodedFilmName.substring(0, 10) + '...' : decodedFilmName;
      return [...baseBreadcrumb, truncatedName];
    }
    return baseBreadcrumb;
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button onClick={handleBack} style={{ marginRight: 8 }}>
            <Icon type="arrow-left" /> 返回
          </Button>
          <PermButton perm="media:edit" onClick={handleAddResource} style={{ marginRight: 8 }}>
            <Icon type="plus-circle" /> 添加资源
          </PermButton>
          <PermButton perm="media:edit" onClick={handleBatchAddResource}>
            <Icon type="plus-circle" /> 批量添加资源
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(getBreadcrumb())}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={24}>
            <Form layout="inline">
              <Form.Item>
                <Select
                  value={filter.show}
                  onChange={(value) => handleFilterChange('show', value)}
                  style={{ width: 120 }}
                  placeholder="展示状态"
                >
                  <Select.Option value="">展示状态</Select.Option>
                  <Select.Option value="true">展示中</Select.Option>
                  <Select.Option value="false">未展示</Select.Option>
                </Select>
              </Form.Item>
            </Form>
          </Col>
        </Row>

        <Table
          func="getMediaItemList"
          index="list"
          rowKey="id"
          filter={getFilter()}
          columns={getColumns}
          pagination={true}
        />

        <AddFilmResourceDrawer
          visible={drawerVisible}
          record={editRecord}
          filmId={filmId || undefined}
          filmName={filmName ? decodeURIComponent(filmName) : ''}
          recommendPositionDetail={recommendPositionDetail}
          onClose={handleDrawerClose}
          onOk={handleDrawerOk}
          skey={drawerKey}
        />

        <BatchAddResourceModal
          visible={batchModalVisible}
          filmId={filmId || undefined}
          filmName={filmName ? decodeURIComponent(filmName) : ''}
          onClose={handleBatchModalClose}
          onOk={handleBatchModalOk}
          skey={batchModalKey}
        />

        {/* ✅ 添加图片预览组件 */}
        <PhotoSlider
          maskOpacity={0.5}
          images={imagePreview.imgs?.map((v: any) => ({ src: v, key: v })) || []}
          visible={imagePreview.visible}
          onClose={() =>
            setImagePreview({
              ...imagePreview,
              visible: false,
            })
          }
          index={imagePreview.index}
          onIndexChange={(index) =>
            setImagePreview({
              ...imagePreview,
              index,
            })
          }
        />
      </div>
    </>
  );
}
