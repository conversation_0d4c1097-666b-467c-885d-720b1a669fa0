import React, { useState, useEffect } from 'react';
import {
  Form,
  Switch,
  Button,
  message,
  Input,
  Row,
  Col,
  Icon,
  Divider,
  Radio,
  Modal,
  InputNumber,
  TimePicker,
  Select,
  Spin,
  Tooltip,
} from 'antd';
import { Drawer, ImageUploader, SearchAndInput } from '@app/components/common';
import { ColorSelectButton } from '@app/components/business/ColorSelectModal';
import { findApi, searchApi } from '@app/api';
import { useDispatch, useSelector } from 'react-redux';
import { setConfig } from '@app/action/config';
import { FormComponentProps } from 'antd/es/form';
import moment from 'moment';
import debounce from 'lodash/debounce';

interface BannerDrawerProps extends FormComponentProps {
  visible: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

interface BannerItem {
  type: number; // 0-影视 1-链接
  id?: string; // 关联影视ID
  media_info?: any; // 影视信息
  jump_url?: string; // 链接地址
  pic_url: string; // 图片地址
  title?: string; // 影视标题
  brief_summary: string; // 一句话推荐
  recommend_start_time?: string; // 推荐播放开始时间
  recommend_end_time?: string; // 推荐播放结束时间
  corner_text: string; // 角标文字
  corner_color: string; // 角标颜色
  show_corner?: boolean; // 是否显示角标
  recommend_number?: any; // 推荐资源序号
  background_color?: string; // 背景色必
  background_color_type?: number; // 背景色类型
  unique_key?: string; // ✅ 添加唯一标识符
}

const BannerDrawerForm: React.FC<BannerDrawerProps> = (props) => {
  const { visible, onClose, onSuccess, form } = props;
  const { getFieldDecorator, validateFields, resetFields } = form;
  const dispatch = useDispatch();
  const loading = useSelector((state: any) => state.config?.mLoading || false);
  
  const [show, setShow] = useState(true); // 默认打开
  const [carousel, setCarousel] = useState(true); // 默认打开
  const [bannerList, setBannerList] = useState<BannerItem[]>([]); // 默认空数组
  const [searchResults, setSearchResults] = useState<any[]>([]); // 搜索结果
  const [searching, setSearching] = useState(false); // 搜索状态

  // 获取轮播图数据
  const fetchBannerData = async () => {
    try {
      dispatch(setConfig({ mLoading: true }));
      const res = await findApi.getMediaCarouselDetail({});
      if (res.data) {
        const data = (res.data as any).detail;
        setShow(data.show !== false); // 默认true
        setCarousel(data.carousel !== false); // 默认true
        if (data.list && data.list.length > 0) {
          const newList = data.list.map((item: any, index: number) => ({
            type: item.type || 0,
            id: item.id,
            media_info: item.media_info,
            jump_url: item.jump_url || '',
            pic_url: item.pic_url || '',
            title: item.title || '',
            brief_summary: item.brief_summary || '',
            recommend_start_time: item.recommend_start_time || '', // 注意字段名映射
            recommend_end_time: item.recommend_end_time || '', // 注意字段名映射
            corner_text: item.corner_text || '',
            corner_color: item.corner_color || '#FF3C47',
            show_corner: !!item.corner_text, // 如果有角标文字则认为启用了角标
            recommend_number: item.recommend_number, // 默认第1个资源
            background_color: item.background_color || '', // 背景色
            background_color_type: item.background_color_type, // 背景色类型
            unique_key: `banner_${Date.now()}_${index}`, // ✅ 添加唯一标识符
          }));
          setBannerList(newList);
          setSearchResults(processBannerList(newList));
        }
      }
    } catch (error) {
      message.error('获取数据失败');
    } finally {
      dispatch(setConfig({ mLoading: false }));
    }
  };
  // 处理轮播图数据，提取有ID的项目
  const processBannerList = (list: BannerItem[]) => {
    const processedList: any[] = [];
    const seenIds = new Set(); // 用于去重的Set

    list.forEach((item) => {
      if (item.id && !seenIds.has(item.id)) {
        // 检查ID是否存在且未重复
        seenIds.add(item.id); // 将ID添加到已见集合中
        const processedItem = {
          id: item.id,
          title: item.title || '',
          brief_summary: item.brief_summary || '',
          corner_color: item.corner_color || '',
          corner_text: item.corner_text || '',
          pic_url: item.pic_url || '',
          recommend_number: item.recommend_number || '',
          recommend_start_time: item.recommend_start_time || '',
          recommend_end_time: item.recommend_end_time || '',
          background_color: item.background_color || '', // 添加背景色字段
          background_color_type: item.background_color_type, // 添加背景色类型字段
        };
        processedList.push(processedItem);
      }
    });

    return processedList;
  };
  useEffect(() => {
    if (visible) {
      fetchBannerData();
    } else {
      // 关闭时重置所有数据
      resetFields();
      setBannerList([]);
      setShow(true);
      setCarousel(true);
      setSearchResults([]); // 清空搜索结果
      setSearching(false); // 重置搜索状态
    }
  }, [visible]);

  // 添加图片
  const handleAdd = () => {
    if (bannerList.length >= 10) {
      message.warning('最多添加10张图片');
      return;
    }
    setBannerList([
      ...bannerList,
      {
        type: 0, // 默认影视
        pic_url: '',
        title: '', // 初始化标题
        brief_summary: '',
        corner_text: '',
        corner_color: '#FF3C47',
        show_corner: false,
        recommend_number: '', // 默认第1个资源
        background_color: '', // 背景色
        background_color_type: 1, // 背景色类型
        unique_key: `banner_${Date.now()}_${bannerList.length}`, // ✅ 添加唯一标识符
      },
    ]);
  };

  // 删除图片
  const handleRemove = (index: number) => {
    const newList = [...bannerList];
    newList.splice(index, 1);
    setBannerList(newList);
  };

  // 上移
  const handleMoveUp = (index: number) => {
    if (index === 0) return;
    const newList = [...bannerList];
    [newList[index], newList[index - 1]] = [newList[index - 1], newList[index]];
    setBannerList(newList);
  };

  // 下移
  const handleMoveDown = (index: number) => {
    if (index === bannerList.length - 1) return;
    const newList = [...bannerList];
    [newList[index], newList[index + 1]] = [newList[index + 1], newList[index]];
    setBannerList(newList);
  };

  // 更新列表项 - ✅ 修改为使用unique_key查找
  const updateListItem = (uniqueKey: string, field: string, value: any) => {
    const newList = [...bannerList];
    const index = newList.findIndex(item => item.unique_key === uniqueKey);
    if (index !== -1) {
      newList[index] = { ...newList[index], [field]: value };
      setBannerList(newList);
    }
  };

  // 搜索影视
  const handleMediaSearch = debounce((value: string, uniqueKey: string) => {
    if (!value) {
      setSearchResults([]);
      return;
    }

    setSearching(true);
    searchApi
      .searchFilmTv({
        keyword: value,
      })
      .then((res: any) => {
        const list = res.data?.list || [];
        setSearchResults(list);
      })
      .catch(() => {
        setSearchResults([]);
      })
      .finally(() => {
        setSearching(false);
      });
  }, 500);

  // 选择影视后填充信息 - ✅ 修改为使用unique_key
  const handleMediaSelect = (uniqueKey: string, media: any) => {
    const index = bannerList.findIndex(item => item.unique_key === uniqueKey);
    if (index === -1) return;

    const item = bannerList[index];
    
    if (!media) {
      updateListItem(uniqueKey, 'media_info', null);
      updateListItem(uniqueKey, 'id', '');
      updateListItem(uniqueKey, 'title', ''); // 清空标题
      updateListItem(uniqueKey, 'recommend_number', ''); // 重置为默认值
      updateListItem(uniqueKey, 'recommend_start_time', ''); // 清空开始时间
      updateListItem(uniqueKey, 'recommend_end_time', ''); // 清空结束时间
      return;
    }

    // 如果已有数据，询问是否覆盖
    const hasExistingData = item.pic_url || item.brief_summary || item.corner_text;

    const fillMediaInfo = () => {
      // 构建新的项目对象
      const updatedItem = {
        ...item,
        // 必须填充的字段
        media_info: media,
        id: media.id,
        title: media.name || media.title || '', // 保存影视标题
      };

      // 自动填充影视信息 - 图片优先级：list_pic_w > pic_url > list_pic
      const picUrl = media.list_pic_w || media.pic_url || media.list_pic;
      if (picUrl) {
        updatedItem.pic_url = picUrl;
      }

      // 填充简介 - 优先级：summary > brief_summary > description
      const summary = media.brief_summary || '';
      if (summary) {
        updatedItem.brief_summary = summary;
      }

      // 填充角标文字
      if (media.corner_text) {
        updatedItem.corner_text = media.corner_text;
        updatedItem.show_corner = true; // 如果有角标文字，自动开启角标开关
      }

      // 填充角标颜色
      if (media.corner_color) {
        updatedItem.corner_color = media.corner_color;
      }

      // 填充背景色
      if (media.background_color) {
        updatedItem.background_color = media.background_color;
      }
      // 填充背景色类型
      if (media.background_color_type) {
        updatedItem.background_color_type = media.background_color_type;
      }
      // 填充推荐播放片段字段
      if (media.recommend_number) {
        updatedItem.recommend_number = media.recommend_number;
      }

      if (media.recommend_start_time) {
        updatedItem.recommend_start_time = media.recommend_start_time;
      }

      if (media.recommend_end_time) {
        updatedItem.recommend_end_time = media.recommend_end_time;
      }


      // 统一替换整个项目
      const newList = [...bannerList];
      newList[index] = updatedItem;
      setBannerList(newList);
    };

    if (hasExistingData) {
      Modal.confirm({
        title: '确认填充',
        content: '检测到已有填写的数据，是否使用影视信息覆盖？',
        onOk: () => {
          fillMediaInfo();
          return Promise.resolve();
        },
        okText: '确认',
        cancelText: '取消',
      });
    } else {
      fillMediaInfo();
    }
  };

  // 提交表单
  const handleSubmit = () => {
    validateFields((err: any, values: any) => {
      if (err) {
        return;
      }

      // 只有当前台显示开关打开时才进行必填项验证
      if (values.show) {
        // 验证每个图片项
        for (let i = 0; i < bannerList.length; i++) {
          const item = bannerList[i];

          // 图片必填
          if (!item.pic_url) {
            message.error(`请上传第${i + 1}张图片`);
            return;
          }

          // 背景色必填
          if (!item.background_color) {
            message.error(`请选择第${i + 1}张图片的背景色`);
            return;
          }

          // 根据类型验证
          if (item.type === 0) {
            // 影视类型，必须选择影视
            if (!item.id) {
              message.error(`请选择第${i + 1}项的关联影视`);
              return;
            }
          } else {
            // 链接类型，必须填写链接
            if (!item.jump_url) {
              message.error(`请输入第${i + 1}项的链接`);
              return;
            }
          }
        }
      }

      dispatch(setConfig({ mLoading: true }));

      const submitData = {
        show: values.show,
        carousel: values.carousel,
        list: bannerList.map((item, index) => {
          const submitItem: any = {
            type: item.type,
            pic_url: item.pic_url,
            brief_summary: item.brief_summary?.trim() || '', // 过滤首尾空格
            background_color: item.background_color || '', // 背景色必填字段
            background_color_type: item.background_color_type, // 背景色类型
          };

          // 根据类型添加必填字段
          if (item.type === 0) {
            // 影视类型
            submitItem.id = item.id;
            submitItem.title = item.title || ''; // 提交标题字段
          } else {
            // 链接类型
            submitItem.jump_url = item.jump_url;
          }

          // 可选字段：推荐播放片段
          if (item.recommend_number) {
            submitItem.recommend_number = item.recommend_number;
          }
          if (item.recommend_start_time) {
            submitItem.recommend_start_time = item.recommend_start_time; // 注意字段名
          }
          if (item.recommend_end_time) {
            submitItem.recommend_end_time = item.recommend_end_time; // 注意字段名
          }

          // 可选字段：自定义角标
          // if (item.show_corner && item.corner_text) {
          //   submitItem.corner_text = item.corner_text;
          //   submitItem.corner_color = item.corner_color;
          // } else {
          //   submitItem.corner_text = '';
          //   submitItem.corner_color = '';
          // }
          if (item.corner_text) {
            submitItem.corner_text = item.corner_text;
          } else {
            submitItem.corner_text = '';
          }
          // 不需要sort_order字段，接口文档中没有

          return submitItem;
        }),
      };


      findApi
        .editMediaCarousel(submitData)
        .then(() => {
          message.success('保存成功');
          onClose();
          if (onSuccess) {
            onSuccess();
          }
        })
        .catch((error) => {
          console.error('提交失败:', error);
        })
        .finally(() => {
          dispatch(setConfig({ mLoading: false }));
        });
    });
  };

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const colorOptions = [
    { label: '红色', value: '#FF3C47' },
    { label: '蓝色', value: '#189AFF' },
    { label: '黄色', value: '#F5CA24' },
  ];

  // 影视搜索列配置
  const mediaColumns = [
    { title: 'ID', dataIndex: 'id', width: 80 },
    { title: '影视名称', dataIndex: 'name' },
    { title: '类型', dataIndex: 'type', width: 80 },
  ];

  // ✅ 修改背景色处理函数，使用unique_key，合并状态更新
  const handleColorChange = (colorData: any, uniqueKey: string) => {
    // ⚡ 优化：将两个字段的更新合并为一次状态更新，避免时序问题
    const newList = [...bannerList];
    const index = newList.findIndex(item => item.unique_key === uniqueKey);
    if (index !== -1) {
      newList[index] = { 
        ...newList[index], 
        background_color_type: colorData.color_type,
        background_color: colorData.bottom_color
      };
      setBannerList(newList);
    }
  };
  return (
    <Drawer
      title="影视轮播图管理"
      visible={visible}
      skey="bannerDrawer"
      onClose={onClose}
      onOk={handleSubmit}
      width={1000}
      maskClosable={false}
      okText="确定"
      config={{ mLoading: loading }}
    >
      <Form {...formLayout}>
        <Form.Item label="前台显示" extra="关闭后在前台不显示这个模块">
          {getFieldDecorator('show', {
            initialValue: show,
            valuePropName: 'checked',
          })(<Switch />)}
        </Form.Item>

        <Form.Item label="自动轮播">
          {getFieldDecorator('carousel', {
            initialValue: carousel,
            valuePropName: 'checked',
          })(<Switch />)}
        </Form.Item>

        {bannerList.length === 0 ? (
          <div
            style={{
              textAlign: 'center',
              padding: '40px 0',
              color: '#999',
            }}
          >
            <p>暂无图片，请点击下方按钮添加</p>
          </div>
        ) : (
          bannerList.map((item, index) => (
            <div key={item.unique_key}> {/* ✅ 使用unique_key作为key */}
              <Divider />

              {/* 排序和删除按钮行 */}
              <Row style={{ marginBottom: 8 }}>
                <Col span={4} style={{ textAlign: 'right', paddingRight: '8px' }}>
                  <Button
                    onClick={() => handleMoveUp(index)}
                    disabled={index === 0}
                    type="primary"
                    icon="up"
                    style={{ marginRight: 4 }}
                  />
                  <Button
                    onClick={() => handleMoveDown(index)}
                    disabled={index === bannerList.length - 1}
                    type="primary"
                    icon="down"
                  />
                </Col>
                <Col span={18} style={{ display: 'flex', alignItems: 'center', height: '32px' }}>
                  <span style={{ fontWeight: 'bold', fontSize: '16px' }}>图片{index + 1}</span>
                  {index === 0 && (
                    <span style={{ color: '#999', marginLeft: 16 }}>最多添加10张图片</span>
                  )}
                </Col>
                <Col span={2} style={{ textAlign: 'right' }}>
                  <Button type="danger" onClick={() => handleRemove(index)}>
                    删除
                  </Button>
                </Col>
              </Row>

              <Form.Item label="类型" required>
                <Radio.Group
                  value={item.type}
                  onChange={(e) => {
                    const newType = e.target.value;
                    const newList = [...bannerList];
                    newList[index] = {
                      ...newList[index],
                      type: newType,
                      // 当切换到链接类型时，清空推荐播放片段相关数据和影视相关数据
                      ...(newType === 1
                        ? {
                            id: '',
                            title: '',
                            media_info: null,
                            recommend_number: '',
                            recommend_start_time: '',
                            recommend_end_time: '',
                          }
                        : {
                            jump_url: '', // 切换到影视类型时，清空链接地址
                          }),
                    };
                    setBannerList(newList);
                  }}
                >
                  <Radio value={0}>影视</Radio>
                  <Radio value={1}>链接</Radio>
                </Radio.Group>
              </Form.Item>

              {item.type === 0 ? (
                // 影视类型
                <>
                  <Form.Item label="关联影视" required>
                    <Select
                      showSearch
                      placeholder="输入标题或ID搜索影视"
                      value={item.id || undefined}
                      onSearch={(value) => handleMediaSearch(value, item.unique_key!)}
                      onChange={(value) => {
                        // 从搜索结果中找到对应的media
                        const media = searchResults.find((m) => m.id === value);
                        if (media) {
                          handleMediaSelect(item.unique_key!, media);
                        }
                      }}
                      filterOption={false}
                      notFoundContent={searching ? <Spin size="small" /> : '暂无数据'}
                      style={{ width: '100%' }}
                    >
                      {searchResults.map((media: any) => (
                        <Select.Option key={media.id} value={media.id}>
                          {media.id} - {media.name || media.title}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </>
              ) : (
                // 链接类型
                <Form.Item label="链接地址" required>
                  <Input
                    value={item.jump_url}
                    onChange={(e) => updateListItem(item.unique_key!, 'jump_url', e.target.value)}
                    placeholder="请输入链接"
                  />
                </Form.Item>
              )}

              <Form.Item label="上传图片" required extra="支持jpg,jpeg,png,gif图片格式，比例为16:9">
                <ImageUploader
                  value={item.pic_url}
                  onChange={(value: string) => updateListItem(item.unique_key!, 'pic_url', value || '')}
                  ratio={16 / 9}
                  accept={['image/png', 'image/jpeg', 'image/jpg', 'image/gif']}
                />
              </Form.Item>
              {/* 背景色 */}
              <Form.Item label="背景色" required>
                <ColorSelectButton
                  value={{ bottom_color: item.background_color, color_type: item.background_color_type}}
                  onChange={(colorData: {
                    color_type: number;
                    auto_color: string;
                    bottom_color: string;
                  }) => {
                    handleColorChange(colorData, item.unique_key!); // ✅ 使用unique_key
                  }}
                  picUrl={item.pic_url || ''}
                  preview={(colorData: any) => (
                    <div
                      style={{
                        width: '50px',
                        height: '50px',
                        background: colorData.bottom_color || '#f0f0f0',
                      }}
                    ></div>
                  )}
                  colors={[
                    {
                      name: '橙色',
                      color: '#FF822C',
                    },
                    {
                      name: '蓝色',
                      color: '#3A7FFF',
                    },
                    {
                      name: '黄色',
                      color: '#FFB12C',
                    },
                    {
                      name: '绿色',
                      color: '#81C44A',
                    },
                    {
                      name: '紫色',
                      color: '#8157FF',
                    },
                    {
                      name: '桃红色',
                      color: '#FF2C7A',
                    },
                    {
                      name: '青色',
                      color: '#24B8B8',
                    },
                  ]}
                />
              </Form.Item>
              <Form.Item label="一句话推荐">
                <Input
                  value={item.brief_summary}
                  onChange={(e) => updateListItem(item.unique_key!, 'brief_summary', e.target.value)}
                  placeholder="最多20字"
                  maxLength={20}
                />
              </Form.Item>

              {/* {item.type === 0 && (
                <Form.Item label="推荐播放片段">
                  <div>
                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                      <span style={{ marginRight: 8 }}>第</span>
                      <InputNumber
                        value={item.recommend_number}
                        onChange={(value) => updateListItem(index, 'recommend_number', value)}
                        min={1}
                        max={9999}
                        precision={0}
                        style={{ width: 200 }}
                        placeholder="选填，输入数字，最多9999"
                      />
                      <span style={{ marginLeft: 8, marginRight: 8 }}>个资源</span>
                      <Tooltip
                        title={
                          <div>
                            <div>1、用于在列表自动播放等场景</div>
                            <div>2、资源不设置，默认第1个</div>
                            <div>开始时间不设置，默认从头开始</div>
                            <div>结束时间不设置，默认播放到最后</div>
                            <div>3、设置数据错误（资源或时间与实际不符），按默认规则处理</div>
                          </div>
                        }
                        placement="top"
                      >
                        <Icon type="question-circle" />
                      </Tooltip>
                    </div>

                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <span style={{ marginRight: 8 }}>选择时间</span>
                      <div style={{ display: 'flex', flex: 1 }}>
                        <TimePicker
                          value={
                            item.recommend_start_time
                              ? moment(item.recommend_start_time, 'HH:mm:ss')
                              : undefined
                          }
                          onChange={(time) => {
                            updateListItem(
                              index,
                              'recommend_start_time',
                              time ? time.format('HH:mm:ss') : ''
                            );
                            // 如果开始时间改变，需要检查结束时间是否仍然有效
                            if (time && item.recommend_end_time) {
                              const endTime = moment(item.recommend_end_time, 'HH:mm:ss');
                              if (endTime.isBefore(time)) {
                                updateListItem(index, 'recommend_end_time', '');
                                message.warning('结束时间不能早于开始时间，已自动清空');
                              }
                            }
                          }}
                          placeholder="开始时间"
                          format="HH:mm:ss"
                          style={{ flex: 1 }}
                          defaultOpenValue={moment('00:00:00', 'HH:mm:ss')}
                        />
                        <span style={{ margin: '0 8px', lineHeight: '32px' }}>至</span>
                        <TimePicker
                          value={
                            item.recommend_end_time
                              ? moment(item.recommend_end_time, 'HH:mm:ss')
                              : undefined
                          }
                          onChange={(time) => {
                            if (time && item.recommend_start_time) {
                              const startTime = moment(item.recommend_start_time, 'HH:mm:ss');
                              if (time.isBefore(startTime)) {
                                message.warning('结束时间不能早于开始时间，请重新选择');
                                setTimeout(() => {
                                  updateListItem(index, 'recommend_end_time', '');
                                }, 100);
                                return;
                              }
                            }
                            updateListItem(
                              index,
                              'recommend_end_time',
                              time ? time.format('HH:mm:ss') : ''
                            );
                          }}
                          placeholder="结束时间"
                          format="HH:mm:ss"
                          style={{ flex: 1 }}
                          defaultOpenValue={
                            item.recommend_start_time
                              ? moment(item.recommend_start_time, 'HH:mm:ss')
                              : moment('00:00:00', 'HH:mm:ss')
                          }
                        />
                      </div>
                    </div>
                  </div>
                </Form.Item>
              )} */}

              {/* <Form.Item label="自定义角标">
                <Switch
                  checked={!!item.show_corner}
                  onChange={(checked) => {
                    setBannerList((prevList) => {
                      const newList = [...prevList];
                      newList[index] = {
                        ...newList[index],
                        show_corner: checked,
                        // 关闭时清空角标相关字段
                        ...(checked
                          ? {}
                          : {
                              corner_text: '',
                              corner_color: '#FF3C47',
                            }),
                      };
                      return newList;
                    });
                  }}
                />
                <Tooltip title="在全部影视列表等页面显示" placement="top">
                  <Icon type="question-circle" style={{ marginLeft: 8 }} />
                </Tooltip>
              </Form.Item> */}

              {/* 角标文字 */}
              <Form.Item label="角标文字">
                <Input
                  value={item.corner_text}
                  onChange={(e) => updateListItem(item.unique_key!, 'corner_text', e.target.value)}
                  placeholder="最多5个字，如推荐、最新上架等"
                  maxLength={5}
                />
              </Form.Item>

              {/* 角标颜色 */}
              {/* {item.show_corner && (
                <Form.Item label="角标颜色" required>
                  <Radio.Group
                    value={item.corner_color}
                    onChange={(e) => updateListItem(index, 'corner_color', e.target.value)}
                  >
                    {colorOptions.map((color) => (
                      <Radio key={color.value} value={color.value}>
                        <span
                          style={{
                            display: 'inline-block',
                            width: 16,
                            height: 16,
                            backgroundColor: color.value,
                            verticalAlign: 'middle',
                            marginRight: 8,
                            border: '1px solid #d9d9d9',
                          }}
                        ></span>
                        {color.label}
                      </Radio>
                    ))}
                  </Radio.Group>
                </Form.Item>
              )} */}
            </div>
          ))
        )}

        {bannerList.length < 10 && (
          <Row style={{ marginTop: 20, textAlign: 'center' }}>
            <Button onClick={handleAdd} type="dashed" icon="plus">
              添加一张图片
            </Button>
          </Row>
        )}
      </Form>
    </Drawer>
  );
};

const BannerDrawer = Form.create<BannerDrawerProps>({ name: 'bannerDrawer' })(BannerDrawerForm);

export default BannerDrawer;
