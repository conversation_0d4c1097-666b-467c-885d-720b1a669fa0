import React, { Component } from 'react';
import {
  Form,
  Input,
  Radio,
  Button,
  Icon,
  message,
  InputNumber,
  TimePicker,
  Switch,
  Checkbox,
  Tooltip,
} from 'antd';
import { FormComponentProps } from 'antd/lib/form';
import { ImageUploader, Drawer } from '@app/components/common';
import moment from 'moment';
import { findApi as api } from '@app/api';
import { setMLoading } from '@app/utils/utils';
import connectSession from '@app/utils/connectSession';

interface AddFilmTvDrawerProps extends FormComponentProps {
  visible: boolean;
  record?: any;
  onClose: () => void;
  onOk?: () => void;
  skey: number;
}

interface AddFilmTvDrawerState {
  isSeries: number; // 是否分集
  startTime?: moment.Moment; // 开始时间
  showCornerMark: boolean; // 是否显示自定义角标
}

// 角标颜色选项
const CORNER_COLORS = [
  { value: '#FF3C47', label: '红色' },
  { value: '#189AFF', label: '蓝色' },
  { value: '#F5CA24', label: '黄色' },
];

@connectSession
class AddFilmTvDrawer extends Component<AddFilmTvDrawerProps, AddFilmTvDrawerState> {
  constructor(props: AddFilmTvDrawerProps) {
    super(props);
    const record = props.record;
    this.state = {
      isSeries: record?.multi_items !== undefined ? (record.multi_items ? 1 : 2) : 1, // 从boolean转换为数字
      startTime: record?.recommend_start_time ? moment(record.recommend_start_time, 'HH:mm:ss') : undefined,
      showCornerMark: record?.corner_text ? true : false, // 如果有角标文字则认为启用了角标
    };
  }

  componentDidUpdate(prevProps: AddFilmTvDrawerProps) {
    if (prevProps.visible !== this.props.visible && this.props.visible) {
      const record = this.props.record;
      // 弹窗打开时，重置状态
      this.setState({
        isSeries: record?.multi_items !== undefined ? (record.multi_items ? 1 : 2) : 1,
        startTime: record?.recommend_start_time
          ? moment(record.recommend_start_time, 'HH:mm:ss')
          : undefined,
        showCornerMark: record?.corner_text ? true : false,
      });
    }
  }

  handleSubmit = () => {
    const { form, onOk } = this.props;
    form.validateFields((err, values) => {
      if (!err) {
        // 处理数据
        const processedValues: any = {
          ...values,
          id: this.props.record?.id, // 编辑时传入id
          // 处理字符串字段的首尾空格
          title: values.title?.trim(),
          summary: values.summary?.trim()?.replace(/^\n+|\n+$/g, ''), // 过滤首尾空换行
          brief_summary: values.brief_summary?.trim(),
          corner_text: values.corner_text?.trim(),
          // 处理时间格式
          recommend_start_time: values.recommend_start_time ? values.recommend_start_time.format('HH:mm:ss') : '',
          recommend_end_time: values.recommend_end_time ? values.recommend_end_time.format('HH:mm:ss') : '',
          // 处理类型数组 - 改为theme_ids
          themes: values.themes?.join(','),
          // 处理是否分集（转换为boolean）
          multi_items: values.multi_items === 1,
        };
        
        // 只有在启用角标时才添加角标相关字段
        if (!values.corner_mark_enabled) {
           processedValues.corner_text = '';
           processedValues.corner_color = '';
        }
        
        // 移除不需要的字段
        delete processedValues.corner_mark_enabled;
        
        
        // 设置loading状态
        setMLoading(this, true);
        
        // 调用API
        api.editMedia(processedValues).then((res: any) => {
            message.success(this.props.record ? '编辑成功' : '添加成功');
            // 如果有onOk回调，调用它（用于刷新列表等）
            if (this.props.onOk) {
              this.props.onOk();
            }
            // 关闭抽屉并清空数据
            this.handleClose();
        }).catch((error: any) => {
          console.error('保存失败:', error);
        }).finally(() => {
          setMLoading(this, false);
        });
      }
    });
  };

  handleSeriesChange = (e: any) => {
    const isSeries = e.target.value;
    this.setState({ isSeries });

    // 清空相关字段
    if (isSeries === 1) {
      // 分集时清空总时长
      this.props.form.setFieldsValue({ duration: undefined });
    } else {
      // 不分集时清空总集数和更新状态
      this.props.form.setFieldsValue({
        item_total_count: undefined,
        finish: undefined,
      });
    }
  };

  handleStartTimeChange = (time: moment.Moment | null) => {
    this.setState({ startTime: time || undefined });
    // 如果开始时间改变，需要检查结束时间是否仍然有效
    if (time) {
      const endTime = this.props.form.getFieldValue('recommend_end_time');
      if (endTime && endTime.isBefore(time)) {
        // 如果结束时间早于新的开始时间，清空结束时间
        this.props.form.setFieldsValue({ recommend_end_time: undefined });
        message.warning('结束时间不能早于开始时间，已自动清空');
      }
    }
  };

  handleEndTimeChange = (time: moment.Moment | null) => {
    if (time && this.state.startTime && time.isBefore(this.state.startTime)) {
      // 先提示用户
      message.warning('结束时间不能早于开始时间，请重新选择');
      // 使用setTimeout确保消息显示后再清空
      setTimeout(() => {
        this.props.form.setFieldsValue({ recommend_end_time: undefined });
      }, 100);
    }
  };

  handleCornerMarkChange = (checked: boolean) => {
    this.setState({ showCornerMark: checked });
    if (!checked) {
      // 关闭时清空相关字段
      this.props.form.setFieldsValue({
        corner_text: undefined,
        corner_color: undefined,
      });
    }
  };

  // 处理关闭抽屉
  handleClose = () => {
    // 重置表单数据
    this.props.form.resetFields();
    
    // 重置组件状态
    this.setState({
      isSeries: 1, // 默认为分集
      startTime: undefined,
      showCornerMark: false,
    });
    
    // 调用父组件的关闭回调
    this.props.onClose();
  };

  render() {
    const { visible, record, onClose, form, skey } = this.props;
    const { getFieldDecorator } = form;
    const { isSeries, showCornerMark } = this.state;
    const isEdit = !!record?.id;

    const formItemLayout = {
      labelCol: { span: 6 },
      wrapperCol: { span: 16 },
    };

    return (
      <Drawer
        title={isEdit ? '编辑影视内容' : '添加影视内容'}
        visible={visible}
        onClose={this.handleClose}
        onOk={this.handleSubmit}
        skey={skey || Date.now()}
        width={800}
        maskClosable={false}
        okText="保存"
      >
        <Form {...formItemLayout}>
          {/* 数据来源 */}
          <Form.Item label="数据来源" required>
            {getFieldDecorator('source', {
              initialValue: record?.source !== undefined ? record.source : 0,
              rules: [{ required: true, message: '请选择数据来源' }],
            })(
              <Radio.Group>
                <Radio value={0}>潮新闻</Radio>
                <Radio value={1}>火把</Radio>
                <Radio value={2}>学习强国</Radio>
                <Radio value={3}>红果短剧</Radio>
              </Radio.Group>
            )}
          </Form.Item>

          {/* 1. 名称 */}
          <Form.Item label="名称" required>
            {getFieldDecorator('title', {
              initialValue: record?.title || '',
              rules: [{ required: true, message: '请输入名称', whitespace: true }],
            })(<Input placeholder="最多20字" maxLength={20} />)}
          </Form.Item>

          {/* 2. 竖封面图 */}
          <Form.Item
            label="竖封面图"
            required
            extra="支持jpg,jpeg,png,gif图片格式，比例3:4"
          >
            {getFieldDecorator('list_pic_h', {
              initialValue: record?.list_pic_h || '',
              rules: [{ required: true, message: '请上传竖封面图' }],
            })(
              <ImageUploader
                ratio={3 / 4}
                accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
              />
            )}
          </Form.Item>

          {/* 3. 横封面图 */}
          <Form.Item
            label="横封面图"
            required
            extra="支持jpg,jpeg,png,gif图片格式，比例16:9"
          >
            {getFieldDecorator('list_pic_w', {
              initialValue: record?.list_pic_w || '',
              rules: [{ required: true, message: '请上传横封面图' }],
            })(
              <ImageUploader
                ratio={16 / 9}
                accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
              />
            )}
          </Form.Item>

          {/* 4. 分类 */}
          <Form.Item label="分类" required>
            {getFieldDecorator('type', {
              initialValue: record?.type || undefined,
              rules: [{ required: true, message: '请选择分类' }],
            })(
              <Radio.Group>
                <Radio value={1}>电视剧</Radio>
                <Radio value={2}>电影</Radio>
                <Radio value={3}>短剧</Radio>
                <Radio value={4}>纪录片</Radio>
              </Radio.Group>
            )}
          </Form.Item>

          {/* 类型 - 多选 - 字段名改为theme_ids */}
          <Form.Item label="类型">
            {getFieldDecorator('themes', {
              initialValue: record?.themes ? record.themes.split(',') : [],
            })(
              <Checkbox.Group>
                <Checkbox value="1">爱情</Checkbox>
                <Checkbox value="2">战争</Checkbox>
                <Checkbox value="3">动作</Checkbox>
                <Checkbox value="4">都市</Checkbox>
                <Checkbox value="5">公益</Checkbox>
                <Checkbox value="6">家庭</Checkbox>
                <Checkbox value="7">金融</Checkbox>
                <Checkbox value="8">历史</Checkbox>
                <Checkbox value="9">励志</Checkbox>
                <Checkbox value="10">文化</Checkbox>
                <Checkbox value="11">喜剧</Checkbox>
                <Checkbox value="12">乡村</Checkbox>
                <Checkbox value="13">悬疑</Checkbox>
              </Checkbox.Group>
            )}
          </Form.Item>

          {/* 5. 是否分集 */}
          <Form.Item label="是否分集" required>
            {getFieldDecorator('multi_items', {
              initialValue: record?.multi_items !== undefined ? (record.multi_items ? 1 : 2) : 1,
              rules: [{ required: true }],
            })(
              <Radio.Group onChange={this.handleSeriesChange}>
                <Radio value={1}>是</Radio>
                <Radio value={2}>否</Radio>
              </Radio.Group>
            )}
          </Form.Item>

          {/* 6. 总时长（不分集时显示） */}
          {isSeries === 2 && (
            <Form.Item label="总时长">
              {getFieldDecorator('duration', {
                initialValue: record?.duration || undefined,
                rules: [{ required: true, message: '请输入总时长' }],
              })(
                <InputNumber
                  placeholder="单位分钟，最多9999"
                  min={1}
                  max={9999}
                  precision={0}
                  style={{ width: '100%' }}
                />
              )}
            </Form.Item>
          )}

          {/* 7. 总集数（分集时显示） - 字段名改为item_total_count */}
          {isSeries === 1 && (
            <Form.Item label="总集数">
              {getFieldDecorator('item_total_count', {
                initialValue: record?.item_total_count || undefined,
                rules: [{ required: true, message: '请输入总集数' }],
              })(
                <InputNumber
                  placeholder="最多9999集"
                  min={1}
                  max={9999}
                  precision={0}
                  style={{ width: '100%' }}
                />
              )}
            </Form.Item>
          )}

          {/* 8. 更新状态（分集时显示） */}
          {isSeries === 1 && (
            <Form.Item label="更新状态" required>
              {getFieldDecorator('finish', {
                initialValue: record?.finish !== undefined ? record.finish : true, // 默认已完结
                rules: [{ required: true }],
              })(
                <Radio.Group>
                  <Radio value={true}>已完结</Radio>
                  <Radio value={false}>更新中</Radio>
                </Radio.Group>
              )}
            </Form.Item>
          )}

          {/* 9. 剧情介绍 */}
          <Form.Item label="剧情介绍">
            {getFieldDecorator('summary', {
              initialValue: record?.summary || '',
            })(
              <Input.TextArea
                placeholder="选填，最多200字，支持换行"
                rows={4}
                maxLength={200}
                style={{ resize: 'none' }}
              />
            )}
          </Form.Item>

          {/* 10. 一句话介绍 */}
          <Form.Item label="一句话介绍">
            {getFieldDecorator('brief_summary', {
              initialValue: record?.brief_summary || '',
            })(<Input placeholder="选填，最多20字" maxLength={20} />)}
          </Form.Item>

          {/* 11. 推荐播放片段 */}
          {/* <Form.Item label="推荐播放片段">
            <div>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                <span style={{ marginRight: 8 }}>第</span>
                <Form.Item style={{ display: 'inline-block', margin: 0 }}>
                  {getFieldDecorator('recommend_number', {
                    initialValue: record?.recommend_number,
                  })(
                    <InputNumber
                      min={1}
                      max={9999}
                      precision={0}
                      style={{ width: 200 }}
                      placeholder="选填，输入数字，最多9999"
                    />
                  )}
                </Form.Item>
                <span style={{ marginLeft: 8, marginRight: 8 }}>个资源</span>
                <Tooltip
                  title={
                    <div>
                      <div>1、用于在列表自动播放等场景</div>
                      <div>2、资源不设置，默认第1个</div>
                      <div>开始时间不设置，默认从头开始</div>
                      <div>结束时间不设置，默认播放到最后</div>
                      <div>3、设置数据错误（资源或时间与实际不符），按默认规则处理</div>
                    </div>
                  }
                  placement="top"
                >
                  <Icon type="question-circle" />
                </Tooltip>
              </div>

              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ marginRight: 8 }}>选择时间</span>
                <div style={{ display: 'flex', flex: 1 }}>
                  <Form.Item style={{ flex: 1, margin: 0, marginRight: 8 }}>
                    {getFieldDecorator('recommend_start_time', {
                      initialValue: record?.recommend_start_time
                        ? moment(record.recommend_start_time, 'HH:mm:ss')
                        : undefined,
                    })(
                      <TimePicker
                        placeholder="开始时间"
                        style={{ width: '100%' }}
                        format="HH:mm:ss"
                        onChange={this.handleStartTimeChange}
                        defaultOpenValue={moment('00:00:00', 'HH:mm:ss')}
                      />
                    )}
                  </Form.Item>
                  <span style={{ margin: '0 8px', lineHeight: '32px' }}>至</span>
                  <Form.Item style={{ flex: 1, margin: 0, marginLeft: 8 }}>
                    {getFieldDecorator('recommend_end_time', {
                      initialValue: record?.recommend_end_time
                        ? moment(record.recommend_end_time, 'HH:mm:ss')
                        : undefined,
                    })(
                      <TimePicker
                        placeholder="结束时间"
                        style={{ width: '100%' }}
                        format="HH:mm:ss"
                        onChange={this.handleEndTimeChange}
                        defaultOpenValue={this.state.startTime || moment('00:00:00', 'HH:mm:ss')}
                      />
                    )}
                  </Form.Item>
                </div>
              </div>
            </div>
          </Form.Item> */}

          {/* 12. 自定义角标 */}
          <Form.Item label="自定义角标">
            {getFieldDecorator('corner_mark_enabled', {
              initialValue: record?.corner_text ? true : false,
              valuePropName: 'checked',
            })(<Switch onChange={this.handleCornerMarkChange} />)}
            <Tooltip title="在全部影视列表等页面显示" placement="top">
              <Icon type="question-circle" style={{ marginLeft: 8 }} />
            </Tooltip>
          </Form.Item>

          {/* 角标文字 */}
          {showCornerMark && (
            <Form.Item label="角标文字" required>
              {getFieldDecorator('corner_text', {
                initialValue: record?.corner_text || '',
                rules: [{ required: true, message: '请输入角标文字', whitespace: true }],
              })(<Input placeholder="最多5个字，如推荐、最新上架等" maxLength={5} />)}
            </Form.Item>
          )}

          {/* 角标颜色 */}
          {showCornerMark && (
            <Form.Item label="角标颜色" required>
              {getFieldDecorator('corner_color', {
                initialValue: record?.corner_color || CORNER_COLORS[0].value,
                rules: [{ required: true }],
              })(
                <Radio.Group>
                  {CORNER_COLORS.map((color) => (
                    <Radio key={color.value} value={color.value}>
                      <span
                        style={{
                          display: 'inline-block',
                          width: 16,
                          height: 16,
                          backgroundColor: color.value,
                          marginRight: 8,
                          verticalAlign: 'middle',
                          border: '1px solid #d9d9d9',
                        }}
                      />
                      {color.label}
                    </Radio>
                  ))}
                </Radio.Group>
              )}
            </Form.Item>
          )}
        </Form>
      </Drawer>
    );
  }
}

export default Form.create<AddFilmTvDrawerProps>()(AddFilmTvDrawer);
