import React, { Component } from 'react';
import { Form, Input, Button, Icon, message, Modal, Switch } from 'antd';
import { FormComponentProps } from 'antd/lib/form';
import { findApi as api } from '@app/api';
import { setMLoading } from '@app/utils/utils';
import connectSession from '@app/utils/connectSession';

const { TextArea } = Input;

interface BatchAddResourceModalProps extends FormComponentProps {
  visible: boolean;
  filmId?: string;
  filmName?: string;
  onClose: () => void;
  onOk?: (data: any) => void;
  skey: number;
}

interface BatchAddResourceModalState {}

@connectSession
class BatchAddResourceModal extends Component<
  BatchAddResourceModalProps,
  BatchAddResourceModalState
> {
  constructor(props: BatchAddResourceModalProps) {
    super(props);
    this.state = {};
  }

  handleSubmit = () => {
    const { form, onOk, filmId } = this.props;

    form.validateFields((err, values) => {
      if (!err) {
        // 处理批量资源数据
        const content = values.content;
        
        if (!content) {
          message.error('请输入资源内容');
          return;
        }

        // 构建请求参数，按照API文档格式
        const requestData = {
          content: content,
          show: values.show || false,
          media_id: parseInt(filmId || '0'), // 转换为数字类型
        };

        // 设置loading状态
        setMLoading(this, true);

        // 调用批量添加API
        api
          .batchAddMediaItems(requestData)
          .then((res: any) => {
            message.success('批量添加成功');
            
            // 关闭弹窗
            this.handleClose();
            
            // 如果有onOk回调，调用它（用于刷新列表等）
            if (onOk) {
              onOk(res);
            }
          })
          .catch((error: any) => {
            console.error('批量添加失败:', error);
          })
          .finally(() => {
            setMLoading(this, false);
          });
      }
    });
  };

  // 处理关闭弹窗
  handleClose = () => {
    const { form, onClose } = this.props;

    // 重置表单数据
    form.resetFields();

    // 调用父组件的onClose方法
    onClose();
  };

  render() {
    const { visible, form, filmName } = this.props;
    const { getFieldDecorator } = form;

    const formItemLayout = {
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
    };

    return (
      <Modal
        title="批量添加资源"
        visible={visible}
        onCancel={this.handleClose}
        onOk={this.handleSubmit}
        width={600}
        maskClosable={false}
        okText="确定"
        cancelText="取消"
        destroyOnClose={true}
        confirmLoading={(this.props as any).config?.mLoading}
      >
        <Form {...formItemLayout}>
          <Form.Item wrapperCol={{ span: 24 }}>
            {getFieldDecorator('content', {
              rules: [
                { required: true, message: '请输入资源内容', whitespace: true },
              ],
            })(
              <TextArea
                placeholder="批量添加影视资源，请联系技术操作"
                rows={6}
                style={{ fontSize: '14px', lineHeight: '1.5' }}
              />
            )}
          </Form.Item>

          <Form.Item label="资源添加后直接上架：">
            {getFieldDecorator('show', {
              initialValue: false,
              valuePropName: 'checked',
            })(
              <Switch />
            )}
            <span style={{ marginLeft: 8, color: '#999', fontSize: '12px' }}>
              不影响影视本身上下架状态
            </span>
          </Form.Item>
        </Form>
      </Modal>
    );
  }
}

export default Form.create<BatchAddResourceModalProps>()(BatchAddResourceModal); 