import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Form,
  Input,
  Button,
  Switch,
  message,
  Divider,
  Row,
  Col,
  Icon,
} from 'antd';
import { FormComponentProps } from 'antd/es/form';
import { Drawer } from '@app/components/common';
import ImageUploader from '@components/common/imageUploader';
import { findApi } from '@app/api';
import { useDispatch, useSelector } from 'react-redux';
import { setConfig } from '@app/action/config';

const { TextArea } = Input;

// 常量定义
const PROMOTION_TYPES = {
  XUEXI: 0, // 学习强国
  HUASHU: 1, // 华数TV
} as const;

const FORM_LIMITS = {
  TITLE_MAX_LENGTH: 20,
  RECOMMENDATION_MAX_LENGTH: 20,
  DURATION_MAX_LENGTH: 10,
  IMAGE_SIZE: 2048,
  IMAGE_RATIO: 2/1,
} as const;

const ACCEPTED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif'] as const;

// 样式常量
const STYLES = {
  section: { marginBottom: 24 },
  sectionTitle: { marginBottom: 16 },
  imageHint: { marginTop: 4, color: '#999', fontSize: 12 },
  form: { 
    paddingBottom: '20px', 
    width: '100%', 
    height: 'calc(100vh - 120px)', 
    overflowY: 'auto' as const 
  },
  footer: { 
    position: 'absolute' as const, 
    bottom: 0, 
    left: 0, 
    right: 0, 
    padding: '16px 24px', 
    borderTop: '1px solid #e8e8e8', 
    backgroundColor: '#fff',
    textAlign: 'right' as const,
  },
} as const;

// 表单布局配置
const FORM_LAYOUT = {
  labelCol: { span: 4 },
  wrapperCol: { span: 18 },
} as const;

// 专区配置
const ZONE_CONFIGS = [
  {
    type: PROMOTION_TYPES.XUEXI,
    name: 'xuexi',
    displayName: '学习强国',
    icon: 'book',
    fieldPrefix: 'xuexi',
  },
  {
    type: PROMOTION_TYPES.HUASHU,
    name: 'huashu',
    displayName: '华数TV',
    icon: 'play-circle',
    fieldPrefix: 'huashu',
  },
] as const;

// 静态表单验证规则（移到组件外部避免重复创建）
const VALIDATION_RULES = {
  title: [
    { required: true, message: '请输入标题' },
    { max: FORM_LIMITS.TITLE_MAX_LENGTH, message: `标题不能超过${FORM_LIMITS.TITLE_MAX_LENGTH}个字符` },
  ],
  image: [
    { required: true, message: '请选择图片' }
  ],
  recommendation: [
    { max: FORM_LIMITS.RECOMMENDATION_MAX_LENGTH, message: `推荐语不能超过${FORM_LIMITS.RECOMMENDATION_MAX_LENGTH}个字符` }
  ],
  duration: [
    { max: FORM_LIMITS.DURATION_MAX_LENGTH, message: `集数/时长不能超过${FORM_LIMITS.DURATION_MAX_LENGTH}个字符` }
  ],
} as const;

// 类型定义
interface PromotionItem {
  type: number; // 0: 学习强国, 1: 华数TV
  title: string;
  url: string;
  pic_url: string;
  brief_summary: string;
  duration: string;
  show: boolean; // 是否显示该专区
}

// 接口响应数据类型
interface PromotionDetailResponse {
  code: number;
  data: {
    list: PromotionItem[];
  };
}

interface FormFieldValues {
  xuexiTitle?: string;
  xuexiLink?: string;
  xuexiImage?: string;
  xuexiRecommendation?: string;
  xuexiDuration?: string;
  huashuTitle?: string;
  huashuLink?: string;
  huashuImage?: string;
  huashuRecommendation?: string;
  huashuDuration?: string;
}

interface InitialValues {
  xuexiEnabled?: boolean;  // 学习强国专区开关
  huashuEnabled?: boolean; // 华数TV专区开关
  list?: PromotionItem[];
}

interface ZoneDrawerProps extends FormComponentProps {
  visible: boolean;
  onClose: () => void;
  onSubmit?: (values: any) => void;
  title?: string;
  initialValues?: InitialValues;
  loading?: boolean;
}

interface ZoneFormSectionProps {
  config: typeof ZONE_CONFIGS[number];
  enabled: boolean;
  onSwitchChange: (checked: boolean) => void;
  onImageUpload: (url: string) => void;
  getFieldDecorator: any;
}

// 数据转换辅助函数
const convertListToFormData = (list: PromotionItem[]): FormFieldValues & { xuexiEnabled: boolean; huashuEnabled: boolean } => {
  const formData: FormFieldValues & { xuexiEnabled: boolean; huashuEnabled: boolean } = {
    xuexiEnabled: false,
    huashuEnabled: false,
  };
  
  list.forEach((item) => {
    if (item.type === PROMOTION_TYPES.XUEXI) {
      Object.assign(formData, {
        xuexiTitle: item.title,
        xuexiLink: item.url,
        xuexiImage: item.pic_url,
        xuexiRecommendation: item.brief_summary,
        xuexiDuration: item.duration,
        xuexiEnabled: item.show, // 直接使用接口返回的show字段值
      });
    } else if (item.type === PROMOTION_TYPES.HUASHU) {
      Object.assign(formData, {
        huashuTitle: item.title,
        huashuLink: item.url,
        huashuImage: item.pic_url,
        huashuRecommendation: item.brief_summary,
        huashuDuration: item.duration,
        huashuEnabled: item.show, // 直接使用接口返回的show字段值
      });
    }
  });
  
  return formData;
};

// 构建单个专区数据的辅助函数
const buildZoneData = (type: number, values: FormFieldValues, prefix: string, enabled: boolean) => ({
  type,
  title: (values as any)[`${prefix}Title`]?.trim() || '',
  url: (values as any)[`${prefix}Link`]?.trim() || '',
  pic_url: (values as any)[`${prefix}Image`] || '',
  brief_summary: (values as any)[`${prefix}Recommendation`]?.trim() || '',
  duration: (values as any)[`${prefix}Duration`]?.trim() || '',
  show: enabled,
});

// 可复用的表单字段组件
const ZoneFormSection: React.FC<ZoneFormSectionProps> = React.memo(({
  config,
  enabled,
  onSwitchChange,
  onImageUpload,
  getFieldDecorator,
}) => {
  const { fieldPrefix, displayName, icon } = config;
  
  // 根据开关状态动态设置校验规则
  const titleRules = useMemo(() => enabled ? VALIDATION_RULES.title : [], [enabled]);
  const imageRules = useMemo(() => enabled ? VALIDATION_RULES.image : [], [enabled]);
  const linkRules = useMemo(() => enabled ? [
    { required: true, message: '请输入链接' },
  ] : [], [enabled]);

  return (
    <div style={STYLES.section}>
      <h3 style={STYLES.sectionTitle}>
        <Icon type={icon} style={{ marginRight: 8 }} />
        {displayName}专区推荐影视
      </h3>
      
      <Form.Item label="功能入口">
        <Switch
          checked={enabled}
          onChange={onSwitchChange}
          checkedChildren="开启"
          unCheckedChildren="关闭"
        />
      </Form.Item>
      
      <Form.Item label="标题" required={enabled}>
        {getFieldDecorator(`${fieldPrefix}Title`, {
          rules: titleRules,
        })(
          <Input
            placeholder="最多20字"
            maxLength={FORM_LIMITS.TITLE_MAX_LENGTH}
          />
        )}
      </Form.Item>

      <Form.Item label="链接" required={enabled}>
        {getFieldDecorator(`${fieldPrefix}Link`, {
          rules: linkRules,
        })(
          <Input 
            placeholder="请输入跳转链接" 
          />
        )}
      </Form.Item>

      <Form.Item label="图片" required={enabled}>
        {getFieldDecorator(`${fieldPrefix}Image`, {
          rules: imageRules,
        })(
          <ImageUploader
            onChange={onImageUpload}
            ratio={FORM_LIMITS.IMAGE_RATIO}
            imgsize={FORM_LIMITS.IMAGE_SIZE}
            accept={[...ACCEPTED_IMAGE_TYPES]}
          />
        )}
        <div style={STYLES.imageHint}>
          支持jpg,jpeg,png,gif图片格式，比例为2:1
        </div>
      </Form.Item>

      <Form.Item label="推荐语">
        {getFieldDecorator(`${fieldPrefix}Recommendation`, {
          rules: VALIDATION_RULES.recommendation,
        })(
          <TextArea
            placeholder="选填，最多20字"
            maxLength={FORM_LIMITS.RECOMMENDATION_MAX_LENGTH}
            rows={3}
          />
        )}
      </Form.Item>

      <Form.Item label="集数/时长">
        {getFieldDecorator(`${fieldPrefix}Duration`, {
          rules: VALIDATION_RULES.duration,
        })(
          <Input
            placeholder="选填，最多10字，例如全12集，120分钟，更新至3集等"
            maxLength={FORM_LIMITS.DURATION_MAX_LENGTH}
          />
        )}
      </Form.Item>
    </div>
  );
});

ZoneFormSection.displayName = 'ZoneFormSection';

const ZoneDrawer: React.FC<ZoneDrawerProps> = ({
  visible,
  onClose,
  onSubmit,
  title = '强国/华数专区',
  initialValues = {},
  loading = false,
  form,
}) => {
  const { getFieldDecorator, validateFieldsAndScroll, resetFields, setFieldsValue } = form;
  const dispatch = useDispatch();
  const globalLoading = useSelector((state: any) => state.config?.mLoading || false);
  
  const [xuexiFunctionEnabled, setXuexiFunctionEnabled] = useState(false);
  const [huashuFunctionEnabled, setHuashuFunctionEnabled] = useState(false);
  const [initialized, setInitialized] = useState(false);

  // 获取推荐详情数据
  const fetchPromotionDetail = useCallback(async () => {
    try {
      dispatch(setConfig({ mLoading: true }));
      const response = await findApi.getPromotionDetail({}) as PromotionDetailResponse;
      
      if (response?.data?.list && Array.isArray(response.data.list)) {
        const formData = convertListToFormData(response.data.list);
        
        // 设置开关状态
        setXuexiFunctionEnabled(formData.xuexiEnabled);
        setHuashuFunctionEnabled(formData.huashuEnabled);
        
        // 回填表单数据
        setFieldsValue(formData);
      } else {
        // 如果没有数据，使用初始值
        setXuexiFunctionEnabled(initialValues.xuexiEnabled ?? false);
        setHuashuFunctionEnabled(initialValues.huashuEnabled ?? false);
        
        if (initialValues.list && initialValues.list.length > 0) {
          const formData = convertListToFormData(initialValues.list);
          setFieldsValue(formData);
        }
      }
    } catch (error) {
      console.error('获取推荐详情失败:', error);
      message.error('获取数据失败，请重试');
      
      // 发生错误时使用初始值
      setXuexiFunctionEnabled(initialValues.xuexiEnabled ?? false);
      setHuashuFunctionEnabled(initialValues.huashuEnabled ?? false);
      
      if (initialValues.list && initialValues.list.length > 0) {
        const formData = convertListToFormData(initialValues.list);
        setFieldsValue(formData);
      }
    } finally {
      dispatch(setConfig({ mLoading: false }));
    }
  }, [initialValues, setFieldsValue, dispatch]);

  useEffect(() => {
    if (visible && !initialized) {
      // 弹窗打开时获取数据
      fetchPromotionDetail();
      setInitialized(true);
    } else if (!visible && initialized) {
      // 组件关闭时重置初始化状态
      setInitialized(false);
    }
  }, [visible, initialized, fetchPromotionDetail]);

  // 构建提交数据
  const buildSubmitData = useCallback((values: FormFieldValues) => {
    const list: (PromotionItem & { show: boolean })[] = [];

    // 总是为学习强国专区传递数据
    list.push(buildZoneData(PROMOTION_TYPES.XUEXI, values, 'xuexi', xuexiFunctionEnabled));
    
    // 总是为华数TV专区传递数据
    list.push(buildZoneData(PROMOTION_TYPES.HUASHU, values, 'huashu', huashuFunctionEnabled));

    return {
      xuexiEnabled: xuexiFunctionEnabled,
      huashuEnabled: huashuFunctionEnabled,
      list,
    };
  }, [xuexiFunctionEnabled, huashuFunctionEnabled]);

  // 处理关闭
  const handleClose = useCallback(() => {
    // 重置表单字段
    resetFields();
    
    // 清空所有表单字段值
    setFieldsValue({
      xuexiTitle: undefined,
      xuexiLink: undefined,
      xuexiImage: undefined,
      xuexiRecommendation: undefined,
      xuexiDuration: undefined,
      huashuTitle: undefined,
      huashuLink: undefined,
      huashuImage: undefined,
      huashuRecommendation: undefined,
      huashuDuration: undefined,
    });
    
    // 重置开关状态
    setXuexiFunctionEnabled(false);
    setHuashuFunctionEnabled(false);
    
    // 重置初始化状态
    setInitialized(false);
    
    // 重置数据加载状态
    // setDataLoading(false); // This line is removed as per the edit hint.
    
    // 调用父组件的关闭回调
    onClose();
  }, [resetFields, setFieldsValue, onClose]);

  // 处理提交
  const handleSubmit = useCallback(async () => {
    // 构建需要校验的字段列表 - 只校验开启状态下的必填字段
    const fieldsToValidate: string[] = [];
    
    if (xuexiFunctionEnabled) {
      fieldsToValidate.push('xuexiTitle', 'xuexiLink', 'xuexiImage');
    }
    
    if (huashuFunctionEnabled) {
      fieldsToValidate.push('huashuTitle', 'huashuLink', 'huashuImage');
    }

    // 统一的保存函数
    const saveData = async (validatedValues: FormFieldValues = {}) => {
      // 获取所有表单字段的值，包括未校验的字段
      const allFieldNames = [
        'xuexiTitle', 'xuexiLink', 'xuexiImage', 'xuexiRecommendation', 'xuexiDuration',
        'huashuTitle', 'huashuLink', 'huashuImage', 'huashuRecommendation', 'huashuDuration'
      ];
      
      const allValues = form.getFieldsValue(allFieldNames);
      
      // 合并校验过的值和所有字段的值
      const finalValues = { ...allValues, ...validatedValues };
      
      const submitData = buildSubmitData(finalValues);
      
      try {
        dispatch(setConfig({ mLoading: true }));
        // 调用 editPromotion 接口保存数据
        await findApi.editPromotion({ list: submitData.list });
        message.success('保存成功');
        onSubmit?.(submitData);
        handleClose();
      } catch (error) {
        console.error('保存失败:', error);
        message.error('保存失败，请重试');
      } finally {
        dispatch(setConfig({ mLoading: false }));
      }
    };

    // 如果有需要校验的字段，则进行校验后保存
    if (fieldsToValidate.length > 0) {
      validateFieldsAndScroll(fieldsToValidate, async (err, values) => {
        if (!err) {
          await saveData(values);
        }
      });
    } else {
      // 没有需要校验的字段，直接保存所有数据
      await saveData();
    }
  }, [xuexiFunctionEnabled, huashuFunctionEnabled, onSubmit, validateFieldsAndScroll, buildSubmitData, handleClose, form, dispatch]);

  // 图片上传回调工厂函数
  const createImageUploadHandler = useCallback((fieldName: string) => (url: string) => {
    form.setFieldsValue({ [fieldName]: url });
  }, [form]);

  // 创建稳定的回调引用
  const handleXuexiSwitchChange = useCallback(setXuexiFunctionEnabled, []);
  const handleHuashuSwitchChange = useCallback(setHuashuFunctionEnabled, []);
  const handleXuexiImageUpload = useMemo(() => createImageUploadHandler('xuexiImage'), [createImageUploadHandler]);
  const handleHuashuImageUpload = useMemo(() => createImageUploadHandler('huashuImage'), [createImageUploadHandler]);

  return (
    <Drawer
      title={title}
      width={800}
      onClose={handleClose}
      onOk={handleSubmit}
      visible={visible}
      maskClosable={false}
      okText="保存"
      skey="zoneDrawer"
      config={{ mLoading: globalLoading }}
    >
      <Form {...FORM_LAYOUT} style={STYLES.form}>
        <ZoneFormSection
          config={ZONE_CONFIGS[0]}
          enabled={xuexiFunctionEnabled}
          onSwitchChange={handleXuexiSwitchChange}
          onImageUpload={handleXuexiImageUpload}
          getFieldDecorator={getFieldDecorator}
        />

        <Divider />

        <ZoneFormSection
          config={ZONE_CONFIGS[1]}
          enabled={huashuFunctionEnabled}
          onSwitchChange={handleHuashuSwitchChange}
          onImageUpload={handleHuashuImageUpload}
          getFieldDecorator={getFieldDecorator}
        />
      </Form>
    </Drawer>
  );
};

export default Form.create<ZoneDrawerProps>()(ZoneDrawer);

