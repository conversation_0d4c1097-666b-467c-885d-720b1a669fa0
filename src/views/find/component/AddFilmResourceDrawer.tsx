import React, { Component } from 'react';
import { Form, Input, Button, Icon, message, Modal } from 'antd';
import { FormComponentProps } from 'antd/lib/form';
import { ImageUploader, NewVideoUploader, Drawer } from '@app/components/common';
import { findApi as api } from '@app/api';
import { setMLoading } from '@app/utils/utils';
import connectSession from '@app/utils/connectSession';

interface AddFilmResourceDrawerProps extends FormComponentProps {
  visible: boolean;
  record?: any;
  filmId?: string;
  filmName?: string;
  recommendPositionDetail?: any;
  onClose: () => void;
  onOk?: () => void;
  skey: number;
}

interface AddFilmResourceDrawerState {}

@connectSession
class AddFilmResourceDrawer extends Component<
  AddFilmResourceDrawerProps,
  AddFilmResourceDrawerState
> {
  constructor(props: AddFilmResourceDrawerProps) {
    super(props);
    this.state = {};
  }

  handleSubmit = () => {
    const { form, onOk, filmId } = this.props;

    form.validateFields((err, values) => {
      if (!err) {
        // 处理数据
        const processedValues: any = {
          media_id: filmId, // 影视ID
          id: this.props.record?.id, // 编辑时传入id
          // 处理字符串字段的首尾空格
          title: values.title?.trim(),
          cover_url: values.cover_url || '',
          video_url: values.video_url,
        };

        // 移除undefined值
        Object.keys(processedValues).forEach((key) => {
          if (processedValues[key] === undefined) {
            delete processedValues[key];
          }
        });

        // 设置loading状态
        setMLoading(this, true);

        // 调用API
        api
          .editMediaItem(processedValues)
          .then((res: any) => {
            message.success(this.props.record ? '编辑成功' : '添加成功');
            // 关闭抽屉
            this.handleClose();
            // 如果有onOk回调，调用它（用于刷新列表等）
            if (this.props.onOk) {
              this.props.onOk();
            }
          })
          .catch((error: any) => {
            console.error('保存失败:', error);
          })
          .finally(() => {
            setMLoading(this, false);
          });
      }
    });
  };

  // 处理关闭抽屉
  handleClose = () => {
    const { form, onClose } = this.props;

    // 重置表单数据
    form.resetFields();

    // 调用父组件的onClose方法
    onClose();
  };

  // 处理视频上传成功
  handleVideoUploadSuccess = (videoUrl: string) => {
    const { form } = this.props;

    // 如果视频上传成功，自动生成封面图链接
    if (videoUrl) {
      // 设置封面图字段的值
      form.setFieldsValue({
        cover_url: videoUrl,
      });
    }
  };
  handleVideoReady = (dimensions: any) => {
    console.log('视频尺寸:', dimensions);
    // 获取到视频尺寸后，拼接到视频链接后
    if (dimensions && dimensions.width && dimensions.height) {
      const { form } = this.props;
      const currentVideoUrl = form.getFieldValue('video_url');

      if (currentVideoUrl) {
        // 移除现有的尺寸参数（如果存在）
        const baseUrl = currentVideoUrl.split('?')[0];
        // 拼接新的尺寸参数
        const newVideoUrl = `${baseUrl}?width=${dimensions.width}&height=${dimensions.height}`;

        // 更新表单中的视频链接
        form.setFieldsValue({
          video_url: newVideoUrl,
        });
      }
    }
  };
  render() {
    const { visible, record, onClose, form, skey, filmName, recommendPositionDetail } = this.props;
    const { getFieldDecorator } = form;
    const isEdit = !!record?.id;

    const formItemLayout = {
      labelCol: { span: 6 },
      wrapperCol: { span: 16 },
    };

    return (
      <Drawer
        title={isEdit ? '编辑影视资源' : '添加影视资源'}
        visible={visible}
        onClose={this.handleClose}
        onOk={this.handleSubmit}
        skey={skey || Date.now()}
        width={720}
        maskClosable={false}
        okText="保存"
      >
        {filmName && (
          <div
            style={{
              marginBottom: 20,
              padding: '10px 15px',
              backgroundColor: '#f0f2f5',
              borderRadius: 4,
            }}
          >
            <span style={{ color: '#666' }}>所属影视：</span>
            <span style={{ fontWeight: 'bold' }}>{filmName}</span>
          </div>
        )}

        <Form {...formItemLayout}>
          {/* 1. 名称 */}
          <Form.Item label="名称" required>
            {getFieldDecorator('title', {
              initialValue: record?.title || '',
              rules: [
                { required: true, message: '请输入名称', whitespace: true },
                { max: 10, message: '名称最多10个字' },
              ],
            })(<Input placeholder="仅用于后台查看，最多10个字" maxLength={10} />)}
          </Form.Item>

          {/* 3. 视频url */}
          <Form.Item label="上传视频" required extra="仅支持mp4视频格式，最大2GB">
            {getFieldDecorator('video_url', {
              initialValue: record?.video_url || '',
              rules: [{ required: true, message: '请上传视频' }],
            })(
              <NewVideoUploader
                maxSize={2 * 1024 * 1024 * 1024} // 2GB
                accept={['video/mp4']}
                onSuccess={this.handleVideoUploadSuccess}
                onVideoReady={this.handleVideoReady}
              />
            )}
          </Form.Item>
          {/* 2. 封面图 */}
          <Form.Item label="封面图" extra="支持jpg,jpeg,png图片格式，比例不限，推荐3:4或16:9">
            {getFieldDecorator('cover_url', {
              initialValue: record?.cover_url || '',
            })(
              <ImageUploader
                ratio={0} // 0表示不限制比例
                accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
                noCrop={true} // 不裁剪
              />
            )}
          </Form.Item>
        </Form>
      </Drawer>
    );
  }
}

export default Form.create<AddFilmResourceDrawerProps>()(AddFilmResourceDrawer);
