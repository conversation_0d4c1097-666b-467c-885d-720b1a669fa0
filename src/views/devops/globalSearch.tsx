import { setConfig } from '@action/config';
import { sysApi as api } from '@app/api';
import connect from '@utils/connectSession';
import { getCrumb, getDocTypeString, requirePerm } from '@utils/utils';
import { Button, Col, Table, Input, message, Row, InputNumber, Checkbox } from 'antd';
import React from 'react';
import Excel from 'exceljs';
import { withRouter } from 'react-router';
import moment from 'moment';

@(withRouter as any)
@connect
class BatchRevoke extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      scrollId: '',
      list: [],
      keyword: '',
      hasNext: false,
      size: 400,
    };
  }

  componentDidMount() {
    this.props.dispatch(
      setConfig({
        selectKeys: this.props.selectKeys,
        openKeys: this.props.openKeys,
      })
    );
  }

  setLoading = (loading: boolean) => {
    this.props.dispatch(setConfig({ loading }));
  };

  getData = (changePage = false) => {
    if (!this.state.keyword) {
      message.error('请输入要搜索的内容');
      return;
    }
    const body: any = {
      keyword: this.state.keyword,
      size: this.state.size,
      type: this.state.isRedboat ? 1 : 0,
    };
    if (this.state.scrollId && changePage) {
      body.scroll_id = this.state.scrollId;
    }
    this.setLoading(true);
    api
      .searchGlobalNews(body)
      .then((res: any) => {
        this.setState({
          list: res.data.list,
          scrollId: res.data.scroll_id,
          hasNext: !(res.data.list.length < this.state.size),
        });
        this.setLoading(false);
      })
      .catch(() => this.setLoading(false));
  };

  search = (value: string) => {
    this.setState({ keyword: value }, () => this.getData());
  };

  exportData = () => {
    const t = message.loading('正在导出');
    const wb = new Excel.Workbook();
    const name = moment().format('YYYY-MM-DD HH_mm_ss');
    const ws = wb.addWorksheet(name);
    ws.columns = [
      { header: 'ID', key: 'id' },
      { header: '标题', key: 'list_title' },
      { header: '原频道', key: 'channel_name' },
      { header: '推至频道', key: 'to_channel' },
      { header: '稿件类型', key: 'doc_type' },
      { header: 'URL', key: 'url' },
    ];
    ws.addRows(this.state.list?.map((item: any) => {
      return {
        ...item,
        to_channel: item.to_article_list?.map((item: any) => {
          return item.channel_name
        })?.join('、'),
        doc_type: getDocTypeString(item.doc_type)
      }
    }));
    wb.xlsx
      .writeBuffer()
      .then((a) => {
        t();
        const blob = new Blob([a], { endings: 'native' });
        const link = document.createElement('a');
        link.download = `${name}.xlsx`;
        link.href = URL.createObjectURL(blob);
        link.click();
        URL.revokeObjectURL(link.href);
      })
      .catch((e: any) => {
        t();
        message.error('导出异常');
        console.error(e);
      });

    // const file = new File(blob, name);
    // console.log(buffer);
  };

  sizeChange = (e: any) => {
    this.setState({
      size: e || 10,
    });
  };

  getColumns = () => {
    return [
      {
        title: 'id',
        dataIndex: 'id',
        width: 110,
      },
      {
        title: '标题',
        dataIndex: 'list_title',
      },
      {
        title: '原频道',
        dataIndex: 'channel_name',
        width: 100
      },
      {
        title: '推至频道',
        dataIndex: 'to_article_list',
        width: 150,
        render: (text: any) => {
          return text?.map((item: any) => {
            return item.channel_name
          })?.join('、')
        }
      },
      {
        title: '稿件类型',
        dataIndex: 'doc_type',
        width: 100,
        render: (text: any) => getDocTypeString(text)
      },
      {
        title: 'url',
        dataIndex: 'url',
        render: (text: string) => (
          <a href={text} target="_blank" rel="noreferrer">
            {text}
          </a>
        ),
      },
    ];
  };

  redboatChange = (e: any) => {
    this.setState({
      isRedboat: e.target.checked,
    });
  };

  render() {
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12} offset={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <Input.Search
              placeholder="输入关键词搜索"
              enterButton={true}
              onSearch={this.search}
              style={{ width: 300, marginRight: 16 }}
            />
            <Checkbox
              checked={this.state.isRedboat}
              onChange={this.redboatChange}
              style={{ marginRight: 16 }}
            >
              是否是ugc
            </Checkbox>
            单页数量：
            <InputNumber
              style={{ width: 80, marginRight: 16 }}
              value={this.state.size}
              onChange={this.sizeChange}
              min={10}
              max={3000}
            />
            <Button
              style={{ marginRight: 16 }}
              disabled={!this.state.hasNext}
              onClick={this.getData.bind(this, true)}
            >
              下一页
            </Button>
            <Button
              type="primary"
              disabled={this.state.list.length === 0}
              style={{ marginRight: 16 }}
              onClick={this.exportData}
            >
              导出本页数据
            </Button>
            当前页共计：{this.state.list.length} 条数据
          </Row>
          <Row>
            <Table
              dataSource={this.state.list}
              rowKey="id"
              columns={this.getColumns()}
              pagination={false}
            />
          </Row>
        </div>
      </>
    );
  }
}

export default BatchRevoke;
