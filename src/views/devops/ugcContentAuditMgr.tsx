import React from "react"
import { Row, Col, Form, Input, Radio, Button, message } from 'antd';
import { getCrumb } from '@utils/utils';
import { sysApi as api } from '@app/api';
import { setConfig } from '@app/action/config';
import connectSession from '@utils/connectSession';

@connectSession
@(Form.create({ name: 'UgcContentAuditMgr' }) as any)
class UgcContentAuditMgr extends React.Component<any, any>  {
  handleSubmit = () => {
    const { form: { validateFields, resetFields } } = this.props
    validateFields((err: any, values: any) => {
      if (!err) {
        this.props.dispatch(setConfig({ loading: true }));
        api.manuallyUgcStatus({ ...values }).then(() => {
          message.success('操作成功，页面自动刷新')
          resetFields()
          this.props.dispatch(setConfig({ loading: false }));
        }).catch(() => {
          this.props.dispatch(setConfig({ loading: false }));
        })
      }
    })
  }


  componentDidMount() {
    this.props.dispatch(setConfig({ openKeys: this.props.openKeys }));
    this.props.dispatch(
      setConfig({ selectKeys: this.props.selectKeys })
    );
  }

  render() {
    const { form: { getFieldDecorator } } = this.props
    return (
      <>
        <Row className="layout-infobar">
          <Col span={24} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Form>
            <Form.Item label="潮新闻ID" extra="请输入需要审核的内容潮新闻ID，单次只能处理1个" style={{ display: 'flex' }}>
              {getFieldDecorator('id', {
                rules: [
                  {
                    required: true,
                    message: '请输入需要审核的内容潮新闻ID，单次只能处理1个',
                  },
                  {
                    pattern: /^\d+$/,
                    message: '请输入纯数字',
                  }
                ],
              })(<Input style={{ width: 300 }} />)}
            </Form.Item>
            <Form.Item label="审核结果" style={{ display: 'flex' }}>
              {getFieldDecorator('status', {
                rules: [
                  {
                    required: true,
                    message: '请选择审核结果',
                  },
                ],
              })(
                <Radio.Group>
                  <Radio value={4}>审核通过</Radio>
                  <Radio value={41}>沉底通过</Radio>
                  <Radio value={42}>审核通过并推荐</Radio>
                  <Radio value={5}>审核不通过</Radio>
                  {/* <Radio value={7}>用户删除</Radio> */}
                  <Radio value={8}>审核不通过并删除</Radio>
                  <Radio value={3}>恢复待审状态</Radio>
                </Radio.Group>
              )}
            </Form.Item>
          </Form>
          <Button type="primary" style={{ width: 130 }} onClick={this.handleSubmit}>确定</Button>
        </div>
      </>
    )
  }
}

export default UgcContentAuditMgr