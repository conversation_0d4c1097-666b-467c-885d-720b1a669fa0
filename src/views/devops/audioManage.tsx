import { setConfig } from '@action/config';
import { sysApi as api } from '@app/api';
import connect from '@utils/connectSession';
import { getCrumb, requirePerm } from '@utils/utils';
import { Button, Col, Form, Input, message, Row, Switch, Checkbox } from 'antd';
import React from 'react';
import { withRouter } from 'react-router';

@(withRouter as any)
@connect
class BatchRevoke extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      data: '',
      isRedboat: false,
    };
  }

  componentDidMount() {
    this.props.dispatch(
      setConfig({
        selectKeys: this.props.selectKeys,
        openKeys: this.props.openKeys,
      })
    );
  }

  setLoading = (loading: boolean) => {
    this.props.dispatch(setConfig({ loading }));
  };

  redboatChange = (e: any) => {
    this.setState({
      isRedboat: e.target.checked,
    });
  };

  inputChange = (e: any) => {
    this.setState({
      data: e.target.value,
    });
  };

  submit = () => {
    const regex = /^([0-9]+,)*[0-9]+$/;
    if (!regex.test(this.state.data)) {
      message.error('请正确输入稿件ID');
      return;
    }
    api.batchUpdateAudio({ ids: this.state.data, type: this.state.isRedboat ? 1 : 0 });
    message.success('已成功');
    this.setState({
      data: '',
    });
  };

  render() {
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12} offset={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Input.TextArea
            value={this.state.data}
            onChange={this.inputChange}
            placeholder="请输入需要更新的稿件ID，以英文逗号“,”分隔，末尾请不要携带逗号，例：1111111,2222222"
            style={{ width: 400 }}
            rows={15}
          />
          <Row style={{ marginTop: 16 }}>
            <Checkbox checked={this.state.isRedboat} onChange={this.redboatChange}>
              是否潮鸣号稿件
            </Checkbox>
          </Row>
          <Row style={{ marginTop: 16 }}>
            {requirePerm(
              this,
              'channel_article:batch_cancel_release'
            )(
              <Button type="primary" onClick={this.submit}>
                批量生成音频
              </Button>
            )}
          </Row>
        </div>
      </>
    );
  }
}

export default BatchRevoke;
