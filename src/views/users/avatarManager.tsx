import { getTableList } from '@action/tableList';
import { userApi as api } from '@app/api';
import { A, ImageUploader, Table } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setLoading, setMenu } from '@utils/utils.tsx';
import { Button, Checkbox, Col, Form, Icon, message, Modal, Row, Switch } from 'antd';
import React from 'react';
import { withRouter } from 'react-router';

@(withRouter as any)
@connect
class AvatarManager extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      image_url: '',
      visible: false,
      loading: false,
      id: '',
      default_flag: false,
      allowCancelDefault: false,
      key: Date.now(),
      customAvatarSwitch: false,
    };
  }

  componentDidMount() {
    this.getData();
    setMenu(this);
  }

  componentDidUpdate(prevProps: any) {
    if (
      prevProps.tableList.timestamp !== this.props.tableList.timestamp &&
      this.props.tableList.allData.portrait_list
    ) {
      this.setState({
        customAvatarSwitch: this.props.tableList.allData.custom_portrait_switch,
      });
    }
  }

  getData = () => {
    this.props.dispatch(getTableList('getAvatarList', 'portrait_list', {}));
  };

  getColumns = () => {
    const { total } = this.props.tableList;
    return [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) => (
          <span>
            {requirePerm(
              this,
              'portrait:sort_update'
            )(
              <A disabled={i === 0} className="sort-up" onClick={() => this.order(record, '0')}>
                <Icon type="up-circle" theme="filled" />
              </A>
            )}{' '}
            {requirePerm(
              this,
              'portrait:sort_update'
            )(
              <A
                disabled={i === total - 1}
                className="sort-down"
                onClick={() => this.order(record, '1')}
              >
                <Icon type="down-circle" theme="filled" />
              </A>
            )}
          </span>
        ),
        width: 80,
      },
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{i + 1}</span>,
        width: 80,
      },
      // {
      //   title: ' ',
      //   key: 'default',
      //   dataIndex: 'default_flag',
      //   render: (text: boolean) => <span>{text ? '默认头像' : ''}</span>,
      //   width: 80,
      // },
      {
        title: '头像',
        key: 'avatar',
        dataIndex: 'image_url',
        render: (text: any) => <img src={text} className="list-pic" />,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) =>
          requirePerm(this, 'portrait:update')(<A onClick={() => this.editRecord(record)}>编辑</A>),
        width: 80,
      },
    ];
  };

  order = (record: any, sortType: string) => {
    setLoading(this, true);
    api
      .sortAvatar({ id: record.id, sort_type: sortType })
      .then(() => {
        message.success('操作成功');
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  updateSwitch = (flag: boolean) => {
    setLoading(this, true);
    api
      .updateCustomAvatarSwitch({ flag })
      .then(() => {
        message.success('操作成功');
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  editRecord = (record: any = {}) => {
    this.setState({
      visible: true,
      key: Date.now(),
      id: record.id || '',
      image_url: record.image_url || '',
      default_flag: record.default_flag || false,
      allowCancelDefault: !record.default_flag,
    });
  };

  handleSubmit = () => {
    const { image_url, default_flag, id } = this.state;
    if (image_url === '') {
      message.error('请上传头像');
      return;
    }
    let func = 'createAvatar';
    const body: any = { image_url };
    if (id) {
      body.id = id;
      // body.defaulted = default_flag;
      body.defaulted = false;
      func = 'updateAvatar';
    }
    this.setState({ loading: true });
    api[func as keyof typeof api](body)
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setState({ loading: false, visible: false });
      })
      .catch(() => this.setState({ loading: false }));
  };

  render() {
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const { image_url, visible, loading, key, customAvatarSwitch, id } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            {requirePerm(
              this,
              'portrait:create'
            )(
              <Button onClick={() => this.editRecord({})} style={{ marginRight: 8 }}>
                <Icon type="plus-circle" /> 添加头像
              </Button>
            )}
            用户自定义上传&nbsp;
            {requirePerm(
              this,
              'portrait:custom_portrait_switch'
            )(<Switch checked={customAvatarSwitch} onChange={this.updateSwitch} />)}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getAvatarList"
            index="portrait_list"
            rowKey="id"
            filter={{}}
            columns={this.getColumns()}
            pagination={false}
          />
          <Modal
            visible={visible}
            key={key}
            title={id ? '编辑头像' : '添加头像'}
            confirmLoading={loading}
            onCancel={() => this.setState({ visible: false })}
            onOk={this.handleSubmit}
          >
            <Form {...formLayout}>
              <Form.Item required={true} label="头像">
                <ImageUploader
                  ratio={1 / 1}
                  value={image_url}
                  onChange={(v: any) => this.setState({ image_url: v })}
                />
              </Form.Item>
              {/* {id && (
                <Form.Item label="默认头像">
                  <Checkbox
                    disabled={!allowCancelDefault}
                    checked={default_flag}
                    onChange={(e: any) => this.setState({ default_flag: e.target.checked })}
                  />
                </Form.Item>
              )} */}
            </Form>
          </Modal>
        </div>
      </>
    );
  }
}

export default AvatarManager;
