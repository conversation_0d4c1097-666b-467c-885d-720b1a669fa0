/* eslint-disable no-return-assign */
import { getTableList } from '@action/tableList';
import { userApi as api } from '@app/api';
import { CommonObject } from '@app/types';
import CreditForm from '@components/business/batchCreditForm';
import { A, Drawer, Table } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setLoading, setMenu } from '@utils/utils.tsx';
import { Button, Col, Form, Icon, Modal, Row } from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';

@(withRouter as any)
@connect
class BatchCredits extends React.Component<any, any> {
  formRef: any;

  constructor(props: any) {
    super(props);
    this.state = {
      loading: false,
      visible: false,
      key: Date.now(),
      detail: {
        visible: false,
        key: Date.now(),
      },
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    setMenu(this);
  }

  getData = (overlap: CommonObject = {}) => {
    const { current, size } = this.props.tableList;
    this.props.dispatch(
      getTableList('getBatchCreditList', 'batch_score_list', { current, size, ...overlap })
    );
  };

  getColumns = () => {
    return [
      {
        title: '积分变动',
        dataIndex: 'score',
        key: 'score',
        render: (text: any) => <span>{text > 0 ? `+${text}` : text}</span>,
        width: 90,
      },
      {
        title: '操作时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 180,
      },
      {
        title: '操作人',
        key: 'created_by',
        dataIndex: 'created_by',
        width: 120,
      },
      {
        title: '系统通知',
        key: 'message',
        dataIndex: 'message',
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'status',
        render: (text: any) => <span>{['成功', '失败', '部分成功'][text]}</span>,
        width: 80,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) =>
          requirePerm(
            this,
            'batch_score:detail'
          )(<A onClick={() => this.getDetail(record)}>查看详情</A>),
        width: 80,
      },
    ];
  };

  getDetail = (record: any) => {
    setLoading(this, true);
    api
      .getBatchCreditDetail({ id: record.id })
      .then((r: any) => {
        this.setState({
          detail: {
            visible: true,
            key: Date.now(),
            ...r.data.batch_score,
          },
        });
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  addRecord = () => {
    this.setState({
      visible: true,
      key: Date.now(),
    });
  };

  setLoading = (loading: boolean) => {
    this.setState({ loading });
  };

  submitEnd = () => {
    this.getData();
    this.setState({ loading: false, visible: false });
  };

  render() {
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const { visible, key, loading, detail } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            <Button onClick={() => this.props.history.push('/view/scorerule')}>
              <Icon type="left-circle" /> 返回积分规则
            </Button>
            {requirePerm(
              this,
              'batch_score:create'
            )(
              <Button onClick={this.addRecord} style={{ marginLeft: 8 }}>
                <Icon type="plus-circle" /> 批量增减积分
              </Button>
            )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getBatchCreditList"
            index="batch_score_list"
            rowKey="id"
            filter={{}}
            columns={this.getColumns()}
            pagination={true}
          />
          <Modal
            title="查看积分详情"
            visible={detail.visible}
            key={detail.key}
            width={600}
            onCancel={() => this.setState({ detail: { ...detail, visible: false } })}
          >
            <Form {...formLayout}>
              <Form.Item label="通知消息">{detail.message}</Form.Item>
              <Form.Item label="状态">{['成功', '失败', '部分成功'][detail.status]}</Form.Item>
              <Form.Item label="增减积分">{detail.score}</Form.Item>
              {detail.type === 0 && (
                <>
                  <Form.Item label="积分成功用户">{detail.success_accounts}</Form.Item>
                  <Form.Item label="积分失败用户">{detail.fail_accounts}</Form.Item>
                </>
              )}
              {detail.type === 1 && (
                <>
                  <Form.Item label="积分成功用户">
                    {detail.success_accounts.split(',').map((v: any) => (
                      <p>
                        <a href={v}>{v}</a>
                      </p>
                    ))}
                  </Form.Item>
                  <Form.Item label="积分失败用户">
                    {detail.fail_accounts.split(',').map((v: any) => (
                      <p>
                        <a href={v}>{v}</a>
                      </p>
                    ))}
                  </Form.Item>
                </>
              )}
            </Form>
          </Modal>
          <Drawer
            title="批量增减积分"
            visible={visible}
            skey={key}
            onClose={() => this.setState({ visible: false })}
            onOk={() => this.formRef.doSubmit()}
          >
            <CreditForm
              onEnd={this.submitEnd}
              wrappedComponentRef={(ref: any) => (this.formRef = ref)}
            />
          </Drawer>
        </div>
      </>
    );
  }
}

export default BatchCredits;
