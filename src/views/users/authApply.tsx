import React, { useEffect, useState } from "react";
import { Button, Col, Icon, Input, Modal, Row, Select } from "antd";
import {getCrumb, UserDetail} from "@app/utils/utils"
import { useHistory } from "react-router";
import { getTableList } from '@app/action/tableList';
import Radio from "antd/es/radio";
import { Table } from '@components/common';
import { useDispatch, useSelector } from "react-redux";
import AuthApplyInfoDrawer from "./authApplyInfoDrawer";
import AuthApplyTemplateModal from "./authApplyTemplateModal";
import { userApi } from "@app/api";
import moment from "moment";
import { setConfig } from "@app/action/config";

export default function AuthApply(props: any) {
  const dispatch = useDispatch();
  const history = useHistory()
  const { current, size } = useSelector((state: any) => state.tableList)
  const [filter, setFilter] = useState({
    audit_status: 0,
    main_type: '',
    search_type: 0,
    keyword: ''
  })
  const [searchState, setSearchState] = useState({
    search_type: 0,
    keyword: ''
  })
  const [authInfoDrawer, setAuthInfoDrawer] = useState({
    visible: false,
    record: null
  })
  const [user, setUser] = useState({
    key: Date.now(),
    visible: false,
    detail: {},
  })

  const showUserDetail = (record: any) => {
    dispatch(setConfig({ loading: true }))
    userApi
      .getUserDetail({ accountId: record.account_id })
      .then((r: any) => {
        dispatch(setConfig({ loading: false }))
        setUser({
          visible: true,
          key: Date.now(),
          detail: r.data.account,
        })
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }))
      });
  }

  const showAuthInfoDrawer = (visible: boolean, record: any) => {
    setAuthInfoDrawer({
      visible,
      record
    })
  }

  const getColumns = () => {
    const columns: any = [
      {
        title: '用户id',
        dataIndex: 'account_id',
        width: 250,
      },
      {
        title: '小潮号',
        key: 'chao_id',
        dataIndex: 'chao_id',
        width: 110,
      },
      {
        title: '用户头像',
        key: 'current_portrait_url',
        dataIndex: 'current_portrait_url',
        render: (text: string) => <img src={text} className="list-pic" alt="" />,
        width: 100,
      },
      {
        title: '用户昵称',
        dataIndex: 'current_nick_name',
        render(text: string, record: any) {
          return <a onClick={() => showUserDetail(record)}>{text}</a>
        }
      },
      {
        title: '账号主体',
        dataIndex: 'main_type',
        render(main_type: number) {
          return main_type === 0 ? '个人' : (main_type === 1 ? '组织/机构' : '')
        }
      },
      {
        title: '绑定手机号',
        dataIndex: 'phone_number',
      },
      {
        title: '提交时间',
        dataIndex: 'created_at',
        render: (text: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      },
    ]

    if (filter.audit_status > 0) {
      columns.push({
        title: '操作时间',
        dataIndex: 'updated_at',
        render: (text: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      }, {
        title: '操作人',
        dataIndex: 'updated_by',
      },)
    }

    columns.push({
      title: '操作',
      key: 'op',
      align: 'center',
      width: 90,
      render: (_: string, record: any) => <a onClick={() => showAuthInfoDrawer(true, record)}>查看详情</a>
    })
    return columns
  }



  const changeFilter = (key: string, val: any, goToFirstPage = false) => {
    const newFilter = {
      ...filter,
      [key]: val
    }
    setFilter(newFilter)
    getData(goToFirstPage, newFilter)
  }

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      const newFilter = {
        ...filter,
        ...searchState
      }
      setFilter(newFilter)
      getData(true, newFilter)
    }
  };

  const getData = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current
    const params: any = { current: cur, size, ...newFilter }
    if (!params.keyword) {
      delete params.keyword
      delete params.search_type
    }
    dispatch(getTableList('getAuditList', 'account_list', params));
  }

  useEffect(() => {
    getData()
  }, [])
  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button onClick={() => history.push('/view/orgAccountList')}><Icon type="left-circle" />返回潮鸣号管理</Button>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={16}>
            <Radio.Group buttonStyle="solid"
              value={filter.audit_status}
              onChange={(e) => changeFilter('audit_status', e.target.value, true)}>
              <Radio.Button value={0}>待审核</Radio.Button>
              <Radio.Button value={1}>已通过</Radio.Button>
              <Radio.Button value={2}>不通过</Radio.Button>
            </Radio.Group>
            <Select style={{ width: 110, marginLeft: 8 }}
              value={filter.main_type}
              onChange={(value) => changeFilter('main_type', value)}>
              <Select.Option value="">账号主体</Select.Option>
              <Select.Option value={0}>个人</Select.Option>
              <Select.Option value={1}>组织/机构</Select.Option>
            </Select>
          </Col>
          <Col span={8} style={{ textAlign: 'right' }}>
            <Select
              value={searchState.search_type}
              style={{ width: 94, marginRight: 8 }}
              onChange={(search_type) => setSearchState({ ...searchState, search_type })}
            >
              <Select.Option value={0}>用户昵称</Select.Option>
              <Select.Option value={1}>用户id</Select.Option>
              <Select.Option value={2}>小潮号</Select.Option>
            </Select>
            <Input
              value={searchState.keyword}
              style={{ marginRight: 8, width: 140 }}
              onChange={(e: any) => setSearchState({ ...searchState, keyword: e.target.value })}
              onKeyPress={handleKey}
              placeholder="请输入搜索内容"
            />
            <Button onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" /> 搜索
            </Button>
          </Col>
        </Row>
        <Table
          func="getAuditList"
          index="account_list"
          filter={filter}
          pagination={true}
          rowKey="id"
          columns={getColumns()}
        />
        <Modal
          visible={user.visible}
          key={user.key}
          title="用户详情"
          width={800}
          onCancel={() => setUser({ ...user, visible: false })}
          onOk={() => setUser({ ...user, visible: false })}
        >
          <UserDetail detail={user.detail}/>
          {/*{getUserDetail(user.detail)}*/}
        </Modal>
        <AuthApplyInfoDrawer
          visible={authInfoDrawer.visible}
          record={authInfoDrawer.record}
          onClose={() => showAuthInfoDrawer(false, authInfoDrawer.record)}
          onEnd={() => getData()} />

        <AuthApplyTemplateModal />
      </div>
    </>
  )
}