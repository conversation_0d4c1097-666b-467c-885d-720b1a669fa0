/* eslint-disable react/prop-types */
import { userApi as api } from '@app/api';
import { getTableList } from '@app/action/tableList';
import { AppState } from '@app/utils/configureStore';
import { getCrumb, previewPic, setMenuHook } from '@app/utils/utils';
import {
  Button,
  Col,
  Divider,
  Icon,
  Input,
  message,
  Modal,
  Radio,
  Row,
  Select,
  Table as AntdTable,
} from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector, useStore } from 'react-redux';
import useXHR from '@utils/useXhr';
import { PermA, PermButton, PermSwitch } from '@app/components/permItems';
import { Table as CustomTable } from '@app/components/common';
import moment from 'moment';

interface DataSourceItem {
  id: number;
  nick_name: string;
  success: boolean;
  message?: string;
}
export default function UserImageAudit(props: any) {
  const [search, setSearch] = useState(() => ({
    search_type: '3',
    keyword: '',
    status: 0,
    audit_by: -1,
    category: 0,
  }));
  const store = useStore();
  const [majorStatus, setMajor] = useState(1);

  const [switches, setSwitches] = useState(() => [false, false]);
  const [listSelectedKeys, setListSelectedKeys] = useState([]);
  const handleSearchChange = (key: any, value: any) => {
    setSearch((s) => ({
      ...s,
      [key]: value,
    }));
  };
  const [input, setInput] = useState({
    search_type: '1',
    keyword: '',
  });
  const { current, size } = useSelector((state: AppState) => state.tableList);
  const dispatch = useDispatch();

  const { run, loading } = useXHR();
  const { run: run0, loading: loading0 } = useXHR();
  const { run: run1, loading: loading1 } = useXHR();

  useEffect(() => {
    setListSelectedKeys([]);
  }, [current]);
  const majorChange = (status: 0 | 1) => {
    if (status === 1) {
      setSearch((s) => ({
        ...s,
        audit_by: -1,
        status: 0,
      }));
    } else {
      handleSearchChange('status', -1);
    }
    setMajor(status);
  };

  const filter = useMemo(() => {
    const f: any = { ...search, type: props.pageType };
    if (search.audit_by === -1) {
      delete f.audit_by;
    }
    if (search.keyword === '') {
      delete f.keyword;
      delete f.search_type;
    }
    return f;
  }, [search]);

  const getList = (overlap: any = {}) => {
    setListSelectedKeys([]);
    dispatch(
      getTableList('getUserImageAuditList', 'account_image_audit', {
        current,
        size,
        ...filter,
        ...overlap,
      })
    );
  };

  const getSwitchData = (type: 0 | 1) => {
    const feature = props.featureCode[type];
    const runner = type === 0 ? run0 : run1;
    runner(api.getUserImageAuditSwitch, { feature }).then((res: any) => {
      setSwitches((s) => {
        const state = [...s];
        state[type] = res.data.switch;
        return state;
      });
    });
  };

  const auditImage = (record: any, status: 1 | 2) => {
    run(api.auditUserImage, { id: record.id, status }, true).then(() => {
      message.success('操作成功');
      getList();
    });
  };
  const getSeq = useCallback((i: number) => (current - 1) * size + i + 1, [current, size]);
  const columns = useMemo(() => {
    const cs: any = [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => getSeq(i),
        width: 70,
      },
      {
        title: '用户ID',
        dataIndex: 'account_id',
        width: 220,
      },
      {
        title: '小潮号',
        key: 'chao_id',
        dataIndex: 'chao_id',
        width: 120,
      },
      {
        title: '用户类型',
        dataIndex: 'category',
        render: (text: number) => ['', '潮客', '潮鸣号'][text],
        width: 80,
      },
      {
        title: '用户昵称',
        dataIndex: 'nick_name',
        width: 140,
      },
      {
        title: ['头像', '背景图', '审核昵称', '用户简介'][props.pageType],
        dataIndex: props.pageType >= 2 ? 'content' : 'image_url',
        render: (text: string) =>
          props.pageType >= 2 ? (
            <span>{text}</span>
          ) : (
            <img src={text} alt="用户图片" className="list-pic" onClick={() => previewPic(text)} />
          ),
      },
    ];
    const opColumns: any = [
      {
        title: '提交时间',
        dataIndex: 'created_at',
        render: (text: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
        width: 95,
      },
      {
        title: '操作',
        key: 'op',
        render: (record: any) => (
          <>
            <PermA
              perm={`account_image_audit:${props.pageType}:pass`}
              onClick={() => auditImage(record, 1)}
            >
              通过
            </PermA>
            <Divider type="vertical" />
            <PermA
              perm={`account_image_audit:${props.pageType}:pass`}
              onClick={() => auditImage(record, 2)}
            >
              拒绝
            </PermA>
          </>
        ),
        width: 95,
      },
    ];
    const passColumns: any = [
      {
        title: '状态',
        dataIndex: 'status',
        render: (text: number) => ['', '通过', '拒绝'][text],
        width: 70,
      },
      {
        title: '提交时间',
        dataIndex: 'created_at',
        render: (text: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
        width: 95,
      },
      {
        title: '审核时间',
        dataIndex: 'audit_at',
        render: (text: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
        width: 95,
      },
      {
        title: '审核人',
        dataIndex: 'audit_by',
        render: (text: any) => text || '机器审核',
        width: 110,
      },
    ];
    if (majorStatus === 0) {
      return cs.concat(passColumns);
    }
    return cs.concat(opColumns);
  }, [majorStatus]);

  useEffect(() => {
    getList({ current: 1 });
    setMenuHook(dispatch, props);
  }, [filter]);

  useEffect(() => {
    getSwitchData(0);
    getSwitchData(1);
  }, []);

  const switchChange = (type: 0 | 1, checked: boolean) => {
    const feature = props.featureCode[type];
    const runner = type === 0 ? run0 : run1;
    runner(api.updateUserImageAuditSwitch, { feature, status: checked }).then(() => {
      message.success('操作成功');
      getSwitchData(type);
    });
  };

  const doSearch = () => {
    setSearch((s) => ({
      ...s,
      ...input,
    }));
  };
  const handleListSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    setListSelectedKeys(selectedRowKeys);
  };
  const batchPass = (status: number) => {
    const openModal = (dataSource: DataSourceItem[]) => {
      let modalColumns = [
        {
          title: `待审核${['', '背景图', '名称', '简介'][props.pageType]}`,
          dataIndex: ['', 'url', 'nick_name', 'content'][props.pageType],
          key: ['', 'url', 'nick_name', 'content'][props.pageType],
          render: (text: any, record: any) => {
            return [
              '',
              <img
                src={text}
                alt="用户图片"
                className="list-pic"
                onClick={() => previewPic(text)}
              />,
              <span>{text}</span>,
              <p
                title={text}
                style={{
                  width: 200,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {text}
              </p>,
            ][props.pageType];
          },
        },
        {
          title: '处理结果',
          dataIndex: 'success',
          render: (text: any, record: any) => (
            <>{text ? '成功' : <span style={{ color: 'red' }}>失败</span>}</>
          ),
          key: 'success',
        },
        {
          title: '原因',
          dataIndex: 'message',
          render: (text: any, record: any) => (
            <>
              {' '}
              <span style={{ color: 'red' }}>{text}</span>
            </>
          ),
          key: 'message',
        },
      ];

      Modal.confirm({
        title: status === 1 ? '批量通过' : '批量拒绝',
        icon: null,
        width: 500,
        content: (
          <div>
            <p>
              {dataSource.filter((item) => item.success === true).length}条成功，
              {dataSource.filter((item) => item.success === false).length}条失败
            </p>
            <AntdTable
              rowKey={'id'}
              dataSource={dataSource}
              columns={modalColumns}
              pagination={false}
            />
          </div>
        ),
        onOk: () => {
          getList();
        },
        onCancel() {
          getList();
        },
      });
    };

    Modal.confirm({
      title: null,
      icon: null,
      content: `已选中${listSelectedKeys.length}条待审数据，确定批量${
        status === 1 ? '通过' : '拒绝'
      }？`,
      onOk: () => {
        api.auditUserImage({ id: listSelectedKeys.toString(), status }).then((res: any) => {
          const { audit_list } = res.data;
          if (audit_list.some((ele: DataSourceItem) => ele.success === false)) {
            openModal(audit_list);
          } else {
            getList();
            message.success('操作成功');
          }
        });
      },
    });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={18}>
          潮客人工审核{' '}
          <PermSwitch
            checked={switches[0]}
            loading={loading0}
            perm={`web_feature:${props.featureCode[0]}`}
            onChange={(checked) => switchChange(0, checked)}
            style={{ marginRight: 8 }}
          />
          潮鸣号人工审核{' '}
          <PermSwitch
            checked={switches[1]}
            loading={loading1}
            perm={`web_feature:${props.featureCode[1]}`}
            onChange={(checked) => switchChange(1, checked)}
          />
        </Col>
        <Col span={6} className="layout-breadcrumb" style={{ marginTop: 0 }}>
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={14}>
            <Radio.Group
              value={majorStatus}
              onChange={(e) => majorChange(e.target.value)}
              style={{ marginRight: 8 }}
              buttonStyle="solid"
            >
              <Radio.Button value={1}>待审核</Radio.Button>
              <Radio.Button value={0}>已审核</Radio.Button>
            </Radio.Group>
            <Select
              value={search.category}
              onChange={(value: any) => handleSearchChange('category', value)}
              style={{ width: 120, marginRight: 8 }}
            >
              <Select.Option value={0}>全部类型</Select.Option>
              <Select.Option value={1}>潮客</Select.Option>
              <Select.Option value={2}>潮鸣号</Select.Option>
            </Select>
            {search.status === 0 && (
              <>
                <PermButton
                  perm={`account_image_audit:${props.pageType}:pass`}
                  onClick={() => {
                    batchPass(1);
                  }}
                  style={{ marginRight: 8 }}
                  disabled={listSelectedKeys.length === 0}
                >
                  批量通过
                </PermButton>
                <PermButton
                  perm={`account_image_audit:${props.pageType}:pass`}
                  onClick={() => {
                    batchPass(2);
                  }}
                  style={{ marginRight: 8 }}
                  disabled={listSelectedKeys.length === 0}
                >
                  批量拒绝
                </PermButton>
              </>
            )}

            {majorStatus === 0 && (
              <>
                <Select
                  value={search.status}
                  onChange={(value: any) => handleSearchChange('status', value)}
                  style={{ width: 120, marginRight: 8 }}
                >
                  <Select.Option value={-1}>全部状态</Select.Option>
                  <Select.Option value={1}>已通过</Select.Option>
                  <Select.Option value={2}>已拒绝</Select.Option>
                </Select>
                <Select
                  value={search.audit_by}
                  onChange={(value: any) => handleSearchChange('audit_by', value)}
                  style={{ width: 120, marginRight: 8 }}
                >
                  <Select.Option value={-1}>审核人</Select.Option>
                  <Select.Option value="">机器审核</Select.Option>
                  <Select.Option value="1">人工审核</Select.Option>
                </Select>
              </>
            )}
          </Col>
          <Col span={10} style={{ textAlign: 'right' }}>
            <Select
              value={input.search_type}
              style={{ width: 140, marginRight: 8 }}
              onChange={(value: any) => setInput((s) => ({ ...s, search_type: value }))}
            >
              <Select.Option value="1">搜索用户ID</Select.Option>
              <Select.Option value="2">搜索手机号</Select.Option>
              <Select.Option value="3">搜索用户昵称</Select.Option>
              <Select.Option value="4">搜索小潮号</Select.Option>
              {props.pageType == 2 && <Select.Option value="5">搜索审核昵称</Select.Option>}
            </Select>
            <Input
              value={input.keyword}
              style={{ width: 140, marginRight: 8 }}
              onChange={(e: any) => setInput({ ...input, keyword: e.target.value })}
              placeholder="输入搜索内容"
            />
            <Button type="primary" onClick={() => doSearch()} style={{ verticalAlign: 'top' }}>
              <Icon type="search" /> 搜索
            </Button>
          </Col>
        </Row>
        <CustomTable
          func="getUserImageAuditList"
          index="account_image_audit"
          multi={search.status === 0}
          filter={filter}
          rowKey="id"
          columns={columns}
          pagination
          selectedRowKeys={listSelectedKeys}
          onSelectChange={handleListSelectChange}
        />
      </div>
    </>
  );
}
