/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
import { getTableList } from '@action/tableList';
import { userApi as api } from '@app/api';
import { CommonObject } from '@app/types';
import { A, Table } from '@components/common';
import connect from '@utils/connectTable';
import {getCrumb,  requirePerm, setLoading, setMenu, UserDetail} from '@utils/utils.tsx';
import { Button, Col, Divider, Form, Icon, Input, message, Modal, Row, Select } from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';

@(withRouter as any)
@connect
class UserFeedback extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      searchType: 'nick_name',
      keyword: '',
      search: '',
      cType: 'nick_name',
      listSelectedKeys: [],
      loading: false,
      type: 1,
      userDetail: {
        visible: false,
        key: `${Date.now()}2`,
        detail: {},
      },
      batchReply: {
        visible: false,
        key: `${Date.now()}1`,
        content: '',
      },
      singleReply: {
        visible: false,
        key: `${Date.now()}3`,
        reply: '',
      },
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    setMenu(this);
  }

  componentDidUpdate(prevProps: any) {
    if (this.props.tableList.timestamp !== prevProps.tableList.timestamp) {
      this.setState({ listSelectedKeys: [] });
    }
  }

  getData = (overlap: CommonObject = {}, filter = this.getFilter()) => {
    this.props.dispatch(
      getTableList('getFeedbackList', 'feedback_list', { ...filter, ...overlap })
    );
  };

  getFilter = () => {
    const { current, size } = this.props.tableList;
    const { type } = this.state;
    if (this.state.keyword) {
      return { current, size, type, [this.state.searchType]: this.state.keyword };
    }
    return { current, size, type };
  };

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 80,
      },
      {
        title: '用户昵称',
        key: 'nick_name',
        dataIndex: 'nick_name',
        render: (text: any, record: any) =>
          this.props.session.permissions.indexOf('accont:detail') > -1 ? (
            <a onClick={() => this.showDetail(record)}>{text}</a>
          ) : (
            <span>{text}</span>
          ),
        width: 200,
      },
      {
        title: '小潮号',
        key: 'chao_id',
        dataIndex: 'chao_id',
        width: 120
      },
      {
        title: '内容',
        dataIndex: 'content',
        key: 'content',
        render: (text: any, record: any) => (
          <span>
            <p style={{ wordBreak: 'break-word' }}>{text}</p>
            {!!record.image_url && (
              <p>
                {record.image_url.split(',').map((v: any) => (
                  <img
                    src={v.replace(/&amp;/g, '&').replace(/"/g, '')}
                    key={v}
                    style={{ marginRight: 8 }}
                    className="list-pic"
                    onClick={() => this.showImg(v)}
                    alt=""
                  />
                ))}
              </p>
            )}
            {!!record.video_url && <video src={record.video_url} style={{ width: 250 }} controls />}
          </span>
        ),
      },
      {
        title: '回复状态',
        key: 'status',
        dataIndex: 'in_reply',
        render: (text: any) => <span>{text ? '已回复' : '未回复'}</span>,
        width: 80,
      },
      {
        title: '发布时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 180,
      },
      {
        title: '操作',
        key: 'op',
        dataIndex: 'id',
        render: (text: any) =>
          requirePerm(
            this,
            'account_feedback:detail'
          )(<A onClick={() => this.singleReply(text)}>回复</A>),
        width: 70,
      },
    ];
  };

  showImg = (url: any) => {
    Modal.success({
      title: (
        <img src={url.replace(/&amp;/g, '&').replace(/"/g, '')} alt="" style={{ width: '100%' }} />
      ),
      width: 550,
      icon: null,
      okText: '关闭',
    });
  };

  showDetail = (record: any) => {
    setLoading(this, true);
    api
      .getUserDetail({ accountId: record.id })
      .then((r: any) => {
        this.setState({
          userDetail: {
            detail: r.data.account,
            visible: true,
            key: Date.now(),
          },
        });
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  handleKey = (e: any) => {
    if (e.which === 13) {
      this.setState(
        {
          searchType: this.state.cType,
          keyword: this.state.search,
        },
        () => this.getData({ current: 1 })
      );
    }
  };

  handleListSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    this.setState({ listSelectedKeys: selectedRowKeys });
  };

  batchReply = () => {
    this.setState({
      batchReply: {
        visible: true,
        key: Date.now(),
        content: '',
      },
    });
  };

  closeModal = (key: string) => {
    this.setState({
      [key]: {
        ...this.state[key],
        visible: false,
      },
    });
  };

  doBatchReply = () => {
    const { content } = this.state.batchReply;
    if (content === '') {
      message.error('请输入回复内容');
      return;
    }
    if (content.length > 200) {
      message.error('回复内容不能超过200个字');
      return;
    }
    this.setState({ loading: true });
    api
      .batchReplyFeedback({
        reply_content: content,
        feedback_ids: this.state.listSelectedKeys.join(','),
      })
      .then(() => {
        message.success('操作成功');
        this.setState({
          loading: false,
        });
        this.getData();
        this.closeModal('batchReply');
      })
      .catch(() => this.setState({ loading: false }));
  };

  singleReply = (id: any) => {
    setLoading(this, true);
    api
      .getFeedbackDetail({ id })
      .then((r: any) => {
        this.setState({
          singleReply: {
            visible: true,
            key: Date.now(),
            reply: '',
            ...r.data.feedback,
          },
        });
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  doSingleReply = () => {
    const { reply, id } = this.state.singleReply;
    if (reply === '') {
      message.error('请输入回复内容');
      return;
    }
    if (reply.length > 200) {
      message.error('回复内容不能超过200个字');
      return;
    }
    this.setState({ loading: true });
    api
      .replyFeedback({
        reply,
        feedback_id: id,
      })
      .then(() => {
        message.success('操作成功');
        setTimeout(() => {
          this.getData();
          this.setState({
            loading: false,
          });
          this.closeModal('singleReply');
        }, 1000);
      })
      .catch(() => this.setState({ loading: false }));
  };

  handleTypeChange = (value: any) => {
    this.setState(
      {
        type: value,
      },
      () => this.getData({ current: 1 })
    );
  };

  render() {
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const { search, cType, userDetail, listSelectedKeys, batchReply, singleReply, loading, type } =
      this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={16}>
            <Select
              value={cType}
              onChange={(v: any) => this.setState({ cType: v })}
              style={{ width: 120 }}
            >
              <Select.Option value="nick_name">用户昵称</Select.Option>
              <Select.Option value="content">反馈内容</Select.Option>
              <Select.Option value="chao_id">小潮号</Select.Option>
            </Select>
            <Input
              value={search}
              placeholder="输入搜索内容"
              style={{ width: 140, marginLeft: 8 }}
              onChange={(e: any) => this.setState({ search: e.target.value })}
              onKeyPress={this.handleKey}
            />
            <Button onClick={() => this.handleKey({ which: 13 })} style={{ marginLeft: 8 }}>
              <Icon type="search" /> 搜索
            </Button>
            {requirePerm(
              this,
              'account_feedback:batch_reply'
            )(
              <Button
                style={{ marginLeft: 8, marginRight: 8 }}
                disabled={listSelectedKeys.length === 0}
                onClick={this.batchReply}
              >
                <Icon type="message" /> 批量回复
              </Button>
            )}
            <Select value={type} onChange={this.handleTypeChange} style={{ width: 100 }}>
              <Select.Option value={1}>问题</Select.Option>
              <Select.Option value={2}>建议</Select.Option>
              <Select.Option value={3}>报料</Select.Option>
            </Select>
          </Col>
          <Col span={8} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            multi={true}
            func="getFeedbackList"
            index="feedback_list"
            filter={this.getFilter()}
            columns={this.getColumns()}
            rowKey="id"
            pagination={true}
            selectedRowKeys={listSelectedKeys}
            onSelectChange={this.handleListSelectChange}
          />
          <Modal
            visible={userDetail.visible}
            key={userDetail.key}
            title="用户详情"
            onCancel={() => this.closeModal('userDetail')}
            onOk={() => this.closeModal('userDetail')}
          >
            {/*{getUserDetail(userDetail.detail)}*/}
            <UserDetail detail={userDetail.detail}/>
          </Modal>
          <Modal
            visible={batchReply.visible}
            key={batchReply.key}
            title="批量回复"
            confirmLoading={loading}
            onCancel={() => this.closeModal('batchReply')}
            onOk={this.doBatchReply}
          >
            <Row>
              <Input.TextArea
                rows={4}
                placeholder="请输入不超过200个字的回复内容"
                value={batchReply.content}
                onChange={(e: any) =>
                  this.setState({ batchReply: { ...batchReply, content: e.target.value } })
                }
              />
            </Row>
            <Row style={{ marginTop: 16 }}>回复人：潮新闻小秘书</Row>
          </Modal>
          <Modal
            visible={singleReply.visible}
            key={singleReply.key}
            title="回复"
            confirmLoading={loading}
            onCancel={() => this.closeModal('singleReply')}
            footer={[
              requirePerm(
                this,
                'account_feedback:save'
              )(
                <Button key="1" onClick={this.doSingleReply} type="primary">
                  确定
                </Button>
              ),
              <Button key="2" onClick={() => this.closeModal('singleReply')}>
                关闭
              </Button>,
            ]}
          >
            <Form {...formLayout}>
              <Form.Item label="用户昵称">{singleReply.nick_name}</Form.Item>
              <Form.Item label="发布时间">
                {moment(singleReply.created_at).format('YYYY-MM-DD HH:mm:ss')}
              </Form.Item>
              <Form.Item label="反馈内容">
                <p>{singleReply.content}</p>
                {!!singleReply.image_url && (
                  <p>
                    {singleReply.image_url.split(',').map((v: any) => (
                      <img
                        src={v.replace(/&amp;/g, '&').replace(/"/g, '')}
                        key={v}
                        style={{ marginRight: 8 }}
                        className="list-pic"
                        alt=""
                      />
                    ))}
                  </p>
                )}
                {singleReply.video_url && (
                  <video src={singleReply.video_url} className="list-pic" controls />
                )}
              </Form.Item>
              <Form.Item label="设备">
                {singleReply.model}&nbsp;{singleReply.platform}&nbsp;{singleReply.os_version}
              </Form.Item>
              <Form.Item label="客户端">{singleReply.app_version}</Form.Item>
              <Form.Item label="已有回复">
                {singleReply.replys &&
                  singleReply.replys.map((v: any, i: number) => (
                    <Row style={{ marginTop: 14 }} key={i}>
                      <p style={{ wordBreak: 'break-all', lineHeight: '1.1' }}>{v.reply}</p>
                      <p style={{ color: '#999', lineHeight: '1.1' }}>
                        {moment(v.created_at).format('YYYY-MM-DD HH:mm:ss')}
                        &nbsp;&nbsp;回复人：{v.created_by}
                      </p>
                    </Row>
                  ))}
              </Form.Item>
              <Form.Item label="回复">
                <Input.TextArea
                  rows={4}
                  placeholder="请输入不超过200个字的回复内容"
                  value={singleReply.reply}
                  onChange={(e: any) =>
                    this.setState({ singleReply: { ...singleReply, reply: e.target.value } })
                  }
                />
              </Form.Item>
              <Form.Item label="回复人">潮新闻小秘书</Form.Item>
            </Form>
          </Modal>
        </div>
      </>
    );
  }
}

export default UserFeedback;
