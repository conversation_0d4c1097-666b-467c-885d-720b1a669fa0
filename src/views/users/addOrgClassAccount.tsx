import { Button, Form, Icon, InputNumber, Modal, Select, Spin, Tooltip, message } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import _, { set } from 'lodash';
import { communityApi, userApi } from '@app/api';

const AddReporterModal = (props: any, ref: any) => {
  const [loading, setLoading] = useState(false);
  const [accountOptions, setAccountOptions] = useState([]);
  const [reporter, setReporter] = useState<any>(undefined);

  // useEffect(() => {
  //   if (!!props.formContent && !!props.formContent.reporter_id) {
  //     setReporter(props.formContent.reporter_id)
  //     handleAccountSearch(props.formContent.reporter_id)
  //   } else {
  //     setReporter(undefined)
  //     handleAccountSearch('')
  //   }
  // }, [props.formContent])

  useEffect(() => {
    setReporter(undefined);
    handleAccountSearch('');
  }, [props.visible]);

  const handleSubmit = (e: any) => {
    e.preventDefault();

    function submit() {
      const parmas = {
        class_id: props.class_id,
        account_id: reporter,
      };
      userApi
        .addOrgClassAccount(parmas)
        .then((res: any) => {
          message.success('添加成功');
          setLoading(false);

          props.onOk && props.onOk();
        })
        .catch(() => {
          // message.error('添加失败');
          setLoading(false);
        });
    }

    if (!!reporter) {
      setLoading(true);

      userApi
        .getTmhAccountDetail({ account_id: reporter })
        .then((res: any) => {
          if (!res.data?.name || res.data?.name == props.class_name) {
            submit();
          } else {
            Modal.confirm({
              title: `该潮鸣号已添加到「${res.data?.name}」分类，是否更换为「${props.class_name}」分类？`,
              onOk: () => {
                submit();
              },
              onCancel: () => {
                setLoading(false);
              },
            });
          }
        })
        .catch(() => {});
    } else {
      message.error('请选择用户');
    }
  };

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val) {
      setAccountOptions([]);
      return;
    }
    // type: 1, current: 1, size: 50, biz_type: props.formContent?.biz_type,
    communityApi
      .recommendAccount_Search({ keyword: val, account_type: 1 })
      .then(({ data }) => {
        const { list } = data as any;
        setAccountOptions(list || []);
      })
      .catch(() => {});
  }, 500);

  const handleAccountChange = (val: any) => {
    setReporter(val);
  };

  return (
    <Modal
      width={500}
      visible={props.visible}
      title="添加潮鸣号"
      key={props.skey}
      onCancel={() => {
        if (!loading) {
          props.onCancel && props.onCancel();
        }
      }}
      onOk={handleSubmit}
      maskClosable={false}
      destroyOnClose={true}
      confirmLoading={loading}
    >
      <Spin spinning={loading}>
        <Select
          style={{ width: '100%' }}
          // defaultValue={props.formContent?.reporter_id}
          value={reporter}
          placeholder="输入昵称或小潮号"
          onSearch={handleAccountSearch}
          showSearch
          onChange={handleAccountChange}
          filterOption={false}
        >
          {accountOptions.map((item: any) => (
            <Select.Option className={'reporter-item'} key={item.id} value={item.id}>
              {item.nick_name}|小潮号：{item.chao_id}
            </Select.Option>
          ))}
        </Select>
        <div style={{ color: '#999', marginTop: 5 }}>
          提示：新添加账号，自动按发布内容排序在对应位置
        </div>
      </Spin>
    </Modal>
  );
};

export default forwardRef<any, any>(AddReporterModal);
