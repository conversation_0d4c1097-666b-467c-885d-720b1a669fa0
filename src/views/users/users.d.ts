import { ICommonTableList } from '@app/types';

export type FAQRecord = {
  id: number;
  title: string;
  content: string;
  enabled: boolean;
  created_by: string;
  created_at: string;
};

export type FAQAllData = {
  help_list: ICommonTableList<FAQRecord>;
  on_show_count: number;
};

export type OrgUsersRecord = {
  id: string;
  nick_name: string;
  mobile: string;
  phone_number: string;
  ref_code: string;
  forbidden: boolean;
  image_url: string;
  created_at: number;
  status: 0 | 1;
  cert_information: string;
  liked_count: number;
  fans_count: number;
  works_count: number;
  follow_count: string;
  access_days: number;
  article_not_down: string;
  article_down: string;
  article_high_quality: string;
};

export type OrgUsersAllData = {
  account_list: ICommonTableList<OrgUsersRecord>;
};

export type VirtualUsersRecord = {
  id: string;
  nick_name: string;
  phone_number: string;
  ref_code: string;
  created_by: string;
  created_at: number;
};

export type VirtualUsersAllData = {
  account_list: ICommonTableList<VirtualUsersRecord>;
};

export type OrgUserArticleRecord = {
  id: string;
  metadata_id: number;
  video_url: string;
  list_pics: string;
  list_title: string;
  account_nick_name: string;
  published_timestamp: number;
};

export type OrgUserArticleAllData = {
  list: ICommonTableList<OrgUserArticleRecord>;
};
