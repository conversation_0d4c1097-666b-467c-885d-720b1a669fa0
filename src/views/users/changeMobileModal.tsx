import {
  Button,
  Form,
  Icon,
  Input,
  InputNumber,
  Modal,
  Select,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import _, { set } from 'lodash';
import { userApi, communityApi } from '@app/api';
import '@app/assets/index.scss';

const AddRecommendUserModal = (props: any, ref: any) => {
  const { getFieldDecorator } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const [loading, setLoading] = useState(false);

  const handleSubmit = (e: any) => {
    e.preventDefault();

    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true);
        userApi
          .updateEmergencyPhone({
            account_id: props.record.id,
            emergency_phone: values.emergency_phone || '',
          })
          .then((res: any) => {
            message.success('操作成功');
            setLoading(false);
            props.onEnd && props.onEnd();
          })
          .catch(() => {
            // message.error('添加失败');
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
        setLoading(false);
      }
    });
  };

  return (
    <Modal
      width={500}
      visible={props.visible}
      title="紧急联系人手机"
      key={props.key}
      onCancel={() => {
        if (!loading) {
          props.onCancel && props.onCancel();
        }
      }}
      onOk={handleSubmit}
      maskClosable={false}
      destroyOnClose={true}
      confirmLoading={loading}
    >
      <Spin spinning={loading}>
        <Form {...formLayout}>
          <Form.Item label="">
            {getFieldDecorator('emergency_phone', {
              initialValue: props.record?.emergency_phone,
              rules: [
                {
                  pattern: /^1\d{10}$/,
                  message: '请输入正确的手机号',
                },
              ],
            })(<Input placeholder="输入紧急联系人手机号"></Input>)}
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<any>({ name: 'AddRecommendUserModal' })(
  forwardRef<any, any>(AddRecommendUserModal)
);
