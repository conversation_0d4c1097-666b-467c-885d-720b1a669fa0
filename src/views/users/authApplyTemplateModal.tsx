import React from "react";
import { A, Drawer, FileUploader } from '@components/common';
import { Form, InputNumber, Select, message, Table, Modal, Input } from "antd";
import connect from '@utils/connectSession';
import { sysApi as api } from '@app/api';
import { setMLoading } from "@app/utils/utils";

@connect
@(Form.create({ name: 'AuthApplyTemplateModal' }) as any)
export default class AuthApplyTemplateModal extends React.Component<any, any> {
  handleOkClick = () => {
    const { form: { validateFields }, onEnd, record } = this.props
    validateFields((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        // if (record && record.id) {
        //   api.updateAppConfig({ ...values, code: record.item_code }).then(() => {
        //     setMLoading(this, false);
        //     message.success('修改成功');
        //     onEnd(false)
        //   }).catch(() => {
        //     setMLoading(this, false);
        //   })
        // } else {
        //   api.createAppConfig({ ...values }).then(() => {
        //     setMLoading(this, false);
        //     message.success('添加成功');
        //     onEnd(true)
        //   }).catch(() => {
        //     setMLoading(this, false);
        //   })
        // }
      }
    })
  }

  render() {
    const { record, visible, onCancel, form: { getFieldDecorator } } = this.props
    const formLayout = {
      labelCol: { span: 5 },
      wrapperCol: { span: 16 },
    };
    return (
      <Modal
        visible={visible}
        key="AuthApplyTemplateModal"
        title="申请函模板"
        // confirmLoading={loading}
        width="500px"
        maskClosable={false}
        onCancel={onCancel}
        onOk={this.handleOkClick}
        destroyOnClose
      >
        <Form {...formLayout}>
          <Form.Item label="申请函模板" extra="仅限doc/docx格式的文件，小于10MB">
            {getFieldDecorator('music_url', {
              // initialValue: this.state.music_url,
              rules: [
                {
                  required: true,
                  message: '请上传申请函模板',
                },
              ],
            })(<FileUploader accept=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document" />)}
          </Form.Item>
        </Form>
      </Modal>
    )
  }
}