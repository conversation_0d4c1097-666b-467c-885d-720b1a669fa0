import { getTableList } from '@action/tableList';
import { userApi as api } from '@app/api';
import { A, ImageUploader, OrderColumn, Table } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setLoading, setMenu } from '@utils/utils.tsx';
import { Button, Checkbox, Col, Form, Icon, message, Modal, Row, Input, Divider } from 'antd';
import React from 'react';
import { withRouter } from 'react-router';
import moment from 'moment';
import AddRecommendUserModal from './addRecommendUserModal';

@(withRouter as any)
@connect
class UserRecommend extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      visible: false,
      loading: false,
      key: Date.now(),
      users: '',
      onShowCount: 0,
    };
  }

  componentDidMount() {
    this.getData({ size: 20 });
    setMenu(this);
  }

  componentDidUpdate(prevProps: any) {
    if (
      prevProps.tableList.timestamp !== this.props.tableList.timestamp &&
      Boolean(this.props.tableList.allData.recommend_list)
    ) {
      this.setState({
        onShowCount: this.props.tableList.allData.on_show_count || 0,
      });
    }
  }

  getData = (params: any = {}) => {
    const { current, size, total } = this.props.tableList;
    this.props.dispatch(getTableList('getRecommendUserList', 'recommend_list', { current, size, ...params }));
  };

  getColumns = () => {
    const { current, size, total } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) => (<OrderColumn
          perm='follow_recommend:update_sort'
          start={1}
          end={this.state.onShowCount}
          pos={getSeq(i)}
          onUp={() => this.order(record, '0')}
          onDown={() => this.order(record, '1')}
          disable={record.status != 1}
        ></OrderColumn>),
        width: 80,
      },
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 80,
      },
      {
        title: '用户昵称',
        key: 'title',
        dataIndex: 'title',
      },
      {
        title: '用户类型',
        key: 'account_cert_type',
        dataIndex: 'account_cert_type',
        render: (text: number) => <span>{['潮客', '潮鸣号', '潮鸣号'][text]}</span>,
        width: 80,
      },
      {
        title: '小潮号',
        key: 'chao_id',
        dataIndex: 'chao_id',
        width: 120
      },
      {
        title: '用户ID',
        key: 'ref_ids',
        dataIndex: 'ref_ids',
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'status',
        render: (text: number) => <span>{text === 1 ? '已推荐' : '未推荐'}</span>,
      },
      {
        title: '添加时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: number) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 170,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <span>
            {requirePerm(
              this,
              'follow_recommend:update_status'
            )(
              <A onClick={() => this.updateRecordStatus(record)}>
                {record.status === 1 ? '取消推荐' : '推荐'}
              </A>
            )}
            <Divider type="vertical" />
            {requirePerm(
              this,
              'follow_recommend:delete'
            )(<A onClick={() => this.deleteRecord(record)}>删除</A>)}
          </span>
        ),
        width: 170,
      },
    ];
  };

  order = (record: any, sortType: string) => {
    setLoading(this, true);
    api
      .updateRecommendUserSort({ id: record.id, sort_flag: sortType })
      .then(() => {
        message.success('操作成功');
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  editRecord = () => {
    this.setState({
      visible: true,
      key: Date.now(),
      users: '',
    });
  };

  deleteRecord = (record: any) => {
    Modal.confirm({
      title: '确认删除吗？',
      onOk: () => {
        setLoading(this, true);
        api
          .deleteRecommendUser({ id: record.id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };

  updateRecordStatus = (record: any) => {
    if (record.status === 0 && this.state.onShowCount >= 20) {
      message.error('推荐关注用户已达上限20个，请先取消再推荐');
      return;
    }
    setLoading(this, true);
    api
      .updateRecommendUserStatus({ id: record.id, status: record.status === 0 ? 1 : 0 })
      .then(() => {
        message.success('操作成功');
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  handleSubmit = () => {
    const { users } = this.state;
    if (users === '') {
      message.error('请填写小潮号');
      return;
    }
    const body: any = { id: users };
    this.setState({ loading: true });
    api
      .createRecommendUsers(body)
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setState({ loading: false, visible: false });
      })
      .catch(() => this.setState({ loading: false }));
  };

  render() {
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const { users, visible, loading, key } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            {requirePerm(
              this,
              'follow_recommend:create'
            )(
              <Button onClick={() => this.editRecord()}>
                <Icon type="plus-circle" /> 添加推荐用户
              </Button>
            )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getRecommendUserList"
            index="recommend_list"
            rowKey="id"
            filter={{}}
            columns={this.getColumns()}
            pagination={true}
          />

          <AddRecommendUserModal
            visible={this.state.visible}
            onCancel={() => this.setState({ visible: false })}
            onEnd={() => {
              this.setState({ visible: false })
              this.getData()
            }}
          >

          </AddRecommendUserModal>
          {/* <Modal
            visible={visible}
            key={key}
            title="添加推荐用户"
            confirmLoading={loading}
            onCancel={() => this.setState({ visible: false })}
            onOk={this.handleSubmit}
          >
            <Form {...formLayout}>
              <Form.Item required={true} label="小潮号">
                <Input.TextArea
                  rows={5}
                  placeholder="输入小潮号，以换行分隔"
                  value={users}
                  onChange={(e: any) => this.setState({ users: e.target.value })}
                />
              </Form.Item>
            </Form>
          </Modal> */}
        </div>
      </>
    );
  }
}

export default UserRecommend;
