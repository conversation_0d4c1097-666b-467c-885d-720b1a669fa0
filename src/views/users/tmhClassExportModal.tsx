import { Button, Col, Form, Icon, InputNumber, Modal, Pagination, Radio, Row, Select, Spin, Table, Tooltip, message } from 'antd'
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react'

import { reportApi } from '@app/api';
import Excel from 'exceljs';

const TmhClassExportModal = (props: any, ref: any) => {

  const [loading, setLoading] = useState(false)
  const [list, setList] = useState<any>([])
  const [showTable, setShowTable] = useState<Boolean>(false)
  const [classifyList, setClassifyList] = useState<any>([])
  const [currentClassify, setCurrentClassify] = useState('')
  const [size, setSize] = useState(1)
  const { getFieldDecorator } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true)
        let promise = null
        if (props.type == 0) {
          // 修改领域
          const parmas = {
            ...values,
            id: props.formContent.id,
            biz_type: props.formContent.biz_type
          }
          promise = reportApi.updateReportClassify(parmas)
        } else {
          const params = {
            ...values,
            id: props.formContent.id,
            biz_type: props.formContent.biz_type,
            status: 1
          }
          if (params.classify_id == -1) {
            delete params.classify_id
          }
          promise = reportApi.changeAuditStatus(params)
        }
        promise.then((res: any) => {
          message.success(props.type == 0 ? '修改成功' : '审核成功');
          setLoading(false)
          props.onOk && props.onOk(props.type)
        }).catch(() => {
          // message.error('添加失败');
          setLoading(false)
        })
      } else {
        message.error('请检查表单内容');
        setLoading(false)
      }
    });
  }

  const handleUploadExcel = async (e: any) => {
    setLoading(true)
    const target = e?.target;
    const file = target.files?.[0];
    if (file) {
      if (file.size / 1024 > 2000) {
        message.error('上传文件不能大于2M');
        return;
      }

      if (file.type.indexOf('sheet') === -1 && file.type.indexOf('ms-excel') === -1 && (file.type !== '')) {
        message.error('必须选择excel文件');
        return;
      }
      target.value = '';


      // 创建一个新的工作簿
      const workbook = new Excel.Workbook();

      // 读取 Excel 文件
      await workbook.xlsx.load(file);
      setLoading(false)
      // 获取第一个工作表
      const worksheet = workbook.getWorksheet(1);

      let rows: any = []
      // 遍历工作表中的数据
      // Row 1: [null,"潮鸣号id","潮鸣号名称","分类"]
      // Row 2: [null,11111,"名称","分类"]
      // Row 3: [null,222,"名称2","分类2"]
      let set = new Set();

      worksheet.eachRow((row: any, rowNumber) => {
        console.log(`Row ${rowNumber}: ${JSON.stringify(row.values)}`);
        if (rowNumber > 1 && row.values?.length >= 4) {
          rows.push({
            id: row.values?.[1] || '',
            title: row.values?.[2] || '',
            classify_name: row.values?.[3] || '',
          })
          if (!!row.values?.[3]) {
            set.add(row.values?.[3])
          }
        }
      });
      setClassifyList(Array.from(set.values()))
      setList(rows)
      setShowTable(true)
      // const url = filter.tmType == 0 ? userApi.provinceImport : userApi.industryImport
      // run(url, { file }, true).then((result) => {
      //   message.success('导入成功')
      //   getList()
      // }).catch((err) => {

      // });
    }
  }

  const columns = [
    {
      dataIndex: 'id',
      title: '潮鸣号id'
    }, {
      dataIndex: 'title',
      title: '潮鸣号名称'
    }, {
      dataIndex: 'classify_name',
      title: '分类'
    }
  ]

  const filterList = !!currentClassify ? list.filter((item: any) => item.classify_name == currentClassify) : list
  const totalLength = filterList?.length || 0
  const dataSource = filterList?.slice((size - 1) * 10, size * 10) || []

  return <Modal
    keyboard={false}
    width={500}
    visible={props.visible}
    title={`批量导入数据-${props.type == 0 ? '内容分类' : '地市分类'}`}
    key={props.skey}
    footer={null}
    onCancel={() => { if (!loading) { props.onCancel && props.onCancel() } }}
    onOk={handleSubmit}
    maskClosable={false}
    destroyOnClose={true}
  >
    <Spin spinning={loading}>
      {!showTable && <div>
        <p><a>下载文件模板</a></p>
        {props.type == 0 ? <p>注：本次导入数据，将<span style={{ color: 'red' }}>全量更新</span>【内容分类】已有的所有账号分类数据，<span style={{ color: 'red' }}>同时影响潮鸣号榜单-行业榜及矩阵页面</span>。如有新增的分类，请先手动添加该分类，再上传表格。</p> : <p>注：本次导入数据，将<span style={{ color: 'red' }}>全量更新</span>【地市分类】已有的所有账号分类数据，<span style={{ color: 'red' }}>影响潮鸣号榜单-融媒榜页面</span>。如有新增的分类，请先手动添加该分类，再上传表格。</p>
        }
        <div style={{ textAlign: 'center' }}>
          <Button type='primary'>点击上传表格
            <input
              type="file"
              onChange={(e) => handleUploadExcel(e)}
              accept=".csv, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
              style={{
                position: 'absolute',
                opacity: 0,
                left: 0,
                top: 0,
                width: '100%',
                height: '100%',
              }}
            />
          </Button>
        </div>
      </div>}
      {showTable && <div>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
          <span>即将导入<span style={{ color: 'red' }}>{list.length}</span>条数据，请确认数据是否正确</span>
          <Select style={{ width: 100 }} defaultValue={''} onChange={(v) => {
            setSize(1)
            setCurrentClassify(v)
          }}>
            <Select.Option value={''}>全部</Select.Option>
            {classifyList?.map((v: any) => {
              return <Select.Option key={v} value={v}>{v}</Select.Option>
            })}
          </Select>
        </div>
        <Table
          columns={columns}
          rowKey={'id'}
          dataSource={dataSource}
          pagination={false}
          style={{ marginTop: 10 }}
        >
        </Table>
        <Row style={{ marginTop: 16 }}>
          <Col span={6} style={{ verticalAlign: 'bottom' }}>
            共{totalLength}条数据
          </Col>
          <Col span={18} className="pagination-pages">
            <Pagination
              showSizeChanger={false}
              showQuickJumper={true}
              pageSize={10}
              current={size}
              // pageSizeOptions={this.props.pageSizeOptions?.length > 0 ? this.props.pageSizeOptions : ['10', '20', '50', '100']}
              onChange={(page: any) => setSize(page)}
              // onShowSizeChange={this.onSizeChange}
              total={totalLength}
            />
          </Col>
        </Row>

        <div style={{ textAlign: 'right', marginTop: '10px' }}>
          <Button onClick={() => props.onCancel && props.onCancel()}>取消</Button>
          <Button disabled={list.length == 0} type='primary' style={{ marginLeft: '10px' }}>确定更新</Button>
        </div>
      </div>}
    </Spin>
  </Modal>

}

export default Form.create<any>({ name: 'TmhClassExportModal' })(forwardRef<any, any>(TmhClassExportModal));
