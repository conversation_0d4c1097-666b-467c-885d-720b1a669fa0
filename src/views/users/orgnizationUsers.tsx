/* eslint-disable no-nested-ternary */
declare const BUILD_ENV: 'dev' | 'test' | 'prev' | 'prod' | 'testb';

import { userApi as api } from '@app/api';
import { IAllProps, IBaseProps, CommonObject } from '@app/types';
import { A, Table, BaseComponent, Drawer, ImageUploader } from '@components/common';
import { connectAll as connect } from '@utils/connect';
import Form from '@components/business/virtualUserForm';
import {
  Button,
  Col,
  Icon,
  message,
  Modal,
  Row,
  Input,
  Select,
  Radio,
  Menu,
  Dropdown,
  Tooltip,
  Checkbox,
  Form as AForm,
} from 'antd';
import React from 'react';
import { withRouter } from 'react-router';
import { copy, searchToObject, setLoading, UserDetail } from '@app/utils/utils';
import { OrgUsersRecord, OrgUsersAllData } from './users';
import { PermButton } from '@app/components/permItems';
import { setTableCache } from '@app/action/tableCache';
import { setTableList } from '@action/tableList';
import ChangeMobileModal from './changeMobileModal';

type State = {
  cType: 'nick_name' | 'mobile' | 'chao_id' | 'id';
  cKeyword: string;
  filter: {
    keyword: string;
    searchType: 'nick_name' | 'mobile' | 'chao_id' | 'id';
    status: number;
    cert_type: string;
    class_id: string;
    virtual_type: string;
  };
  form: {
    id: string;
    password: string;
    type: 1 | 2;
    visible: boolean;
    key: number;
    err: string;
  };
  form2: {
    type: 0 | 1;
    visible: boolean;
    key: number;
    title: string;
  };
  form3: {
    classIds: any;
    visible: boolean;
    key: number;
    id: any;
  };
  // authApplyTemplate: {
  //   visible: boolean;
  // },
  classList: any[];
  filterClassList: any[];
  detail: CommonObject;
  certForm: {
    visible: boolean;
    key: string;
    id: string;
    type: number;
    info: string;
  };
};

type Props = IBaseProps<IAllProps<OrgUsersRecord, OrgUsersAllData>>;
class OrgUserManager extends BaseComponent<IAllProps<OrgUsersRecord, OrgUsersAllData>, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      filter: {
        searchType: 'nick_name',
        keyword: '',
        status: 2,
        cert_type: '',
        class_id: searchToObject().class_id || '',
        virtual_type: '',
      },
      cType: 'nick_name',
      cKeyword: '',
      form: {
        visible: false,
        key: Date.now(),
        id: '',
        password: '',
        type: 1,
        err: '',
      },
      detail: {
        visible: false,
        key: Date.now() + 1,
      },
      form2: {
        visible: false,
        key: Date.now() + 2,
        type: 1,
        title: '',
      },
      form3: {
        visible: false,
        key: Date.now() + 3,
        classIds: '',
        id: '',
      },
      // authApplyTemplate: {},
      classList: [],
      filterClassList: [],
      certForm: {
        visible: false,
        key: Date.now() + 1,
        id: '',
        type: 0,
        info: '',
      },
    };
  }

  componentDidMount() {
    this.setMenu();
    if (
      this.props.tableCache?.beforeRoute === this.props.match?.path &&
      this.props.tableCache.records.length > 0
    ) {
      this.props.dispatch(setTableList(this.props.tableCache));

      if (this.props.tableCache?.filters) {
        this.setState({
          filter: this.props.tableCache?.filters,
          cType: this.props.tableCache?.filters.searchType,
          cKeyword: this.props.tableCache?.filters.keyword,
        });
      }
    } else {
      this.getData({ current: 1, size: 10 });
    }

    this.getCmhClassify();
  }

  getCmhClassify = () => {
    api.getSimpleOrgCategoryList({}).then((res: any) => {
      this.setState({
        filterClassList: res.data.tmh_class_list,
      });
    });
  };

  getData = (overlap: CommonObject = {}, filter = this.getFilter()) => {
    this.dispatchTable('getOrgUserList', 'account_list', { ...filter, ...overlap });
  };

  getFilter = () => {
    const { status, keyword, searchType, cert_type, class_id, virtual_type } = this.state.filter;
    const { current, size } = this.props.tableList;
    const filter: any = { current, size, status, virtual_type };
    if (cert_type) {
      filter.cert_type = cert_type;
    }
    if (!!class_id) {
      filter.class_id = class_id;
    }
    if (keyword) {
      filter[searchType] = keyword;
    }
    return filter;
  };

  editClass = (record: any) => {
    api.getSimpleOrgCategoryList({ account_id: record.id }).then((res: any) => {
      this.setState({
        classList: res.data.tmh_class_list,
        form3: {
          visible: true,
          key: Date.now(),
          id: record.id,
          classIds: res.data.tmh_class_list.filter((v: any) => v.selected)?.[0]?.id || '',
        },
      });
    });
  };

  editInfo = (record: any) => {
    let { nick_name } = record;
    let { cert_information } = record;
    let { image_url } = record;
    function content(data: { nick_name: string; cert_information: string; image_url: string }) {
      return (
        <AForm wrapperCol={{ span: 16 }} labelCol={{ span: 7 }} labelAlign="left">
          <AForm.Item label="用户昵称" required>
            <Input
              placeholder="用户昵称最多16个字"
              defaultValue={data.nick_name}
              maxLength={16}
              onChange={(e: any) => change('nick_name', e.target.value)}
            />
          </AForm.Item>
          <AForm.Item label="认证信息" required>
            <Input
              defaultValue={data.cert_information}
              placeholder="认证信息最多25个字"
              maxLength={25}
              onChange={(e: any) => change('cert_information', e.target.value)}
            />
          </AForm.Item>
          <AForm.Item label="账号头像" required extra="支持jpg,jpeg,png图片格式， 比例为1:1">
            <ImageUploader
              value={data.image_url}
              ratio={1}
              // fixedSize={{ width: 120, height: 120 }}
              onChange={(e: any) => change('image_url', e)}
              imgMaxWidth={200}
            />
          </AForm.Item>
        </AForm>
      );
    }
    const change = (type: any, value: any) => {
      if (type === 'nick_name') {
        nick_name = value;
      } else if (type === 'cert_information') {
        cert_information = value;
      } else if (type === 'image_url') {
        if (!value) {
          image_url = '';
        } else {
          image_url = value;
        }
      }
      editModal.update({ content: content({ nick_name, cert_information, image_url }) });
    };
    let editModal = Modal.confirm({
      title: '编辑资料',
      content: content({ nick_name, cert_information, image_url }),
      onOk: (closeFunc: Function) => {
        if (!nick_name) {
          message.error('请输入用户昵称');
          return;
        }
        if (!cert_information) {
          message.error('请输入认证信息');
          return;
        }
        if (!image_url) {
          message.error('请选择头像');
          return;
        }
        api
          .updateOrgUserInfo({
            account_id: record.id,
            nick_name,
            cert_information,
            image_url,
          })
          .then(() => {
            message.success('操作成功');
            this.getData();
            closeFunc();
          });
      },
    });
    editModal;
  };

  cancelCert = (record: any) => {
    Modal.confirm({
      title: '取消认证后，账号将变为未认证状态的「潮客」账号，不再显示在当前列表中。',
      onOk: () => {
        this.setLoading(true);
        console.log('取消认证');
        api
          .updateUserCert({ account_id: record.id, cert_type: 0 })
          .then(() => {
            message.success('操作成功');
            this.getData();
            this.setLoading(false);
          })
          .catch(() => this.setLoading(false));
      },
    });
  };
  setCert = (record: any) => {
    this.setState({
      certForm: {
        visible: true,
        key: Date.now(),
        type: record.cert_type || 0,
        info: record.cert_information || '',
        id: record.id,
      },
    });
  };
  closeCert = () => {
    this.setState({
      certForm: { ...this.state.certForm, visible: false },
    });
  };
  typeChange = (e: any) => {
    this.setState({
      certForm: { ...this.state.certForm, type: e.target.value },
    });
  };

  infoChange = (e: any) => {
    this.setState({
      certForm: { ...this.state.certForm, info: e.target.value },
    });
  };
  qualityTypeChange = (e: any) => {
    this.setState({
      honorForm: {
        ...this.state.honorForm,
        quality_type: e,
      },
    });
  };
  submitCert = () => {
    const { id, type, info } = this.state.certForm;
    if (type !== 0 && info.trim() === '') {
      message.error('请填写认证信息');
      return;
    }
    if (type !== 0 && info.length > 25) {
      message.error('认证信息不能超过25个字');
      return;
    }
    setLoading(this, true);
    api
      .updateUserCert({
        account_id: id,
        cert_type: type,
        cert_information: type === 0 ? '' : info,
      })
      .then(() => {
        this.closeCert();
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  shadowConvert = (record: any) => {
    Modal.confirm({
      title: record.virtual_type != 2 ? '确定设为马甲号？' : '确定取消马甲号？',
      content:
        record.virtual_type != 2
          ? '设为马甲号后，可用于在评论系统后台发布评论（操作后请联系评论系统负责人，同步最新马甲号数据）'
          : '马甲号改为普通账号后，将无法在评论系统后台使用',
      onOk: () => {
        setLoading(this, true);
        api
          .shadowConvert({ id: record.id, shadow: record.virtual_type != 2 })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };

  togglePhoneValid = (record: any) => {
    Modal.confirm({
      title: !record.old_phone_valid
        ? '确定要取消该用户的换绑免验证？'
        : '设置后，该用户更换绑定手机号时，不要求验证原手机号',
      onOk: () => {
        setLoading(this, true);
        api
          .togglePhoneValid({ id: record.id, old_phone_valid: !record.old_phone_valid ? 1 : 0 })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };

  resetMobile = (record: any) => {
    this.setState({
      changeMobileModal: {
        visible: true,
        record,
        key: Date.now(),
      },
    });
  };

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    const getDropDown = (record: any) => {
      const menu = (
        <Menu>
          {this.requirePerm('account:tm_detail')(
            <Menu.Item onClick={this.getDetail.bind(this, record)}>查看详情</Menu.Item>
          )}
          {this.requirePerm('account:update_account_pwd')(
            <Menu.Item onClick={this.resetPassword.bind(this, record)}>重置密码</Menu.Item>
          )}
          {this.requirePerm('account:tm_reset_portrait')(
            <Menu.Item onClick={this.resetAvatar.bind(this, record)}>重置头像</Menu.Item>
          )}
          {!record.status &&
            this.requirePerm('account:tm_log_off')(
              <Menu.Item onClick={() => this.setDelete(record)}>
                {record.status === 1 ? '解除注销' : '注销账号'}
              </Menu.Item>
            )}
          {this.requirePerm('account:set_tmh_class')(
            <Menu.Item onClick={() => this.editClass(record)}>编辑分类</Menu.Item>
          )}
          {this.requirePerm('account:set_info')(
            <Menu.Item onClick={() => this.editInfo(record)}>编辑资料</Menu.Item>
          )}
          {this.requirePerm('account:update_user_cert:org')(
            <Menu.Item onClick={() => this.setCert(record)}>修改认证</Menu.Item>
          )}
          <Menu.Item
            onClick={() => {
              const host =
                {
                  dev: 'https://tmtest.tidenews.com.cn',
                  test: 'https://tmtest.tidenews.com.cn',
                  prev: 'https://tmprev.tidenews.com.cn',
                  prod: 'https://tidenews.com.cn',
                  testb: 'https://tmtest.tidenews.com.cn',
                }[BUILD_ENV] || 'https://tmtest.tidenews.com.cn';

              const url = `${host}/native/user_page.html?id=${record.id}`;

              copy(url)
                .then(() => {
                  message.success('链接已复制');
                })
                .catch(() => {
                  message.error('复制失败');
                });
            }}
          >
            复制主页地址
          </Menu.Item>

          {this.requirePerm('account:shadow_convert')(
            <Menu.Item onClick={() => this.shadowConvert(record)}>
              {record.virtual_type != 2 ? '设为' : '取消'}马甲号
            </Menu.Item>
          )}

          {this.requirePerm('account:orgOldPhoneValid')(
            <Menu.Item onClick={() => this.togglePhoneValid(record)}>
              {!record.old_phone_valid ? '取消换绑免验证' : '换绑免验证'}
            </Menu.Item>
          )}
          {this.requirePerm('account:org_emergency_phone')(
            <Menu.Item onClick={() => this.resetMobile(record)}>紧急联系人</Menu.Item>
          )}
        </Menu>
      );
      return (
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };

    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '潮鸣号名称',
        key: 'name',
        dataIndex: 'nick_name',
        render: (text: any, record: any) =>
          this.requirePerm('ugc_article:list_view')(
            <A onClick={this.toArticleList.bind(this, record)}>{text}</A>
          ),
      },
      {
        title: '小潮号',
        key: 'chao_id',
        dataIndex: 'chao_id',
        width: 120,
      },
      {
        title: '头像',
        key: 'image_url',
        dataIndex: 'image_url',
        render: (text: string) => (
          <img src={text} className="list-pic" style={{ height: 60 }} alt="" />
        ),
        width: 100,
      },
      {
        title: '分类',
        dataIndex: 'tmh_classes_str',
      },
      {
        title: '认证类型',
        key: 'cert_type',
        dataIndex: 'cert_type',
        render: (text: number) => <span>{['', '个人认证', '机构认证'][text]}</span>,
        width: 80,
      },
      {
        title: '认证信息',
        dataIndex: 'cert_information',
      },
      {
        title: '账号类型',
        dataIndex: 'virtual_type',
        width: 80,
        render: (text: number) => <span>{['普通', '虚拟', '马甲'][text]}</span>,
      },
      {
        title: '关注数',
        dataIndex: 'fans_count',
        width: 70,
      },
      {
        title: '上架视频量',
        dataIndex: 'on_works_count',
        width: 100,
      },
      {
        title: '上架图文量',
        dataIndex: 'on_image_count',
        width: 100,
        render: (text: any) => text || 0,
      },
      {
        title: '上架短图文量',
        dataIndex: 'on_short_image_count',
        width: 100,
        render: (text: any) => text || 0,
      },
      {
        title: '绑定手机号',
        key: 'mobile',
        dataIndex: 'phone_number',
        width: 110,
      },
      {
        title: '用户ID',
        key: 'id',
        dataIndex: 'id',
        width: 110,
      },
      {
        title: '状态',
        key: 'forbidden',
        dataIndex: 'forbidden',
        render: (text: any, record: any) => (
          <span>{record.status === 1 ? '注销' : text ? '禁言' : '正常'}</span>
        ),
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => getDropDown(record),
        width: 70,
        fixed: 'right',
      },
    ];
  };

  setDelete = (record: any) => {
    const doSetDelete = () => {
      this.setLoading(true);
      api
        .setUserDeleteStatus({ account_id: record.id, log_off: record.status !== 1 })
        .then(() => {
          message.success('操作成功');
          this.getData();
          this.setLoading(false);
        })
        .catch(() => this.setLoading(false));
    };
    if (record.status !== 1) {
      Modal.confirm({
        title: '确定要注销此账号吗?注销后将不能正常登录!',
        onOk: doSetDelete,
      });
    } else {
      doSetDelete();
    }
  };

  getDetail = (record: any) => {
    this.setLoading(true);
    api
      .getTMUserDetail({ accountId: record.id })
      .then((r: any) => {
        this.setState({
          detail: {
            ...r.data.account,
            visible: true,
            key: Date.now(),
          },
        });
        this.setLoading(false);
      })
      .catch(() => this.setLoading(false));
  };

  resetAvatar = (record: any) => {
    this.setLoading(true);
    api
      .resetUserAvatar({ account_id: record.id, type: 1 })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setLoading(false);
      })
      .catch(() => this.setLoading(false));
  };

  resetPassword = (record: any) => {
    this.setState({
      form: {
        visible: true,
        key: Date.now(),
        id: record.id,
        password: '',
        type: 1,
        err: '',
      },
    });
  };

  handleTypeChange = (value: 'nick_name' | 'mobile' | 'chao_id' | 'id') => {
    this.setState({
      cType: value,
    });
  };

  toArticleList = (record: any) => {
    this.props.dispatch(
      setTableCache({
        beforeRoute: this.props.match.path,
        ...this.props.tableList,
        filters: this.state.filter,
      })
    );
    console.log('this.state.filter', this.state.filter);
    this.props.history.push(encodeURI(`/view/orgUserArticleList/${record.id}/${record.nick_name}`));
  };

  handleKeywordChange = (e: any) => {
    this.setState({
      cKeyword: e.target.value,
    });
  };

  doSearch = () => {
    this.setState(
      {
        filter: {
          ...this.state.filter,
          searchType: this.state.cType,
          keyword: this.state.cKeyword,
        },
      },
      () => {
        this.getData({ current: 1 });
      }
    );
  };

  setFormType = (value: 1 | 2) => {
    this.setState({
      form: {
        ...this.state.form,
        type: value,
      },
    });
  };

  passwordChange = (e: any) => {
    const { value } = e.target;
    const regex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]+$/;
    let err = '';
    if (value.length < 8 || !regex.test(value)) {
      err = '密码为8-30位，仅支持数字和字母的组合';
    }
    this.setState({
      form: {
        ...this.state.form,
        err,
        password: value,
      },
    });
  };

  closeModal = () => {
    this.setState({
      form: {
        ...this.state.form,
        visible: false,
      },
      form3: {
        ...this.state.form3,
        visible: false,
      },
    });
  };

  submit = () => {
    const { form } = this.state;
    if ((form.type === 1 && form.err) || (form.type === 1 && form.password.length < 8)) {
      message.error('密码为8-30位，仅支持数字和字母的组合');
      return;
    }
    const body: CommonObject = {
      account_id: form.id,
    };
    if (form.type === 1) {
      body.pwd = form.password;
    } else {
      body.pwd = '';
    }
    this.setConfirmLoading(true);
    api
      .resetOrgUserPassword(body)
      .then((r: any) => {
        this.setConfirmLoading(false);
        this.closeModal();
        Modal.info({
          title: '重置成功',
          content: r.data.pwd,
        });
      })
      .catch(() => {
        this.setConfirmLoading(false);
      });
  };

  filterChange = (type: string, value: any) => {
    this.setState(
      {
        filter: { ...this.state.filter, [type]: value },
      },
      () => this.getData({ current: 1 })
    );
  };

  showForm = (type: 0 | 1) => {
    this.setState({
      form2: {
        type,
        visible: true,
        key: Date.now(),
        title: type === 1 ? '创建潮鸣号' : '创建邀请码',
      },
    });
  };

  submitEnd = () => {
    this.getData();
    this.closeDrawer();
  };

  closeDrawer = () => {
    this.setState({
      form2: {
        ...this.state.form2,
        visible: false,
      },
    });
  };

  submitForm3 = () => {
    // if (this.state.form3.classIds.length === 0) {
    //   message.error('请选择分类');
    //   return;
    // }
    this.setConfirmLoading(true);
    api
      .updateOrgUserCategory({
        account_id: this.state.form3.id,
        tmh_class_ids: this.state.form3.classIds,
      })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setConfirmLoading(false);
        this.closeModal();
      })
      .catch(() => {
        this.setConfirmLoading(false);
      });
  };

  classValuesChange = (value: any) => {
    this.setState({
      form3: {
        ...this.state.form3,
        classIds: value.target.value,
      },
    });
  };

  // showAuthApplyTemplate = (visible: boolean) => {
  //   this.setState({
  //     authApplyTemplate: {
  //       ...this.state.authApplyTemplate,
  //       visible
  //     }
  //   })
  // }

  render() {
    const { cKeyword, cType, form, detail, filter, form2, form3, classList, certForm } = this.state;
    const text = (
      <p>
        <p>1、重置密码：可以将潮鸣号用户密码修改为指定密码或随机密码</p>
        <p>2、点击潮鸣号名称可以查看该用户的作品</p>
        <p>3、查看详情：可以查看用户详细信息</p>
        <p>4、重置头像：可以将用户头像重置</p>
      </p>
    );
    const formLayout = {
      labelCol: { span: 6 },
      wrapperCol: { span: 16 },
    };
    return (
      <>
        <Row className="layout-infobar">
          <Col span={16}>
            {this.requirePerm('account:virtual_create')(
              <Button onClick={this.showForm.bind(this, 1)} style={{ marginRight: 8 }}>
                <Icon type="plus-circle" /> 创建潮鸣号
              </Button>
            )}
            <PermButton
              perm="account_audit:view"
              onClick={() => this.props.history.push('/view/authApply')}
              style={{ marginRight: 8 }}
            >
              审核认证申请
            </PermButton>
            <PermButton
              perm="h5Content:view:8"
              onClick={() => this.props.history.push('/view/cmhAgreement')}
              style={{ marginRight: 8 }}
            >
              服务协议
            </PermButton>
            <PermButton
              perm="h5Content:view:9"
              onClick={() => this.props.history.push('/view/cmhAuthQuestion')}
              style={{ marginRight: 8 }}
            >
              常见问题
            </PermButton>
            <PermButton
              perm="ugc:encourage:permissions:list:1"
              onClick={() => this.props.history.push('/view/longVideoApply')}
              style={{ marginRight: 8 }}
            >
              审核长视频申请
            </PermButton>
          </Col>
          <Col span={8} className="layout-breadcrumb">
            {this.getCrumb()}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <Col span={12}>
              <Select
                value={filter.status}
                onChange={this.filterChange.bind(this, 'status')}
                style={{ marginRight: 8, width: 120 }}
              >
                <Select.Option value={2}>用户状态</Select.Option>
                <Select.Option value={0}>正常</Select.Option>
                <Select.Option value={-1}>注销</Select.Option>
              </Select>
              <Select
                value={filter.cert_type}
                onChange={this.filterChange.bind(this, 'cert_type')}
                style={{ marginRight: 8, width: 120 }}
              >
                <Select.Option value="">认证类型</Select.Option>
                <Select.Option value="1">个人认证</Select.Option>
                <Select.Option value="2">机构认证</Select.Option>
              </Select>
              <Select
                value={filter.class_id}
                onChange={this.filterChange.bind(this, 'class_id')}
                style={{ marginRight: 8, width: 120 }}
              >
                <Select.Option value="">分类</Select.Option>
                {this.state.filterClassList?.map((item) => {
                  return <Select.Option value={`${item.id}`}>{item.name}</Select.Option>;
                })}
              </Select>
              <Tooltip title={text}>
                <Icon type="question-circle" />
              </Tooltip>

              <Select
                value={filter.virtual_type}
                onChange={this.filterChange.bind(this, 'virtual_type')}
                style={{ marginLeft: 8, width: 120 }}
              >
                <Select.Option value="">账号类型</Select.Option>
                <Select.Option value="0">普通</Select.Option>
                <Select.Option value="1">虚拟</Select.Option>
                <Select.Option value="2">马甲</Select.Option>
              </Select>
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <Select
                value={cType}
                onChange={this.handleTypeChange}
                style={{ marginRight: 8, width: 160 }}
              >
                <Select.Option value="nick_name">搜索潮鸣号名称</Select.Option>
                <Select.Option value="mobile">搜索绑定手机号</Select.Option>
                <Select.Option value="chao_id">搜索小潮号</Select.Option>
                <Select.Option value="id">搜索用户id</Select.Option>
              </Select>
              <Input
                value={cKeyword}
                onChange={this.handleKeywordChange}
                style={{ marginRight: 8, width: 180 }}
                placeholder="请输入搜索内容"
                onKeyPress={this.handleKey}
              />
              <Button onClick={this.doSearch}>
                <Icon type="search" /> 搜索
              </Button>
            </Col>
          </Row>
          <Table
            func="getOrgUserList"
            index="account_list"
            filter={this.getFilter()}
            rowKey="id"
            columns={this.getColumns()}
            pagination={true}
            tableProps={{ scroll: { x: 1500 } }}
          />
          <Modal
            visible={detail.visible}
            key={detail.key}
            title="用户详情"
            width={800}
            onCancel={() => this.setState({ detail: { visible: false, key: detail.key } })}
            onOk={() => this.setState({ detail: { visible: false, key: detail.key } })}
          >
            {/*{getUserDetail(this.state.detail, true)}*/}
            <UserDetail detail={this.state.detail} isTM={true} />
          </Modal>
          <Modal
            visible={form3.visible}
            key={form3.key}
            title="编辑分类"
            onCancel={this.closeModal}
            onOk={this.submitForm3}
            confirmLoading={(this.props as any).config.mLoading}
          >
            <Radio.Group value={form3.classIds} onChange={this.classValuesChange}>
              {classList.map((v) => (
                <Radio value={v.id} key={v.id}>
                  {v.name}
                </Radio>
              ))}
            </Radio.Group>
          </Modal>
          <Modal
            visible={form.visible}
            key={form.key}
            title="重置密码"
            onCancel={this.closeModal}
            onOk={this.submit}
            confirmLoading={(this.props as any).config.mLoading}
          >
            <Row style={{ marginBottom: 16 }}>
              <Radio checked={form.type === 1} onClick={this.setFormType.bind(this, 1)}>
                人工设置&nbsp;&nbsp;
                <Input
                  value={form.password}
                  onChange={this.passwordChange}
                  placeholder="请输入新密码"
                  maxLength={30}
                  onFocus={this.setFormType.bind(this, 1)}
                  style={{ width: 140 }}
                />
                <span style={{ color: 'red' }}>&nbsp;{form.err}</span>
              </Radio>
            </Row>
            <Row>
              <Radio checked={form.type === 2} onClick={this.setFormType.bind(this, 2)}>
                随机生成
              </Radio>
            </Row>
          </Modal>
          <Modal
            visible={certForm.visible}
            key={certForm.key}
            title="编辑用户认证"
            onCancel={this.closeCert}
            onOk={this.submitCert}
          >
            <AForm {...formLayout}>
              <AForm.Item required={true} label="认证类型">
                <Radio.Group value={certForm.type} onChange={this.typeChange}>
                  <Radio value={0}>无认证</Radio>
                  <Radio value={1}>个人认证</Radio>
                  <Radio value={2}>机构认证</Radio>
                </Radio.Group>
                <p
                  style={{
                    height: 14,
                    color: '#aaa',
                    marginTop: -8,
                  }}
                >
                  新的潮鸣号体系下，个人或机构认证均是潮鸣号
                </p>
              </AForm.Item>
              {certForm.type !== 0 && (
                <AForm.Item required={true} label="认证信息">
                  <Input placeholder="最多25字" value={certForm.info} onChange={this.infoChange} />
                </AForm.Item>
              )}
            </AForm>
          </Modal>

          <ChangeMobileModal
            {...this.state.changeMobileModal}
            onCancel={() =>
              this.setState({
                changeMobileModal: { ...this.state.changeMobileModal, visible: false },
              })
            }
            onEnd={() => {
              this.setState({
                changeMobileModal: { ...this.state.changeMobileModal, visible: false },
              });
              this.getData();
            }}
          ></ChangeMobileModal>
          <Drawer
            visible={form2.visible}
            skey={form2.key}
            title={form2.title}
            onClose={this.closeDrawer}
            onOk={this.handleSubmitForm.bind(this, 'form')}
          >
            <Form
              classList={this.state.filterClassList}
              type={form.type}
              wrappedComponentRef={this.setFormRef.bind(this, 'form')}
              onEnd={this.submitEnd}
            />
          </Drawer>
          {/* <AuthApplyTemplateModal visible={authApplyTemplate.visible} onCancel={() => this.showAuthApplyTemplate(false)} /> */}
        </div>
      </>
    );
  }
}

export default withRouter(connect<OrgUsersRecord, OrgUsersAllData>()(OrgUserManager));
