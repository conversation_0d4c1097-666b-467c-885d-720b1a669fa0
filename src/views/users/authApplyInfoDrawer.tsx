import { Checkbox, Drawer, Form, Input, Modal, Radio, Select, Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import ImagePreview from '@app/components/common/imagePreview';
import { userApi as api } from '@app/api';
import { PermButton } from '@app/components/permItems';
import moment from 'moment';
import { ImageUploader } from '@app/components/common';

function ShowResource(props: any) {
  const { url, setPreviewImageUrl } = props;
  if (!url) {
    return null;
  }
  const pathComponents = url.split('.');
  const ext = pathComponents[pathComponents.length - 1];
  if (ext.toLowerCase().startsWith('pdf')) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', lineHeight: '20px' }}>
        <a href={url} target="blank" style={{ wordBreak: 'break-all' }} rel="noreferrer">
          {url}
        </a>
      </div>
    );
  }
  return (
    <img
      style={{ width: 100, cursor: 'pointer' }}
      src={url}
      onClick={() => setPreviewImageUrl(url)}
    />
  );
}

export default function AuthApplyInfoDrawer(props: any) {
  const { record, visible, onClose, onEnd } = props;
  const [previewImageUrl, setPreviewImageUrl] = useState('');
  const [detail, setDetail] = useState<any>({});
  const [loading, setLoading] = useState(false);

  const [classList, setClassList] = useState([]);

  const directPass = () => {
    Modal.confirm({
      title: '审核通过后，请手动创建新账号',
      onOk: () => {
        // [isPass ? 'cert_information' : 'reason']: val.trim()
        const params = { id: detail.id, audit_status: 1 };
        // if (isPass) {
        //   params.cert_type = certType;
        // }
        api
          .submitAudit(params)
          .then((data) => {
            onClose();
            onEnd();
          })
          .catch(() => {});
      },
    });
  };

  const handleChangePass = (isPass: boolean) => {
    let val = detail.cert_information || '';
    let certType = detail.main_type == 0 ? 1 : 2;
    let modal: any;
    let nick_name = detail.nick_name;
    let tmh_class_ids = '';
    let img = detail.portrait_url;
    const validator = () => {
      const validate: any = {};
      let result = true;
      if (val.trim().length === 0) {
        validate.reason = {
          status: 'error',
          help: isPass ? '请输入认证信息' : '请输入驳回原因',
        };
        result = false;
      }

      if (nick_name.trim().length === 0) {
        result = false;
        validate.nickName = {
          status: 'error',
          help: '请输入昵称',
        };
      }

      if (!img) {
        result = false;
        validate.img = {
          status: 'error',
          help: '请上传账号头像',
        };
      }

      modal.update({ content: contentFn(validate) });
      return result;
    };
    const onInput = (e: any) => {
      val = e.target.value;
      validator();
    };

    const certTypeChange = (e: any) => {
      certType = e.target.value;
    };

    const handleChangeNickName = (e: any) => {
      nick_name = e;
      validator();
    };

    const handleFieldsChange = (e: any) => {
      tmh_class_ids = e.target.value;
    };

    const onImageChange = (url: any) => {
      img = url;
      validator();
    };

    const contentFn = (validate: any = {}) => {
      return (
        <Form {...formLayout}>
          {isPass && (
            <Form.Item
              label={'认证类型'}
              // validateStatus={validateStatus}
              // help={help}
              required
            >
              <Radio.Group defaultValue={certType} onChange={certTypeChange}>
                <Radio style={{ fontSize: 13 }} value={1}>
                  个人认证
                </Radio>
                <Radio style={{ fontSize: 13 }} value={2}>
                  机构认证
                </Radio>
              </Radio.Group>
            </Form.Item>
          )}
          <Form.Item
            label={isPass ? '认证信息' : '原因'}
            validateStatus={validate.reason?.status ?? ''}
            help={validate.reason?.help ?? ''}
            required
          >
            {isPass && <Input placeholder="最多25字" defaultValue={val} maxLength={25} onInput={onInput} />}
            {!isPass && (
              <Input.TextArea
                rows={5}
                placeholder="必填，最多50字"
                maxLength={50}
                onInput={onInput}
              />
            )}
          </Form.Item>
          {isPass && (
            <Form.Item
              label="用户昵称"
              validateStatus={validate.nickName?.status ?? ''}
              help={validate.nickName?.help ?? ''}
              required
            >
              <Input
                placeholder="用户昵称最多16个字"
                defaultValue={detail.nick_name}
                maxLength={16}
                onChange={(e: any) => handleChangeNickName(e.target.value)}
              />
            </Form.Item>
          )}
          {isPass && (
            <Form.Item
              label="账号头像"
              required
              extra="支持jpg,jpeg,png图片格式， 比例为1:1"
              validateStatus={validate.img?.status ?? ''}
              help={validate.img?.help ?? ''}
            >
              <ImageUploader ratio={1} value={img} onChange={onImageChange} imgMaxWidth={'100%'}></ImageUploader>
            </Form.Item>
          )}
          {isPass && (
            <Form.Item label="分类">
              <Radio.Group onChange={(e: any) => handleFieldsChange(e)}>
                {classList.map((v: any) => (
                  <Radio value={v.id} key={v.id}>
                    {v.name}
                  </Radio>
                ))}
              </Radio.Group>
            </Form.Item>
          )}
        </Form>
      );
    };
    const formLayout = {
      labelCol: { span: isPass ? 6 : 4 },
      wrapperCol: { span: isPass ? 18 : 20 },
    };
    modal = Modal.confirm({
      title: isPass ? '审核通过' : '审核不通过',
      content: contentFn(),
      width: 500,
      onOk(closeFunc: Function) {
        if (validator()) {
          modal.update({ okButtonProps: { loading: true } });
          const params = {
            id: detail.id,
            audit_status: isPass ? 1 : 2,
            [isPass ? 'cert_information' : 'reason']: val.trim(),
          };
          if (isPass) {
            params.cert_type = certType;
            params.nick_name = nick_name.trim();
            params.tmh_class_ids = tmh_class_ids;
            params.portrait_url = img;
            params.cert_information = val
          }
          api
            .submitAudit(params)
            .then((data) => {
              modal.update({ okButtonProps: { loading: false } });
              closeFunc();
              onClose();
              onEnd();
            })
            .catch(() => {
              modal.update({ okButtonProps: { loading: false } });
            });
        }
      },
    });
  };
  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  useEffect(() => {
    if (visible) {
      setLoading(true);
      // setDetail({})
      api
        .getAuditDetail({ account_id: record.account_id })
        .then(({ data }) => {
          setLoading(false);
          const { audit_detail } = data as any;
          setDetail(audit_detail);
        })
        .catch(() => {
          setLoading(false);
        });

      api.getSimpleOrgCategoryList({}).then((res: any) => {
        setClassList(res.data.tmh_class_list || []);
      });
    }
  }, [visible]);

  return (
    <>
      <Drawer
        className="rox-drawer"
        visible={visible}
        title="申请详情"
        key="AuthApplyInfoDrawer"
        destroyOnClose={true}
        maskClosable={false}
        width={850}
        onClose={onClose}
        bodyStyle={{ overflowY: 'hidden' }}
      >
        <Spin tip="正在加载..." spinning={loading} wrapperClassName={'wrapper_drawer_dialog'}>
          <div className="rox-drawer-content">
            <Form {...formLayout}>
              <Form.Item label="用户昵称">{detail.current_nick_name}</Form.Item>
              <Form.Item label="小潮号">{detail.chao_id}</Form.Item>
              <Form.Item label="账号主体">
                {detail.main_type === 0 ? '个人' : detail.main_type === 1 ? '组织/机构' : ''}
              </Form.Item>
              {detail.main_type == 1 && (
                <Form.Item label="账号开设方式">
                  {detail.create_type == 0 ? '升级当前账号' : '开设新账号'}
                </Form.Item>
              )}

              <Form.Item label="申请账号名称">{detail.nick_name}</Form.Item>
              <Form.Item label="申请账号头像">
                <ShowResource url={detail.portrait_url} setPreviewImageUrl={setPreviewImageUrl} />
              </Form.Item>
              <Form.Item label="申请认证信息">{detail.cert_information}</Form.Item>

              {detail.main_type === 0 && (
                <>
                  <Form.Item label="实人认证">
                    {detail.face_cert > 1 ? '已完成' : '未完成'}
                  </Form.Item>
                  <Form.Item label="真实姓名">{detail.true_name}</Form.Item>
                  <Form.Item label="身份证号码">{detail.cert_no}</Form.Item>
                  {detail.face_cert <= 1 && (
                    <Form.Item label="手持身份证照片">
                      <ShowResource
                        url={detail.cert_doc_url}
                        setPreviewImageUrl={setPreviewImageUrl}
                      />
                    </Form.Item>
                  )}
                </>
              )}

              {detail.main_type === 1 && (
                <>
                  <Form.Item label="主体类型">{detail.subject_type}</Form.Item>
                  <Form.Item label="账号主体全称">{detail.main_type_name}</Form.Item>
                  <Form.Item label="统一社会信用代码">{detail.uniform_social_code}</Form.Item>
                  <Form.Item label="证件凭证">
                    <ShowResource
                      url={detail.cert_doc_url}
                      setPreviewImageUrl={setPreviewImageUrl}
                    />
                  </Form.Item>
                </>
              )}

              <Form.Item label="创作领域">{detail.create_field}</Form.Item>

              <Form.Item label="其他平台账号">
                {detail.platform} - {detail.platform_no}
              </Form.Item>
              <Form.Item label="申请函">
                <ShowResource
                  url={detail.cert_request_url}
                  setPreviewImageUrl={setPreviewImageUrl}
                />
              </Form.Item>
              <Form.Item label="补充材料">
                <ShowResource url={detail.extra_doc_url} setPreviewImageUrl={setPreviewImageUrl} />
              </Form.Item>
              <Form.Item label="日常联系人姓名">{detail.daily_contact_name}</Form.Item>
              <Form.Item label="日常联系人电话">{detail.daily_contact_phone}</Form.Item>
              <Form.Item label="紧急联系人姓名">{detail.emergency_contact_name}</Form.Item>
              <Form.Item label="紧急联系人电话">{detail.emergency_contact_phone}</Form.Item>
              {detail.audit_status === 2 && <Form.Item label="驳回原因">{detail.reason}</Form.Item>}
              {detail.audit_status !== 0 && (
                <>
                  <Form.Item label="操作时间">
                    {detail.updated_at > 0 &&
                      moment(detail.updated_at).format('YYYY-MM-DD HH:mm:ss')}
                  </Form.Item>
                  <Form.Item label="操作人">{detail.updated_by}</Form.Item>
                </>
              )}
            </Form>
          </div>
          {/* detail.audit_status === 0 && detail.main_type === 0 && detail.face_cert > 1 && */}
          {detail.audit_status === 0 && (
            <div className="rox-drawer-footer">
              <PermButton
                type="primary"
                style={{ marginRight: 8 }}
                onClick={() => {
                  if (detail.create_type == 0) {
                    handleChangePass(true);
                  } else {
                    directPass();
                  }
                }}
                perm="account:audit_submit"
              >
                通过
              </PermButton>
              <PermButton onClick={() => handleChangePass(false)} perm="account:audit_submit">
                不通过
              </PermButton>
            </div>
          )}
        </Spin>
      </Drawer>
      <ImagePreview src={previewImageUrl} onClose={() => setPreviewImageUrl('')} />
    </>
  );
}
