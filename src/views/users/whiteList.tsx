import { userApi as api } from '@app/api';
import { ITableProps, IBaseProps, CommonObject } from '@app/types';
import {Table, BaseComponent, Drawer, A} from '@components/common';
import Form from '@components/business/whiteListForm';
import { connectAll as connect } from '@utils/connect';
import {Button, Col, Icon, Row, Input, Tooltip, Divider, Modal, message} from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import { VirtualUsersRecord, VirtualUsersAllData } from './users';
import {setConfig} from "@action/config";
import {requirePerm} from "@utils/utils";

interface State {
  keyword: string;
  form: {
    visible: boolean;
    key: number;
    title: string;
    scenes: string,
    isEdit: boolean,
    account_id: string,
    nick_name:string,
    chao_id: string
  };
};

type Props = IBaseProps<ITableProps<VirtualUsersRecord, VirtualUsersAllData>>;

class VirtualAccount extends BaseComponent<
  ITableProps<VirtualUsersRecord, VirtualUsersAllData>,
  State
> {
  constructor(props: Props) {
    super(props);
    this.state = {
      keyword: '',
      form: {
        visible: false,
        key: Date.now(),
        title: '',
        isEdit: false,
        scenes: '',
        account_id: '',
        nick_name:'',
        chao_id: '',
      },
    };
  }

  componentDidMount() {
    this.setMenu();
    this.getData({ current: 1, size: 10 });
  }
  getData = (overlap: CommonObject = {}, filter = this.getFilter()) => {
    this.dispatchTable('getWhiteList', 'list', { ...filter, ...overlap });
  };

  getFilter = () => {
    const { current, size } = this.props.tableList;
    const {  keyword } = this.state;
    return {
      keyword,
      current,
      size,
    };
  };

  delList = (record:{id:number}) => {
    Modal.confirm({
      title: (
          <p>
            确认删除吗？
          </p>
      ),
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        api.deleteWhiteList({id:record.id})
            .then(() => {
              message.success('操作成功');
              this.props.dispatch(setConfig({ loading: false }));
              this.getData();
            })
            .catch(() => this.props.dispatch(setConfig({ loading: false })));
      },
    });
  };
  editList = (record:any) => {
    this.setState({
      form: {
        visible: true,
        key: Date.now(),
        title: '编辑机审白名单',
        isEdit: true,
        scenes: record.scenes_list,
        account_id: record.account_id,
        chao_id: record.chao_id,
        nick_name: record.nick_name,
      },
    });
  }

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '小潮号',
        key: 'chao_id',
        dataIndex: 'chao_id',
        width: 120,
      },
      {
        title: '用户昵称',
        key: 'nick_name',
        dataIndex: 'nick_name',
        width: 120,
      },
      {
        title: '账号类型',
        key: 'cert_type',
        dataIndex: 'cert_type',
        render: (text: any, record: any, i: number) => <span>{['潮客', '潮鸣号',  '潮鸣号'][text]}</span>,
        width: 120,
      },
      {
        title: '不检测场景',
        key: 'scenes_list',
        dataIndex: 'scenes_list',
        render: (text: [string], record: any, i: number) => <span>
          { text?.map(item =>{
           return ['','','发布内容-图片信息','编辑账号-文字信息','编辑账号-图片信息'][Number(item)]
          }).join(',')
          }
        </span>,
      },
      {
        title: '最后操作人',
        key: 'updated_by',
        dataIndex: 'updated_by',
        width: 180,
      },
      {
        title: '最后编辑时间',
        key: 'updated_at',
        dataIndex: 'updated_at',
        render: (text: any) => (<span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>),
        width: 180,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => {
          return <span>
             {requirePerm(
                 this,
                 'media:review:white:save'
             )(
            <A onClick={() => this.editList(record)}>编辑</A>)}
            <Divider type="vertical" />
            {requirePerm(
                this,
                'media:review:white:delete'
            )(<A onClick={() => this.delList(record)}>删除</A>)}
          </span>
        },
        width: 100,
      }
    ];
  };


  handleKeywordChange = (e: any) => {
    this.setState({
      keyword: e.target.value,
    });
  };

  doSearch = () => {
    this.getData({current: 1});
  };

  closeDrawer = () => {
    this.setState({
      form: {
        ...this.state.form,
        visible: false,
      },
    });
  };

  showForm = (type: 0 | 1) => {
    this.setState({
      form: {
        ...this.state.form,
        visible: true,
        key: Date.now(),
        title: '创建机审白名单',
        isEdit: false,
        scenes: '',
        account_id: '',
        nick_name: '',
      },
    });
  };

  submitEnd = () => {
    this.getData();
    this.closeDrawer();
  };

  render() {
    const {  form } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12} />
          <Col span={12} className="layout-breadcrumb">
            {this.getCrumb()}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <Col span={12}>
              {this.requirePerm('media:review:white:save')(
                <Button onClick={this.showForm.bind(this, 0)} style={{ marginRight: 8 }}>
                  <Icon type="plus-circle" /> 添加机审白名单
                </Button>
              )}
              <Tooltip title="在客户端及创作者平台发布内容/编辑账号信息等操作时，可跳过机审检测，直接提交到后台等待人工审核">
                <Icon type="question-circle" />
              </Tooltip>
            </Col>
            {/*<Col span={12} style={{ textAlign: 'right' }}>*/}
            {/*  <Input*/}
            {/*    onChange={this.handleKeywordChange}*/}
            {/*    style={{ marginRight: 8, width: 160 }}*/}
            {/*    placeholder="请输入搜索内容"*/}
            {/*    onKeyPress={this.handleKey}*/}
            {/*  />*/}
            {/*  <Button onClick={this.doSearch}>*/}
            {/*    <Icon type="search" /> 搜索*/}
            {/*  </Button>*/}
            {/*</Col>*/}
          </Row>
          <Table
            func="getWhiteList"
            index="list"
            filter={this.getFilter()}
            rowKey="id"
            columns={this.getColumns()}
            pagination={true}
          />
          <Drawer
            visible={form.visible}
            skey={form.key}
            title={form.title}
            onClose={this.closeDrawer}
            onOk={this.handleSubmitForm.bind(this, 'form')}
          >
            <Form
              wrappedComponentRef={this.setFormRef.bind(this, 'form')}
              formContent={form}
              onEnd={this.submitEnd}
            />
          </Drawer>
        </div>
      </>
    );
  }
}

export default withRouter(connect<VirtualUsersRecord, VirtualUsersAllData>()(VirtualAccount));
