import { Button, DatePicker, Form, Icon, Input, InputNumber, Modal, Radio, Spin, Tooltip, message } from 'antd'
import React, { forwardRef, useImperativeHandle, useState } from 'react'

import { communityApi } from '@app/api';
import moment from 'moment';

const PullDataModal = (props: any, ref: any) => {

  const [loading, setLoading] = useState(false)
  const { getFieldDecorator } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true)
        const parmas = {
          ids: props.formContent.id,
          start: moment(values.time[0]).format('YYYYMMDDHHmmss'),
          end: moment(values.time[1]).format('YYYYMMDDHHmmss')
        }        
        communityApi.spiderPullData(parmas).then((res: any) => {
          message.success('拉取成功');
          setLoading(false)
          props.onOk && props.onOk(res)
        }).catch(() => {
          message.error('操作失败');
          setLoading(false)
        })
      } else {
        message.error('请检查表单内容');
        setLoading(false)
      }
    });
  }

  return <Modal
    width={400}
    visible={props.visible}
    title="拉取数据"
    key={props.skey}
    onCancel={() => { if (!loading) { props.onCancel && props.onCancel() } }}
    onOk={handleSubmit}
    maskClosable={false}
    destroyOnClose={true}
  >
    <Spin spinning={false}>
      <Form>
        <Form.Item label="选择数据的时间范围">
        {getFieldDecorator('time', {
            // initialValue: ,
            rules: [
              {
                required: true,
                message: '请选择数据的时间范围',
              },
            ],
          })(<DatePicker.RangePicker showTime />)}
        </Form.Item>
      </Form>
    </Spin>
  </Modal>

}

export default Form.create<any>({ name: 'PullDataModal' })(forwardRef<any, any>(PullDataModal));
