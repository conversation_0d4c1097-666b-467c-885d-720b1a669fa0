import { getTableList } from '@action/tableList';
import { userApi as api } from '@app/api';
import { CommonObject } from '@app/types';
import MessageForm from '@components/business/userMessageForm';
import { A, Drawer, Table } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setLoading, setMenu } from '@utils/utils';
import { Button, Col, Divider, Form, Icon, message, Modal, Row, Radio } from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';

enum TABSTATE {
  USERMESSAGE = 1,
  SYSTEMMESSAGE,
}

@(withRouter as any)
@connect
class UserMessageList extends React.Component<any, any> {
  formRef: any;

  constructor(props: any) {
    super(props);
    this.state = {
      type: TABSTATE.USERMESSAGE,
      loading: false,
      visible: false,
      key: Date.now(),
      detail: {
        visible: false,
        key: Date.now(),
      },
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    setMenu(this);
  }

  handleTypeChange = (e: any) => {
    if (e.target.value === this.state.type) {
      return;
    }
    this.setState(
      {
        type: e.target.value,
      },
      () => {
        // 切换
        if (e.target.value === TABSTATE.USERMESSAGE) {
          // this.getData();
        } else if (e.target.value === TABSTATE.SYSTEMMESSAGE) {
          // this.getData1();
        }
        console.log(e.target);
      }
    );
  };

  getData = (overlap: CommonObject = {}) => {
    const { current, size } = this.props.tableList;
    this.props.dispatch(
      getTableList('getUserMessageList', 'notice_list', { current, size, ...overlap })
    );
  };

  getColumns = () => {
    return [
      {
        title: '消息标题',
        key: 'notice_title',
        dataIndex: 'notice_title',
        width: 120,
      },
      {
        title: '消息内容',
        key: 'body',
        dataIndex: 'body',
      },
      {
        title: '图片',
        key: 'image_url',
        dataIndex: 'image_url',
        render: (text: any) =>
          text ? (
            // eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions
            <img onClick={() => this.showImg(text)} src={text} className="list-pic" />
          ) : (
            <span>无配图</span>
          ),
        width: 130,
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'status',
        render: (text: any) => <span>{['成功', '失败', '部分成功'][text]}</span>,
        width: 90,
      },
      {
        title: '创建人',
        key: 'created_by',
        dataIndex: 'created_by',
        width: 120,
      },
      {
        title: '发布时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 180,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <span>
            {requirePerm(
              this,
              'notice:detail'
            )(<A onClick={() => this.showDetail(record)}>查看详情</A>)}
            <Divider type="vertical" />
            {requirePerm(
              this,
              'notice:deleted'
            )(<A onClick={() => this.deleteRecord(record)}>删除</A>)}
          </span>
        ),
        width: 120,
      },
    ];
  };

  showImg = (url: any) => {
    Modal.success({
      title: <img src={url} style={{ width: '100%' }} />,
      icon: null,
      width: 550,
      okText: '关闭',
    });
  };

  deleteRecord = (record: any) => {
    Modal.confirm({
      title: '确认删除这条消息吗？',
      onOk: () => {
        setLoading(this, true);
        api
          .deleteUserMessage({ id: record.id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };

  showDetail = (record: any) => {
    setLoading(this, true);
    api
      .getUserMessageDetail({ id: record.id })
      .then((r: any) => {
        this.setState({
          detail: {
            visible: true,
            key: Date.now(),
            ...r.data.notice,
          },
        });
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  closeDetail = () => {
    this.setState({ detail: { ...this.state.detail, visible: false } });
  };

  createMessage = () => {
    this.setState({
      visible: true,
      key: Date.now(),
    });
  };

  closeDrawer = () => {
    this.setState({
      visible: false,
    });
    this.getData();
  };

  submitEnd = () => {
    this.getData({ current: 1 });
    this.setState({
      visible: false,
      loading: false,
    });
  };

  render() {
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const { loading, visible, detail, key, type } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            {/* <Radio.Group
              value={type}
              style={{ marginRight: 8 }}
              onChange={this.handleTypeChange}
              buttonStyle="solid"
            >
              <Radio.Button value={TABSTATE.USERMESSAGE}>用户消息通知</Radio.Button>
              <Radio.Button value={TABSTATE.SYSTEMMESSAGE}>系统消息配置</Radio.Button>
            </Radio.Group> */}
            {type === TABSTATE.USERMESSAGE &&
              requirePerm(
                this,
                'notice:create'
              )(
                <Button onClick={this.createMessage}>
                  <Icon type="plus-circle" /> 新建消息
                </Button>
              )}
            {type === TABSTATE.SYSTEMMESSAGE &&
              requirePerm(
                this,
                'notice:create'
              )(
                <Button onClick={this.createMessage} style={{ marginRight: 8 }}>
                  <Icon type="plus-circle" /> 新建系统消息
                </Button>
              )}
            {type === TABSTATE.SYSTEMMESSAGE &&
              requirePerm(
                this,
                'notice:create'
              )(
                <Button onClick={this.createMessage}>
                  <Icon type="plus-circle" /> 通用动态消息键值对配置
                </Button>
              )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getUserMessageList"
            index="notice_list"
            filter={{}}
            columns={this.getColumns()}
            rowKey="id"
            pagination={true}
          />
          <Modal
            visible={detail.visible}
            title="查看消息详情"
            key={detail.key}
            onCancel={this.closeDetail}
            onOk={this.closeDetail}
            width={600}
          >
            <Form {...formLayout}>
              <Form.Item label="消息标题">{detail.notice_title}</Form.Item>
              <Form.Item label="消息内容">{detail.body}</Form.Item>
              <Form.Item label="消息配图">
                {detail.image_url ? (
                  <img src={detail.image_url} style={{ width: '100%' }} />
                ) : (
                  '无配图'
                )}
              </Form.Item>
              <Form.Item label="跳转链接">
                {detail.url ? (
                  <a href={detail.url} target="_blank" rel="noreferrer">
                    {detail.url}
                  </a>
                ) : (
                  '无'
                )}
              </Form.Item>
              {detail.select_type === 0
                ? [
                    <Form.Item label="发送成功用户" key="1">
                      {detail.success_accounts}{detail.chao_id ? `（${detail.chao_id}）` : ''}
                    </Form.Item>,
                    <Form.Item label="发送失败用户" key="2">
                      {detail.fail_accounts}{detail.chao_id ? `（${detail.chao_id}）` : ''}
                    </Form.Item>,
                  ]
                : [
                    <Form.Item label="发送用户" key="1">
                      <span style={{ wordBreak: 'break-all' }}>
                        <a href={detail.orginal_accounts_file} target="_blank" rel="noreferrer">
                          {detail.orginal_accounts_file}
                        </a>
                      </span>
                    </Form.Item>,
                    <Form.Item label="发送失败用户" key="2">
                      <span style={{ wordBreak: 'break-all' }}>
                        <a href={detail.fail_accounts_file} target="_blank" rel="noreferrer">
                          {detail.fail_accounts_file}
                        </a>
                      </span>
                    </Form.Item>,
                  ]}
            </Form>
          </Modal>
          <Drawer
            title="新建用户消息"
            visible={visible}
            skey={key}
            onClose={this.closeDrawer}
            onOk={() => this.formRef.doSubmit()}
          >
            <MessageForm
              // eslint-disable-next-line no-shadow
              setLoading={(loading: boolean) => this.setState({ loading })}
              onEnd={this.submitEnd}
              // eslint-disable-next-line no-return-assign
              wrappedComponentRef={(v: any) => (this.formRef = v)}
            />
          </Drawer>
        </div>
      </>
    );
  }
}

export default UserMessageList;
