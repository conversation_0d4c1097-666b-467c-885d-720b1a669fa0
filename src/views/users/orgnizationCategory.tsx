import { getTableList, setTableList } from '@app/action/tableList';
import { userApi as api } from '@app/api';
import { OrderColumn, Table } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setMenu } from '@utils/utils';
import { Button, Col, Form, Icon, Input, InputNumber, message, Modal, Radio, Row, Select } from 'antd';
import React, { Component } from 'react';
import { withRouter } from 'react-router';
import moment from 'moment';
import { CommonObject } from '@app/types';
import { PermA } from '@app/components/permItems';
import { setTableCache } from '@app/action/tableCache';
import connectAll from '@app/utils/connectAll';
import TmhClassExportModal from './tmhClassExportModal';

type Api = 'createOrgCategory' | 'updateOrgCategory';

@(withRouter as any)
@connectAll
class OrgCategory extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      name: '',
      mtitle: '',
      loading: false,
      visible: false,
      key: Date.now(),
      type: 1,
      cityList: [],
      typeFilter: 0,
      areaId: '',
      exportModal: {
        visible: false,
        type: 0
      }
    };
  }

  componentDidMount() {
    if (this.props.tableCache?.beforeRoute === this.props.match?.path && this.props.tableCache.records.length > 0) {
      this.getData({ current: this.props.tableCache.current, size: this.props.tableCache.size })
    } else {
      this.getData({ current: 1, size: 10 });
    }
    setMenu(this);
    this.getAreaList();
  }

  getData = (overlap: CommonObject = {}) => {
    this.props.dispatch(
      getTableList('getOrgCategoryList', 'tmh_class_list', { ...this.getFilter(), ...overlap })
    );
  };

  getFilter() {
    const { current, size } = this.props.tableList;
    return { type: this.state.typeFilter, current, size };
  }

  getAreaList = () => {
    api.getAreaList().then((res: any) => {
      let cityList: any[] = [];
      Promise.all(res.data.area_list.map((v: any) => api.getAreaList({ parent_id: v.id }))).then(
        (allCityList) => {
          allCityList.forEach((c: any) => {
            cityList = [
              ...cityList,
              ...c.data.area_list.map((s: any) => ({ id: s.id, name: s.name })),
            ];
          });
          this.setState({ cityList });
        }
      );
    });
  };

  order = (record: any, current: Number, type: Number) => {
    this.setState({ loading: true });
    api.orderOrgCategory({ id: record.id, current: current, offset: type })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setState({ loading: false });
      })
      .catch(() => this.setState({ loading: false }));
  }

  getColumns = () => {
    const { current, size, total, allData } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;

    if (this.state.typeFilter == 0) {
      return [
        {
          title: '排序',
          key: 'order',
          render: (text: any, record: any, i: number) => {
            return <OrderColumn
              pos={getSeq(i)}
              start={1}
              end={allData.on_show_count}
              perm="tmh_class:order"
              disableUp={!record.enabled}
              disableDown={!record.enabled}
              onUp={() => this.order(record, getSeq(i), -1)}
              onDown={() => this.order(record, getSeq(i), 1)}
            />
          },
          width: 80,
        },
        {
          title: '序号',
          key: 'seq',
          render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
          width: 60
        },
        {
          title: '分类名称',
          dataIndex: 'name',
          width: 150
        },
        {
          title: '账号数',
          dataIndex: 'account_count',
          width: 120,
          render: (text: any, record: any) => (<a onClick={() => {
            this.props.dispatch(
              setTableCache({
                beforeRoute: this.props.match.path,
                ...this.props.tableList,
              })
            )
            this.props.history.push(
              encodeURI(`/view/orgAccountMgrList/${record.id}/${record.name}`)
            );
          }}>{text || 0}</a>)
        },
        {
          title: '状态',
          dataIndex: 'enabled',
          render: (text: number) => text ? '显示中' : '隐藏',
          width: 80
        },
        {
          title: '最后操作人',
          dataIndex: 'updated_by',
          // width: 120,
        },
        {
          title: '最后操作时间',
          dataIndex: 'updated_at',
          render: (text: number) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
          width: 170
        },
        {
          title: '操作',
          dataIndex: 'op',
          render: (text: any, record: any, i: number) => {
            return <>
              {/* <PermA perm='tmh_class:update' style={{ marginRight: 5 }} onClick={() => this.editRecord({ ...record, area_type: 0 })}>编辑</PermA> */}
              {record.enabled && <PermA perm='tmh_class:order' style={{ marginRight: 5 }} onClick={() => this.handelSort(record, getSeq(i))}>排序</PermA>}
              <PermA perm='tmh_class:update_status' style={{ marginRight: 5 }} onClick={() => this.updateEnableStatus(record)}>{record.enabled ? '设为隐藏' : '设为显示'}</PermA>
              <PermA perm='tmh_class:delete' onClick={() => this.deleteRecord(record)}>删除</PermA>

            </>
          }
        }
      ];
    } else {
      return [
        {
          title: '序号',
          key: 'seq',
          render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        },
        {
          title: '分类名称',
          dataIndex: 'name',
          render: (text: any, record: any) => <a href={`/view/orgAccountList?class_id=${record.id}`}>{text}</a>
        },
        {
          title: '创建时间',
          dataIndex: 'created_at',
          render: (text: number) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
          title: '创建人',
          dataIndex: 'created_by',
        },
      ];
    }
  };

  editRecord = (record: any = {}) => {
    const { total } = this.props.tableList;
    // if (!record.id && total >= 10) {
    //   message.error('分类数量已达上限10个，请先删除再添加');
    //   return;
    // }
    this.setState({
      visible: true,
      key: Date.now(),
      id: record.id || '',
      name: record.name || '',
      mtitle: record.id ? '编辑分类' : '添加分类',
      type: record.area_type,
      areaId: ''
    });
  };

  updateEnableStatus = (record: any) => {
    Modal.confirm({
      title: record.enabled ? '将该分类从客户端的潮鸣号矩阵分类页面隐藏' : '将该分类显示到客户端的潮鸣号矩阵页面',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        this.setState({ loading: true });
        api.updateOrgCategoryStatus({ id: record.id, status: !record.enabled })
          .then(() => {
            message.success('操作成功');
            this.getData();
            this.setState({ loading: false });
          })
          .catch(() => this.setState({ loading: false }));
      },
      onCancel: () => {
      },
    });

  }

  deleteRecord = (record: any) => {
    Modal.confirm({
      title: '删除后，原本在该分类下的潮鸣号将变为“无分类”',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        this.setState({ loading: true });
        api.deleteOrgCategory({ id: record.id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            this.setState({ loading: false });
          })
          .catch(() => this.setState({ loading: false }));
      },
      onCancel: () => {
      },
    });
  }

  // 排序
  handelSort = (record: any, pos: any) => {
    const posChange = (value: number | undefined) => {
      pos = value;
    };
    // if (param.keyword || param.status) {
    //   newSort = undefined
    // }
    Modal.confirm({
      title: `调整排序`,
      icon: null,
      content: (
        <div>
          <InputNumber
            placeholder="请输入修改序号"
            min={1}
            max={this.props.tableList.total}
            onChange={posChange}
            defaultValue={pos}
            style={{ width: '100%' }}
            precision={0}
          />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!pos) {
          message.error('请填写序号');
          return;
        }
        api
          .updateOrgCategory({
            id: record.id,
            sort_number: pos,
            type: 0,
          })
          .then(() => {
            message.success('操作成功');
            this.getData();
            closeFunc();
          });
      },
    });
  };

  handleSubmit = () => {
    const { id, name, type, areaId } = this.state;
    if (type === 0 && name === '') {
      message.error('请填写分类名称');
      return;
    }
    if (type === 0 && name.length > 10) {
      message.error('分类名称不能多于4个字');
      return;
    }
    if (type === 1 && !areaId) {
      message.error('请选择城市');
      return;
    }
    let func = 'createOrgCategory';
    const body: any = { type };
    if (id) {
      body.id = id;
      func = 'updateOrgCategory';
    }
    if (type === 1) {
      body.area_id = areaId;
    } else {
      body.name = name;
    }
    this.setState({ loading: true });
    api[func as Api](body)
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setState({ loading: false, visible: false });
      })
      .catch(() => this.setState({ loading: false }));
  };

  typeFilterChange = (e: any) => {
    this.setState(
      {
        typeFilter: e.target.value,
      },
      () => this.getData({ current: 1 })
    );
  };

  render() {
    const { name, visible, mtitle, loading, key, type, cityList, areaId, typeFilter } = this.state;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            <Radio.Group defaultValue={typeFilter} buttonStyle="solid" onChange={this.typeFilterChange}>
              <Radio.Button value={0}>内容分类</Radio.Button>
              <Radio.Button value={1}>地市分类</Radio.Button>
            </Radio.Group>
            {typeFilter == 0 && requirePerm(
              this,
              'tmh_class:create:0'
            )(
              <Button
                onClick={() => this.editRecord({ area_type: 0 })}
                style={{ marginRight: 8, marginLeft: 8 }}
              >
                <Icon type="plus-circle" /> 添加内容分类
              </Button>
            )}
            {typeFilter == 1 && requirePerm(
              this,
              'tmh_class:create:1'
            )(
              <Button onClick={() => this.editRecord({ area_type: 1 })}
                style={{ marginRight: 8, marginLeft: 8 }}>
                <Icon type="plus-circle" /> 添加地市分类
              </Button>
            )}
            {/* {requirePerm(
              this,
              ''
            )(
              <Button onClick={() => {
                this.setState({
                  ...this.state,
                  exportModal: {
                    key: Date.now(),
                    visible: true,
                    type: typeFilter
                  }
                })
              }}>
                批量导入数据
              </Button>
            )} */}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getOrgCategoryList"
            index="tmh_class_list"
            rowKey="id"
            filter={{ type: typeFilter }}
            columns={this.getColumns()}
            pagination={true}
          />

          <Modal
            title={mtitle}
            visible={visible}
            confirmLoading={loading}
            onCancel={() => this.setState({ visible: false })}
            onOk={this.handleSubmit}
            maskClosable={false}
          >
            <Form {...formLayout}>
              <Form.Item required={true} label="分类名称">
                {type === 0 ? (
                  <Input
                    value={name}
                    placeholder="请填写名称"
                    maxLength={4}
                    onChange={(e: any) => this.setState({ name: e.target.value?.trim() })}
                  />
                ) : (
                  <Select
                    value={areaId}
                    onChange={(value: any) => this.setState({ areaId: value })}
                  >
                    <Select.Option value="" disabled>
                      选择城市
                    </Select.Option>
                    {cityList.map((v: any) => (
                      <Select.Option value={v.id} key={v.id}>
                        {v.name}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Form>
          </Modal>

          <div key={this.state.exportModal?.key}>
            <TmhClassExportModal
              {...this.state.exportModal}
              onCancel={() => { this.setState({ ...this.state, exportModal: { ...this.state.exportModal, visible: false } }) }}
            >

            </TmhClassExportModal>
          </div>
        </div>
      </>
    );
  }
}

export default OrgCategory;
