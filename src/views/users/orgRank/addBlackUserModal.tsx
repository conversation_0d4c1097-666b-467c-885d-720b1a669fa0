import { Button, Form, Icon, InputNumber, Modal, Select, Spin, Tooltip, message } from 'antd'
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react'
import _, { set } from "lodash";
import { userApi, communityApi } from '@app/api';
import '@app/assets/index.scss';

const AddBlackUserModal = (props: any, ref: any) => {

  const { getFieldDecorator } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const [loading, setLoading] = useState(false)
  const [accountOptions, setAccountOptions] = useState([])
  // const [reporter, setReporter] = useState(undefined)

  useEffect(() => {
    if (props.visible) {
      // setReporter(undefined)
      handleAccountSearch('')
    }
    // if (!!props.formContent && !!props.formContent.reporter_id) {
    //   setReporter(props.formContent.reporter_id)
    //   handleAccountSearch(props.formContent.reporter_id)
    // } else {
    //   setReporter(undefined)
    //   handleAccountSearch('')
    // }
  }, [props.visible])


  const handleSubmit = (e: any) => {
    e.preventDefault();

    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true)
        userApi.addBlacklistRecord({ ...values })
          .then((res: any) => {
            message.success('新增成功');
            setLoading(false)
            props.onEnd && props.onEnd()
          }).catch(() => {
            // message.error('添加失败');
            setLoading(false)
          })
      } else {
        message.error('请检查表单内容');
        setLoading(false)
      }
    });
  }

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val) {
      setAccountOptions([])
      return
    }
    // type: 1, current: 1, size: 50, biz_type: props.formContent?.biz_type,
    communityApi.recommendAccount_Search({ keyword: val })
      .then((res) => {
        setAccountOptions(res?.data?.list || [])
      })
      .catch(() => {
      })
  }, 500)

  const handleAccountChange = (val: any) => {
    // setReporter(val)
  }

  return <Modal
    width={500}
    visible={props.visible}
    title="添加黑名单用户"
    key={props.skey}
    onCancel={() => { if (!loading) { props.onCancel && props.onCancel() } }}
    onOk={handleSubmit}
    maskClosable={false}
    destroyOnClose={true}
    confirmLoading={loading}
  >
    <Spin spinning={loading}>
      <Form {...formLayout}>
        <Form.Item label="用户">
          {getFieldDecorator('account_id', {
            initialValue: undefined,
            rules: [{ required: true, message: '请选择用户', }],
          })(
            <Select
              // value={reporter}
              placeholder="输入用户昵称或小潮号"
              onSearch={handleAccountSearch}
              showSearch
              filterOption={false}
            >
              {accountOptions.map((d: any) => (
                <Select.Option key={d.id} value={d.id}>
                  {['潮客 - ', '潮鸣号 - ', '潮鸣号 - '][d.cert_type] + d.nick_name} | 小潮号：{d.chao_id}</Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>
      </Form>

    </Spin>
  </Modal>

}

export default Form.create<any>({ name: 'AddBlackUserModal' })(forwardRef<any, any>(AddBlackUserModal));
