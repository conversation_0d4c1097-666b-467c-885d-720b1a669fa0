/* eslint-disable no-nested-ternary */
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { Table } from '@components/common';
import { getCrumb } from '@app/utils/utils';
import { Row, Col, Button } from 'antd';
import { getTableList } from '@app/action/tableList';
import moment from 'moment';

export default function ServiceKeywords(props: any) {
  const dispatch = useDispatch();
  const { type } = useRouteMatch<{ type: string }>().params;
  const history = useHistory();

  const getList = () => {
    dispatch(getTableList('getRankLog', 'list', { type_id: type, current: 1, size: 10 }));
  };

  const columns = [
    {
      title: '时间',
      dataIndex: 'created_at',
      render: (text: number) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 180,
    },
    {
      title: '操作人',
      dataIndex: 'opt_admin_user_name',
      width: 120,
    },
    {
      title: '内容',
      dataIndex: 'opt_content',
      // eslint-disable-next-line react/no-danger
      render: (text: any) => <span dangerouslySetInnerHTML={{ __html: text }} />,
    },
  ];

  useEffect(() => {
    getList();
  }, []);

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button onClick={() => history.push(`/view/singleRank/${type}`)}>返回</Button>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          columns={columns}
          func="getRankLog"
          index="list"
          filter={{ type_id: type }}
          rowKey="id"
          pagination
        />
      </div>
    </>
  );
}
