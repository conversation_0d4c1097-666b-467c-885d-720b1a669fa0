/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, useMemo } from 'react';
import { useDispatch, useStore, useSelector } from 'react-redux';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { A, PreviewMCN, SearchAndInput } from '@components/common';
import { getCrumb } from '@app/utils/utils';
import {
  Row,
  Col,
  Button,
  message,
  Icon,
  Modal,
  Table,
  Form,
  Select,
  Tooltip,
  InputNumber,
  Radio,
} from 'antd';
import { getTableList, clearTableList } from '@app/action/tableList';
import { userApi as api } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@app/components/permItems';

export default function ServiceKeywords(props: any) {
  const [form, setForm] = useState(
    () =>
      ({
        visible: false,
        key: Date.now(),
      } as any)
  );
  const [previewForm, setPreview] = useState(
    () =>
      ({
        visible: false,
        key: Date.now() + 1,
      } as any)
  );
  const [iForm, setIForm] = useState(
    () =>
      ({
        visible: false,
        key: Date.now() + 2,
      } as any)
  );
  const dispatch = useDispatch();
  const { type } = useRouteMatch<{ type: string }>().params;
  const numberName = { '1': '传播力指数', '2': '发稿量', '3': '热度值' }[type];
  const history = useHistory();
  const { loading, run } = useXHR();

  const {
    current_begin_date: currentDate = '',
    publish_status: published = true,
    name = '',
    markable = false,
    rank_dates: rankDates = [],
    ranks = [],
  } = useSelector((state: any) => state.tableList.allData);

  const getList = (cd: any = currentDate) => {
    dispatch(getTableList('getRankDetailList', 'ranks', { type_id: type, begin_date: cd }));
  };

  const formChange = (key: string, value: any) => {
    setForm({
      ...form,
      [key]: value,
    });
  };

  const editRecord = (formType: 'value' | 'rule', record: any) => {
    setForm({
      visible: true,
      key: Date.now(),
      type: formType,
      id: record.id,
      value: record.current_value,
      title: formType === 'value' ? '编辑数据' : '',
    });
  };

  const deleteRank = (record: any) => {
    Modal.confirm({
      title: '确定删除吗',
      onOk: () => {
        run(api.deleteRankItem, { id: record.id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const preview = (article: any) => {
    setPreview({
      visible: true,
      key: Date.now(),
      data: article
    });
  };

  const shortVideoColumns = [
    {
      key: 'seq',
      render: (text: any, record: any, i: number) => i + 1,
      width: 70,
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 70,
    },
    {
      title: '短视频',
      key: 'video',
      render: (record: any) => (
        <A onClick={() => preview(record.article)}>
          {record.article && record.article.list_title}
        </A>
      ),
    },
    {
      title: '潮鸣号',
      dataIndex: 'nick_name',
    },
    {
      title: '播放量',
      dataIndex: 'current_value',
      render: (text: any, record: any) => (
        <span>
          {text}
          <PermA
            perm="tmh_rank:update_value"
            disabled={published}
            onClick={() => editRecord('value', record)}
            style={{ marginLeft: 8 }}
          >
            <Icon type="form" />
          </PermA>
        </span>
      ),
      width: 200,
    },
    {
      title: '操作',
      key: 'op',
      render: (record: any) => (
        <span>
          <PermA
            perm="tmh_rank:del_rank_item"
            disabled={published}
            onClick={() => deleteRank(record)}
          >
            删除
          </PermA>
        </span>
      ),
      width: 70,
    },
  ];

  const columns = [
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => i + 1,
      width: 70,
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 70,
    },
    {
      title: '潮鸣号',
      dataIndex: 'nick_name',
    },
    {
      title: type === '2' ? '发稿量' : '潮鸣号指数',
      dataIndex: 'current_value',
      render: (text: any, record: any) => (
        <span>
          {text}
          <PermA
            perm="tmh_rank:update_value"
            disabled={published}
            onClick={() => editRecord('value', record)}
            style={{ marginLeft: 8 }}
          >
            <Icon type="form" />
          </PermA>
        </span>
      ),
    },
    {
      title: (
        <span>
          排名变化&nbsp;&nbsp;
          <Tooltip title="显示当前周期排名与上一周期相比较的排名变化">
            <Icon type="question-circle" />
          </Tooltip>
        </span>
      ),
      dataIndex: 'changed_rank',
      render: (text: any, record: any) =>
        markable ? (
          record.new_on_rank ? (
            'new'
          ) : (
            <span>
              <Icon type={text === 0 ? 'minus-circle' : text > 0 ? 'rise' : 'fall'} />
              &nbsp;{text !== 0 ? Math.abs(text) : ''}
            </span>
          )
        ) : (
          ''
        ),
    },
    {
      title: '操作',
      key: 'op',
      render: (record: any) => (
        <span>
          <PermA
            disabled={published}
            perm="tmh_rank:del_rank_item"
            onClick={() => deleteRank(record)}
          >
            删除
          </PermA>
        </span>
      ),
      width: 70,
    },
  ];

  useEffect(() => {
    getList();
  }, []);

  const submitKeyword = () => {
    const service = api.updateRankValue;
    const body: any = { id: form.id, value: form.value };
    run(service, body).then(() => {
      message.success('操作成功');
      setForm({ ...form, visible: false });
      getList();
    });
  };

  const submitInsert = () => {
    if (iForm.value.length === 0 || !iForm.cv) {
      message.error('请填写所有内容');
      return;
    }
    const body: any = {
      begin_date: currentDate,
      type_id: type,
      account_id: iForm.value[0],
      value: iForm.cv,
    };
    if (type === '3') {
      // eslint-disable-next-line prefer-destructuring
      body.article_id = iForm.articleId[0];
    }
    run(api.insertRankItem, body).then(() => {
      getList(currentDate);
      setIForm({ ...iForm, visible: false });
    });
  };

  const showOperateLog = () => {
    dispatch(clearTableList());
    history.push(`/view/ranklog/${type}`);
  };
  const insertRecord = () => {
    setIForm({
      visible: true,
      key: Date.now(),
      value: [],
      cv: 0,
      title: type === '3' ? '插入短视频' : '插入潮鸣号',
      searchType: '2',
    });
  };
  const doPublish = () => {
    Modal.confirm({
      title: '确定发布？',
      onOk: () => {
        run(api.publishRank, { begin_date: currentDate, type_id: type }, true).then(() => {
          getList();
        });
      },
    });
  };

  const tmhcolumns = [
    {
      title: '潮鸣号名称',
      dataIndex: 'name',
    },
  ];

  const svcolumns = [
    {
      title: '短视频',
      dataIndex: 'list_title',
    },
    {
      title: '潮鸣号名称',
      dataIndex: 'name',
    },
  ];

  const searchValueChange = ({ channelArticles }: any) => {
    setIForm({
      ...iForm,
      value: channelArticles.length > 0 ? [channelArticles[0].account_id] : [],
      articleId: channelArticles.length > 0 ? [channelArticles[0].article_id] : [],
    });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button onClick={() => history.push('/view/tmhRankMgr')}>返回</Button>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={12}>
            <Select
              value={currentDate}
              style={{ width: 140 }}
              onChange={(value: string) => getList(value)}
            >
              {rankDates.map((v: any) => (
                <Select.Option value={v.begin_date} key={v.begin_date}>
                  {v.begin_date}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col span={12} style={{ textAlign: 'right' }}>
            <PermButton
              perm="tmh_rank_log:view"
              type="primary"
              style={{ marginRight: 8 }}
              onClick={showOperateLog}
            >
              操作日志
            </PermButton>
            <PermButton
              perm="tmh_rank:insert_rank_item"
              type="primary"
              style={{ marginRight: 8 }}
              disabled={published}
              onClick={insertRecord}
            >
              {type === '3' ? '插入短视频' : '插入潮鸣号'}
            </PermButton>
            <PermButton
              perm="tmh_rank:publish_rank"
              type="primary"
              disabled={published}
              onClick={doPublish}
            >
              发布最新榜单
            </PermButton>
          </Col>
        </Row>
        <Table
          columns={type === '3' ? shortVideoColumns : columns}
          rowKey="id"
          pagination={false}
          dataSource={ranks}
        />
        <Modal
          visible={form.visible}
          title={form.title}
          onCancel={() => setForm((s: any) => ({ ...s, visible: false }))}
          onOk={submitKeyword}
          confirmLoading={loading}
          key={form.key}
          width={600}
        >
          {form.type === 'value' && (
            <Form labelCol={{ span: 4 }} wrapperCol={{ span: 19 }}>
              <Form.Item label={numberName} required>
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  step={type === '1' ? 0.01 : 1}
                  value={form.value}
                  onChange={(e: any) => formChange('value', e || 0)}
                  placeholder={`请输入${numberName}`}
                />
              </Form.Item>
            </Form>
          )}
        </Modal>

        <Modal
          visible={iForm.visible}
          title={iForm.title}
          onCancel={() => setIForm((s: any) => ({ ...s, visible: false }))}
          onOk={submitInsert}
          confirmLoading={loading}
          key={iForm.key}
          width={600}
        >
          <Form labelCol={{ span: 4 }} wrapperCol={{ span: 19 }}>
            <Form.Item label="搜索类型">
              <Radio.Group
                value={iForm.searchType}
                onChange={(e) => setIForm((s: any) => ({ ...s, searchType: e.target.value }))}
              >
                <Radio value="1">搜索ID</Radio>
                <Radio value="2">搜索关键词</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item label={type === '3' ? '小视频' : '潮鸣号名称'} required>
              <SearchAndInput
                func="searchRankItem"
                max={1}
                columns={type === '3' ? svcolumns : tmhcolumns}
                body={{ type: type === '3' ? 2 : 1, search_type: iForm.searchType }}
                placeholder="输入内容搜索"
                selectOptionDisplay={(d) => {
                  return type === '3' ? `${d.name} - ${d.list_title}` : d.name;
                }}
                funcIndex="list"
                excludeIds={ranks.map((v: any) => v.id)}
                onChange={() => {}}
                triggerInitialValueChange={searchValueChange}
              />
            </Form.Item>
            <Form.Item label={numberName} required>
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                step={type === '1' ? 0.01 : 1}
                value={iForm.cv}
                onChange={(e: any) => setIForm({ ...iForm, cv: e || 0 })}
                placeholder={`请输入${numberName}`}
              />
            </Form.Item>
          </Form>
        </Modal>

        <PreviewMCN
          {...previewForm}
          onClose={() => setPreview({ ...previewForm, visible: false })}
        />
      </div>
    </>
  );
}
