import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector, useStore } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, A, OrderColumn } from '@components/common';
import { getCrumb, objectToQueryString, searchToObject, setMenuHook } from '@app/utils/utils';
import {
  Row,
  Col,
  Input,
  Divider,
  Switch,
  message,
  Icon,
  Modal,
  Form,
  Button,
  DatePicker,
  Select,
  TimePicker,
  Radio,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { userApi as api, userApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@app/components/permItems';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import CkExponentConfigDrawer from './ckExponentConfigDrawer';
import RankPublishConfigModal from './rankPublishConfigModal';
import UpdateRankModal from './updateRankModal';
import SendRewardsDrawer from './sendRewards'; // ✅ 导入上榜奖励抽屉组件
import ManualPublishingModal from './manualPublishingModal';

export default function NewCkRankList(props: any) {
  const {
    total,
    allData: {
      publish_switch = 0,
      month_publish_time = '',
      publish_time = '',
      month_publish_day = '1',
      publish_day = '1',
    },
  } = useSelector((state: any) => state.tableList);

  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const { loading, run } = useXHR();

  const [filter, setFilter] = useState<any>({
    category: parseInt(searchToObject().category ?? 0),
  });

  const [rank_dates, setRankDate] = useState([]);

  const [changeNameModal, setChangeNameModal] = useState<any>({
    visible: false,
    key: Date.now(),
  });

  const [exponentConfigDrawer, setExponentConfigDrawer] = useState<any>({
    visible: false,
  });

  const [updateDateModal, setUpdateDateModal] = useState<any>({
    visible: false,
    beginDate: undefined,
    key: Date.now(),
  });

  const [publishModal, setPublishModal] = useState<any>({
    key: Date.now() + 1,
    visible: false,
    publish_switch: true,
    month_publish_time: undefined,
    month_publish_day: '1',
    week_publish_day: '1',
    week_publish_time: undefined,
  });

  const [manualPublishingModal, setManualPublishingModal] = useState<any>({
    visible: false,
    key: Date.now() + 2,
    rankType: 0,
  });

  const [rewardModal, setRewardModal] = useState<{ visible: boolean }>({ visible: false });

  const getList = () => {
    dispatch(getTableList('getCkRankList', 'page', { ...filter }));
  };

  const editRecord = (type: 'name' | 'rule', record: any) => {
    setChangeNameModal({
      visible: true,
      key: Date.now(),
      type,
      id: record.id,
      name: record.type,
    });
  };

  const handleSort = (record: any, sort_type: 1 | 2) => {
    run(api.updateCkRankSort, { id: record.id, sort_type, category: filter.category }, true).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  const updateStatus = (record: any) => {
    run(api.updateCkRankStatus, { id: record.id, status: record.status == 1 ? 2 : 1 }, true).then(
      () => {
        message.success('操作成功');
        getList();
      }
    );
  };

  const columns = [
    {
      title: '排序',
      key: 'sort',
      render: (text: any, record: any, i: number) => (
        <OrderColumn
          perm="ranks_ck:sort"
          pos={i}
          start={0}
          end={total - 1}
          onUp={() => handleSort(record, 1)}
          onDown={() => handleSort(record, 2)}
        />
      ),
      width: 70,
    },
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => i + 1,
      width: 70,
    },
    {
      title: '榜单名称',
      dataIndex: 'name',
      render: (text: any, record: any) => (
        <PermA
          perm="ranks_ck:result_data"
          onClick={() =>
            history.push(
              `/view/singleCkRank/${record.id}/${record.rank_type}/${
                filter.category
              }/${encodeURIComponent(record.name)}`
            )
          }
        >
          {text}
        </PermA>
      ),
    },
    {
      title: '前台展示榜单名称',
      dataIndex: 'type',
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (text: any) => (text == 1 ? '展示中' : '不展示'),
      width: 80,
    },
    {
      title: '操作',
      key: 'op',
      render: (record: any) => (
        <span>
          <PermA perm="ranks_ck:online" onClick={() => updateStatus(record)}>
            {record.status == 1 ? '下架' : '上架'}
          </PermA>
          <Divider type="vertical" />
          <PermA
            perm="ranks_ck:edit"
            onClick={() => editRecord('name', record)}
            style={{ marginLeft: 8 }}
          >
            编辑名称
          </PermA>
          <Divider type="vertical" />
          <PermA
            perm="ranks_ck:result_data"
            onClick={() =>
              history.push(
                `/view/singleCkRank/${record.id}/${record.rank_type}/${
                  filter.category
                }/${encodeURIComponent(record.name)}`
              )
            }
          >
            榜单数据
          </PermA>
        </span>
      ),
      width: 220,
    },
  ];

  useEffect(() => {
    setMenuHook(dispatch, props);
    handleGetMonths();
  }, []);

  useEffect(() => {
    getList();
  }, [filter]);

  const handleChangeName = () => {
    if (!changeNameModal.name?.trim()) {
      message.error('请填写名称');
      return;
    }
    const body: any = { id: changeNameModal.id, type: changeNameModal.name.trim() };

    run(api.updateCkRank, body).then(() => {
      message.success('操作成功');
      setChangeNameModal({ visible: false });
      getList();
    });
  };

  const handleGetMonths = async () => {
    try {
      const res = await userApi.getCkRankWeekList({});
      const list = res?.data?.list || [];
      setRankDate(list);
    } catch (error) {}
  };

  const onChangeType = (v: any, name: any) => {
    let { pathname: path, search } = history.location;
    const obj = searchToObject();
    obj[name] = v;
    path = `${path}?${objectToQueryString(obj)}`;
    history.replace(path);
    setFilter({
      ...filter,
      [name]: v,
    });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton
            perm="ranks_ck:config"
            onClick={() => setExponentConfigDrawer({ visible: true })}
          >
            指数配置
          </PermButton>
          {/* <PermButton
            perm="ranks_ck:publish"
            style={{ marginLeft: 8 }}
            onClick={() =>
              setPublishModal({
                key: Date.now() + 1,
                visible: true,
                month_publish_time: !!month_publish_time
                  ? moment(month_publish_time, 'HH:mm')
                  : undefined,
                month_publish_day: month_publish_day,
                week_publish_time: !!publish_time ? moment(publish_time, 'HH:mm') : undefined,
                week_publish_day: publish_day,
                publish_switch: Boolean(publish_switch),
              })
            }
          >
            发榜设置
          </PermButton> */}
          <PermButton
            perm="ranks_ck:update_data"
            style={{ marginLeft: 8 }}
            onClick={() => {
              setUpdateDateModal({ visible: true, key: Date.now(), dates: rank_dates });
            }}
          >
            更新榜单数据
          </PermButton>
          <PermButton
            perm="ranks_ck:blacklist"
            style={{ marginLeft: 8 }}
            onClick={() => history.push(`/view/ckRankBlackList`)}
          >
            黑名单管理
          </PermButton>
          <PermButton
            perm="ranks_ck:publish"
            style={{ marginLeft: 8 }}
            onClick={() =>
              setManualPublishingModal({
                visible: true,
                rankType: filter.category,
                key: Date.now() + 2,
              })
            }
          >
            手动发榜
          </PermButton>
          {filter.category === 0 && (
            <PermButton
              perm="ranks_chaoke:earnings_list"
              style={{ marginLeft: 8 }}
              onClick={() => setRewardModal({ visible: true })}
            >
              上榜奖励
            </PermButton>
          )}
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 10 }}>
          <Radio.Group
            value={filter.category}
            buttonStyle="solid"
            onChange={(e) => onChangeType(e.target.value, 'category')}
          >
            <Radio.Button value={1}>周榜</Radio.Button>
            <Radio.Button value={0}>月榜</Radio.Button>
          </Radio.Group>
        </Row>
        <Table
          func="getCkRankList"
          index="page"
          filter={filter}
          columns={columns}
          rowKey="id"
          pagination={false}
        />
        <Modal
          visible={changeNameModal.visible}
          title={'编辑榜单'}
          onCancel={() => setChangeNameModal({ visible: false })}
          onOk={handleChangeName}
          confirmLoading={loading}
          key={changeNameModal.key}
        >
          <Form labelCol={{ span: 4 }} wrapperCol={{ span: 19 }}>
            <Form.Item label="榜单名称" required>
              <Input
                maxLength={15}
                value={changeNameModal.name}
                onChange={(e: any) =>
                  setChangeNameModal({ ...changeNameModal, name: e.target.value })
                }
                placeholder="请输入榜单名称"
              />
            </Form.Item>
          </Form>
        </Modal>

        {/* 更新榜单数据 */}
        <UpdateRankModal
          type="ck"
          {...updateDateModal}
          onCancel={() => setUpdateDateModal({ visible: false, key: Date.now() })}
          onEnd={() => {
            setUpdateDateModal({ visible: false, key: Date.now() });
          }}
        ></UpdateRankModal>

        {/* 发榜设置 */}
        <RankPublishConfigModal
          {...publishModal}
          type="ck"
          onOk={() => {
            setPublishModal({ visible: false });
            getList();
          }}
          onCancel={() => setPublishModal({ visible: false })}
        ></RankPublishConfigModal>

        <ManualPublishingModal
          {...manualPublishingModal}
          type="ck"
          dates={rank_dates}
          onOk={() => {
            setManualPublishingModal({ visible: false });
            getList();
          }}
          onCancel={() => setManualPublishingModal({ visible: false })}
        ></ManualPublishingModal>

        {/* ✅ 使用新的SendRewardsDrawer替代原来的简单Modal */}
        <SendRewardsDrawer
          visible={rewardModal.visible}
          onClose={() => setRewardModal({ visible: false })}
          onOk={() => setRewardModal({ visible: false })}
          rankType="ck"
        />

        <CkExponentConfigDrawer
          {...exponentConfigDrawer}
          onClose={() => setExponentConfigDrawer({ visible: false })}
          onOk={() => setExponentConfigDrawer({ visible: false })}
        ></CkExponentConfigDrawer>
      </div>
    </>
  );
}
