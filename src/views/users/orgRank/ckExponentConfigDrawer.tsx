import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { userApi } from '@app/api';
import '@components/business/styles/business.scss';
import { useDispatch } from 'react-redux';
import { Drawer } from '@app/components/common';
import { simpleCopy } from '@app/utils/utils';
import { Col, Icon, Input, InputNumber, Modal, Row, Select, message } from 'antd';
import { checkRule<PERSON>son } from './exponentConfigDrawer';
import { setConfig } from '@app/action/config';

/* 每一级指标默认数据 */
const defaultData2: any = {
  name: '',
  factor: undefined,
  weight: undefined,
  level: 3,
  index_id: '',
};
const defaultData1: any = {
  name: '',
  weight: undefined,
  level: 2,
  index_id: 0,
  children: [
    {
      name: '',
      factor: undefined,
      weight: undefined,
      level: 3,
      index_id: '',
    },
  ],
};
const defaultData: any = {
  name: '',
  weight: undefined,
  level: 1,
  index_id: 0,
  children: [
    {
      name: '',
      weight: undefined,
      level: 2,
      index_id: 0,
      children: [
        {
          name: '',
          factor: undefined,
          weight: undefined,
          level: 3,
          index_id: '',
        },
      ],
    },
  ],
};

/* icon颜色 */
const ICON_STYLE = {
  fontSize: '24px',
  margin: '2px 4px',
};

const ExponentConfigDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();

  const [data, setData] = useState<any>([]);
  const [ruleData, setRuleData] = useState<any>([]);
  const [thirdList, setThirdList] = useState<any>([]);

  useEffect(() => {
    if (props.visible) {
      setRuleData([simpleCopy(defaultData)]);
      initData();
      handleRequestConfig();
    }
  }, [props.visible]);

  useEffect(() => {
    if (data.length !== 0) {
      setRuleData(data);
    } else {
      setRuleData([simpleCopy(defaultData)]);
    }
  }, [data]);

  /**
   * 查询三级指标
   * 查询榜单下拉列表
   */
  const initData = async () => {
    userApi
      .getCkRankConfigInfo({})
      .then((res: any) => {
        setData(res?.data?.tree || []);
      })
      .catch(() => {});
  };
  const handleRequestConfig = async () => {
    userApi
      .getCkRankConfigItems({})
      .then((res: any) => {
        setThirdList(res?.data?.list || []);
      })
      .catch(() => {});
  };

  // 删除一级规则
  function deleteRankData(k: number, k1?: number, k2?: number): void {
    const rule_data = simpleCopy(ruleData);
    if (k1 !== undefined && k2 !== undefined && k2 >= 0) {
      rule_data?.[k]?.children?.[k1]?.children?.splice(k2, 1);
    } else if (k1 !== undefined && k1 >= 0) {
      rule_data?.[k]?.children?.splice(k1, 1);
    } else if (k >= 0) {
      rule_data.splice(k, 1);
    }
    setRuleData(rule_data);
  }
  // 新增一级规则
  function addRankData(k: number, k1?: number, k2?: number) {
    const rule_data = simpleCopy(ruleData);
    if (k1 !== undefined && k2 !== undefined && k2 >= 0) {
      rule_data?.[k]?.children?.[k1].children?.splice(k2 + 1, 0, simpleCopy(defaultData2));
    } else if (k1 !== undefined && k1 >= 0) {
      rule_data?.[k]?.children?.splice(k1 + 1, 0, simpleCopy(defaultData1));
    } else if (k >= 0) {
      rule_data.splice(k + 1, 0, simpleCopy(defaultData));
    }
    setRuleData(rule_data);
  }
  function changeRankData<T extends 'weight' | 'factor' | 'name' | 'index_id'>(
    key: T,
    v: T extends 'weight' | 'factor' | 'index_id' ? number : string | number,
    k: number,
    k1?: number,
    k2?: number
  ): void {
    const rule_data = simpleCopy(ruleData);

    if (k1 !== undefined && k2 !== undefined && k2 >= 0) {
      const ki2 = rule_data?.[k]?.children?.[k1]?.children?.[k2];
      if (ki2) {
        switch (key) {
          case 'index_id':
            ki2.index_id = v as number;
            ki2.name = (thirdList.find((el: any) => el.id === v) || {}).name as string;
            break;
          case 'name':
            ki2.name = v as string;
            break;
          case 'factor':
            ki2.factor = v as number;
            break;
          case 'weight':
            ki2.weight = v as number;
            break;
          default:
            break;
        }
      }
    } else if (k1 !== undefined && k1 >= 0) {
      const ki1 = rule_data?.[k]?.children?.[k1];
      if (ki1) {
        switch (key) {
          case 'name':
            ki1.name = v as string;
            break;
          case 'weight':
            ki1.weight = v as number;
            break;
          default:
            break;
        }
      }
    } else if (k >= 0) {
      const ki = rule_data?.[k];
      if (ki) {
        switch (key) {
          case 'name':
            ki.name = v as string;
            break;
          case 'weight':
            ki.weight = v as number;
            break;
          default:
            break;
        }
      }
    }
    setRuleData(rule_data);
  }

  /* 弹窗开关 */
  async function handleConfirm(): Promise<void> {
    if (ruleData?.length <= 0) {
      message.error('请添加指数配置');
      return;
    }
    const inspectionResults = checkRuleJson(ruleData);
    if (inspectionResults) {
      message.error(inspectionResults);
      return;
    }
    Modal.info({
      title: '提示',
      content: (
        <div>
          <p>指标更新将对未发布的榜单数据生效，</p>
          <p>最新指标将在更新后呈现，请耐心等待。</p>
        </div>
      ),
      icon: null,
      okText: '确认',
      onOk: async () => {
        dispatch(setConfig({ mLoading: true }));
        userApi
          .updateCkRankConfig({
            json: JSON.stringify(ruleData),
          })
          .then(() => {
            dispatch(setConfig({ mLoading: false }));
            message.success('更新成功');
            props.onOk?.();
          })
          .catch(() => {
            dispatch(setConfig({ mLoading: false }));
          });
      },
    });
  }

  return (
    <Drawer
      title="指数配置"
      visible={props.visible}
      skey={props.skey}
      onClose={props.onClose}
      closeText="取消"
      okText="确定"
      onOk={() => handleConfirm()}
      maskClosable={true}
      width={1000}
    >
      <>
        {ruleData.length === 0 && (
          <Icon style={ICON_STYLE} type="plus-circle" onClick={() => addRankData(0)} />
        )}
        {ruleData.map((i: any, k: any) => (
          // eslint-disable-next-line react/no-array-index-key
          <Row style={{ margin: '20px 0 0' }} key={k}>
            <Col span={2} className="icons">
              <Icon style={ICON_STYLE} type="plus-circle" onClick={() => addRankData(k)} />
              <Icon style={ICON_STYLE} type="minus-circle" onClick={() => deleteRankData(k)} />
            </Col>
            <Col span={21}>
              <Row>
                <Col span={7}>
                  <Input
                    maxLength={10}
                    placeholder="请输入一级指标名称"
                    value={i.name}
                    onChange={(e) => changeRankData('name', e.target.value.trim(), k)}
                  />
                </Col>
                <Col span={4} style={{ textAlign: 'right', lineHeight: '32px' }}>
                  权重：
                </Col>
                <Col span={7}>
                  <InputNumber
                    value={i.weight}
                    min={0}
                    max={100}
                    onChange={(v: any) => changeRankData('weight', v, k)}
                  />
                  %
                </Col>
                {/* 二级 */}
                {i?.children?.map((i1: any, k1: any) => (
                  // eslint-disable-next-line react/no-array-index-key
                  <Col span={24} key={`${k}-${k1}`}>
                    <Row style={{ marginTop: 20 }}>
                      <Col span={3}>
                        <Icon
                          style={ICON_STYLE}
                          type="plus-circle"
                          onClick={() => addRankData(k, k1)}
                        />
                        <Icon
                          style={ICON_STYLE}
                          type="minus-circle"
                          onClick={() => deleteRankData(k, k1)}
                        />
                      </Col>
                      <Col span={21}>
                        <Row>
                          <Col span={9}>
                            <Input
                              maxLength={10}
                              placeholder="请输入二级指标名称"
                              value={i1.name}
                              onChange={(e) => changeRankData('name', e.target.value.trim(), k, k1)}
                            />
                          </Col>
                          <Col span={4} style={{ textAlign: 'right', lineHeight: '32px' }}>
                            权重：
                          </Col>
                          <Col span={9}>
                            <InputNumber
                              value={i1.weight}
                              min={0}
                              max={100}
                              onChange={(v: any) => changeRankData('weight', v, k, k1)}
                            />
                            %
                          </Col>
                          {/* 三级 */}
                          {i1?.children?.map((i2: any, k2: any) => (
                            // eslint-disable-next-line react/no-array-index-key
                            <Col span={24} key={`${k}-${k1}-${k2}`}>
                              <Row style={{ marginTop: 20 }}>
                                <Col span={1}>
                                  <Icon
                                    style={ICON_STYLE}
                                    type="plus-circle"
                                    onClick={() => addRankData(k, k1, k2)}
                                  />
                                  {k2 >= 1 && (
                                    <Icon
                                      style={ICON_STYLE}
                                      type="minus-circle"
                                      onClick={() => deleteRankData(k, k1, k2)}
                                    />
                                  )}
                                </Col>
                                <Col
                                  span={4}
                                  style={{
                                    textAlign: 'right',
                                    lineHeight: '32px',
                                    fontSize: '16px',
                                  }}
                                >
                                  三级指标：
                                </Col>
                                <Col span={6}>
                                  <Select
                                    value={i2.index_id}
                                    dropdownMatchSelectWidth={false}
                                    style={{ width: '100%' }}
                                    onChange={(v) => {
                                      changeRankData('index_id', v, k, k1, k2);
                                    }}
                                  >
                                    <Select.Option value="">请选择</Select.Option>
                                    {thirdList.map((v: any) => (
                                      <Select.Option value={v.id} key={v.id}>
                                        {v.name}
                                      </Select.Option>
                                    ))}
                                  </Select>
                                </Col>
                                <Col span={2} style={{ textAlign: 'right', lineHeight: '32px' }}>
                                  权重：
                                </Col>
                                <Col span={4}>
                                  <InputNumber
                                    value={i2.weight}
                                    min={0}
                                    max={100}
                                    onChange={(v: any) => changeRankData('weight', v, k, k1, k2)}
                                  />
                                  %
                                </Col>
                                <Col span={2} style={{ textAlign: 'right', lineHeight: '32px' }}>
                                  系数：
                                </Col>
                                <Col span={2}>
                                  <InputNumber
                                    value={i2.factor}
                                    min={1}
                                    max={99999}
                                    onChange={(v: any) => changeRankData('factor', v, k, k1, k2)}
                                  />
                                </Col>
                              </Row>
                            </Col>
                          ))}
                        </Row>
                      </Col>
                    </Row>
                  </Col>
                ))}
                {i?.children?.length <= 0 && (
                  <Col span={24}>
                    <Row style={{ marginTop: 20 }}>
                      <Icon style={ICON_STYLE} type="plus-circle" onClick={() => addRankData(k, 0)} />
                    </Row>
                  </Col>
                )}
              </Row>
            </Col>
          </Row>
        ))}
      </>
    </Drawer>
  );
};

export default forwardRef<any, any>(ExponentConfigDrawer);
