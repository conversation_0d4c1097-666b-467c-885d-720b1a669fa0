import React, { useState, useEffect } from 'react';
import { useDispatch, useStore } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, A, OrderColumn } from '@components/common';
import { getCrumb } from '@app/utils/utils';
import { Row, Col, Input, Divider, Switch, message, Icon, Modal, Form } from 'antd';
import { getTableList } from '@app/action/tableList';
import { userApi as api } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA } from '@app/components/permItems';
import { setConfig } from '@app/action/config';

export default function ServiceKeywords(props: any) {
  const [form, setForm] = useState(
    () =>
      ({
        visible: false,
        key: Date.now(),
      } as any)
  );
  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const { loading, run } = useXHR();

  const getList = () => {
    dispatch(getTableList('getOrgRankList', 'rank_types', {}));
  };

  const formChange = (key: string, value: any) => {
    setForm({
      ...form,
      [key]: value,
    });
  };

  const editRecord = (type: 'name' | 'rule', record: any) => {
    setForm({
      visible: true,
      key: Date.now(),
      type,
      id: record.id,
      name: record.name,
      rule: record.rule_desc,
      switch: record.rule_switch,
      title: type === 'name' ? '编辑榜单名称' : '编辑榜单规则',
    });
  };

  const handleSort = (record: any, flag: 0 | 1) => {
    run(api.updateOrgRankSort, { id: record.id, sort_flag: flag }, true).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  const updateStatus = (record: any) => {
    run(api.updateOrgRankStatus, { id: record.id, status: !record.status }, true).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  const columns = [
    {
      title: '排序',
      key: 'sort',
      render: (text: any, record: any, i: number) => (
        <OrderColumn
          perm="tmh_rank:update_sort"
          pos={i}
          start={0}
          end={store.getState().tableList.allData.on_show_count - 1}
          onUp={() => handleSort(record, 0)}
          onDown={() => handleSort(record, 1)}
        />
      ),
      width: 70,
    },
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => i + 1,
      width: 70,
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 70,
    },
    {
      title: '榜单',
      dataIndex: 'name',
      render: (text: any, record: any) => (
        <span>
          <PermA
            perm="tmh_rank:single_list"
            onClick={() => history.push(`/view/singleRank/${record.id}`)}
          >
            {text}
          </PermA>
          <PermA
            perm="tmh_rank:update_rank_name"
            onClick={() => editRecord('name', record)}
            style={{ marginLeft: 8 }}
          >
            <Icon type="form" />
          </PermA>
        </span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (text: boolean) => (text ? '展示中' : '待展示'),
      width: 80,
    },
    {
      title: '操作',
      key: 'op',
      render: (record: any) => (
        <span>
          <PermA perm="tmh_rank:update_status" onClick={() => updateStatus(record)}>
            {record.status ? '下线' : '上线'}
          </PermA>
          <Divider type="vertical" />
          <PermA perm="tmh_rank:update_rule" onClick={() => editRecord('rule', record)}>
            榜单规则
          </PermA>
        </span>
      ),
      width: 130,
    },
  ];

  useEffect(() => {
    dispatch(
      setConfig({
        selectKeys: props.selectKeys,
        openKeys: props.openKeys,
      })
    );
    getList();
  }, []);

  const submitKeyword = () => {
    let service;
    const body: any = { id: form.id };
    if (form.type === 'name') {
      service = api.updateOrgRankName;
      if (!form.name) {
        message.error('请填写名称');
        return;
      }
      body.rank_name = form.name;
    } else {
      service = api.updateOrgRankRule;
      if (!form.rule) {
        message.error('请填写榜单规则');
        return;
      }
      body.rule_desc = form.rule;
      body.rule_switch = form.switch;
    }
    run(service, body).then(() => {
      message.success('操作成功');
      setForm({ ...form, visible: false });
      getList();
    });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12} />
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          func="getOrgRankList"
          index="rank_types"
          filter={{}}
          columns={columns}
          rowKey="id"
          pagination={false}
        />
        <Modal
          visible={form.visible}
          title={form.title}
          onCancel={() => setForm((s: any) => ({ ...s, visible: false }))}
          onOk={submitKeyword}
          confirmLoading={loading}
          key={form.key}
        >
          {form.type === 'name' && (
            <Form labelCol={{ span: 4 }} wrapperCol={{ span: 19 }}>
              <Form.Item label="榜单名称" required>
                <Input
                  value={form.name}
                  onChange={(e: any) => formChange('name', e.target.value)}
                  placeholder="请输入榜单名称"
                />
              </Form.Item>
            </Form>
          )}
          {form.type === 'rule' && (
            <Form labelCol={{ span: 4 }} wrapperCol={{ span: 19 }}>
              <Form.Item label="榜单规则" required>
                <Input.TextArea
                  value={form.rule}
                  onChange={(e: any) => formChange('rule', e.target.value)}
                  placeholder="请输入榜单规则"
                  maxLength={500}
                />
              </Form.Item>
              <Form.Item label="规则开关" required>
                <Switch
                  checked={form.switch}
                  onChange={(checked: boolean) => formChange('switch', checked)}
                />
              </Form.Item>
            </Form>
          )}
        </Modal>
      </div>
    </>
  );
}
