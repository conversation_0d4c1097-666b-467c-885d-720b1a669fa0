/* eslint-disable no-nested-ternary */
declare const BUILD_ENV: 'dev' | 'test' | 'prev' | 'prod' | 'testb';

import React, { useState, useEffect, useMemo, useReducer } from 'react';
import { useDispatch, useStore, useSelector } from 'react-redux';
import { useHistory, useParams, useRouteMatch } from 'react-router-dom';
import { A, SearchAndInput } from '@components/common';
import { getCrumb } from '@app/utils/utils';
import ReactClipboard from 'react-clipboardjs-copy';
import {
  Row,
  Col,
  Button,
  message,
  Icon,
  Modal,
  Form,
  Select,
  Tooltip,
  InputNumber,
  Radio,
  Input,
} from 'antd';
import { getTableList, clearTableList } from '@app/action/tableList';
import { userApi as api, userApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@app/components/permItems';
import Table from '@app/components/common/table';
import RankInstructionsModal from './rankInstructionsModal';
import moment from 'moment';
import { setConfig } from '@app/action/config';

export default function CmhRankClassMgr(props: any) {
  const [keyword, setKeyword] = useState<any>(undefined);
  const dispatch = useDispatch();
  const { id, rank_type, rank_name } = useParams<any>();
  const history = useHistory();
  const { loading, run } = useXHR();
  const {
    allData: { publish, rank },
    total,
    current,
    size,
    records = [],
  } = useSelector((state: any) => state.tableList);

  const [, forceUpdate] = useReducer((x) => x + 1, 0);
  const [rankCategorys, setRankCategorys] = useState<any>([]);
  const locations = [
    '杭州市',
    '宁波市',
    '温州市',
    '湖州市',
    '嘉兴市',
    '绍兴市',
    '金华市',
    '衢州市',
    '舟山市',
    '台州市',
    '丽水市',
  ];

  const [filter, setFilter] = useState<any>({
    type_value: '',
    tmType: 0,
    search_type: '',
    keyword: ''
  });

  const [search, setSearch] = useState({
    keyword: '',
    search_type: 1,
  })

  useEffect(() => {
    handleGetCategorys();
  }, []);

  useEffect(() => {
    getList();
  }, [filter]);

  const getList = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current;
    dispatch(
      getTableList(filter.tmType == 0 ? 'getCmhProvinceList' : 'getCmhIndustryList', 'page', {
        current: cur,
        size,
        ...newFilter,
      })
    );
  };

  const getSeq = (i: number) => (current - 1) * size + i + 1;
  const columns = [
    {
      title: '序号',
      key: 'id',
      render: (a: any, b: any, c: number) => getSeq(c),
    },
    {
      title: '潮鸣号id',
      key: 'cmh_id',
      dataIndex: 'cmh_id',
    },
    {
      title: '潮鸣号名称',
      key: 'cmh_name',
      dataIndex: 'cmh_name',
    },
    {
      title: filter.tmType == 0 ? '地市' : '类别',
      dataIndex: filter.tmType == 0 ? 'location' : 'industry',
      key: filter.tmType == 0 ? 'location' : 'industry',
    },
  ];

  const downloadTemplate = () => {
    dispatch(setConfig({ loading: true }));
    userApi
      .exportCmgRankClassData({ ...filter })
      .then((res: any) => {
        dispatch(setConfig({ loading: false }));
        const a = document.createElement('a');
        a.href = window.URL.createObjectURL(res.data);
        a.download = `文件模板.xlsx`;
        a.click();
      })
      .catch((err) => {
        dispatch(setConfig({ loading: false }));
      });
  };

  // 获取行业类别
  const handleGetCategorys = () => {
    userApi.getCmhRankCategorys({}).then((res: any) => {
      setRankCategorys(res?.data?.list || []);
    });
  };

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      const newFilter = {
        ...filter,
        keyword: search.keyword || '',
        search_type: search.search_type
      };
      
      if (!search.keyword) {
        delete newFilter.keyword
        delete newFilter.search_type
      }

      setFilter(newFilter);
    }
  };

  const handleUploadExcel = (e: any) => {
    const target = e?.target;
    const file = target.files?.[0];
    if (file) {
      if (file.size / 1024 > 2000) {
        message.error('上传文件不能大于2M');
        return;
      }

      if (
        file.type.indexOf('sheet') === -1 &&
        file.type.indexOf('ms-excel') === -1 &&
        file.type !== ''
      ) {
        message.error('必须选择excel文件');
        return;
      }
      target.value = '';
      const url = filter.tmType == 0 ? userApi.provinceImport : userApi.industryImport;
      run(url, { file }, true)
        .then((result) => {
          message.success('导入成功');
          getList();
        })
        .catch((err) => {});
    }
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button style={{ marginRight: 8 }} onClick={() => history.goBack()}>
            返回
          </Button>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb([...props.breadCrumb, rank_name])}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={14}>
            <Radio.Group
              style={{ marginRight: 8 }}
              value={filter.tmType}
              buttonStyle="solid"
              onChange={(e) =>
                setFilter({
                  ...filter,
                  tmType: e.target.value,
                  type_value: '',
                })
              }
            >
              <Radio.Button value={0}>省内融媒体号</Radio.Button>
              <Radio.Button value={1}>行业融媒号</Radio.Button>
            </Radio.Group>
            <Select
              value={filter.type_value}
              onChange={(v) => setFilter({ ...filter, type_value: v })}
              style={{ width: 140, marginRight: 8 }}
            >
              <Select.Option value={''} key={0}>
                类别
              </Select.Option>
              {(filter.tmType == 0 ? locations : rankCategorys).map((v: any) => (
                <Select.Option value={v} key={v}>
                  {v}
                </Select.Option>
              ))}
            </Select>
            {/* <Button style={{ marginRight: 8 }}>
              导入参评主体
              <input
                type="file"
                onChange={(e) => handleUploadExcel(e)}
                accept=".csv, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                style={{
                  position: 'absolute',
                  opacity: 0,
                  left: 0,
                  top: 0,
                  width: '100%',
                  height: '100%',
                }}
              />
            </Button>
            <Button
              style={{ marginRight: 8 }}
              onClick={() => downloadTemplate()}
            >
              下载文件模板
            </Button> */}
          </Col>
          <Col span={10} style={{ textAlign: 'right' }}>
            <Select
              value={search.search_type}
              style={{ width: 150, marginRight: 8, marginLeft: 8 }}
              onChange={(e) => {
                setSearch({ ...search, search_type: e });
              }}
            >
              <Select.Option value={1}>潮鸣号名称</Select.Option>
              <Select.Option value={2}>潮鸣号id</Select.Option>
            </Select>

            <Input
              style={{ width: 160, marginRight: 8 }}
              onKeyPress={(e) => handleKey(e)}
              value={search.keyword}
              placeholder={'请输入搜索内容'}
              onChange={(e) => setSearch({ ...search, keyword: e.target.value })}
            />
            <Button onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" />
              搜索
            </Button>
          </Col>
        </Row>
        <span style={{ color: 'red' }}>
              注：参评主体的账号如需修改分类，请在【潮鸣号分类】页面操作
            </span>
        <Table
          columns={columns}
          rowKey="id"
          func={filter.tmType == 0 ? 'getCmhProvinceList' : 'getCmhIndustryList'}
          index="page"
          filter={filter}
          pagination={true}
        />
      </div>
    </>
  );
}
