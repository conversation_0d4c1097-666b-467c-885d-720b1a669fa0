/* eslint-disable no-nested-ternary */
declare const BUILD_ENV: 'dev' | 'test' | 'prev' | 'prod' | 'testb';

import React, { useState, useEffect, useMemo, useReducer } from 'react';
import { useDispatch, useStore, useSelector } from 'react-redux';
import { useHistory, useParams, useRouteMatch } from 'react-router-dom';
import { A, PreviewMCN, SearchAndInput } from '@components/common';
import { getCrumb } from '@app/utils/utils';
import ReactClipboard from 'react-clipboardjs-copy';
import {
  Row,
  Col,
  Button,
  message,
  Icon,
  Modal,
  Form,
  Select,
  Tooltip,
  InputNumber,
  Radio,
  Input,
} from 'antd';
import { getTableList, clearTableList } from '@app/action/tableList';
import { userApi as api, userApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@app/components/permItems';
import Table from '@app/components/common/table';
import RankInstructionsModal from './rankInstructionsModal';
import moment from 'moment';
import { setConfig } from '@app/action/config';

const format = (date: string) => {
  if (!date) return date;
  return moment(date, 'YYYYMMDD').format('YYYY.MM.DD');
};

export default function SingleCkRank(props: any) {
  const [keyword, setKeyword] = useState<any>(undefined);
  const dispatch = useDispatch();
  const { id, rank_type, rank_name, category } = useParams<any>();
  const history = useHistory();
  const { loading, run } = useXHR();
  const [, forceUpdate] = useReducer((x) => x + 1, 0);

  const {
    allData: { publish, rank, columns },
    total,
    current,
    size,
    records = [],
  } = useSelector((state: any) => state.tableList);
  const [data, setData] = useState<any>({});

  const [rankInstructionsModal, setRankInstructionsModal] = useState<any>({
    visible: false,
  });

  const [showCountModal, setShowCountModal] = useState<any>({
    visible: false,
    count: 0,
  });

  const [months, setMonths] = useState<any>([]);
  const [rank_dates, setRankDate] = useState<any>([]);
  const [filter, setFilter] = useState<any>({
    keyword: '',
    rank_id: id,
    category: category,
    // date: undefined,
    // week_number: '',
    // year: '',
  });
  const [rank_status, setRankStatus] = useState<any>(false);
  const { allData } = useSelector((state: any) => state.tableList);
  useEffect(() => {
    setRankStatus(allData?.publish?.status === 1 && allData?.publish?.is_show === 1);
  }, [allData]);

  useEffect(() => {
    if (category == 0) {
      handleGetMonths();
    } else {
      handleGetWeeks();
    }
  }, []);

  useEffect(() => {
    if ((category == 0 && !!filter.summary_month) || (category == 1 && !!filter.year)) {
      getList(true);
    }
  }, [filter]);

  const getList = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current;

    dispatch(getTableList('getSingleCkRankList', 'page', { current: cur, size, ...newFilter }));
  };

  const getSeq = (i: number) => (current - 1) * size + i + 1;
  const getColumns = () => {
    let value = [
      {
        title: '序号',
        key: 'seq',
        render: (a: any, b: any, c: number) => getSeq(c),
      },
      {
        title: '潮客',
        key: 'cmh_name',
        dataIndex: 'cmh_name',
      },
      {
        title: '指数',
        key: 'index_number',
        dataIndex: 'index_number',
      },
      ...(columns || []).map((v: any, i: any) => ({
        title: `${v.name}得分`,
        render: (a: any, b: any) => Number(((b.details[i] || {}).rvalue || 0).toFixed(2)),
      })),
      {
        title: '健康度得分',
        key: 'operation_value',
        dataIndex: 'operation_value',
        width: 110,
        render: (a: any, b: any) =>
          b.isPointEdit ? (
            <InputNumber
              placeholder="输入正负数字"
              value={b.new_operation_value}
              onChange={(e) => {
                b.new_operation_value = e;
                forceUpdate();
              }}
            />
          ) : (
            Number((a || 0).toFixed(2))
          ),
      },
      {
        title: '操作',
        width: 100,
        render: (_: any, a: any, b: number) => (
          <span>
            {!a.isPointEdit ? (
              <PermA perm="ranks_ck:update_data" onClick={() => toggleEdit(b, true)}>
                编辑
              </PermA>
            ) : (
              <>
                <PermA
                  perm=""
                  style={{ marginRight: 8 }}
                  onClick={() => {
                    handleModifyRankData(b);
                  }}
                >
                  保存
                </PermA>
                <PermA perm="" onClick={() => toggleEdit(b, false)}>
                  取消
                </PermA>
              </>
            )}
          </span>
        ),
      },
    ];
    return value;
  };

  const handleGetMonths = () => {
    const monthsList = [];
    // 从2023年9月开始到现在所有月份

    const now = moment();
    const start = moment('202309', 'YYYYMM');
    while (start.isBefore(now, 'month')) {
      monthsList.push(start.format('YYYYMM'));
      start.add(1, 'months');
    }
    setMonths(monthsList.reverse());

    const active = monthsList[0] || '';
    setFilter({
      ...filter,
      summary_month: active,
    });
  };

  const handleGetWeeks = () => {
    userApi
      .getCkRankWeekList({})
      .then(async (res: any) => {
        const list = res?.data?.list || [];
        setRankDate(list);

        const active = list[0] || '';
        setFilter({
          ...filter,
          date: !!active ? `${active.week_number}-${active.year}` : undefined,
          week_number: active.week_number || '',
          year: active.year || '',
        });
      })
      .catch((err) => {});
  };

  // 保存健康度
  async function handleModifyRankData(i: number) {
    const record = records[i];
    if (record.new_operation_value === undefined) {
      message.error('请输入对应健康度得分');
      return;
    }
    const params: any = {
      cmh_id: record.cmh_id,
      result_id: id,
      operation_value: record.new_operation_value,
    };
    if (category == 0) {
      params.summary_month = publish.summary_month;
    } else {
      params.summary_start_date = publish.summary_start_date;
      params.summary_end_date = publish.summary_end_date;
    }
    userApi
      .ckModifyRankData(params)
      .then((result) => {
        message.success('编辑成功');
        getList();
      })
      .catch((err) => {});
  }

  // 切换编辑状态
  const toggleEdit = (i: number, type: boolean) => {
    records[i].isPointEdit = type;
    forceUpdate();
  };

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      setFilter({
        ...filter,
        keyword: keyword || '',
      });
    }
  };

  // 手动发榜
  const handlePublish = () => {
    Modal.confirm({
      title: '确定要发榜吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        if (!!publish?.id) {
          userApi
            .ckManualSinglePublish({ publish_id: publish?.id || '' })
            .then((result) => {
              message.success('手动发榜成功');
              getList();
            })
            .catch((err) => {});
        } else {
          message.warning('本期榜单暂无数据，无法发榜');
        }
      },
      onCancel: () => {},
    });
  };

  // 更新展示数量
  const updateShowNum = async () => {
    if (!publish?.id) {
      return;
    }
    const num = showCountModal.count;
    if (num == undefined) {
      message.error('请输入展示数量');
      return;
    }
    userApi
      .updateCkRankShowNum({ publish_id: publish?.id, num })
      .then((result) => {
        message.success('修改成功');
        publish.show_num = num;
        setShowCountModal({ visible: false });
      })
      .catch((err) => {});
  };

  const handleExportData = () => {
    Modal.confirm({
      title: '确定导出当前榜单吗？',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        userApi
          .exportCkRankData({ ...filter })
          .then((res: any) => {
            dispatch(setConfig({ loading: false }));
            const a = document.createElement('a');
            a.href = window.URL.createObjectURL(res.data);
            a.download =
              category == 0
                ? `${rank_name}_月榜_${publish?.summary_month}.xlsx`
                : `${rank_name}_周榜_${publish?.summary_start_date}_${publish?.summary_end_date}.xlsx`;
            a.click();
          })
          .catch((err) => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const handleChageRankDate = async (value: any) => {
    const active = value.split('-');
    setFilter({
      ...filter,
      week_number: active[0],
      year: active[1],
      date: value,
    });
  };

  const host: any =
    {
      dev: 'https://tmtest.tidenews.com.cn',
      test: 'https://tmtest.tidenews.com.cn',
      prev: 'https://tmprev.tidenews.com.cn',
      prod: 'https://tidenews.com.cn',
      testb: 'https://tmtest.tidenews.com.cn',
    }[BUILD_ENV] || 'https://tmtest.tidenews.com.cn';

  // 后期域名需要切换
  const rank_url = `${host}/chao-rank/chaoKe?tenantId=1&current_rank=${rank_name || ''}&rank_id=${
    id || ''
  }&rank_type=${rank_type || ''}&category=${category || ''}`;

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button style={{ marginRight: 8 }} onClick={() => history.goBack()}>
            返回
          </Button>
          <ReactClipboard
            action="copy"
            text={rank_url}
            onSuccess={() => message.success('链接已复制')}
            onError={() => message.error('复制失败')}
          >
            <a style={{ display: 'none' }} id="link">
              xxxx
            </a>
          </ReactClipboard>
          <Button
            style={{ marginRight: 8 }}
            onClick={() => {
              const myLink = document.getElementById('link');
              myLink?.click();
            }}
          >
            复制榜单链接
          </Button>
          <Button
            style={{ marginRight: 8 }}
            onClick={() => setRankInstructionsModal({ visible: true, skey: Date.now() })}
          >
            榜单说明
          </Button>
          <PermButton
            perm=""
            onClick={() => setShowCountModal({ visible: true, count: publish?.show_num || 1 })}
          >
            展示数量
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb([...props.breadCrumb, rank_name])}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={18}>
            {category == 0 ? (
              <Select
                value={filter.summary_month}
                style={{ width: 140, marginRight: 8 }}
                onChange={(value: string) => {
                  setFilter({
                    ...filter,
                    summary_month: value,
                  });
                }}
              >
                {months.map((v: any) => (
                  <Select.Option value={v} key={v}>
                    {moment(`${v}`, 'YYYYMM').format('YYYY.MM')}
                  </Select.Option>
                ))}
              </Select>
            ) : (
              <Select
                value={filter.date}
                style={{ width: 300, marginRight: 8 }}
                onChange={(value: string) => handleChageRankDate(value)}
              >
                {rank_dates.map((v: any) => (
                  <Select.Option
                    value={`${v.week_number}-${v.year}`}
                    key={`${v.week_number}-${v.year}`}
                  >
                    {format(v.start_date)}-{format(v.end_date)}
                  </Select.Option>
                ))}
              </Select>
            )}
            <PermButton perm="" style={{ marginRight: 8 }} onClick={() => handleExportData()}>
              导出当前榜单
            </PermButton>
            <PermButton perm="ranks_ck:publish" type="primary" onClick={() => handlePublish()}>
              手动发榜
            </PermButton>
            {rank_status ? (
              <span style={{ marginLeft: 8 }}>已发榜</span>
            ) : (
              <span style={{ marginLeft: 8 }}>未发榜</span>
            )}
          </Col>
          <Col span={6} style={{ textAlign: 'right' }}>
            <Input
              style={{ width: 160, marginRight: 8 }}
              onKeyPress={(e) => handleKey(e)}
              value={keyword}
              placeholder="搜索潮客名称"
              onChange={(e) => setKeyword(e.target.value)}
            />
            <Button onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" />
              搜索
            </Button>
          </Col>
        </Row>
        <Row style={{ marginBottom: 16 }}>
          阅读数：{publish?.pv || 0}&nbsp;&nbsp;转发量：{publish?.share_count || 0}
        </Row>
        <Table
          columns={getColumns()}
          rowKey="id"
          func={'getSingleCkRankList'}
          index="page"
          filter={filter}
          pagination={true}
        />

        {/* 榜单说明 */}
        <RankInstructionsModal
          type={2}
          id={id}
          {...rankInstructionsModal}
          desc_switch={rank?.desc_switch}
          description={rank?.description}
          onCancel={() => setRankInstructionsModal({ visible: false })}
          onEnd={() => {
            setRankInstructionsModal({ visible: false });
            getList();
          }}
        ></RankInstructionsModal>

        {/* 展示数量 */}
        <Modal
          visible={showCountModal.visible}
          onCancel={() => setShowCountModal({ visible: false })}
          onOk={() => updateShowNum()}
          title="展示数量"
          width={500}
        >
          <InputNumber
            min={0}
            max={100}
            value={showCountModal.count}
            onChange={(v: any) => setShowCountModal({ ...showCountModal, count: v })}
          />
        </Modal>
      </div>
    </>
  );
}
