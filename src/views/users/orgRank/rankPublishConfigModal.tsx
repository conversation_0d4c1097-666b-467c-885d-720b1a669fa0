import { userApi } from '@app/api';
import { Col, message, Modal, Row, Select, Switch, TimePicker } from 'antd';
import React, { useEffect, useState } from 'react';

export default function RankPublishConfigModal(props: any) {
  const [values, setValues] = useState<any>({});
  const [loading, setLoading] = useState<boolean>(false);
  const dayList: any = [...new Array(28)].map((el, i) => ({
    label: `${i + 1}日`,
    value: String(i + 1),
  }));

  useEffect(() => {
    if (props.visible) {
      setValues({
        publish_switch: props.publish_switch,
        month_publish_time: props.month_publish_time,
        month_publish_day: props.month_publish_day,
        week_publish_day: props.week_publish_day,
        week_publish_time: props.week_publish_time,
      });
    }
  }, [props.visible]);

  /* 弹窗开关 */
  async function handlePublishConfirm(): Promise<void> {
    if (values.publish_switch) {
      if (!values.month_publish_time || !values.week_publish_time) {
        message.error('请输入发榜时间');
        return;
      }
      if (!values.month_publish_day || !values.week_publish_day) {
        message.error('请选择发榜周期');
        return;
      }
    }
    setLoading(true);

    const params: any = {};
    if (props.type == 'ck') {
      params.month_publish_time = values.publish_switch
        ? values.month_publish_time.format('HH:mm:00')
        : '';
      params.month_day = values.publish_switch ? values.month_publish_day : '';

      params.publish_time = values.publish_switch
        ? values.week_publish_time.format('HH:mm:00')
        : '';
      params.day = values.publish_switch ? values.week_publish_day : '';
    } else {
      params.publish_time = values.publish_switch
        ? values.month_publish_time.format('HH:mm:00')
        : '';
      params.day = values.publish_switch ? values.month_publish_day : '';

      params.week_publish_time = values.publish_switch
        ? values.week_publish_time.format('HH:mm:00')
        : '';
      params.week_day = values.publish_switch ? values.week_publish_day : '';
    }
    const request =
      props.type == 'ck'
        ? userApi.changeCkRankPublishSwitch({ open: values.publish_switch ? 1 : 0 }).then(() => {
            return values.publish_switch
              ? userApi.changeCkRankPublishTime(params)
              : Promise.resolve();
          })
        : userApi.changeCmhRankPublishSwitch({ open: values.publish_switch ? 1 : 0 }).then(() => {
            return values.publish_switch
              ? userApi.changeCmhRankPublishTime(params)
              : Promise.resolve();
          });

    request
      .then(() => {
        props.onOk();
        message.success('发榜设置成功');
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      });
  }
  return (
    <Modal
      width={850}
      visible={props.visible}
      onOk={() => handlePublishConfirm()}
      onCancel={() => props.onCancel()}
      title="发榜设置"
      confirmLoading={loading}
    >
      <Row style={{ margin: '20px 0 0' }}>
        <Col span={3} style={{ textAlign: 'right' }}>
          定时发榜：
        </Col>
        <Col span={20} style={{ marginBottom: '20px' }}>
          <Switch
            checked={values.publish_switch}
            onChange={(e) => setValues({ ...values, publish_switch: e })}
          />
        </Col>
        <Col span={3} style={{ textAlign: 'right', lineHeight: '32px' }}>
          发榜时间：
        </Col>
        <Col span={20}>
          <span style={{ marginRight: 10 }}>每月</span>
          <Select
            style={{ width: '100px', marginRight: 10 }}
            disabled={!values.publish_switch}
            value={values.month_publish_day}
            onChange={(e) => setValues({ ...values, month_publish_day: e })}
          >
            {dayList.map((item: any) => {
              return (
                <Select.Option key={item.value} value={item.value}>
                  {item.label}
                </Select.Option>
              );
            })}
          </Select>
          <TimePicker
            disabled={!values.publish_switch}
            value={values.month_publish_time}
            placeholder="请选择发榜时间"
            style={{ width: '200px' }}
            // showNow={false}
            format={'HH:mm:00'}
            onChange={(e: any) => setValues({ ...values, month_publish_time: e })}
          />
        </Col>
      </Row>
      <Row
        style={{
          marginTop: 10,
        }}
      >
        <Col span={3} style={{ textAlign: 'right', lineHeight: '32px' }}></Col>
        <Col span={20}>
          <span style={{ marginRight: 10 }}>每周</span>
          <Select
            style={{ width: '100px', marginRight: 10 }}
            disabled={!values.publish_switch}
            value={values.week_publish_day}
            onChange={(e) => setValues({ ...values, week_publish_day: e })}
          >
            {['周一', '周二', '周三', '周四', '周五', '周六', '周日'].map(
              (item: any, index: number) => {
                return <Select.Option value={`${index + 1}`}>{item}</Select.Option>;
              }
            )}
          </Select>
          <TimePicker
            disabled={!values.publish_switch}
            value={values.week_publish_time}
            placeholder="请选择发榜时间"
            style={{ width: '200px' }}
            // showNow={false}
            format={'HH:mm:00'}
            onChange={(e: any) => setValues({ ...values, week_publish_time: e })}
          />
        </Col>
      </Row>
    </Modal>
  );
}
