/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, useMemo } from 'react';
import { useDispatch, useStore, useSelector } from 'react-redux';
import { useHistory, useParams, useRouteMatch } from 'react-router-dom';
import { A, PreviewMCN, SearchAndInput } from '@components/common';
import { getCrumb } from '@app/utils/utils';
import ReactClipboard from "react-clipboardjs-copy";
import {
  Row,
  Col,
  Button,
  message,
  Icon,
  Modal,
  Form,
  Select,
  Tooltip,
  InputNumber,
  Radio,
  Input,
} from 'antd';
import { getTableList, clearTableList } from '@app/action/tableList';
import { userApi as api, userApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@app/components/permItems';
import Table from '@app/components/common/table';
import AddBlackUserModal from './addBlackUserModal';

export default function CkRankBlacklist(props: any) {
  const dispatch = useDispatch();
  const { id, rank_type, rank_name } = useParams<any>();
  const history = useHistory();
  const { loading, run } = useXHR();

  const {
    total,
    current,
    size,
  } = useSelector((state: any) => state.tableList);

  const [filter, setFilter] = useState<any>({
  })

  const [addModalVisible, setAddModalVisible] = useState<any>(false)

  useEffect(() => {
    getList()
  }, [])

  const getList = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current

    dispatch(getTableList('getCkRankBlacklist', 'list', { current: cur, size, ...newFilter }));
  }

  const getSeq = (i: number) => (current - 1) * size + i + 1;
  const getColumns = () => {
    let columns = [
      {
        title: '序号',
        key: 'seq',
        render: (a: any, b: any, c: number) => getSeq(c),
      }, {
        title: '用户昵称',
        key: 'cmh_name',
        dataIndex: 'cmh_name',
      }, {
        title: '小潮号',
        key: 'chao_id',
        dataIndex: 'chao_id',
      },
      {
        title: '用户ID',
        key: 'cmh_id',
        dataIndex: 'cmh_id',
      },
      {
        title: '操作',
        width: 100,
        render: (_: any, a: any, b: number) => (<PermA perm="" onClick={() => deleteRecords(a)}>移除</PermA>)
      },
    ];
    return columns;
  }

  const deleteRecords = (record: any) => {
    run(userApi.deleteBlacklistRecord, { account_id: record.cmh_id }, true).then((result) => {
      getList()
    }).catch((err) => {

    });
  }

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button style={{ marginRight: 8 }} onClick={() => history.goBack()}>返回</Button>
          <Button onClick={() => setAddModalVisible(true)}>
            <Icon type="plus-circle" /> 添加账号
          </Button>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb([...props.breadCrumb, rank_name])}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          columns={getColumns()}
          rowKey="id"
          func={'getCkRankBlacklist'}
          index="list"
          filter={filter}
        />

        <AddBlackUserModal visible={addModalVisible}
          onCancel={() => setAddModalVisible(false)}
          onEnd={() => {
            setAddModalVisible(false)
            getList()
          }}
        ></AddBlackUserModal>
      </div >
    </>
  );
}
