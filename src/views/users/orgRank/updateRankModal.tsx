import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { CKEditor } from '@app/components/common';
import { Col, DatePicker, Form, Modal, Radio, Row, Select, Switch, message } from 'antd';
import { userApi } from '@app/api';
import moment from 'moment';

const UpdateRankModal = (props: any, ref: any) => {
  const { getFieldDecorator, getFieldValue, setFieldsValue } = props.form;
  const [rankType, setRankType] = useState<number>(0);
  const rank_dates = (props.dates || []).map((el: any) => ({
    summary_year: el.year || el.summary_year,
    summary_week_number: el.week_number || el.summary_week_number,
    summary_start_date: el.start_date || el.summary_start_date,
    summary_end_date: el.end_date || el.summary_end_date,
  }));

  const handleSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const params: any = {};
        if (props.type == 'ck') {
          if (rankType == 1) {
            params.year = values.year;
            params.weeknumber = values.week;
          } else {
            params.summary_month = moment(values.month).format('YYYYMM');
          }
        } else {
          if (rankType == 1) {
            // 周榜
            params.year = values.year;
            params.week = values.week;
          } else {
            // 月榜
            const day = moment(values.month, 'YYYY-MM');
            params.year = day.year();
            params.month = day.month() + 1;
          }
        }

        (props.type == 'ck' ? userApi.updateCkRankData : userApi.updateCmhRankData)(params)
          .then(() => {
            message.success('更新成功');
            props.onEnd && props.onEnd();
          })
          .catch(() => {});
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  return (
    <Modal
      title="更新榜单数据"
      visible={props.visible}
      onCancel={props.onCancel}
      onOk={() => handleSubmit()}
      width={500}
      key={props.skey}
    >
      <Radio.Group
        style={{ marginBottom: 10 }}
        value={rankType}
        buttonStyle="solid"
        onChange={(e) => setRankType(e.target.value)}
      >
        <Radio.Button value={1}>周榜</Radio.Button>
        <Radio.Button value={0}>月榜</Radio.Button>
      </Radio.Group>

      <Form labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} autoComplete="off">
        {rankType == 1 ? (
          <>
            <Form.Item label="年份" labelAlign="left">
              {getFieldDecorator('year', {
                initialValue: undefined,
                rules: [{ required: true, message: '请选择年份' }],
              })(
                <Select
                  style={{ width: '100%' }}
                  placeholder="选择要更新年份"
                  onChange={(v) => {
                    setFieldsValue({
                      week: undefined,
                    });
                  }}
                >
                  {[...new Set(rank_dates.map((el: any) => el.summary_year))].map((v: any) => (
                    <Select.Option value={v} key={v}>
                      {v}
                    </Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
            <Form.Item label="数据统计周期" labelAlign="left">
              {getFieldDecorator('week', {
                initialValue: undefined,
                rules: [{ required: true, message: '请选择数据统计周期' }],
              })(
                <Select style={{ width: '100%' }} placeholder="选择要更新的榜单周期">
                  {rank_dates
                    .filter((el: any) => el.summary_year === getFieldValue('year'))
                    ?.map((v: any) => (
                      <Select.Option value={v.summary_week_number} key={v.summary_week_number}>
                        {v.summary_start_date}-{v.summary_end_date}
                      </Select.Option>
                    ))}
                </Select>
              )}
            </Form.Item>
          </>
        ) : (
          <Form.Item label="">
            {getFieldDecorator('month', {
              initialValue: undefined,
              rules: [{ required: true, message: '请选择月份' }],
            })(
              <DatePicker.MonthPicker
                placeholder="请选择月份"
                style={{ width: '100%' }}
                allowClear
                disabledDate={(current: any) =>
                  current && current.startOf('month') > moment().subtract(1, 'M').startOf('month')
                }
              />
            )}
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};

export default Form.create<any>({ name: 'UpdateRankModal' })(forwardRef<any, any>(UpdateRankModal));
