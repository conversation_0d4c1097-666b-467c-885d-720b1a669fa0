import React, { forwardRef, useImperative<PERSON>and<PERSON> } from "react";
import { CKEditor } from "@app/components/common";
import { Form, Modal, Switch, message } from "antd";
import { userApi } from "@app/api";

const RankInstructions = (props: any, ref: any) => {

  const { getFieldDecorator } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  function handleUpDateRule() {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        if (!values?.desc || values?.desc === '<p><br></p>') {
          message.error('请输入榜单说明');
          return;
        }
        const params = {
          id: props.id,
          desc: values?.desc,
          desc_switch: Number(values?.desc_switch)
        }
        const api = props.type == 1 ? userApi.updateCmhRankDesc : userApi.updateCkRankDesc
        api(params)
          .then(() => {
            message.success('操作成功');
            props.onEnd();
          })
          .catch(() => { });
      } else {
        message.error('请检查表单内容');
      }
    });
  }


  return <Modal
    visible={props.visible}
    onCancel={props.onCancel}
    onOk={() => handleUpDateRule()}
    width={800}
    key={props.skey}
  >
    <Form
      labelCol={{ span: 3 }}
      wrapperCol={{ span: 20 }}
      autoComplete="off"
    >
      <Form.Item label="榜单说明">
        {getFieldDecorator('desc', {
          initialValue: props.description,
          rules: [
            {
              required: true,
              message: '请输入榜单说明',
            },
          ],
        })(<CKEditor />)}
      </Form.Item>
      <Form.Item label="开关">
        {getFieldDecorator('desc_switch', {
          initialValue: !!props.desc_switch,
          valuePropName: 'checked',
          rules: [
            {
              required: true,
              message: '',
            },
          ],
        })(<Switch
          checkedChildren="on"
          unCheckedChildren="off"
        />)}


      </Form.Item>
    </Form>
  </Modal>
}

export default Form.create<any>({ name: 'RankInstructions' })(forwardRef<any, any>(RankInstructions));