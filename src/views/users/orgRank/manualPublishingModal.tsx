import { userApi } from '@app/api';
import { Col, message, Modal, Radio, Row, Select } from 'antd';
import moment from 'moment';
import React, { useEffect, useState } from 'react';

const format = (date: string, type = 'YYYY.MM.DD') => {
  if (!date) return date;
  return moment(date, 'YYYYMMDD').format(type);
};

export default function ManualPublishingModal(props: any) {
  const [values, setValues] = useState<any>({});
  const [loading, setLoading] = useState<boolean>(false);
  const rank_dates = props.dates || [];

  const handleGetMonths = () => {
    const monthsList = [];

    for (let i = 1; i <= 6; i++) {
      const date = moment().subtract(i, 'months');
      monthsList.push(date.format('YYYYMM'));
    }

    return monthsList;
  };

  const months = handleGetMonths();

  useEffect(() => {
    if (props.visible) {
      setValues({
        rankType: props.rankType,
      });
    }
  }, [props.visible]);

  const handleManualPublishing = async () => {
    const params: any = {};
    if (values.rankType == 1) {
      if (props.type == 'ck') {
        if (!values.date) {
          message.error('请选择发榜周期');
          return;
        }

        params.summary_start_date = values.summary_start_date;
        params.summary_end_date = values.summary_end_date;
        params.date = values.date;
      } else {
        if (!values.summary_week) {
          message.error('请选择发榜周期');
          return;
        }
        params.summary_week = values.summary_week;
        params.summary_year = values.summary_year;
      }
    } else {
      if (!values.summary_month) {
        message.error('请选择发榜周期');
        return;
      }
      params.summary_month = values.summary_month;
    }
    setLoading(true);

    (props.type == 'ck' ? userApi.ckManualPublish : userApi.cmhManualPublish)(params)
      .then(() => {
        message.success('手动发榜成功');
        props.onOk();
      })
      .catch(() => {})
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <Modal
      title="手动发榜"
      visible={props.visible}
      width={500}
      onCancel={() => props.onCancel()}
      onOk={handleManualPublishing}
    >
      <Radio.Group
        style={{ marginBottom: 10 }}
        value={values.rankType}
        buttonStyle="solid"
        onChange={(e) => setValues({ ...values, rankType: e.target.value })}
      >
        <Radio.Button value={1}>周榜</Radio.Button>
        <Radio.Button value={0}>月榜</Radio.Button>
      </Radio.Group>
      <Row>
        <Col span={6} style={{ lineHeight: '30px' }}>
          选择发榜周期：
        </Col>
        <Col span={16}>
          {values.rankType == 1 ? (
            props.type == 'ck' ? (
              <Select
                value={values.date}
                onChange={(v) => {
                  const active = v.split('-');
                  setValues({
                    ...values,
                    summary_start_date: active[0],
                    summary_end_date: active[1],
                    date: v,
                  });
                }}
                style={{ width: '300px' }}
              >
                {rank_dates.map((v: any) => (
                  <Select.Option
                    value={`${format(v.start_date, 'YYYYMMDD')}-${format(v.end_date, 'YYYYMMDD')}`}
                    key={`${v.week_number}-${v.year}`}
                  >
                    {format(v.start_date)}-{format(v.end_date)}
                  </Select.Option>
                ))}
              </Select>
            ) : (
              <Select
                value={
                  !!values.summary_year
                    ? `${values.summary_year}-${values.summary_week}`
                    : undefined
                }
                onChange={(v) => {
                  const [year, week] = v.split('-');
                  setValues({
                    ...values,
                    summary_year: year,
                    summary_week: week,
                  });
                }}
                style={{ marginRight: 10, width: '200px' }}
              >
                {rank_dates.map((v: any) => {
                  return (
                    <Select.Option
                      value={`${v.summary_year}-${v.summary_week_number}`}
                      key={`week-${v.summary_year}-${v.summary_week_number}`}
                    >
                      {v.summary_start_date}-{v.summary_end_date}
                    </Select.Option>
                  );
                })}
              </Select>
            )
          ) : (
            <Select
              value={values.summary_month}
              onChange={(v) => setValues({ ...values, summary_month: v })}
              style={{ marginRight: 10, width: '100px' }}
            >
              {months.map((v: any) => (
                <Select.Option value={v} key={`month-${v}`}>
                  {moment(`${v}`, 'YYYYMM').format('YYYY.MM')}
                </Select.Option>
              ))}
            </Select>
          )}
        </Col>
        <Col span={24} style={{ marginTop: '10px' }}>
          将对该周期下所有{props.type == 'ck' ? '潮客' : '潮鸣号'}
          榜进行发榜。已发榜周期的将更新数据。
        </Col>
      </Row>
    </Modal>
  );
}
