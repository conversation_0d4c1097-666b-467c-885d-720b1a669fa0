import { userApi as api } from '@app/api';
import { ITableProps, IBaseProps, CommonObject } from '@app/types';
import { A, Table, BaseComponent, PreviewMCN, OrderColumn } from '@components/common';
import { connectTable as connect } from '@utils/connect';
import {
  Button,
  Col,
  Icon,
  Row, Select, Tooltip,
} from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import { OrgUserArticleRecord, OrgUserArticleAllData } from './users';
import Radio from "antd/es/radio";
import { filter } from "lodash";
import showImagePreviewModal from "@components/common/imagePreviewModal";
import { resolveNewsType, showDataSetModal, showIDDetailModal, showReadCountDetailModal } from "@utils/utils";
import GetVisaModal from "@components/business/GetVisaModal";
import ImagePreviewColumn from '@app/components/common/imagePreviewColumn';

type State = {
  id: string;
  name: string;
  preview: {
    visible: boolean;
    skey: number;
    data: CommonObject;
  };
  filter: {
    retrieved: string,
    content_level: string,
    doc_type: string
  };
  getVisa: {
    visible: boolean,
    record: any
  },
};

type Props = IBaseProps<
  ITableProps<OrgUserArticleRecord, OrgUserArticleAllData>,
  { id: string; name: string }
>;

class OrgUserArticleList extends BaseComponent<
  ITableProps<OrgUserArticleRecord, OrgUserArticleAllData>,
  State,
  { id: string; name: string }
> {
  constructor(props: Props) {
    super(props);
    // console.log('props.match.params.name', props.match.params.name)
    this.state = {
      id: props.match.params.id,
      name: props.match.params.name,
      preview: {
        visible: false,
        skey: Date.now() + 1,
        data: {}
      },
      filter: {
        retrieved: '',
        content_level: '',
        doc_type: ''
      },
      getVisa: {
        visible: false,
        record: null
      },
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
  }

  getData = (overlap: CommonObject = {}, filters = this.getFilters()) => {
    this.dispatchTable('getOrgUserArticleList', 'article_list', { ...filters, ...overlap });
  };

  getFilters = () => {
    const { current, size } = this.props.tableList;
    const { id, filter } = this.state;
    const filters: CommonObject = { current, size, account_id: id, ...filter };
    return filters;
  };

  preview = (record: any) => {
    this.setState({
      preview: {
        visible: true,
        skey: Date.now(),
        data: record
      },
    });
  };
  changeFilter = (key: string, val: any, goToFirstPage = false) => {
    this.setState({
      filter: {
        ...this.state.filter,
        [key]: val
      }
    }, () => {
      this.getData()
    })
  }
  showGetVisaModal(record: any, visible: boolean) {
    this.setState({
      getVisa: {
        visible,
        record
      }
    })
  }
  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '潮新闻ID',
        key: 'scid',
        dataIndex: 'id',
        render: (text: any, record: any) => (<a onClick={() => showIDDetailModal(record)}>{text}</a>),
        width: 100,
      },
      {
        title: '内容标题',
        key: 'list_title',
        dataIndex: 'list_title',
        render: (text: any, record: any, i: number) =>
          <a
            style={{
              display: '-webkit-box',
              textOverflow: 'ellipsis',
              WebkitLineClamp: 3,
              WebkitBoxOrient: 'vertical',
              overflow: "hidden",
            }}
            title={text}
            onClick={this.preview.bind(this, record)}>{text || '-'}</a>,
      },
      {
        title: '封面图/配图',
        key: 'pic_array',
        dataIndex: 'pic_array',
        width: 150,
        align: 'center',
        render: (text: any, record: any) => (<div style={{ height: 60, textAlign: 'center' }}>
          <ImagePreviewColumn text={text?.[0]} imgs={record.pic_array}></ImagePreviewColumn>
          {/* <img src={text?.[0]} className='list-pic' onMouseEnter={() => showImagePreviewModal({ images: record.pic_array })}></img> */}
        </div>)
      },
      {
        title: '内容类型',
        key: 'doc_type',
        dataIndex: 'doc_type',
        render: (doc_type: any) => resolveNewsType(doc_type, 1),
        width: 100,
      },
      {
        title: '内容等级',
        key: 'content_level',
        dataIndex: 'content_level',
        render: (content_level: number) => ['通过', '沉底通过', '推荐'][content_level],
        width: 75,
      },
      {
        title: <span>取签状态
          <Tooltip placement="top"
            title={<span>已取稿——内容被取稿但未签发；<br />已取签——内容被取稿且被签发</span>}>
            <Icon style={{ paddingLeft: '5px' }} type="question-circle" />
          </Tooltip>
        </span>,
        key: 'retrieved',
        dataIndex: 'retrieved',
        width: 100,
        render: (retrieved: number, record: any) => (
          <a onClick={this.showGetVisaModal.bind(this, record, true)}>{['', '已取稿', '已取签'][retrieved]}</a>
        )
      },
      {
        title: '阅读数',
        key: 'fake_count',
        dataIndex: 'fake_count',
        width: 100,
        render: (text: any, record: any) => (<a onClick={() => showReadCountDetailModal(record)}>{text}</a>)
      },
      {
        title: '点赞数',
        width: 90,
        dataIndex: 'like_count',
        // render: (text: any, record: any) => <a onClick={() => showDataSetModal(record, 0)}>{text || 0}</a>
        render: (text: any, record: any) => text || 0

      },
      {
        title: '评论数',
        width: 90,
        dataIndex: 'comment_count',
        render: (text: any, record: any) => text || 0
      },
      {
        title: '发布时间',
        key: 'published_timestamp',
        dataIndex: 'published_timestamp',
        render: (text: any, record: any) => (
          <div>
            <div>{moment(text).format('YYYY-MM-DD')}</div>
            <div>{moment(text).format('HH:mm:ss')}</div>
          </div>
        ),
        width: 100,
      },
    ];
  };

  backToTopic = () => {
    this.props.history.push('/view/orgAccountList');
  };

  closePreview = () => {
    this.setState({
      preview: { ...this.state.preview, visible: false },
    });
  };

  render() {
    const { preview, name, filter, getVisa } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            <Button onClick={this.backToTopic} style={{ marginRight: 8 }}>
              <Icon type="left-circle-o" />
              返回潮鸣号列表
            </Button>
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {this.getCrumb(['用户中心', '潮鸣号管理', name])}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <Select style={{ width: 110, marginLeft: 8 }}
              value={filter.doc_type}
              onChange={(value) => this.changeFilter('doc_type', value)}>
              <Select.Option value="">内容类型</Select.Option>
              <Select.Option value={10}>视频</Select.Option>
              <Select.Option value={12}>短图文</Select.Option>
              <Select.Option value={13}>长文章</Select.Option>
            </Select>
            <Select style={{ width: 110, marginLeft: 8 }}
              value={filter.content_level}
              onChange={(value) => this.changeFilter('content_level', value)}>
              <Select.Option value="">内容等级</Select.Option>
              <Select.Option value={0}>通过</Select.Option>
              <Select.Option value={2}>推荐</Select.Option>
              <Select.Option value={1}>沉底通过</Select.Option>
            </Select>
            <Select style={{ width: 110, marginLeft: 8 }}
              value={filter.retrieved}
              onChange={(value) => this.changeFilter('retrieved', value)}>
              <Select.Option value="">取签状态</Select.Option>
              <Select.Option value={0}>未取稿</Select.Option>
              <Select.Option value={1}>已取稿</Select.Option>
              <Select.Option value={2}>已取签</Select.Option>
            </Select>
          </Row>
          <Table
            func="getOrgUserArticleList"
            index="article_list"
            columns={this.getColumns()}
            pagination={true}
            rowKey="id"
            filter={this.getFilters()}
          />
          <PreviewMCN {...preview} onClose={this.closePreview} />
          <GetVisaModal
            record={getVisa.record}
            visible={getVisa.visible}
            onCancel={() => this.showGetVisaModal(getVisa.record, false)} />
        </div>
      </>
    );
  }
}

export default withRouter(
  connect<OrgUserArticleRecord, OrgUserArticleAllData>()(OrgUserArticleList)
);
