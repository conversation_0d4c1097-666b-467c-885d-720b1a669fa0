import { getTableList } from '@action/tableList';
import { userApi as api } from '@app/api';
import { CommonObject } from '@app/types';
import { A, Table } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setLoading, setMenu } from '@utils/utils';
import { Button, Col, Divider, Icon, Input, message, Modal, Row } from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';

@(withRouter as any)
@connect
class PrivilegeGroup extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    setMenu(this);
  }

  getData = (overlap: CommonObject = {}) => {
    const { current, size } = this.props.tableList;
    this.props.dispatch(
      getTableList('getPrivilegeGroupList', 'privilege_group_list', { current, size, ...overlap }),
    );
  }

  getColumns = () => {
    return [
      {
        title: 'ID',
        key: 'id',
        dataIndex: 'id',
        width: 70,
      },
      {
        title: '组名',
        key: 'group_name',
        dataIndex: 'group_name',
      },
      {
        title: '创建时间',
        key: 'time',
        dataIndex: 'created_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
      },
      {
        title: '创建人',
        key: 'created_by',
        dataIndex: 'created_by',
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <span>
            {requirePerm(this, 'privilege_group:update')(
              <A onClick={() => this.editRecord(record)}>编辑</A>,
            )}
            <Divider type="vertical" />
            {requirePerm(this, 'privilege_user:list')(
              <A
                onClick={() =>
                  this.props.history.push(
                    `/view/privilegeGroupUser/${record.id}/${record.group_name}`,
                  )
                }
              >
                管理用户
              </A>,
            )}
            <Divider type="vertical" />
            {requirePerm(this, 'privilege_group:deleted')(
              <A onClick={() => this.deleteRecord(record)}>删除</A>,
            )}
          </span>
        ),
        width: 180,
      },
    ];
  }

  deleteRecord = (record: any) => {
    Modal.confirm({
      title: <p>确认删除特权组“{record.group_name}”吗？</p>,
      onOk: () => {
        setLoading(this, true);
        api
          .deletePrivilegeGroup({ id: record.id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  }

  editRecord = (record: any) => {
    let name = record.group_name || '';
    const handleNameChange = (e: any) => (name = e.target.value);
    Modal.confirm({
      title: <p>{record.id ? '编辑' : '添加'}特权组</p>,
      content: (
        <Input placeholder="输入特权组名称" defaultValue={name} onChange={handleNameChange} />
      ),
      onOk: (closeFunc: any) => {
        if (name === '') {
          message.error('请填写名称');
          return;
        }
        if (name.length > 30) {
          message.error('名称长度不能大于30个字');
          return;
        }
        let func = 'createPrivilegeGroup';
        const body: any = { group_name: name };
        if (record.id) {
          func = 'updatePrivilegeGroup';
          body.id = record.id;
        }
        setLoading(this, true);
        api[func as keyof typeof api](body)
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
            closeFunc();
          })
          .catch(() => setLoading(this, false));
      },
    });
  }

  render() {
    return (
      <React.Fragment>
        <Row className="layout-infobar">
          <Col span={12}>
            <Button onClick={() => this.props.history.push('/view/userList')}>
              <Icon type="left-circle" /> 返回用户列表
            </Button>
            {requirePerm(this, 'privilege_group:create')(
              <Button onClick={() => this.editRecord({})} style={{ marginLeft: 8 }}>
                <Icon type="plus-circle" /> 添加特权组
              </Button>,
            )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getPrivilegeGroupList"
            index="privilege_group_list"
            rowKey="id"
            filter={{}}
            pagination={true}
            columns={this.getColumns()}
          />
        </div>
      </React.Fragment>
    );
  }
}

export default PrivilegeGroup;
