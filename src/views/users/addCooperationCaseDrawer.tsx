import SearchAndInput from '@components/common/newNewsSearchAndInput';
import {
  Button,
  Checkbox,
  Dropdown,
  Form,
  Icon,
  Input,
  InputNumber,
  Menu,
  Modal,
  Radio,
  Select,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { communityApi, userApi } from '@app/api';
import '@components/business/styles/business.scss';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { A, Drawer, ImageUploader, VideoUploader } from '@app/components/common';

const AddCooperationCaseDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const { getFieldDecorator } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
      doSubmit,
    }),
    []
  );

  const doSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body = {
          ...values,
        };

        if (props.formContent?.id) {
          body.id = props.formContent?.id;
        }
        dispatch(setConfig({ mLoading: true }));
        userApi
          .editCooperationCase(body)
          .then(() => {
            dispatch(setConfig({ mLoading: false }));
            message.success('操作成功');
            props.onEnd();
          })
          .catch(() => {
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  return (
    <Drawer
      title={!!props.formContent ? '编辑活动案例' : '添加活动案例'}
      visible={props.visible}
      skey={props.skey}
      onClose={props.onClose}
      onOk={doSubmit}
      okText="保存"
      // footer={((<><Button style={{ width: 100, marginRight: 8 }} onClick={props.onClose}>取消</Button>
      //   <Dropdown trigger={['hover']} overlay={menu} placement="topCenter">
      //     <PermButton style={{ width: 100 }} type="primary" perm='' onClick={props.onOk}>
      //       保存<Icon type="up" />
      //     </PermButton>
      //   </Dropdown>

      // </>))}
    >
      <Form {...formLayout}>
        <Form.Item label="标题">
          {getFieldDecorator('title', {
            initialValue: props.formContent?.title,
            rules: [
              {
                required: true,
                message: '请输入标题',
                whitespace: true,
              },
              {
                max: 64,
                message: '最多64字',
              },
            ],
          })(<Input placeholder="请输入案例标题，最多64字"></Input>)}
        </Form.Item>

        <Form.Item label="封面图" extra="支持jpg、png、jpeg格式，比例为2:1">
          {getFieldDecorator('pic', {
            initialValue: props.formContent?.pic,
            rules: [
              {
                required: true,
                message: '请上传封面图',
              },
            ],
          })(<ImageUploader ratio={2 / 1} />)}
        </Form.Item>

        <Form.Item label="链接">
          {getFieldDecorator('link', {
            initialValue: props.formContent?.link,
            rules: [
              {
                required: true,
                validator: (rule: any, value: any, callback: any) => {
                  const regex = /^https?:\/\//;
                  if (!value) {
                    return callback('请输入链接');
                  } else if (!regex.test(value)) {
                    return callback('请正确填写链接格式');
                  } else {
                    return callback();
                  }
                },
              },
            ],
          })(<Input placeholder="请输入案例介绍链接"></Input>)}
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'AddCooperationCaseDrawer' })(
  forwardRef<any, any>(AddCooperationCaseDrawer)
);
