import { Button, Form, Icon, Input, InputNumber, Modal, Radio, Spin, Tooltip, message } from 'antd'
import React, { forwardRef, useImperativeHandle, useState } from 'react'

import { userApi } from '@app/api';

const ClassAccountSortModal = (props: any, ref: any) => {

  const [loading, setLoading] = useState(false)
  const { getFieldDecorator } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  const formLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true)
        const parmas = {
          ...props.formContent,
        }
        if (values.type == 1) {
          parmas.position = values.position
        }

        userApi.orderOrgClassAccount(parmas).then((res: any) => {
          message.success(!props.record ? '新增成功' : '更新成功');
          setLoading(false)
          props.onOk && props.onOk()
        }).catch(() => {
          // message.error('添加失败');
          setLoading(false)
        })
      } else {
        message.error('请检查表单内容');
        setLoading(false)
      }
    });
  }

  // const handleTypeChange = (e: any) => {
  //   console.log('xx', e)
  // }
  return <Modal
    width={500}
    visible={props.visible}
    title='调整排序'
    key={props.skey}
    onCancel={() => { if (!loading) { props.onCancel && props.onCancel() } }}
    onOk={handleSubmit}
    maskClosable={false}
    destroyOnClose={true}
  >
    <Spin spinning={false}>
      <Form {...formLayout}>
        <Form.Item label="排序方式">
          {getFieldDecorator('type', {
            initialValue: props.fix ? 1 : 0,
            rules: [
              {
                required: true,
                message: '请选择排序方式',
              }
            ],
          })(<Radio.Group>
            <Radio value={0}>自动排序</Radio>
            <Radio value={1}>固定排序</Radio>
          </Radio.Group>
          )}
        </Form.Item>

        {props.form.getFieldValue('type') == 1 && <Form.Item label="排序值">
          {getFieldDecorator('position', {
            initialValue: props.fix ? props.fixPos : props.pos,
            rules: [
              {
                required: true,
                message: '请输入排序值',
              }
            ],
          })(<InputNumber
            placeholder="请输入排序值数字"
            min={1}
            max={props.max}
            style={{ width: '100%' }}
            precision={0}
          />)}
        </Form.Item>}

      </Form>
    </Spin>
  </Modal>

}

export default Form.create<any>({ name: 'ClassAccountSortModal' })(forwardRef<any, any>(ClassAccountSortModal));
