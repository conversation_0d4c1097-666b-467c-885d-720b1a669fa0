import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Col, Divider, Icon, Input, message, Modal, Row, Select, Tooltip } from "antd";
import { getCrumb, UserDetail } from "@app/utils/utils"
import { useHistory } from "react-router";
import { getTableList } from '@app/action/tableList';
import Radio from "antd/es/radio";
import { Table } from '@components/common';
import { useDispatch, useSelector } from "react-redux";
import { opApi, sysApi, userApi } from "@app/api";
import moment from "moment";
import { setConfig } from "@app/action/config";
import { PermA } from "@components/permItems";

export default function AuthApply(props: any) {
    const dispatch = useDispatch();
    const history = useHistory()
    const { current, size } = useSelector((state: any) => state.tableList)
    const [filter, setFilter] = useState({
        status: 0,
        main_type: '',
        type: 1,
        search_type: 0,
        keyword: ''
    })
    const [searchState, setSearchState] = useState({
        search_type: 0,
        keyword: ''
    })
    const [user, setUser] = useState({
        key: Date.now(),
        visible: false,
        detail: {},
    })
    const [approved, setApproved] = useState({
        key: Date.now(),
        visible: false,
        long_video_id: '',
        id: null,
        permissionGroupList: []
    })
    const [failedReview, setFailedReview] = useState({
        key: Date.now(),
        visible: false,
        reason: '',
        id: null
    })

    const showUserDetail = (record: any) => {
        dispatch(setConfig({ loading: true }))
        userApi
            .getUserDetail({ accountId: record.account_id })
            .then((r: any) => {
                dispatch(setConfig({ loading: false }))
                setUser({
                    visible: true,
                    key: Date.now(),
                    detail: r.data.account,
                })
            })
            .catch(() => {
                dispatch(setConfig({ loading: false }))
            });
    }
    const approvedHandleOk = () => {
        console.log('Selected value:', selectedValue);
    }
    const reviewFailed = (record: any) => {
    }
    const approvedOk = () => {
        if (!approved.long_video_id || approved.long_video_id == '')
            return message.error('请选择加入的长视频权限组')
        sysApi.permissionsReview({
            status: 1,
            long_video_id: approved.long_video_id,
            id: approved.id
        }).then((r: any) => {
            setApproved({
                ...approved,
                long_video_id: '',
                visible: false
            })
            getData()
        }).catch(() => {
            setApproved({
                ...approved,
                long_video_id: '',
                visible: false
            })
            getData()
        });
    }
    const failedReviewOk = () => {
        let { reason } = failedReview
        if (!reason)
            return message.error('请输入原因')
        if (reason.match(/^\s+$/)) {
            return message.error('原因不能为空格')
        }
        sysApi.permissionsReview({
            status: 2,
            reason: failedReview.reason.trim(),
            id: failedReview.id
        }).then((r: any) => {
            setFailedReview({
                ...failedReview,
                reason: '',
                visible: false
            })
            getData()
        }).catch(() => {
            setFailedReview({
                ...failedReview,
                reason: '',
                visible: false
            })
        });
        // setApproved({
        //     ...approved,
        //     visible: false
        // })

    }
    const getColumns = () => {

        const getSeq = (i: number) => (current - 1) * size + i + 1;
        const columns: any = [
            {
                title: '序号',
                key: 'seq',
                render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
                width: 90,
            },
            {
                title: '用户昵称',
                dataIndex: 'nick_name',
                key: 'nick_name',
                width: 120,
                render(text: string, record: any) {
                    return <a onClick={() => showUserDetail(record)}>{text}</a>
                }
            },
            {
                title: '已发布视频数',
                dataIndex: 'video_count',
                key: 'video_count',
                width: 120
            },
            {
                title: '申请理由',
                key: 'reason',
                dataIndex: 'reason',
                render(text: any) {
                    return <p
                        style={{ whiteSpace: 'pre-wrap' }}
                        title={text}
                    >
                        {text}
                    </p>
                }
            },
            {
                title: `${filter.status == 0 ? '申请' : '操作'}时间`,
                dataIndex: 'apply_at',
                key: 'apply_at',
                render: (text: any, record: any) => moment(filter.status == 0 ? text : record.updated_at).format('YYYY-MM-DD HH:mm:ss'),
                width: 155
            },
            {
                title: '操作',
                key: 'op',
                align: 'center',
                render(record: any) {
                    return <span>
                        <PermA perm="ugc:encourage:permissions:review:1" onClick={() => {
                            opApi.getSimpleList().then((r: any) => {
                                setApproved({
                                    ...approved,
                                    id: record.id,
                                    visible: true,
                                    permissionGroupList: r.data.list
                                })
                            }).catch(() => {
                            });
                        }
                        }>通过</PermA>
                        <Divider type="vertical" />
                        <PermA perm="ugc:encourage:permissions:review:1" onClick={() => setFailedReview({
                            ...failedReview,
                            id: record.id,
                            visible: true,
                            reason: ''
                        })}>不通过</PermA>
                    </span>
                },
                width: 120,
            },
        ]
        if (filter.status === 1) {
            delete columns[2]
            columns.splice(4, 0, {
                title: <span>
                    加入权限组&nbsp;
                    <Tooltip
                        title="此处显示的是在通过这条申请时加入的权限组。如后续更改权限，信息不作更新。"
                    >
                        <Icon type="question-circle" style={{ marginRight: 8 }} />
                    </Tooltip>
                </span>,
                key: 'long_video_name',
                dataIndex: 'long_video_name',
                width: 120
            }, {
                title: '操作人',
                key: 'updated_by',
                dataIndex: 'updated_by',
                width: 120
            })
            delete columns[7]
        }
        if (filter.status === -1) {
            delete columns[2]
            columns.splice(4, 0, {
                title: '原因',
                key: 'reject_reason',
                dataIndex: 'reject_reason',
                width: 160,
                render(text: any) {
                    return <div
                        title={text}
                    >
                        {text}
                    </div>
                }
            }, {
                title: '操作人',
                key: 'updated_by',
                dataIndex: 'updated_by',
                width: 120
            })
            delete columns[7]
        }
        console.log('被调用', columns)
        return columns
    }


    const changeFilter = (key: string, val: any, goToFirstPage = false) => {
        const newFilter = {
            ...filter,
            [key]: val
        }
        setFilter(newFilter)
        getData(goToFirstPage, newFilter)
    }


    const getData = (goToFirstPage = false, newFilter = filter) => {
        let cur = goToFirstPage ? 1 : current
        const params: any = { current: cur, size, ...newFilter }
        if (!params.keyword) {
            delete params.keyword
            delete params.search_type
        }
        // dispatch(getTableList('getAuditList', 'account_list', params));
        dispatch(getTableList('getLongVideoDurationList', 'list', params));
    }
    useEffect(() => {
        getData()
    }, [])
    return (
        <>
            <Row className="layout-infobar">
                <Col span={12}>
                    <Button onClick={() => history.push('/view/orgAccountList')}><Icon type="left-circle" />返回潮鸣号管理</Button>
                </Col>
                <Col span={12} className="layout-breadcrumb">
                    {getCrumb(props.breadCrumb)}
                </Col>
            </Row>
            <div className="component-content">
                <Row style={{ marginBottom: 16 }}>
                    <Col span={16}>
                        <Radio.Group buttonStyle="solid"
                            value={filter.status}
                            onChange={(e) => changeFilter('status', e.target.value, true)}>
                            <Radio.Button value={0}>待审核</Radio.Button>
                            <Radio.Button value={1}>已通过</Radio.Button>
                            <Radio.Button value={-1}>不通过</Radio.Button>
                        </Radio.Group>
                    </Col>
                    <Col span={8} style={{ textAlign: 'right' }}>

                    </Col>
                </Row>
                <Table
                    func="getLongVideoDurationList"
                    index="list"
                    filter={filter}
                    pagination={true}
                    rowKey="id"
                    columns={getColumns()}
                />
                <Modal
                    visible={user.visible}
                    key={user.key}
                    title="用户详情"
                    width={800}
                    onCancel={() => setUser({ ...user, visible: false })}
                    onOk={() => setUser({ ...user, visible: false })}
                >
                    {/*{getUserDetail(user.detail)}*/}
                    <UserDetail detail={user.detail} />
                </Modal>
                <Modal
                    visible={approved.visible}
                    key={approved.key}
                    title="通过"
                    width={500}
                    onCancel={() => setApproved({ ...approved, visible: false })}
                    onOk={() => {
                        approvedOk()
                    }}
                >
                    <p>请选择加入的长视频权限组：</p>
                    <Select
                        style={{ width: '100%' }}
                        value={approved.long_video_id}
                        onChange={(value: number) => setApproved({ ...approved, long_video_id: value })}>
                        <Select.Option key={''} value={''}>请选择权限组</Select.Option>
                        {
                            approved.permissionGroupList.map((item: { id: number, name: string, duration: number }) => {
                                return <Select.Option key={item.id}
                                    value={item.id}>{item.name}-{item.duration}秒</Select.Option>
                            })
                        }
                    </Select>
                </Modal>
                <Modal
                    visible={failedReview.visible}
                    key={failedReview.key}
                    title="不通过"
                    width={500}
                    onCancel={() => setFailedReview({ ...failedReview, visible: false })}
                    onOk={() => {
                        failedReviewOk()
                    }}
                    okButtonProps={{ disabled: failedReview.reason == '' || !failedReview.reason }}
                >
                    <p>请输入原因（将发送系统通知/站内信告知用户）：</p>
                    <Input
                        value={failedReview.reason}
                        placeholder={'请输入原因，最多50字'}
                        maxLength={50}
                        onChange={(e) => setFailedReview({ ...failedReview, reason: e.target.value })} />
                </Modal>
            </div>
        </>
    )
}