import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useStore, useDispatch } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, A } from '@components/common';
import { getCrumb } from '@app/utils/utils';
import { Row, Col, Input, Divider, Button, message, Icon, Modal, Form } from 'antd';
import { getTableList } from '@app/action/tableList';
import { userApi as api } from '@app/api';
import useXHR from '@utils/useXhr';

export default function ChaokeStar(props: any) {
  const [form, setForm] = useState(
    () =>
      ({
        visible: false,
        key: Date.now(),
      } as any)
  );
  const dispatch = useDispatch();
  const history = useHistory();
  const { loading, run } = useXHR();

  const getList = () => {
    dispatch(getTableList('getChaokeStarList', 'list', {}));
  };

  const formChange = (e: any) => {
    setForm({
      ...form,
      name: e.target.value,
    });
  };

  const editRecord = (record: any) => {
    setForm({
      visible: true,
      key: Date.now(),
      id: record.id,
      name: record.name,
    });
  };
  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确认删除分类“${record.name}”？`,
      onOk: () => {
        run(api.deleteChaokeStar, { id: record.id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const columns = useMemo(() => {
    return [
      {
        title: 'ID',
        dataIndex: 'id',
      },
      {
        title: '分类',
        dataIndex: 'name',
      },
      {
        title: '操作',
        key: 'op',
        render: (record: any) => (
          <span>
            <A disabled={record.name === '未分类'} onClick={() => editRecord(record)}>
              编辑
            </A>
            <Divider type="vertical" />
            <A disabled={record.name === '未分类'} onClick={() => deleteRecord(record)}>
              删除
            </A>
          </span>
        ),
        width: 110,
      },
    ];
  }, []);

  useEffect(() => {
    getList();
  }, []);

  const submitKeyword = () => {
    if (!form.name) {
      message.error('请填写分类名');
      return;
    }
    if (form.name.length > 6) {
      message.error('分类名不能超过6个字');
      return;
    }
    const body = form.id ? { name: form.name, id: form.id } : { name: form.name };
    run(api.createChaokeStar, body).then(() => {
      message.success('操作成功');
      setForm((s: any) => ({ ...s, visible: false }));
      getList();
    });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button onClick={() => history.push('/view/serviceKeywords')}>
            <Icon type="left-circle" /> 搜索词管理
          </Button>
          <Button
            style={{ marginLeft: 8 }}
            onClick={() => setForm({ visible: true, key: Date.now(), name: '' })}
          >
            <Icon type="plus-circle" /> 新建分类
          </Button>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          func="getChaokeStarList"
          index="list"
          filter={{}}
          columns={columns}
          rowKey="id"
          pagination={false}
        />
        <Modal
          visible={form.visible}
          title={form.id ? '编辑分类' : '新建分类'}
          onCancel={() => setForm((s: any) => ({ ...s, visible: false }))}
          onOk={submitKeyword}
          confirmLoading={loading}
          key={form.key}
        >
          <Form labelCol={{ span: 4 }} wrapperCol={{ span: 19 }}>
            <Form.Item label="分类名" required>
              <Input
                value={form.name}
                onChange={formChange}
                placeholder="请输入不超过6个字的分类名"
              />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </>
  );
}
