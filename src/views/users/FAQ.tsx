import { userApi as api } from '@app/api';
import { ITableProps, IBaseProps, CommonObject } from '@app/types';
import { FAQRecord, FAQAllData } from './users';
import { A, Table, BaseComponent, Drawer } from '@components/common';
import Form from '@components/business/FAQForm';
import { connectTable as connect } from '@utils/connect';
import { Button, Col, Icon, message, Modal, Row, Menu, Dropdown, Divider } from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';

type State = {
  form: {
    visible: boolean;
    key: number;
    title: string;
    content: string;
    id?: number;
  };
  preview: {
    visible: boolean;
    key: number;
    title: '';
    content: '';
  };
  onCount: number;
};

type Props = IBaseProps<ITableProps<FAQRecord, FAQAllData>>;

class FAQManager extends BaseComponent<ITableProps<FAQRecord, FAQAllData>, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      form: {
        visible: false,
        key: Date.now(),
        title: '',
        content: '',
      },
      preview: {
        title: '',
        content: '',
        visible: false,
        key: Date.now() + 1,
      },
      onCount: 0,
    };
  }

  componentDidMount() {
    this.setMenu();
    this.getData({ current: 1, size: 10 });
  }

  componentDidUpdate(prevProps: Props) {
    if (
      prevProps.tableList.timestamp !== this.props.tableList.timestamp &&
      Boolean(this.props.tableList.allData.help_list)
    ) {
      this.setState({
        onCount: this.props.tableList.allData.on_show_count,
      });
    }
  }

  getData = (overlap: CommonObject = {}) => {
    const { current, size } = this.props.tableList;
    this.dispatchTable('getFAQList', 'help_list', { current, size, ...overlap });
  }

  getColumns = () => {
    const { current, size, total } = this.props.tableList;
    const { onCount } = this.state;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    const getDropdown = (record: any) => {
      const menu = (
        <Menu>
          {this.requirePerm('help:update')(
            <Menu.Item onClick={this.editRecord.bind(this, record)}>编辑</Menu.Item>
          )}
          {this.requirePerm('help:update_enabled')(
            <Menu.Item onClick={this.updateStatus.bind(this, record)}>
              {record.enabled ? '下线' : '上线'}
            </Menu.Item>
          )}
          {this.requirePerm('help:delete')(
            <Menu.Item onClick={this.deleteRecord.bind(this, record)}>删除</Menu.Item>
          )}
        </Menu>
      );
      return (
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };

    return [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) => (
          <span>
            {this.requirePerm('help:update_sort')(
              <A
                disabled={getSeq(i) === 0 || !record.enabled}
                className="sort-up"
                onClick={this.exchangeOrder.bind(this, record.id, 0)}
              >
                <Icon type="up-circle" theme="filled" />
              </A>
            )}{' '}
            {this.requirePerm('help:update_sort')(
              <A
                disabled={getSeq(i) >= onCount || !record.enabled}
                className="sort-down"
                onClick={this.exchangeOrder.bind(this, record.id, 1)}
              >
                <Icon type="down-circle" theme="filled" />
              </A>
            )}
          </span>
        ),
        width: 70,
      },
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '问题描述',
        key: 'title',
        dataIndex: 'title',
        render: (text: any, record: any) => <A onClick={this.preview.bind(this, record)}>{text}</A>,
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'enabled',
        render: (text: boolean) => <span>{text ? '上线' : '下线'}</span>,
        width: 80,
      },
      {
        title: '创建人',
        key: 'created_by',
        dataIndex: 'created_by',
        width: 110,
      },
      {
        title: '创建时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: number) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 170,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => getDropdown(record),
        width: 70,
      },
    ];
  }

  editRecord = (record: any = {}) => {
    this.setState({
      form: {
        visible: true,
        key: Date.now(),
        id: record.id || '',
        title: record.title || '',
        content: record.content || '',
      },
    });
  }

  exchangeOrder = (id: number, sortFlag: number) => {
    this.setLoading(true);
    api
      .sortFAQ({ id, sort_flag: sortFlag })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setLoading(false);
      })
      .catch(() => {
        this.setLoading(false);
      });
  }

  updateStatus = (record: any) => {
    this.setLoading(true);
    api
      .updateFAQStatus({ id: record.id, enabled: !record.enabled })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setLoading(false);
      })
      .catch(() => {
        this.setLoading(false);
      });
  }

  deleteRecord = (record: any) => {
    Modal.confirm({
      title: '确定删除吗？',
      onOk: () => {
        this.setLoading(true);
        api
          .deleteFAQ({ id: record.id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            this.setLoading(false);
          })
          .catch(() => {
            this.setLoading(false);
          });
      },
    });
  }

  preview = (record: any) => {
    this.setState({
      preview: {
        visible: true,
        key: Date.now(),
        content: record.content,
        title: record.title,
      },
    });
  }

  closeDrawer = () => {
    this.setState({
      form: { ...this.state.form, visible: false },
      preview: { ...this.state.preview, visible: false },
    });
  }

  onSubmitEnd = () => {
    this.getData();
    this.closeDrawer();
  }

  render() {
    const { preview, form } = this.state;
    return (
      <React.Fragment>
        <Row className="layout-infobar">
          <Col span={12}>
            {this.requirePerm('help:create')(
              <Button onClick={this.editRecord}>
                <Icon type="plus-circle-o" />
                创建问题
              </Button>
            )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {this.getCrumb()}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getFAQList"
            index="help_list"
            pagination={true}
            filter={{}}
            columns={this.getColumns()}
            rowKey="id"
          />
          <Drawer
            title="预览问题"
            onClose={this.closeDrawer}
            skey={preview.key}
            visible={preview.visible}
            closeText="关闭"
          >
            <Row style={{ textAlign: 'center', fontSize: 24 }}>{preview.title}</Row>
            <Divider type="horizontal" />
            <Row>
              <div dangerouslySetInnerHTML={{ __html: preview.content }} />
            </Row>
          </Drawer>
          <Drawer
            title={`${form.id ? '编辑' : '创建'}问题`}
            onClose={this.closeDrawer}
            skey={form.key}
            visible={form.visible}
            onOk={this.handleSubmitForm.bind(this, 'form')}
          >
            <Form
              formContent={form}
              wrappedComponentRef={this.setFormRef.bind(this, 'form')}
              onEnd={this.onSubmitEnd}
            />
          </Drawer>
        </div>
      </React.Fragment>
    );
  }
}

export default withRouter(connect<FAQRecord, FAQAllData>()(FAQManager));
