/* eslint-disable no-return-assign */
declare const BUILD_ENV: 'dev' | 'test' | 'prev' | 'prod' | 'testb';

import { getTableList } from '@action/tableList';
import { userApi as api } from '@app/api';
import { CommonObject, IOperationActionData } from '@app/types';
import { A, Table, FileUploader } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, UserDetail, requirePerm, setLoading, setMenu, copy } from '@utils/utils';
import {
  Button,
  Col,
  Icon,
  Input,
  message,
  Modal,
  Row,
  Select,
  Menu,
  Dropdown,
  Form,
  Radio,
  Tooltip,
  Timeline,
  DatePicker,
} from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import ChangeMobileModal from './changeMobileModal';

@(withRouter as any)
@connect
class UserList extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      filter: {
        status: '2',
        keyword: '',
        searchType: 'nick_name',
        cert_type: '',
        in_pub_white: '', // 白名单筛选字段
        quality_type: '',
        official_cert_status: '',
        virtual_type: '',
      },
      currentType: 'nick_name',
      currentKeyword: '',
      visible: false,
      key: Date.now(),
      detail: {},
      certForm: {
        visible: false,
        key: Date.now() + 1,
        id: '',
        type: 0,
        info: '',
      },
      officialCertForm: {
        visible: false,
        key: Date.now() + 1,
        id: '',
        type: 0,
        info: '',
      },
      honorForm: {
        visible: false,
        key: Date.now() + 1,
        account_id: '',
        quality_type: '',
        valid_time: '',
      },
      form: {
        visible: false,
        key: Date.now(),
        id: '',
        password: '',
        type: 1,
        err: '',
      },
      operateLog: {
        visible: false,
        title: '',
        content: '',
        logs: [],
        key: Date.now(),
      } as any,
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    setMenu(this);
  }

  getData = (overlap: CommonObject = {}, filter = this.getFilter()) => {
    this.props.dispatch(getTableList('getUserList', 'account_list', { ...filter, ...overlap }));
  };

  getFilter = () => {
    const {
      status,
      keyword,
      searchType,
      cert_type,
      in_pub_white,
      quality_type,
      official_cert_status,
      virtual_type,
    } = this.state.filter;
    const { current, size } = this.props.tableList;
    const filter: any = { current, size };
    if (status) {
      filter.status = status;
    }
    if (cert_type) {
      filter.cert_type = cert_type;
    }
    if (quality_type) {
      filter.quality_type = quality_type;
    }
    if (in_pub_white) {
      filter.in_pub_white = in_pub_white;
    }
    if (keyword) {
      filter[searchType] = keyword;
    }

    if (official_cert_status) {
      filter.official_cert_status = official_cert_status;
    }

    if (virtual_type) {
      filter.virtual_type = virtual_type;
    }

    return filter;
  };
  getOperateLog = (record: any) => {
    let arr = [];
    api
      .getTaskLog({ account_id: record.id })
      .then((r: any) => {
        const arr = [];
        const data = r.data.logs;
        let keyArr = Object.keys(data);
        keyArr.map((item) => {
          arr.push({ date: item, list: data[item].reverse() });
        });
        console.log(arr);
        //
        this.setState({
          operateLog: {
            visible: true,
            nick_name: record.nick_name,
            chao_id: record.chao_id,
            logs: arr,
            key: Date.now(),
          } as any,
        });
      })
      .catch();
  };
  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    const getDropDown = (record: any) => {
      const menu = (
        <Menu>
          {requirePerm(
            this,
            'account:detail'
          )(<Menu.Item onClick={() => this.showDetail(record)}>查看详情</Menu.Item>)}
          {requirePerm(
            this,
            'account:reset_quality'
          )(<Menu.Item onClick={() => this.setHonor(record)}>荣誉标识</Menu.Item>)}
          {/* {record.status === 1 &&
            requirePerm(
              this,
              'account:log_off'
            )(<Menu.Item onClick={() => this.cancelDelete(record)}>解除注销</Menu.Item>)} */}
          {requirePerm(
            this,
            'account:update_user_cert'
          )(<Menu.Item onClick={() => this.setCert(record)}>潮鸣号认证</Menu.Item>)}

          {requirePerm(
            this,
            'account:update_official_cert'
          )(<Menu.Item onClick={() => this.setOfficialCert(record)}>官方认证</Menu.Item>)}

          {/* {requirePerm(
            this,
            'account:update_user_tag'
          )(
            <Menu.Item onClick={() => this.setUserTag(record)}>
              {record.user_tag === 1 ? '取消' : '设为'}顺丰侠
            </Menu.Item>
          )} */}
          {(record.face_cert === 0 || record.face_cert === 1) &&
            record.status === 0 &&
            requirePerm(
              this,
              'account:pub_white'
            )(
              <Menu.Item onClick={() => this.toggleWhiteList(record)}>
                {record.in_pub_white ? '取消发布免实名' : '发布免实名'}
              </Menu.Item>
            )}
          {requirePerm(
            this,
            'account:oldPhoneValid'
          )(
            <Menu.Item onClick={() => this.togglePhoneValid(record)}>
              {!record.old_phone_valid ? '取消换绑免验证' : '换绑免验证'}
            </Menu.Item>
          )}
          {requirePerm(
            this,
            'account_forbid:forbid'
          )(
            <Menu.Item onClick={() => this.blackListRecord(record)}>
              {record.status === 2 ? '取消' : '加入'}黑名单
            </Menu.Item>
          )}
          {requirePerm(
            this,
            'account:shadow_convert'
          )(
            <Menu.Item onClick={() => this.shadowConvert(record)}>
              {record.virtual_type != 2 ? '设为' : '取消'}马甲号
            </Menu.Item>
          )}
          {requirePerm(
            this,
            'account:reset_portrait'
          )(<Menu.Item onClick={() => this.resetAvatar(record)}>重置头像</Menu.Item>)}

          {requirePerm(
            this,
            'account:reset_nick_name'
          )(<Menu.Item onClick={() => this.resetNickname(record)}>重置昵称</Menu.Item>)}

          {requirePerm(
            this,
            'account:update_account_pwd:normal'
          )(<Menu.Item onClick={this.resetPassword.bind(this, record)}>重置密码</Menu.Item>)}

          {requirePerm(
            this,
            // 'account:emergency_phone'
            ''
          )(<Menu.Item onClick={this.resetMobile.bind(this, record)}>紧急联系人</Menu.Item>)}

          <Menu.Item
            onClick={() => {
              const host =
                {
                  dev: 'https://tmtest.tidenews.com.cn',
                  test: 'https://tmtest.tidenews.com.cn',
                  prev: 'https://tmprev.tidenews.com.cn',
                  prod: 'https://tidenews.com.cn',
                  testb: 'https://tmtest.tidenews.com.cn',
                }[BUILD_ENV] || 'https://tmtest.tidenews.com.cn';

              const url = `${host}/native/user_page.html?id=${record.id}`;

              copy(url)
                .then(() => {
                  message.success('链接已复制');
                })
                .catch(() => {
                  message.error('复制失败');
                });
            }}
          >
            复制主页地址
          </Menu.Item>

          {record.face_cert >= 2 &&
            requirePerm(
              this,
              'account:cancel_face_cert'
            )(<Menu.Item onClick={this.cancelFaceCert.bind(this, record)}>删除实人认证</Menu.Item>)}
        </Menu>
      );
      return (
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };
    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '用户昵称',
        key: 'nick_name',
        dataIndex: 'nick_name',
      },
      {
        title: '小潮号',
        key: 'chao_id',
        dataIndex: 'chao_id',
        width: 120,
      },
      {
        title: '用户头像',
        key: 'image_url',
        dataIndex: 'image_url',
        render: (text: string) => <img src={text} className="list-pic" alt="" />,
        width: 90,
      },
      {
        title: '荣誉标识',
        key: 'quality_type',
        dataIndex: 'quality_type',
        render: (text: number, record: any) => (
          <span style={{ cursor: 'pointer' }} onClick={this.getOperateLog.bind(this, record)}>
            {['无', '活跃', '品质'][text || 0]}
          </span>
        ),
        width: 90,
      },
      // {
      //   title: '认证信息',
      //   key: 'cert_type',
      //   dataIndex: 'cert_type',
      //   render: (text: number) => <span>{['', '个人认证', '机构认证'][text]}</span>,
      //   width: 90,
      // },
      {
        title: '绑定手机号',
        key: 'phone_number',
        dataIndex: 'phone_number',
        width: 150,
      },
      // {
      //   title: '状态',
      //   key: 'forbidden',
      //   dataIndex: 'forbidden',
      //   render: (text: any, record: any) => (
      //     <span>{[text ? '禁言' : '正常', '注销', '黑名单'][record.status]}</span>
      //   ),
      //   width: 90,
      // },
      {
        title: '实人认证',
        key: 'face_cert',
        dataIndex: 'face_cert',
        render: (text: number) => <span>{['未认证', '未认证', '已认证', '已认证'][text]}</span>,
        width: 90,
      },
      {
        title: '官方认证',
        key: 'official_cert_status',
        dataIndex: 'official_cert_status',
        render: (text: number) => <span>{['否', '是'][text]}</span>,
        width: 90,
      },
      {
        title: '账号类型',
        dataIndex: 'virtual_type',
        width: 80,
        render: (text: number) => <span>{['普通', '虚拟', '马甲'][text]}</span>,
      },
      // {
      //     title: '注册时间',
      //     key: 'created_at',
      //     dataIndex: 'created_at',
      //     render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
      //     width: 170,
      // },
      {
        title: '用户ID',
        key: 'id',
        dataIndex: 'id',
        width: 120,
      },
      {
        title: (
          <span>
            操作&nbsp;
            <Tooltip title="1、设为顺丰侠，将用户设置为顺丰侠" placement="topLeft">
              <Icon type="question-circle" />
            </Tooltip>
          </span>
        ),
        key: 'op',
        render: (text: any, record: any) => getDropDown(record),
        width: 70,
      },
    ];
  };

  blackListRecord = (record: any) => {
    Modal.confirm({
      title: record.status === 2 ? '确定从黑名单移除？' : '确定拉入黑名单？',
      content: record.status === 2 ? '' : '拉黑后，用户无法登录APP',
      onOk: () => {
        setLoading(this, true);
        api
          .setUserBlackList({ id: record.id, api: record.status === 2 ? 'unforbid' : 'forbid' })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };

  shadowConvert = (record: any) => {
    Modal.confirm({
      title: record.virtual_type != 2 ? '确定设为马甲号？' : '确定取消马甲号？',
      content:
        record.virtual_type != 2
          ? '设为马甲号后，可用于在评论系统后台发布评论（操作后请联系评论系统负责人，同步最新马甲号数据）'
          : '马甲号改为普通账号后，将无法在评论系统后台使用',
      onOk: () => {
        setLoading(this, true);
        api
          .shadowConvert({ id: record.id, shadow: record.virtual_type != 2 })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };

  batchBlackList = () => {
    let fileUrl = '';
    const fileChange = (url: string) => (fileUrl = url);
    Modal.confirm({
      icon: null,
      title: (
        <Row>
          <Col span={8}>
            <Tooltip
              title={
                <span>
                  文件格式如下：
                  <img src="/assets/tipx.png" alt="" />
                </span>
              }
            >
              <Icon type="question-circle" />
            </Tooltip>
            上传用户：
          </Col>
          <Col span={16}>
            <FileUploader onChange={fileChange} accept=".xls,.xlsx" />
          </Col>
        </Row>
      ),
      content: '为保证性能单个Excel表格不要超过5000条数据',
      onOk: (closeFunc: Function) => {
        if (!fileUrl) {
          message.error('请上传文件');
          return;
        }
        api
          .batchUserBlackList({
            excel_url: fileUrl,
          })
          .then((res) => {
            if (res.data) {
              const fileName = `black_list_fail_list_${Date.now()}.xlsx`;
              Modal.info({
                title: '部分用户拉黑失败，点击下载查看：',
                content: (
                  <a href={window.URL.createObjectURL(res.data)} download={fileName}>
                    {fileName}
                  </a>
                ),
                onOk: () => {
                  closeFunc();
                  this.getData();
                },
              });
            } else {
              message.success('操作成功');
              closeFunc();
              this.getData();
            }
          });
      },
    });
  };

  toggleWhiteList = (record: any) => {
    Modal.confirm({
      title: record.in_pub_white
        ? '确定要取消该用户的发布免实名？'
        : '设置后，该用户在客户端发布内容时，不要求实名认证',
      onOk: () => {
        setLoading(this, true);
        api
          .toggleWhiteList({ id: record.id, pub_white: record.in_pub_white ? 0 : 1 })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };

  togglePhoneValid = (record: any) => {
    Modal.confirm({
      title: !record.old_phone_valid
        ? '确定要取消该用户的换绑免验证？'
        : '设置后，该用户更换绑定手机号时，不要求验证原手机号',
      onOk: () => {
        setLoading(this, true);
        api
          .togglePhoneValid({ id: record.id, old_phone_valid: !record.old_phone_valid ? 1 : 0 })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };

  resetNickname = (record: any) => {
    Modal.confirm({
      title: '确定将用户昵称重置为随机的默认昵称？',
      onOk: () => {
        setLoading(this, true);
        api
          .resetNickName({ account_id: record.id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };

  resetPassword = (record: any) => {
    this.setState({
      form: {
        visible: true,
        key: Date.now(),
        id: record.id,
        password: '',
        type: 1,
        err: '',
      },
    });
  };

  resetMobile = (record: any) => {
    this.setState({
      changeMobileModal: {
        visible: true,
        record,
        key: Date.now(),
      },
    });
  };

  cancelFaceCert = (record: any) => {
    Modal.confirm({
      title: '确定删除实人认证记录？',
      onOk: () => {
        setLoading(this, true);
        api
          .cancelFaceCert({ id: record.id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };

  cancelDelete = (record: any) => {
    setLoading(this, true);
    api
      .setUserDeleteStatus({ account_id: record.id, log_off: false })
      .then(() => {
        message.success('操作成功');
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  resetAvatar = (record: any) => {
    setLoading(this, true);
    api
      .resetUserAvatar({ account_id: record.id })
      .then(() => {
        message.success('操作成功');
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  setUserTag = (record: any) => {
    setLoading(this, true);
    api
      .updateUserTag({ account_id: record.id, user_tag: record.user_tag === 1 ? 0 : 1 })
      .then((r: any) => {
        message.success('操作成功');
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  getNowMonth = () => {
    const today = new Date();
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    lastDayOfMonth.setHours(23, 59, 59, 0);
    const timestamp = lastDayOfMonth.getTime();
    return timestamp;
  };
  setHonor = (record: any) => {
    api.qualityDetail({ account_id: record.id }).then((res) => {
      const { quality } = res.data;
      if (quality && quality.quality_type != 0) {
        if (quality.valid_time && quality.valid_time - this.getNowMount() < 0) {
          quality.quality_type = 0;
          quality.valid_time = '';
        }
        this.setState(
          {
            honorForm: {
              visible: true,
              key: Date.now(),
              account_id: record.id,
              quality_type: quality.quality_type,
              valid_time: quality.valid_time ? moment(quality.valid_time).format('YYYY-MM-DD') : '',
            },
          },
          () => {
            this.qualityTypeChange(quality.quality_type);
          }
        );
      } else {
        this.setState({
          honorForm: {
            visible: true,
            key: Date.now(),
            account_id: record.id,
            quality_type: 0,
          },
        });
      }
    });
  };
  setCert = (record: any) => {
    this.setState({
      certForm: {
        visible: true,
        key: Date.now(),
        type: record.cert_type || 0,
        info: record.cert_information || '',
        id: record.id,
      },
    });
  };

  showDetail = (record: any) => {
    setLoading(this, true);
    api
      .getUserDetail({ accountId: record.id })
      .then((r: any) => {
        this.setState({
          visible: true,
          key: Date.now(),
          detail: r.data.account,
        });
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  submitHonor = () => {
    const { account_id, quality_type, valid_time = '' } = this.state.honorForm;
    const params: any = { account_id, quality_type };
    if (quality_type !== 0) {
      if (!valid_time || valid_time === '') return message.error('请选择有效期');

      params.valid_time = valid_time;
    }

    api
      .updateQualityReset(params)
      .then((r: any) => {
        this.setState({
          honorForm: {
            visible: false,
            key: Date.now(),
            account_id: '',
            quality_type: 0,
          },
        });
        this.getData();
      })
      .catch(() => {
        this.setState({
          honorForm: {
            visible: false,
            key: Date.now(),
            account_id: '',
            quality_type: 0,
          },
        });
        this.getData();
      });
  };
  submitCert = () => {
    const { id, type, info } = this.state.certForm;
    if (type !== 0 && info.trim() === '') {
      message.error('请填写认证信息');
      return;
    }
    if (type !== 0 && info.length > 25) {
      message.error('认证信息不能超过25个字');
      return;
    }
    setLoading(this, true);
    api
      .updateUserCert({
        account_id: id,
        cert_type: type,
        cert_information: type === 0 ? '' : info,
      })
      .then(() => {
        this.closeCert();
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  closeHonor = () => {
    this.setState({
      honorForm: { ...this.state.honorForm, visible: false },
    });
  };
  closeCert = () => {
    this.setState({
      certForm: { ...this.state.certForm, visible: false },
    });
  };

  setOfficialCert = (record: any) => {
    this.setState({
      officialCertForm: {
        visible: true,
        key: Date.now(),
        type: record.official_cert_status || 0,
        info: record.official_cert_info || '',
        id: record.id,
      },
    });
  };

  submitOfficialCert = () => {
    const { id, type, info } = this.state.officialCertForm;
    if (type !== 0 && info.trim() === '') {
      message.error('请填写认证信息');
      return;
    }
    if (type !== 0 && info.length > 25) {
      message.error('认证信息不能超过25个字');
      return;
    }
    setLoading(this, true);
    api
      .updateOfficialCert({
        id,
        info: info?.trim(),
      })
      .then(() => {
        this.closeOfficialCert();
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  closeOfficialCert = () => {
    this.setState({
      officialCertForm: { ...this.state.officialCertForm, visible: false },
    });
  };

  officialCertTypeChange = (e: any) => {
    this.setState({
      officialCertForm: { ...this.state.officialCertForm, type: e.target.value, info: '' },
    });
  };

  officialCertInfoChange = (e: any) => {
    this.setState({
      officialCertForm: { ...this.state.officialCertForm, info: e.target.value },
    });
  };

  setFormType = (value: 1 | 2) => {
    this.setState({
      form: {
        ...this.state.form,
        type: value,
      },
    });
  };

  passwordChange = (e: any) => {
    const { value } = e.target;
    const regex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]+$/;
    let err = '';
    if (value.length < 8 || !regex.test(value)) {
      err = '密码为8-30位，仅支持数字和字母的组合';
    }
    this.setState({
      form: {
        ...this.state.form,
        err,
        password: value,
      },
    });
  };

  closeModal = () => {
    this.setState({
      form: {
        ...this.state.form,
        visible: false,
      },
    });
  };

  submit = () => {
    const { form } = this.state;
    if ((form.type === 1 && form.err) || (form.type === 1 && form.password.length < 8)) {
      message.error('密码为8-30位，仅支持数字和字母的组合');
      return;
    }
    const body: CommonObject = {
      account_id: form.id,
    };
    if (form.type === 1) {
      body.pwd = form.password;
    } else {
      body.pwd = '';
    }
    setLoading(this, true);
    api
      .resetOrgUserPassword(body)
      .then((r: any) => {
        setLoading(this, false);
        this.closeModal();
        Modal.info({
          title: '重置成功',
          content: r.data.pwd,
        });
      })
      .catch(() => {
        setLoading(this, false);
      });
  };

  handleKey = (e: any) => {
    if (e.which === 13) {
      this.setState(
        {
          filter: {
            ...this.state.filter,
            searchType: this.state.currentType,
            keyword: this.state.currentKeyword,
          },
        },
        () => this.getData({ current: 1 })
      );
    }
  };

  typeChange = (e: any) => {
    this.setState({
      certForm: { ...this.state.certForm, type: e.target.value },
    });
  };

  infoChange = (e: any) => {
    this.setState({
      certForm: { ...this.state.certForm, info: e.target.value },
    });
  };

  qualityTypeChange = (e: any) => {
    this.setState({
      honorForm: {
        ...this.state.honorForm,
        quality_type: e,
      },
    });
  };

  disabledDate(current: any) {
    const currentDate = current.date();
    const nextDate = current.clone().endOf('month').date();
    return currentDate !== nextDate;
  }

  validTimeChange = (e: any) => {
    console.log();
  };
  getNowMount = () => {
    const today = new Date(); // 获取当前日期
    const year = today.getFullYear(); // 获取当前年份
    const month = today.getMonth() + 1; // 获取当前月份（注意，月份从0开始，所以需要加一）
    const lastDay = new Date(year, month, 0); // 使用下个月的第0天，即为本月的最后一天
    const day = lastDay.getDate(); // 获取最后一天的日期
    const formattedDate = `${year}-${month.toString().padStart(2, '0')}-${day
      .toString()
      .padStart(2, '0')}`; // 格式化日期为 "YYYY-MM-DD" 的格式
    return formattedDate;
  };
  getNextMonth = (hasTime = false) => {
    // 获取当前时间
    let currentDate = new Date();
    // 设置为下个月的第一天
    currentDate.setMonth(currentDate.getMonth() + 1, 1);
    // 获取下个月的年份和月份
    let year = currentDate.getFullYear();
    let month = currentDate.getMonth() + 1;
    // 获取下个月的天数
    let nextMonthDays = new Date(year, month, 0).getDate();
    // 格式化为 YYYY-MM-DD 或者 YYYY-MM-DD HH:mm:ss 格式
    let formattedDate = year + '-' + (month < 10 ? '0' + month : month) + '-' + nextMonthDays;
    if (hasTime) {
      formattedDate += ' 23:59:59';
    }

    return formattedDate;
  };

  filterChange = (type: string, value: any) => {
    this.setState(
      {
        filter: { ...this.state.filter, [type]: value },
      },
      () => this.getData({ current: 1 })
    );
  };

  render() {
    const { certForm, filter, form, operateLog, honorForm, officialCertForm } = this.state;
    const formLayout = {
      labelCol: { span: 6 },
      wrapperCol: { span: 16 },
    };
    return (
      <>
        <Row className="layout-infobar">
          <Col span={18}>
            {requirePerm(
              this,
              'privilege_group:list_page'
            )(
              <Button
                onClick={() => this.props.history.push('/view/privilegeGroup')}
                style={{ marginRight: 8 }}
              >
                <Icon type="team" /> 特权组管理
              </Button>
            )}
            {requirePerm(
              this,
              'account_forbid:batch_forbid'
            )(
              <Button onClick={this.batchBlackList} style={{ marginRight: 8 }}>
                批量拉黑
              </Button>
            )}
            {requirePerm(
              this,
              'account:shadow_list'
            )(
              <Button
                onClick={() => this.props.history.push('/view/waistcoatUser')}
                style={{ marginRight: 8 }}
              >
                马甲用户管理
              </Button>
            )}
            {requirePerm(
              this,
              'h5Content:view:10'
            )(
              <Button
                onClick={() => this.props.history.push('/view/incentiveProblems')}
                style={{ marginRight: 8 }}
              >
                激励常见问题
              </Button>
            )}
          </Col>
          <Col span={6} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <Col span={12}>
              <Select
                value={filter.status}
                onChange={this.filterChange.bind(this, 'status')}
                style={{ marginRight: 8, width: 120 }}
              >
                <Select.Option value="2">用户状态</Select.Option>
                <Select.Option value="0">正常</Select.Option>
                <Select.Option value="-1">注销</Select.Option>
                <Select.Option value="3">黑名单</Select.Option>
              </Select>
              {/*<Select*/}
              {/*  value={filter.cert_type}*/}
              {/*  onChange={this.filterChange.bind(this, 'cert_type')}*/}
              {/*  style={{ marginRight: 8, width: 120 }}*/}
              {/*>*/}
              {/*  <Select.Option value="">用户认证</Select.Option>*/}
              {/*  <Select.Option value="1">个人认证</Select.Option>*/}
              {/*  <Select.Option value="0">无认证</Select.Option>*/}
              {/*</Select>*/}
              <Select
                value={filter.in_pub_white}
                onChange={this.filterChange.bind(this, 'in_pub_white')}
                style={{ marginRight: 8, width: 120 }}
              >
                <Select.Option value="">发布免实名</Select.Option>
                <Select.Option value="1">免实名</Select.Option>
                <Select.Option value="0">需实名</Select.Option>
              </Select>
              {/* overlayStyle={{ maxWidth: 400 }} */}
              <Tooltip
                title={
                  <>
                    1、非虚拟账号可设置免实名，在客户端发布作品不要求实名认证；
                    <br />
                    2、虚拟账号无需设置，默认跳过实名；
                    <br />
                    3、此处仅筛选手动设置的
                  </>
                }
                style={{ marginRight: 8 }}
              >
                <Icon type="question-circle" style={{ marginRight: 8 }} />
              </Tooltip>
              <Select
                value={filter.quality_type}
                onChange={this.filterChange.bind(this, 'quality_type')}
                style={{ marginRight: 8, width: 120 }}
              >
                <Select.Option value="">荣誉标识</Select.Option>
                <Select.Option value="1">活跃潮客</Select.Option>
                <Select.Option value="2">品质潮客</Select.Option>
                <Select.Option value="0">无</Select.Option>
              </Select>

              <Select
                value={filter.official_cert_status}
                onChange={this.filterChange.bind(this, 'official_cert_status')}
                style={{ marginRight: 8, width: 120 }}
              >
                <Select.Option value="">官方认证</Select.Option>
                <Select.Option value="1">是</Select.Option>
                <Select.Option value="0">否</Select.Option>
              </Select>
              <Select
                value={filter.virtual_type}
                onChange={this.filterChange.bind(this, 'virtual_type')}
                style={{ marginRight: 8, width: 120 }}
              >
                <Select.Option value="">账号类型</Select.Option>
                <Select.Option value="0">普通</Select.Option>
                <Select.Option value="1">虚拟</Select.Option>
                <Select.Option value="2">马甲</Select.Option>
              </Select>
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <Select
                value={this.state.currentType}
                style={{ width: 130, marginRight: 8, marginLeft: 8 }}
                onChange={(v: any) => this.setState({ currentType: v })}
              >
                <Select.Option value="nick_name">搜索用户昵称</Select.Option>
                <Select.Option value="id">搜索用户ID</Select.Option>
                <Select.Option value="mobile">搜索手机号</Select.Option>
                <Select.Option value="chao_id">搜索小潮号</Select.Option>
              </Select>
              <Input
                value={this.state.currentKeyword}
                style={{ marginRight: 8, width: 120 }}
                onChange={(e: any) => this.setState({ currentKeyword: e.target.value })}
                onKeyPress={this.handleKey}
                placeholder="输入搜索内容"
              />
              <Button onClick={() => this.handleKey({ which: 13 })}>
                <Icon type="search" /> 搜索
              </Button>
            </Col>
          </Row>
          <Table
            func="getUserList"
            index="account_list"
            rowKey="id"
            filter={this.getFilter()}
            columns={this.getColumns()}
            pagination={true}
          />
          <Modal
            visible={this.state.visible}
            key={this.state.key}
            title="用户详情"
            width={800}
            onCancel={() => this.setState({ visible: false })}
            onOk={() => this.setState({ visible: false })}
          >
            {/*{getUserDetail(this.state.detail)}*/}
            <UserDetail detail={this.state.detail} />
          </Modal>
          <Modal
            visible={honorForm.visible}
            key={honorForm.key}
            title="荣誉标识"
            width={550}
            onCancel={this.closeHonor}
            onOk={this.submitHonor}
            okButtonProps={{
              disabled: !this.props.session.permissions.includes('account:reset_quality'),
            }}
          >
            <Form {...formLayout}>
              <Form.Item required={true} label="类型">
                <Radio.Group
                  value={honorForm.quality_type}
                  onChange={(e) => {
                    this.qualityTypeChange(e.target.value);
                  }}
                >
                  <Radio value={0}>无</Radio>
                  <Radio value={1}>活跃潮客</Radio>
                  <Radio value={2}>品质潮客</Radio>
                </Radio.Group>
              </Form.Item>
              {honorForm.quality_type !== 0 && (
                <Form.Item required={true} label="有效期">
                  <Radio.Group
                    value={honorForm.valid_time}
                    onChange={(e) => {
                      this.setState({
                        honorForm: {
                          ...this.state.honorForm,
                          valid_time: e.target.value,
                        },
                      });
                    }}
                  >
                    <Radio value={this.getNowMount()}>{this.getNowMount()}</Radio>
                    <Radio value={this.getNextMonth()}>{this.getNextMonth()}</Radio>
                  </Radio.Group>
                  <div style={{ height: 14, lineHeight: '14px' }}>
                    1、用户完成本月任务后，有效期自动延长到次月底；
                  </div>
                  <div style={{ height: 28, lineHeight: '14px', marginTop: 5 }}>
                    2、未完成任务的用户可在后台直接设置，如果后续用户自行完成任务，将自动更新为用户主动解锁的状态
                  </div>
                </Form.Item>
              )}
            </Form>
          </Modal>
          <Modal
            visible={certForm.visible}
            key={certForm.key}
            title="编辑用户认证"
            onCancel={this.closeCert}
            onOk={this.submitCert}
          >
            <Form {...formLayout}>
              <Form.Item required={true} label="认证类型">
                <Radio.Group value={certForm.type} onChange={this.typeChange}>
                  <Radio value={0}>无认证</Radio>
                  <Radio value={1}>个人认证</Radio>
                  <Radio value={2}>机构认证</Radio>
                </Radio.Group>
                <p
                  style={{
                    height: 14,
                    color: '#aaa',
                    marginTop: -8,
                  }}
                >
                  新的潮鸣号体系下，个人或机构认证均是潮鸣号
                </p>
              </Form.Item>
              {certForm.type !== 0 && (
                <Form.Item required={true} label="认证信息">
                  <Input placeholder="最多25字" value={certForm.info} onChange={this.infoChange} />
                </Form.Item>
              )}
            </Form>
          </Modal>

          {/* 官方认证 */}
          <Modal
            visible={officialCertForm.visible}
            key={officialCertForm.key}
            title="设置官方认证"
            onCancel={this.closeOfficialCert}
            onOk={this.submitOfficialCert}
          >
            <Form {...formLayout}>
              <Form.Item required={true} label="官方认证">
                <Radio.Group value={officialCertForm.type} onChange={this.officialCertTypeChange}>
                  <Radio value={0}>否</Radio>
                  <Radio value={1}>是</Radio>
                </Radio.Group>
                {/* <p style={{
                                    height: 14,
                                    color: '#aaa',
                                    marginTop: -8
                                }}>新的潮鸣号体系下，个人或机构认证均是潮鸣号</p> */}
              </Form.Item>
              {officialCertForm.type !== 0 && (
                <Form.Item required={true} label="认证信息">
                  <Input
                    placeholder="最多25字"
                    value={officialCertForm.info}
                    onChange={this.officialCertInfoChange}
                  />
                </Form.Item>
              )}
            </Form>
          </Modal>
          <ChangeMobileModal
            {...this.state.changeMobileModal}
            onCancel={() =>
              this.setState({
                changeMobileModal: { ...this.state.changeMobileModal, visible: false },
              })
            }
            onEnd={() => {
              this.setState({
                changeMobileModal: { ...this.state.changeMobileModal, visible: false },
              });
              this.getData();
            }}
          ></ChangeMobileModal>

          <Modal
            visible={form.visible}
            key={form.key}
            title="重置密码"
            onCancel={this.closeModal}
            onOk={this.submit}
          >
            <Row style={{ marginBottom: 16 }}>
              <Radio checked={form.type === 1} onClick={this.setFormType.bind(this, 1)}>
                人工设置&nbsp;&nbsp;
                <Input
                  value={form.password}
                  onChange={this.passwordChange}
                  placeholder="请输入新密码"
                  maxLength={30}
                  onFocus={this.setFormType.bind(this, 1)}
                  style={{ width: 140 }}
                />
                <span style={{ color: 'red' }}>&nbsp;{form.err}</span>
              </Radio>
            </Row>
            <Row>
              <Radio checked={form.type === 2} onClick={this.setFormType.bind(this, 2)}>
                随机生成
              </Radio>
            </Row>
          </Modal>
          <Modal
            visible={operateLog.visible}
            title="荣誉记录"
            key={operateLog.key}
            cancelText={null}
            onCancel={() => {
              this.setState({ operateLog: { ...operateLog, visible: false } });
            }}
            onOk={() => {
              this.setState({ operateLog: { ...operateLog, visible: false } });
            }}
          >
            <p>用户昵称：{operateLog.nick_name}</p>
            <p>小潮号：{operateLog.chao_id}</p>
            <br />
            <div>
              <Timeline>
                {operateLog.logs?.map((v: any, i: number) => [
                  <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                    &nbsp;
                  </Timeline.Item>,
                  v.list.map((action: IOperationActionData, index: number) => (
                    <Timeline.Item
                      className="timeline-dot"
                      data-show={action.time.slice(11, 26)}
                      key={`time${i}-action${index}`}
                    >
                      {action.action}
                    </Timeline.Item>
                  )),
                ])}
              </Timeline>
            </div>
          </Modal>
        </div>
      </>
    );
  }
}

export default UserList;
