import React, { forwardRef, useEffect, useImperative<PERSON>andle, useRef, useState } from 'react'
import { Button, Col, Dropdown, Form, Icon, Input, Menu, Modal, Radio, Row, Select, Tooltip, message } from "antd";
import AppServiceForm from '@app/components/business/appServiceForm';
import { requirePerm, getCrumb, setMenuHook, requirePerm4Function, searchToObject } from '@app/utils/utils';
import { useDispatch, useSelector, useStore } from 'react-redux';
import { useHistory } from 'react-router';
import { setConfig } from '@app/action/config';
import { PermA, PermButton } from '@app/components/permItems';
import PendingReview from './components/pendingReview';
import Passed from './components/passed';
import NotPassed from './components/notPassed';
import { creativeRevenueApi } from '@app/api';
import moment from 'moment';

interface FilterState {
    author_type: number | string;
    search_type: number;
    keyword: string;
    account_id: string;
    payout_status: number | string;
    sort_by?: number;
    sort_asc?: number;
}

export default function WithdrawalApplicationMgr(props: any) {
    const dispatch = useDispatch();
    const history = useHistory()
    const { session } = useStore().getState()
    const { current, size, total, records } = useSelector((state: any) => state.tableList)
    const [pageType, setPageType] = useState(0)
    const contentRef = useRef<any>()
    const [filter, setFilter] = useState<FilterState>({
        author_type: '',
        search_type: 1,
        payout_status: '',
        keyword: '',
        account_id: '',
    });

    useEffect(() => {
        setMenuHook(dispatch, props);

        const { selectKeys, openKeys } = props;
        dispatch(
            setConfig({ selectKeys, openKeys })
        );
    }, [])

    const exportData = () => {
        Modal.confirm({
            title: '单次最多可导出5000行数据',
            content: '如果当前列表数量超出上限，仅导出前5000行 ',
            okText: '确定',
            cancelText: '取消',
            onOk: () => {
                doExportData();
            },
        });
    }

    const doExportData = () => {
        dispatch(setConfig({ loading: true }));
        creativeRevenueApi
            .withdrawExport({ ...filter, status: pageType === 0 ? 1 : 2 })
            .then((res: any) => {
                dispatch(setConfig({ loading: false }));
                const a = document.createElement('a');
                a.href = window.URL.createObjectURL(res.data);
                a.download = `潮新闻-创作收益-提现申请-${pageType === 0 ? '待审核' : '已打款'}-${moment().format('YYYYMMDD')}.xlsx`;
                a.click();
            })
            .catch((err) => {
                dispatch(setConfig({ loading: false }));
            });
    };

    const filterChange = (newFilter: FilterState) => {
        setFilter(newFilter)
    }

    return (
        <>
        <Row className="layout-infobar">
            <Col span={16}>
                <Radio.Group defaultValue={+pageType} buttonStyle="solid" style={{ marginRight: 8 }} onChange={(e) => {
                    setPageType(e.target.value)
                }}>
                    {requirePerm4Function(
                    session,
                    `earnings_withdraw_request:list`
                    )(<Radio.Button value={0}>待审核</Radio.Button>)}

                    {requirePerm4Function(
                    session,
                    `earnings_withdraw_request:already_passed_list`
                    )(<Radio.Button value={1}>已通过</Radio.Button>)}
                    
                    {requirePerm4Function(
                    session,
                    `earnings_withdraw_request:not_passed_list`
                    )(<Radio.Button value={2}>不通过</Radio.Button>)}
                </Radio.Group>

                <>
                    {pageType != 2 && <PermButton perm={"earnings_withdraw_request:request_export"} onClick={() => exportData()}>
                        导出数据
                    </PermButton>}
                </>

            </Col>
            <Col span={8} className="layout-breadcrumb">
                {getCrumb(props.breadCrumb)}
            </Col>
        </Row>
        <div className="component-content">
            {pageType == 0 && <PendingReview filterChange={filterChange} />}
            {pageType == 1 && <Passed filterChange={filterChange} />}
            {pageType == 2 && <NotPassed />}
        </div>
        </>
    );
}