import { clearTableList, getTableList } from '@action/tableList';
import {
  CommonObject,
  TableFetchStatus,
  TableList,
  IComponentProps,
  ITableProps,
} from '@app/types';
import { connectTable as connect } from '@utils/connect';
import { Col, Pagination, Row, Table, Icon, message } from 'antd';
import React from 'react';
import { listApi } from '@app/api';
import { SortableContainer, SortableElement, SortableHandle, SortEnd } from 'react-sortable-hoc';

interface TableProps extends React.Props<React.Component> {
  pagination?: boolean;
  tableProps?: CommonObject;
  total?: string | number;
  tableList?: TableList & TableFetchStatus;
  filter: CommonObject;
  func: keyof typeof listApi;
  index: string;
  columns: CommonObject[];
  updateFilter?: (...arg: any) => void;
  rowKey: string | ((record: any) => string);
  multi?: boolean;
  selectedRowKeys?: string[];
  getCheckboxProps?: ((record: any) => CommonObject);
  onSelectChange?: (keys: any, rows: any) => void;
  draggable?: boolean;
  getRecordDraggable?: (record: any, index: number) => boolean;
  onDragEnd?: (oldIndex: number, newIndex: number) => void;
  sizeChange?: (size: number) => void;
  pageSizeOptions: string[];
}

const SET_TABLE = 'SET_TABLE';

const DragHandle = SortableHandle(() => (
  <Icon type="menu" style={{ cursor: 'grab', color: '#999' }} />
));
const SortableItem = SortableElement((props: any) => <tr {...props} />);
const CustomSortableContainer = SortableContainer((props: any) => <tbody {...props} />);

class TableComponent extends React.Component<IComponentProps & ITableProps & TableProps, {}> {
  constructor(props: any) {
    super(props);
  }

  componentWillUnmount() {
    if (this.props.dispatch) {
      this.props.dispatch(clearTableList());
    }
  }

  onPageChange = (page: number) => {
    const { updateFilter, filter, func, index } = this.props;
    const { size } = this.props.tableList as TableList;
    if (this.props.dispatch) {
      this.props.dispatch(getTableList(func, index, { size, ...filter, current: page }, () => {}));
    }
    if (updateFilter) {
      updateFilter({ current: page });
    }
  };

  onSizeChange = (page: number, pageSize: number) => {
    const { updateFilter, filter, func, index, sizeChange } = this.props;
    if (this.props.dispatch) {
      this.props.dispatch(
        getTableList(func, index, { ...filter, current: 1, size: pageSize }, () => {})
      );
    }
    if (sizeChange) {
      sizeChange(pageSize);
    }
    if (updateFilter) {
      updateFilter({ size: pageSize });
    }
  };

  updateFilter = () => {
    const { updateFilter } = this.props;
  };

  dragEnd = ({ newIndex, oldIndex }: SortEnd) => {
    const { onDragEnd } = this.props;
    if (onDragEnd && typeof onDragEnd === 'function') {
      onDragEnd(oldIndex, newIndex);
    }
  };

  render() {
    const { records, current, size, total } = this.props.tableList as TableList;
    // console.log('table list:',this.props)
    const {
      tableProps = {},
      pagination,
      columns,
      rowKey,
      draggable = false,
      getRecordDraggable = () => true,
    } = this.props;
    const { multi, selectedRowKeys, onSelectChange, getCheckboxProps } = this.props;
    const showTotal = this.props.total ? this.props.total : total;
    const paginationSpan = 18;

    const rowSelection = multi
      ? {
          selectedRowKeys: selectedRowKeys || [],
          onChange: onSelectChange || (() => { }),
          getCheckboxProps: getCheckboxProps
        }
      : undefined;

    const selfColumns: any = draggable
      ? [
          {
            title: ' ',
            key: 'drag-sort',
            render: (_: any, record: any, index: number) =>
              getRecordDraggable(record, index) ? <DragHandle /> : null,
            width: 30,
          } as CommonObject,
        ].concat(columns)
      : columns;

    const DraggableContainer = (props: any) => {
      return (
        <CustomSortableContainer
          useDragHandle
          disableAutoscroll
          helperClass="row-dragging"
          onSortEnd={this.dragEnd}
          {...props}
        />
      );
    };

    const DraggableBodyRow = ({ className, style, ...restProps }: any) => {
      // function findIndex base on Table rowKey props and should always be a right array index
      const index = records.findIndex(
        (x) => (typeof rowKey === 'function' ? rowKey(x) : x[rowKey]) === restProps['data-row-key']
      );
      return <SortableItem index={index} {...restProps} className={className} style={style} />;
    };

    return (
      <div>
        <Row>
          <Table
            pagination={false}
            dataSource={records}
            columns={selfColumns}
            {...tableProps}
            rowKey={rowKey}
            rowSelection={rowSelection}
            components={
              draggable
                ? {
                    body: {
                      wrapper: DraggableContainer,
                      row: DraggableBodyRow,
                    },
                  }
                : {}
            }
          />
        </Row>
        {Boolean(pagination) && (
          <Row style={{ marginTop: 16 }}>
            <Col span={6} style={{ verticalAlign: 'bottom' }}>
              共{showTotal}条数据
            </Col>
            <Col span={paginationSpan} className="pagination-pages">
              <Pagination
                showSizeChanger={true}
                showQuickJumper={true}
                pageSize={size}
                current={current}
                pageSizeOptions={this.props.pageSizeOptions?.length > 0 ? this.props.pageSizeOptions : ['10', '20', '50', '100']}
                onChange={this.onPageChange}
                onShowSizeChange={this.onSizeChange}
                total={total}
              />
            </Col>
          </Row>
        )}
      </div>
    );
  }
}

export default connect()(TableComponent);
