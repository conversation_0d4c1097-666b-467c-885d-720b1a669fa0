import {
    Col,
    Form,
    Row,
    Table,
    Button,
    Modal,
} from 'antd';
import React, { forwardRef, useEffect, useState } from 'react';

import { creativeRevenueApi, releaseListApi } from '@app/api';
import '@components/business/styles/business.scss';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { Drawer, PreviewMCN } from '@app/components/common';

const UserRewardList = (props: any, ref: any) => {
    const dispatch = useDispatch();

    const [userRewardList, setUserRewardList] = useState<{
        visible: boolean;
        amount: number;
        record: any;
    }>({
        visible: false,
        amount: 0,
        record: null,
    });

    const [dataSource, setDataSource] = useState<any>([]);

    const [preview, setPreview] = useState({
        visible: false,
        skey: Date.now(),
        data: {},
    });
    useEffect(() => {
        if (props.visible) {
            getList();
        }
        else {
            setDataSource([]);
        }
        setUserRewardList({ ...userRewardList, visible: props.visible });
    }, [props.visible]);

    const getList = () => {
        dispatch(setConfig({ loading: true }));
        creativeRevenueApi.withdrawDetail({
            request_id: props.record.id
        })
        .then((r: any) => {
            dispatch(setConfig({ loading: false }));
            const data = r.data?.list;
            let allAmount = 0
            data.forEach((item: any) => {
                allAmount += item.amount
            })
            if (data) {
                setUserRewardList({
                    visible: true,
                    amount: allAmount,
                    record: props.record,
                })
                setDataSource(data)
            }
        })
        .catch(() => {
            dispatch(setConfig({ loading: false }));
        });
        
    }

    const handleUserRewardListClose = () => {
        setUserRewardList({ ...userRewardList, visible: false });
        props.onClose();
    }

    const handleShowArticle = (record: any) => {
        if (record.fee_type === 1 && record.mlf_id) {
            toMlf(record.mlf_id)
        }
        else {
            setPreview({ visible: true, skey: Date.now(), data: { ...record, doc_type: 10, id: record.article_id } })
        }
    }

    // 跳转到媒立方
    const toMlf = (
        id: number,
        record?: any,
        channelID?: string
        ) => {
        releaseListApi
            .toMlf('mlf_detail_url', { mlf_id: id })
            .then((r: any) => {
                window.open(r.data.url);
            })
            .catch((error) => {

            });
    };

    const handleFeeDetail = async (record: any) => {
        try {
            const response = await creativeRevenueApi.getRankDetail({
                article_id: record.article_id,
            });
            const data:any = response.data;
            Modal.info({
                title: `上榜详情`,
                okText: '确定',
                content: (<div style={{ padding: '20px 0' }}>
                    {data?.rank_name ? (
                        <div>
                        <a
                            href={data?.rank_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{ color: '#1890ff', textDecoration: 'none', fontSize: '14px' }}
                        >
                            {data.rank_name}
                        </a>
                        </div>
                    ) : (
                        <div style={{ textAlign: 'center', color: '#999', padding: '40px 0' }}>
                        暂无上榜详情
                        </div>
                    )}
                </div>),
            });

        } catch (error) {
            
        }
    };

    const columns = [
        {
            title: '序号',
            dataIndex: 'id',
            key: 'id',
            width: 50,
            render: (text: any, record: any, index: number) => {
                return index + 1;
            }
        },
        {
            title: '金额',
            dataIndex: 'amount',
            key: 'amount',
            width: 100,
        },
        {
            title: '收益类型',
            dataIndex: 'fee_type_str',
            key: 'fee_type_str',
            width: 100,
            render: (text: string, record: any) => {
                if (!text) return '-';

                // ✅ 上榜奖励类型添加详情按钮
                if (record.fee_type === 3) {
                    return (
                    <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                        <span>{text}</span>
                        <Button
                            type="dashed"
                            size="small"
                            style={{ padding: 3, paddingTop: 0, paddingBottom: 0 }}
                            onClick={() => handleFeeDetail(record)}
                            >
                            详
                        </Button>
                    </div>
                    );
                }

                return text;
            },
        },
        {
            title: '稿费等级',
            dataIndex: 'fee_grade_str',
            key: 'fee_grade_str',
            width: 100,
        },
        {
            title: '关联稿件',
            dataIndex: 'list_title',
            key: 'list_title',
            render: (text: any, record: any) => {
                return (<a onClick={() => handleShowArticle(record)}>{text}</a>)
            },
        },
        {
            title: '稿件发布时间',
            key: 'release_time_str',
            dataIndex: 'release_time_str',
            width: 120,
        },
        {
            title: '收益获得时间',
            key: 'income_time_str',
            dataIndex: 'income_time_str',
            width: 120,
        },
    ];

    return (
        <>
            <Drawer
                visible={userRewardList.visible}
                title={`${userRewardList.record ? userRewardList.record.nick_name : ''}的提现收益明细`}
                skey={`UserRewardListDrawer`}
                maskClosable={true}
                onClose={handleUserRewardListClose}
                footer={(<>
                    <Row justify={'end'} type={'flex'}>
                        <Col span={6}>
                            <div style={{ fontSize: '18px' }}>
                                {`金额总计：${userRewardList.amount.toFixed(2)}元`}
                            </div>
                        </Col>
                        {props.footer &&
                        <Col span={8}>
                            { props.footer }
                        </Col>}

                    </Row>
                </>)}
            >
                <div>
                    <Table bordered dataSource={dataSource} columns={columns} pagination={false} />
                </div>

                <PreviewMCN
                    { ...preview }
                    onClose={() => setPreview({ ...preview, visible: false, data: {} })} 
                />
            </Drawer>
        </>
        
        
    );
};

export default Form.create<any>({ name: 'UserRewardList' })(
    forwardRef<any, any>(UserRewardList)
);
