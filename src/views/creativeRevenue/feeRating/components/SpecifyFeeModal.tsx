import React, { useState, useEffect } from 'react';
import { Modal, Form, Radio, Input, message, Table, Icon } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import NewNewsSearchAndInput from '@components/common/newNewsSearchAndInput';
import { creativeRevenueApi } from '@app/api';
import { useDispatch, useSelector } from 'react-redux';
import { setConfig } from '@app/action/config';

interface SpecifyFeeModalProps extends FormComponentProps {
  visible: boolean;
  onCancel: () => void;
  onOk: () => void;
}
const SpecifyFeeModal: React.FC<SpecifyFeeModalProps> = ({ form, visible, onCancel, onOk }) => {
  const dispatch = useDispatch();
  const mLoading = useSelector((state: any) => state.config?.mLoading || false);
  const { getFieldDecorator, getFieldValue, validateFields, resetFields, setFieldsValue } = form;
  const [selectedArticles, setSelectedArticles] = useState<any[]>([]);
  // 移除这两个状态，改用 form 的值
  // const [feeGrade, setFeeGrade] = useState<string>('');
  // const [customAmount, setCustomAmount] = useState<number | undefined>(undefined);

  // 重置表单
  useEffect(() => {
    if (!visible) {
      resetFields();
      setSelectedArticles([]);
    }
  }, [visible, resetFields]);

  // 稿件选择列配置
  const articleColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '标题',
      dataIndex: 'list_title',
      key: 'list_title',
      width: 200,
      render: (text: string) => <div>{text}</div>,
    },
    {
      title: '类型',
      dataIndex: 'doc_type',
      key: 'doc_type',
      width: 80,
      render: (text: number) => {
        const typeMap: { [key: string]: string } = {
          '10': '小视频',
          '12': '短图文',
          '13': '长文章',
        };
        return typeMap[text] || '-';
      },
    },
    {
      title: '作者',
      dataIndex: 'creator',
      key: 'creator',
      width: 100,
    },
    {
      title: '发布时间',
      dataIndex: 'published_at',
      key: 'published_at',
      width: 160,
      render: (text: string) => formatDateTime(text),
    },
  ];

  // 处理稿件选择变化
  const handleArticleChange = (articles: any[]) => {
    setSelectedArticles(articles);
  };

  // 处理确定
  const handleOk = () => {
    validateFields((err) => {
      if (err) return;

      // 验证通过后提交
      submitFee();
    });
  };

  // 提交稿费数据
  const submitFee = () => {
    dispatch(setConfig({ mLoading: true })); // ⚡ 正确位置：移到函数内部
    const selectedArticle = selectedArticles[0];
    const feeGrade = getFieldValue('fee_grade'); // 从表单获取值
    const customAmount = getFieldValue('custom_amount'); // 从表单获取值

    const submitData = {
      article_id: selectedArticle.id,
      fee_grade: feeGrade,
      amount: feeGrade === '0' ? customAmount : null,
    };

    creativeRevenueApi
      .addFee(submitData)
      .then((res: any) => {
        if (res.data.result === 1) {
          message.success('指定稿费成功');
          onOk();
        }
      })
      .catch(() => {})
      .finally(() => {
        dispatch(setConfig({ mLoading: false }));
      });
  };
  // 将时间戳转换为年月日时分秒格式
  const formatDateTime = (timestamp: number | string): string => {
    const date = new Date(Number(timestamp));

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };
  return (
    <Modal
      title="指定稿费"
      visible={visible}
      onCancel={() => {
        if (!mLoading) {
          onCancel();
        }
      }}
      onOk={handleOk}
      width={800}
      okButtonProps={{ disabled: selectedArticles.length === 0 }}
      confirmLoading={mLoading}
      destroyOnClose
    >
      <Form layout="vertical">
        <Form.Item label="选择稿件" required>
          <span style={{ color: '#999', fontSize: '12px', marginBottom: '8px', display: 'block' }}>
            类型限制：小视频、短图文、长文章，仅限审核通过的稿件
          </span>
          {getFieldDecorator('articles', {
            initialValue: [],
            rules: [
              {
                required: true,
                message: '请选择稿件',
                type: 'array',
              },
            ],
          })(
            <NewNewsSearchAndInput
              max={1}
              func="listArticleRecommendSearch"
              columns={articleColumns}
              placeholder="输入ID或标题搜索稿件"
              body={{
                doc_types: '10,12,13', // 小视频、短图文、长文章
                audit_status: 1, // 仅审核通过的
              }}
              onChange={handleArticleChange}
              tips="最多只能选择1篇稿件"
            />
          )}
        </Form.Item>

        <Form.Item label="稿费等级" required>
          {getFieldDecorator('fee_grade', {
            initialValue: '',
            rules: [
              {
                required: true,
                message: '请选择稿费等级',
              },
            ],
          })(
            <Radio.Group>
              <Radio value="1">甲等 (500元)</Radio>
              <Radio value="2">乙等 (300元)</Radio>
              <Radio value="3">丙等 (100元)</Radio>
              <Radio value="4">丁等 (50元)</Radio>
              <Radio value="5">戊等 (10元)</Radio>
              <Radio value="0">
                其他{' '}
                {getFieldDecorator('custom_amount', {
                  initialValue: undefined,
                  rules: [
                    {
                      validator: (_rule, value, callback) => {
                        // 只有当选择"其他"等级时才验证自定义金额
                        if (getFieldValue('fee_grade') === '0') {
                          if (!value) {
                            message.error('请输入稿费金额');
                            callback('请输入稿费金额');
                            return;
                          }

                          const numValue = Number(value);
                          if (isNaN(numValue) || numValue <= 0) {
                            message.error('请输入有效的稿费金额');
                            callback('请输入有效的稿费金额');
                            return;
                          }

                          if (numValue > 800) {
                            // 自动设置为999
                            form.setFieldsValue({ custom_amount: 800 });
                            callback();
                            return;
                          }
                        }
                        callback();
                      }
                    }
                  ]
                })(
                  <Input
                    placeholder="输入稿费金额"
                    style={{ width: '120px', marginLeft: 8 }}
                    type="number"
                    min={1}
                    max={800}
                    onChange={(e) => {
                      const value = Number(e.target.value);
                      if (value > 800) {
                        message.error('稿费金额不能超过800元');
                        form.setFieldsValue({ custom_amount: 800 });
                      }
                    }}
                  />
                )}
                元
              </Radio>
            </Radio.Group>
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create<SpecifyFeeModalProps>()(SpecifyFeeModal);
