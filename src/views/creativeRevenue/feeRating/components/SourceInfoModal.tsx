import React, { useState } from 'react';
import { Modal } from 'antd';
import PreviewMCN from '@components/common/previewMCN';

// 格式化标题显示
const formatTitle = (doc_type: number, list_title: string, html_content: string): string => {
  if (doc_type === 10) {
    // 小视频展示前30字
    return list_title?.slice(0, 30) + (list_title.length > 30 ? '...' : '');
  } else if (doc_type === 12) {
    // 短图文展示描述前30字，纯图显示"图片内容"
    return list_title? list_title?.slice(0, 30)
      : '图片内容';
  } else if (doc_type === 13) {
    // 长文章展示完整标题
    return list_title || '';
  }
  return list_title || '';
};

interface SourceInfoModalProps {
  visible: boolean;
  onCancel: () => void;
  sourceInfo: any;
}

const SourceInfoModal: React.FC<SourceInfoModalProps> = ({ visible, onCancel, sourceInfo }) => {
  const { id, doc_type, list_title, url, html_content } = sourceInfo;

  // ✅ 预览稿件弹窗状态
  const [preview, setPreview] = useState({
    visible: false,
    data: {},
    skey: Date.now(),
  });

  // ✅ 处理稿件标题点击预览
  const handlePreviewClick = (e: React.MouseEvent) => {
    e.preventDefault(); // 阻止默认链接跳转行为

    setPreview({
      visible: true,
      data: {
        article_id: id,
        doc_type: doc_type,
      },
      skey: Date.now(),
    });
  };

  return (
    <Modal
      visible={visible}
      title="源稿信息"
      onCancel={onCancel}
      onOk={onCancel}
      width={400}
      destroyOnClose
      maskClosable={false}
      cancelButtonProps={{ style: { display: 'none' } }}
    >
      <div>
        <div>
          <span>潮新闻ID：</span>
          <span>{id}</span>
        </div>
        <div>
          <span>稿件类型：</span>
          <span>
            {doc_type === 10
              ? '小视频'
              : doc_type === 12
              ? '短图文'
              : doc_type === 13
              ? '长文章'
              : doc_type}
          </span>
        </div>
        <div>
          <span>稿件链接：</span>
          <a
            onClick={handlePreviewClick}
            style={{
              cursor: 'pointer',
              color: '#1890ff',
              textDecoration: 'none'
            }}
            onMouseOver={(e) => (e.currentTarget.style.textDecoration = 'underline')}
            onMouseOut={(e) => (e.currentTarget.style.textDecoration = 'none')}
          >
            {formatTitle(doc_type, list_title, html_content)}
          </a>
        </div>
      </div>

      {/* ✅ 预览稿件弹窗 */}
      <PreviewMCN
        visible={preview.visible}
        skey={preview.skey}
        data={preview.data}
        onClose={() => setPreview({ ...preview, visible: false })}
      />
    </Modal>
  );
};

export default SourceInfoModal;
