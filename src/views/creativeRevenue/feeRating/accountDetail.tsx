import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Col,
  DatePicker,
  Icon,
  Row,
  Select,
  Table,
  message,
  Modal,
  Pagination,
} from 'antd';
import { useParams, useHistory, useLocation } from 'react-router-dom';
import { getCrumb } from '@utils/utils';
import { community<PERSON>pi, releaseList<PERSON>pi, creativeRevenueApi } from '@app/api';
import moment from 'moment';
import { PermButton } from '@app/components/permItems';
import PreviewMCN from '@app/components/common/previewMCN';
import SourceInfoModal from '@app/views/creativeRevenue/feeRating/components/SourceInfoModal';

interface AccountDetail {
  id: number;
  account_id: string;
  chao_id: string;
  author_type: number;
  total_balance: number;
  withdrawn_balance: number;
  expired_balance: number;
  freezing_balance: number;
  unwithdrawn_balance: number;
  withdrawable_balance: number;
  created_time: number;
  last_updated: number;
  alipay_user_no: string;
  nick_name: string;
  nick_name_str?: string; // 添加 nick_name_str 字段
  author_type_str: string;
}

interface Transaction {
  id: number;
  flow_no: string;
  account_id: string;
  nick_name: string;
  nick_name_str?: string; // 添加 nick_name_str 字段
  cert_name: string;
  alipay_user_no: string;
  biz_type: number;
  fee_type: number;
  explain: string;
  list_title: string;
  url: string;
  amount: number;
  amount_str: string; // 添加 amount_str 字段
  flow_status: number;
  channel: string;
  flow_time: number;
  description: string;
  notes: string;
  flow_status_str: string;
  flow_time_str: string;
  fee_type_str: string;
  biz_type_str: string;
  order_id?: string;
  mlf_id?: number; // 新增 mlf_id 字段
  ugc_article?: {
    // 新增 ugc_article 字段
    newsId: string;
    contentType: string;
    contentLink: string;
  };
  article_id?: string; // 新增 article_id 字段
}

interface ListResponse<T> {
  total: number;
  size: number;
  pages: number;
  current: number;
  records: T[];
}

interface FundFlowResponse {
  list: ListResponse<Transaction>;
  unwithdrawn_balance: number;
  withdrawn_balance: number;
}

interface ApiResponse<T> {
  code: number;
  data: T;
}

// 账户详情
export default function AccountDetail(props: any) {
  const history = useHistory();
  const { accountId } = useParams<{ accountId: string }>();
  const location = useLocation<{ nick_name: string }>();
  const routeNickName = location.state?.nick_name;
  const [loading, setLoading] = useState(false);
  const [accountDetail, setAccountDetail] = useState<AccountDetail | null>(null);
  const [transactions, setTransactions] = useState<any[]>([]);
  const [withdrawalModalVisible, setWithdrawalModalVisible] = useState(false);
  const [currentWithdrawal, setCurrentWithdrawal] = useState<any>(null);
  
  // 筛选条件
  const [filter, setFilter] = useState({
    biz_type: '',
    fee_type: '',
    begin: undefined as any,
    end: undefined as any,
    current: 1,
    size: 10,
  });

  // 分页状态
  const [pagination, setPagination] = useState({
    total: 0,
    current: 1,
    pageSize: 10,
  });

  // 加载账户详情
  useEffect(() => {
    let mounted = true;

    const loadData = async () => {
    try {
      setLoading(true);
        const response = await communityApi.getEarningsFundFlowList({ account_id: accountId }) as any;
        console.log('API响应:', response); // 添加调试日志
      
        // 修改判断条件，不再检查 code 字段
        if (mounted && response?.data) {
          // 设置账户详情
      setAccountDetail({
            unwithdrawn_balance: response.data.unwithdrawn_balance,
            withdrawn_balance: response.data.withdrawn_balance,
          } as any);
          
          // 设置交易记录
          const { list } = response.data;
          console.log('交易记录:', list.records); // 添加调试日志
          
          // 确保 records 是数组
          const records = Array.isArray(list.records) ? list.records : [];
          console.log('处理后的记录:', records); // 添加调试日志
          setTransactions(records);
          
          setPagination({
            total: list.total || 0,
            current: list.current || 1,
            pageSize: list.size || 10,
          });
        }
    } catch (error) {
      console.error('获取账户详情失败:', error);
        if (mounted) {
      message.error('获取账户详情失败');
        }
      } finally {
        if (mounted) {
      setLoading(false);
    }
      }
    };

    loadData();
    return () => {
      mounted = false;
    };
  }, [accountId]);

  // 加载交易记录
  useEffect(() => {
    // 移除初始判断条件，确保筛选条件变化时都会调用接口
    let mounted = true;
    
    const loadData = async () => {
    try {
      setLoading(true);
        const params = {
          account_id: accountId,
          size: filter.size,
          current: filter.current,
          biz_type: filter.biz_type, // 直接传递，无论是否为空
          fee_type: filter.fee_type, // 直接传递，无论是否为空
          ...(filter.begin && { begin: moment(filter.begin).format('YYYY-MM-DD HH:mm:ss') }),
          ...(filter.end && { end: moment(filter.end).format('YYYY-MM-DD HH:mm:ss') }),
        };
        
        const response = await communityApi.getEarningsFundFlowList(params) as any;
        // 修改判断条件，不再检查 code 字段
        if (mounted && response?.data?.list) {
          const { list } = response.data;
          setTransactions(list.records || []);
          setPagination({
            total: list.total || 0,
            current: list.current || 1,
            pageSize: list.size || 10,
          });
        }
    } catch (error) {
      console.error('获取交易记录失败:', error);
        if (mounted) {
      message.error('获取交易记录失败');
        }
      } finally {
        if (mounted) {
      setLoading(false);
    }
      }
    };

    loadData();
    return () => {
      mounted = false;
    };
  }, [accountId, filter]);

  // 处理分页变化
  const handleTableChange = (pagination: any) => {
    setFilter(prev => ({
      ...prev,
      current: pagination.current,
      size: pagination.pageSize,
    }));
  };

  // 处理筛选条件变更
  const handleFilterChange = (key: string, value: any) => {
    setFilter(prev => ({
      ...prev,
      [key]: value,
      current: 1, // 重置页码
    }));
  };

  // 处理日期范围变更
  const handleDateRangeChange = (dates: any) => {
    setFilter(prev => ({
      ...prev,
      begin: dates?.[0],
      end: dates?.[1],
      current: 1, // 重置页码
    }));
  };

  // 处理导出数据
  const handleExport = () => {
    Modal.confirm({
      title: '单次最多可导出 5000 行数据',
      content: (
        <div>
          如果当前列表数量超出上限，仅导出前 5000 行
        </div>
      ),
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        performExport();
      },
      width: 400,
      centered: true,
      maskClosable: false,
    });
  };

  // 执行导出操作
  const performExport = async () => {
    try {
      const params = {
        account_id: accountId,
        ...(filter.biz_type && { biz_type: filter.biz_type }),
        ...(filter.fee_type && { fee_type: filter.fee_type }),
        ...(filter.begin && { begin: moment(filter.begin).format('YYYY-MM-DD HH:mm:ss') }),
        ...(filter.end && { end: moment(filter.end).format('YYYY-MM-DD HH:mm:ss') }),
      };

      const response = await communityApi.exportEarningsFundFlowByAccountId(params);
      const a = document.createElement('a');
      a.href = window.URL.createObjectURL(response.data);
      // 优先使用路由传递的昵称，如果没有则使用 accountDetail 中的昵称
      const nickName = routeNickName || accountDetail?.nick_name || accountDetail?.nick_name_str || '未知用户';
      a.download = `潮新闻-创作收益-${nickName}-${moment().format('YYYYMMDD')}.xlsx`;
      a.click();
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败');
    }
  };

  // 处理查看提现详情
  const handleViewWithdrawalDetail = (record: Transaction) => {
    // 这里应该调用实际的API获取详情，现在使用模拟数据
    const mockDetail = {
      withdraw_method: '个人支付宝',
      cert_name: record.cert_name || '未知',
      alipay_user_no: record.alipay_user_no || '未绑定',
      flow_status: record.flow_status,
      order_id: record.order_id || record.alipay_trade_no || record.flow_no || '-',
      description: record.description || '未知原因',
      flow_time_str: record.flow_time_str || '-',
    };
    
    setCurrentWithdrawal(mockDetail);
    setWithdrawalModalVisible(true);
  };

  // 关闭提现详情弹窗
  const handleCloseWithdrawalModal = () => {
    setWithdrawalModalVisible(false);
    setCurrentWithdrawal(null);
  };

  // 添加预览相关的状态
  const [preview, setPreview] = useState({
    visible: false,
    data: {},
    skey: Date.now(),
  });

  // 添加源稿信息相关的状态
  const [sourceInfo, setSourceInfo] = useState({
    visible: false,
    sourceInfo: {
      newsId: '',
      contentType: '',
      contentLink: '',
    },
  });

  // 添加上榜详情弹窗状态
  const [rankDetailModal, setRankDetailModal] = useState<{
    visible: boolean;
    data: any;
    loading: boolean;
  }>({
    visible: false,
    data: null,
    loading: false,
  });

  // 跳转到媒立方
  const toMlf = (
    id: number,
    type: 'mlf_detail_url',
    record?: any,
    channelID?: string
  ) => {
    releaseListApi
      .toMlf(type, { mlf_id: id })
      .then((r: any) => {
        window.open(r.data.url);
      })
      .catch((error) => {
        
      });
  };

  // 处理上榜详情
  const handleFeeDetail = async (record: any) => {
    try {
      setRankDetailModal({
        ...rankDetailModal,
        loading: true,
      });
      
      const response = await creativeRevenueApi.getRankDetail({
        article_id: record.article_id,
      });

      setRankDetailModal({
        visible: true,
        data: response.data,
        loading: false,
      });
    } catch (error) {
      console.error('获取上榜详情失败：', error);
      setRankDetailModal({
        visible: false,
        data: null,
        loading: false,
      });
    }
  };

  // 显示源稿信息弹窗
  const showSourceInfo = (record: any) => {
    setSourceInfo({
      visible: true,
      sourceInfo: {
        ...record.ugc_article,
      },
    });
  };

  // 表格列配置
  const columns = [
    {
      title: '金额（元）',
      dataIndex: 'amount_str',
      key: 'amount_str',
      width: 120,
      render: (text: string) => {
        if (!text) return '-';
        // 如果第一个字符不是负号，则添加+号
        return text.charAt(0) !== '-' ? `+${text}` : text;
      },
    },
    {
      title: '事项',
      dataIndex: 'explain',
      key: 'explain',
      width: 150,
      render: (text: string, record: Transaction) => {
        if (!text) return '-';
        // 根据flow_status显示对应状态文本
        let statusText = '';
        let statusColor = '';
        if (record.flow_status === 1) {
          statusText = '提现中';
          // 移除蓝色，使用默认色
        } else if (record.flow_status === 2) {
          statusText = '提现成功';
          statusColor = '#52c41a'; // 绿色
        } else if (record.flow_status === 3) {
          statusText = '提现失败';
          statusColor = '#f5222d'; // 红色
        }

        // 如果是上榜奖励类型，显示详按钮
        if (record.fee_type === 3) {
          return (
            <span style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
              <span>{text}</span>
              <Button
                type="dashed"
                size="small"
                style={{ padding: 3, paddingTop: 0, paddingBottom: 0 }}
                onClick={() => handleFeeDetail(record)}
              >
                详
              </Button>
              {statusText && (
                <span style={{ color: statusColor }}>({statusText})</span>
              )}
            </span>
          );
        }

        // 如果是提现成功或失败状态，整个事项可点击
        if (record.flow_status === 2 || record.flow_status === 3) {
          return (
            <span style={{ display: 'flex', alignItems: 'center', gap: 4, cursor: 'pointer' }} onClick={() => handleViewWithdrawalDetail(record)}>
              <span>{text}</span>
              {statusText && (
                <span style={{ color: statusColor }}>({statusText})</span>
              )}
            </span>
          );
        }

        // 其他情况只显示事项和状态，不可点击
        return (
          <span>
            {text} {statusText && <span style={{ color: statusColor }}>({statusText})</span>}
          </span>
        );
      },
    },
    {
      title: '关联稿件',
      dataIndex: 'list_title',
      key: 'list_title',
      width: 200,
      render: (text: string, record: any) => {
        if (!text) return '-';
        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <a
              onClick={() => {
                if (record.mlf_id) {
                  toMlf(record.mlf_id, 'mlf_detail_url');
                } else {
                  setPreview({
                    visible: true,
                    data: {
                      article_id: record.article_id,
                      doc_type: 10,
                    },
                    skey: Date.now(),
                  });
                }
              }}
            >
              {text}
              {record.mlf_id ? (
                <Button
                  type="dashed"
                  size="small"
                  style={{ padding: 3, paddingTop: 0, paddingBottom: 0, marginLeft: 5 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    showSourceInfo(record);
                  }}
                >
                  源
                </Button>
              ) : (
                ''
              )}
            </a>
          </div>
        );
      },
    },
    {
      title: '时间',
      dataIndex: 'flow_time_str',
      key: 'flow_time_str',
      width: 160,
    },
  ];

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button onClick={() => history.goBack()}>
            <Icon type="arrow-left" /> 返回
          </Button>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>

      <div className="component-content">
        {/* 账户统计数据 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={4}>
            <div style={{ 
              background: '#f8f9fa',
              padding: '12px 16px',
              borderRadius: '4px',
            }}>
              <div style={{ 
                display: 'flex',
                alignItems: 'center',
                marginBottom: '4px',
              }}>
                <span style={{ fontSize: 14, color: '#666' }}>待提现金额</span>
              </div>
              <div style={{ 
                fontSize: 20,
                fontWeight: 'bold',
                color: '#262626',
              }}>
                {accountDetail?.unwithdrawn_balance?.toFixed(2) || '0.00'}
              </div>
            </div>
          </Col>
          <Col span={4}>
            <div style={{ 
              background: '#f8f9fa',
              padding: '12px 16px',
              borderRadius: '4px',
            }}>
              <div style={{ 
                display: 'flex',
                alignItems: 'center',
                marginBottom: '4px',
              }}>
                <span style={{ fontSize: 14, color: '#666' }}>已提现金额</span>
              </div>
              <div style={{ 
                fontSize: 20,
                fontWeight: 'bold',
                color: '#262626',
              }}>
                {accountDetail?.withdrawn_balance?.toFixed(2) || '0.00'}
              </div>
            </div>
          </Col>
        </Row>

        {/* 筛选工具栏 */}
        <Row style={{ marginBottom: 16 }}>
          <Col span={24}>
            <div style={{ display: 'flex', flexWrap: 'nowrap', alignItems: 'center' }}>
              <PermButton perm="earnings_fund_flow:export_fund_flow_by_account_id" type="primary" onClick={handleExport} style={{ marginRight: 8, flexShrink: 0 }}>
                <Icon type="download" /> 导出数据
              </PermButton>
            <Select
                value={filter.biz_type}
                onChange={(value) => handleFilterChange('biz_type', value)}
                style={{ width: 120, marginRight: 8, flexShrink: 0 }}
              placeholder="按收支筛选"
            >
              <Select.Option value="">按收支筛选</Select.Option>
                <Select.Option value="1">收益</Select.Option>
                <Select.Option value="2">提现</Select.Option>
                <Select.Option value="3">过期</Select.Option>
            </Select>
            <Select
                value={filter.fee_type}
                onChange={(value) => handleFilterChange('fee_type', value)}
                style={{ width: 120, marginRight: 8, flexShrink: 0 }}
              placeholder="收益类型"
            >
              <Select.Option value="">收益类型</Select.Option>
              <Select.Option value="1">取稿稿费</Select.Option>
              <Select.Option value="2">指定稿费</Select.Option>
              <Select.Option value="3">上榜奖励</Select.Option>
            </Select>
            <DatePicker.RangePicker 
                value={[filter.begin, filter.end]}
              onChange={handleDateRangeChange}
                style={{ marginRight: 8, flexShrink: 0 }}
                showTime
            />
            </div>
          </Col>
        </Row>

        {/* 交易记录表格 */}
        <Table
          rowKey="id"
          dataSource={transactions}
          columns={columns}
          loading={loading}
          pagination={false}
          scroll={{ x: 1200 }}
        />

        <Row style={{ marginTop: 16 }}>
          <Col span={12}>
            <div style={{ lineHeight: '32px' }}>
              共 {pagination.total} 条数据
            </div>
          </Col>
          <Col span={12} style={{ textAlign: 'right' }}>
            <Pagination
              total={pagination.total}
              pageSize={pagination.pageSize}
              current={pagination.current}
              showSizeChanger
              showQuickJumper
              pageSizeOptions={['10', '20', '50', '100']}
              onChange={(page, pageSize) => handleTableChange({ current: page, pageSize })}
              onShowSizeChange={(current, size) => handleTableChange({ current: 1, pageSize: size })}
            />
          </Col>
        </Row>
      </div>

      {/* 提现详情弹窗 */}
      <Modal
        title="提现详情"
        visible={withdrawalModalVisible}
        onCancel={handleCloseWithdrawalModal}
        footer={[
          <Button key="close" onClick={handleCloseWithdrawalModal}>
            关闭
          </Button>
        ]}
        width={400}
      >
        {currentWithdrawal && (
          <div>
            <p>提现方式：{currentWithdrawal.withdraw_method}</p>
            <p>支付宝姓名：{currentWithdrawal.cert_name}</p>
            <p>支付宝账号：{currentWithdrawal.alipay_user_no}</p>
            {currentWithdrawal.flow_status === 2 ? (
              <>
                <p>支付宝交易号：{currentWithdrawal.order_id}</p>
                <p>到账时间：{currentWithdrawal.flow_time_str}</p>
              </>
            ) : (
              <>
                <p>失败原因：{currentWithdrawal.description}</p>
                <p>失败时间：{currentWithdrawal.flow_time_str}</p>
              </>
            )}
          </div>
        )}
      </Modal>

      {/* 预览稿件弹窗 */}
      <PreviewMCN
        visible={preview.visible}
        skey={preview.skey}
        data={preview.data}
        onClose={() => setPreview({ ...preview, visible: false })}
      />

      {/* 上榜详情弹窗 */}
      <Modal
        visible={rankDetailModal.visible}
        title="上榜详情"
        width={600}
        onCancel={() => setRankDetailModal({ ...rankDetailModal, visible: false })}
        footer={[
          <Button
            key="ok"
            type="primary"
            onClick={() => setRankDetailModal({ ...rankDetailModal, visible: false })}
          >
            确定
          </Button>,
        ]}
        destroyOnClose={true}
      >
        {rankDetailModal.loading ? (
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <Icon type="loading" />
          </div>
        ) : (
          <div style={{ padding: '20px 0' }}>
            {rankDetailModal.data?.rank_name ? (
              <div>
                <a
                  href={rankDetailModal.data?.rank_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{ color: '#1890ff', textDecoration: 'none', fontSize: '14px' }}
                >
                  {rankDetailModal.data.rank_name}
                </a>
              </div>
            ) : (
              <div style={{ textAlign: 'center', color: '#999', padding: '40px 0' }}>
                暂无上榜详情
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 源稿信息弹窗 */}
      <SourceInfoModal
        visible={sourceInfo.visible}
        sourceInfo={sourceInfo.sourceInfo}
        onCancel={() => setSourceInfo({ ...sourceInfo, visible: false })}
      />
    </>
  );
} 