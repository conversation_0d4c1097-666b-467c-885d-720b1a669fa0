import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch, useStore } from 'react-redux';
import { setConfig } from '@action/config';
import { getCrumb, setMenuHook } from '@utils/utils';
import {
  Button,
  Col,
  Row,
  Menu,
  Dropdown,
  Icon,
  Modal,
  message,
  Divider,
  Select,
  Input,
  Tooltip,
} from 'antd';
import { Table, OrderColumn } from '@components/common';
import { getTableList } from '@app/action/tableList';
import { sysApi as api } from '@app/api';
import ReactClipboard from 'react-clipboardjs-copy';
import GenerateShortUrlModal from './generateShortUrlModal';
import { PermA, PermButton } from '@app/components/permItems';
import moment from 'moment';

export default function ShortURLManager(props: any) {
  const dispatch = useDispatch();
  const { permissions } = useStore().getState().session;
  const { total, current, size, records = [] } = useSelector((state: any) => state.tableList);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editRecord, setEditRecord] = useState(null);
  const [keyword, setKeywrod] = useState('');
  const [filter, setFilter] = useState({});

  const deleteShortUrl = (record: any) => {
    Modal.confirm({
      title: <p>删除后，将无法通过该短链访问到原始链接</p>,
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        api
          .deleteShortUrl({ id: record.id })
          .then(() => {
            message.success('操作成功');
            dispatch(setConfig({ loading: false }));
            getData();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const handleCreateReadPreference = () => {
    setEditRecord(null);
    setAddModalVisible(true);
  };

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const columns: any = [
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => getSeq(i),
      width: 70,
    },
    {
      title: '短链接',
      dataIndex: 'short_url',
      width: 300,
    },
    {
      title: '原始链接',
      dataIndex: 'url',
    },
    {
      title: (
        <div>
          访问量&nbsp;
          <Tooltip title="短链接被访问的次数（pv），每10分钟统计一次" placement="top">
            <Icon type="question-circle" />
          </Tooltip>
        </div>
      ),
      dataIndex: 'pv',
      width: 120,
    },
    {
      title: '生成时间',
      dataIndex: 'created_at',
      width: 160,
      render: (text: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'op',
      align: 'center',
      width: 120,
      render: (text: any, record: any) => (
        <>
          <ReactClipboard
            action="copy"
            text={record.short_url}
            onSuccess={() => message.success('链接已复制')}
            onError={() => message.error('复制失败')}
          >
            <a>复制</a>
          </ReactClipboard>
          <Divider type="vertical" />
          <PermA perm="short_url:delete" onClick={() => deleteShortUrl(record)}>
            删除
          </PermA>
        </>
      ),
    },
  ];

  const getData = (goToFirstPage = false) => {
    let cur = goToFirstPage ? 1 : current;
    dispatch(getTableList('getShortURLList', 'list', { current: cur, size, ...filter }));
  };

  const submitEnd = (goToFirstPage: boolean) => {
    getData(goToFirstPage);
    setAddModalVisible(false);
  };

  useEffect(() => {
    setMenuHook(dispatch, props);
  }, []);

  useEffect(() => {
    getData();
  }, [filter]);

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      const newFilter = {
        ...filter,
        keyword,
      };
      setFilter(newFilter);
    }
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton perm="short_url:save" onClick={handleCreateReadPreference}>
            生成短链接
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 10 }}>
          <Col span={24}>
            <Input
              value={keyword}
              style={{ marginRight: 8, width: 160 }}
              onChange={(e: any) => setKeywrod(e.target.value)}
              onKeyPress={handleKey}
              placeholder="输入搜索内容"
            />
            <Button onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" /> 搜索
            </Button>
          </Col>
        </Row>
        <Table
          func="getShortURLList"
          index="list"
          pagination={true}
          rowKey="id"
          columns={columns}
          filter={filter}
        />
        <div key={`${addModalVisible}`}>
          <GenerateShortUrlModal
            visible={addModalVisible}
            onCancel={() => {
              setAddModalVisible(false);
            }}
            onEnd={(value: any) => {
              setAddModalVisible(false);
              getData(true);
            }}
          />
        </div>
      </div>
    </>
  );
}
