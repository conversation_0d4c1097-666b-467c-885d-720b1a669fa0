import Table from "@app/components/common/table";
import { getCrumb } from "@app/utils/utils";
import { Button, Col, Divider, Icon, Input, Modal, Row, message } from "antd";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector, useStore } from "react-redux";
import AddConfigOptionDrawer from "@app/components/business/AddConfigOptionDrawer";
import { getTableList } from "@app/action/tableList";
import { setConfig } from '@action/config';
import { sysApi as api } from '@app/api';
import moment from 'moment';
import { PermA } from "@app/components/permItems";

export default function ConfigOptionMgr(props: any) {
  const dispatch = useDispatch();
  const { permissions } = useStore().getState().session;
  const { current, size } = useSelector((state: any) => state.tableList)
  const [keywordInput, setKeywordInput] = useState('')
  let [keyword, setKeyword] = useState('')
  const [configOptionDrawerVisible, setConfigOptionDrawerVisible] = useState(false)
  const [drawerEditRecord, setDrawerEditRecord] = useState(null)
  const getSeq = (i: number) => (current - 1) * size + i + 1;
  const columns: any = [
    {
      title: '序号',
      key: 'seq',
      render: (a: any, b: any, c: number) => getSeq(c),
      width: 70,
    },
    {
      title: '配置项名称',
      dataIndex: 'item_code',
      width: 120,
    },
    {
      title: '配置信息',
      dataIndex: 'item_value',
      ellipsis: true,
    },
    {
      title: '备注说明',
      dataIndex: 'remark',
      ellipsis: true,
    },
    {
      title: '最后修改人',
      dataIndex: 'updated_by',
      width: 150,
    },
    {
      title: '最后修改时间',
      dataIndex: 'updated_at',
      width: 150,
      render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render(text: string, record: any) {
        return record.status === 1 ? '已上线' : '未上线'
      }
    },
    {
      title: '操作',
      key: 'op',
      align: 'center',
      width: 150,
      render: (record: any) => (<span>
        <PermA
          perm={'app_item_config:save'}
          onClick={() => editRecord(record)}
        >
          编辑
        </PermA>
        <Divider type="vertical" />
        <PermA
          perm={'app_item_config:save'}
          onClick={() => deleteRecord(record)}
        >
          删除
        </PermA>
        <Divider type="vertical" />
        <PermA
          perm={'app_item_config:save'}
          onClick={() => toggleStatus(record)}
        >
          {['上架', '下架'][record.status]}
        </PermA>
      </span>)
    }
  ]
  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: <p>确定删除此配置项？</p>,
      onOk: () => {
        dispatch(setConfig({ loading: true }))
        api.deleteAppConfig({ code: record.item_code })
          .then(() => {
            message.success('操作成功')
            dispatch(setConfig({ loading: false }))
            getData()
          }).catch(() => {
            dispatch(setConfig({ loading: false }))
          })
      },
    });
  }
  const handleCreateConfigOption = () => {
    setDrawerEditRecord(null)
    setConfigOptionDrawerVisible(true)
  }
  const editRecord = (record: any) => {
    setDrawerEditRecord(record)
    setConfigOptionDrawerVisible(true)
  }
  const toggleStatus = (record: any) => {
    dispatch(setConfig({ loading: true }))
    api.updateAppConfigStatus({ code: record.item_code, status: record.status === 1 ? 0 : 1 })
      .then(() => {
        message.success('操作成功')
        dispatch(setConfig({ loading: false }))
        getData()
      }).catch(() => {
        dispatch(setConfig({ loading: false }))
      })
  }
  const getFilter = () => {
    return {
      current,
      size,
      code: keyword,
    }
  }
  const getData = (overlap: any = {}, filter = getFilter()) => {
    dispatch(getTableList('getAppConfigList', 'app_config_list', { ...filter, ...overlap }));
  }

  const submitEnd = (goToFirstPage: boolean) => {
    getData(goToFirstPage ? { current: 1 } : {})
    setConfigOptionDrawerVisible(false)
  }

  const handleSearch = () => {
    keyword = keywordInput
    setKeyword(keywordInput)
    getData({ current: 1 })
  }

  useEffect(() => {
    const { selectKeys, openKeys } = props
    dispatch(
      setConfig({ selectKeys, openKeys })
    );
    getData({ current: 1, size: 10 })
  }, [])

  const canCreateConfig = permissions.indexOf('app_item_config:save') > 0;

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button onClick={handleCreateConfigOption} disabled={!canCreateConfig}>配置项添加</Button>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row>
          <Input
            style={{ width: 200, marginRight: 8 }}
            onKeyPress={e => { if (e.key === 'Enter') { handleSearch() } }}
            value={keywordInput}
            placeholder="请输入搜索内容"
            onChange={(e) => setKeywordInput(e.target.value)}
            allowClear
          />
          <Button onClick={handleSearch}>
            <Icon type="search" />
            搜索
          </Button>
        </Row>
        <Table
          filter={getFilter()}
          func="getAppConfigList"
          index="app_config_list"
          pagination={true}
          rowKey="id"
          columns={columns}
        />
        <AddConfigOptionDrawer record={drawerEditRecord} visible={configOptionDrawerVisible} onClose={() => setConfigOptionDrawerVisible(false)} onEnd={submitEnd} />
      </div>
    </>
  )
}