import { setConfig } from '@action/config';
import { getTableList } from '@app/action/tableList';
import { sysApi as api } from '@app/api';
import { Table, BaseComponent, A, Drawer, } from '@components/common';
import { connectTable as connect } from '@utils/connect';
import { getCrumb, requirePerm } from '@utils/utils';
import { Button, Col, Icon, Input, Row, Select } from 'antd';
import React from 'react';
import { withRouter } from 'react-router';
import CityModelManagerForm from "@components/business/cityModelManagerForm"
import {
  CommonObject,
  TSysChannelCategoryRecord,
  CommonResponse,
  TSysChannelCategoryData,
  TSysChannelData,
  ISysChannel,
  ITableProps,
  IBaseProps,
} from '@app/types';

type State = {
  categoryList: TSysChannelCategoryRecord[];
  category: number;
  search: string;
  keyword: string;
  cityModelForm: any;
};

type Props = IBaseProps<ITableProps<ISysChannel, TSysChannelData>>;

class ChannelList extends BaseComponent<ITableProps<ISysChannel, TSysChannelData>, State> {
  formRef: any; //频道管理
  constructor(props: Props) {
    super(props);
    this.state = {
      categoryList: [],
      category: 1,
      search: '',
      keyword: '',
      cityModelForm: {
        visible: false, //频道管理弹窗是否显示
        key: '',   //唯一值
      },
    };
  }

  componentDidMount() {
    this.getCategoryList();
    this.setMenu();
  }

  getCategoryList = () => {
    api.getChannelCategoryList().then((r: CommonResponse<TSysChannelCategoryData>) => {
      this.setState(
        {
          categoryList: r.data.channel_category_list,
          category: r.data.channel_category_list[0].id,
        },
        () => this.getData({ current: 1, size: 10 })
      );
    });
  };

  getData = (overlap: CommonObject = {}, filter = this.getFilter()) => {
    this.dispatchTable('getChannelList', 'channel_list', { ...filter, ...overlap });
  };

  getFilter = () => {
    const { current, size } = this.props.tableList;
    return {
      current,
      size,
      category_id: this.state.category,
      keyword: this.state.keyword,
    };
  };
  // 城市模块 编辑 弹出抽屉
  editRecord = (record: any) => {
    this.setState({
      cityModelForm: {
        ...this.state.cityModelForm,
        visible: true,
        ...record
      },
    })
  }

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;

    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      },
      {
        title: '名称',
        key: 'name',
        dataIndex: 'name',
      },
      {
        title: '潮新闻ID',
        key: 'id',
        dataIndex: 'id',
      },
      {
        title: this.state.category === 2 ? '城市编码' : '',
        key: this.state.category === 2 ? 'city_code' : '',
        render: (record: any) => <span>{this.state.category === 2 && record.city_code != 0 ? record.city_code : ''}</span>,
      },
      {
        title: '媒立方ID',
        key: 'mlf_id',
        dataIndex: 'mlf_id',
      },
      {
        title: this.state.category === 2 ? '操作' : '',
        key: 'op',
        render: this.state.category === 2 ? (record: any) => (
          <span>
            {requirePerm(
              this,
              `channel:2:associated_area_gov`
            )(<A onClick={this.editRecord.bind(this, record)}>编辑</A>)}
          </span>
        ) : '',
        width: 100,
      }
    ];
  };

  handleCategoryChange = (value: number) => {
    this.setState({ category: value }, () => {
      this.getData({ ...this.getFilter(), current: 1 });
    });
  };

  doSearch = () => {
    this.setState(
      {
        keyword: this.state.search,
      },
      () => this.getData({ current: 1 })
    );
  };

  searchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    this.setState({
      search: e.target.value,
    });
  };
  // 关闭弹窗
  closeLog = () => {
    this.setState({
      cityModelForm: {
        ...this.state.cityModelForm,
        visible: false
      }
    });
  };
  // 弹窗数据提交
  onSubmitEnd = () => {
    this.getData();
    this.closeLog();
  };
  setRef = (ref: 'formRef', instance: any) => {
    this[ref] = instance;
  };

  render() {
    const { cityModelForm } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            <Select
              value={this.state.category}
              style={{ width: 160 }}
              onChange={this.handleCategoryChange}
            >
              {this.state.categoryList.map((v: any) => (
                <Select.Option key={v.id} value={v.id}>
                  {v.name}
                </Select.Option>
              ))}
            </Select>
            <Input
              onKeyPress={this.handleKey}
              style={{ width: 160, margin: '0 8px' }}
              value={this.state.search}
              placeholder="请输入频道名称搜索"
              onChange={this.searchInputChange}
            />
            <Button onClick={this.doSearch}>
              <Icon type="search" /> 搜索
            </Button>
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getChannelList"
            index="channel_list"
            filter={this.getFilter()}
            pagination={true}
            rowKey="id"
            columns={this.getColumns()}
          />
        </div>
        <Drawer
          visible={cityModelForm.visible}
          title={`编辑`}
          skey={cityModelForm.key}
          onClose={this.closeLog}
          onOk={() => this.formRef.doSubmit()}
        >
          <CityModelManagerForm
            onEnd={this.onSubmitEnd}
            formContent={cityModelForm}
            wrappedComponentRef={this.setRef.bind(this, 'formRef')}
          />
        </Drawer>
      </>
    );
  }
}

export default withRouter(connect<ISysChannel, TSysChannelData>()(ChannelList));
