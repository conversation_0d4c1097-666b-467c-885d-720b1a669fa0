import { setConfig } from '@action/config';
import { getTableList, setTableList } from '@app/action/tableList';
import { sysApi as api } from '@app/api';
import { CommonObject, ITableProps } from '@app/types';
import { A, Drawer, Table, BaseComponent } from '@components/common';
import { connectTable as connect } from '@utils/connect';
import { getCrumb } from '@utils/utils';
import { Button, Col, Dropdown, Icon, Menu, message, Modal, Row, Select } from 'antd';
import moment from 'moment';
import React, { lazy } from 'react';
import { withRouter } from 'react-router';

const AppNavForm = lazy(() => import('@components/business/appNavForm'));
const OldCityNavForm = lazy(() => import('@app/components/business/oldCityNavForm'));
const ProvinceNavForm = lazy(() => import('@components/business/provinceNavForm'));
const CityNavForm = lazy(() => import('@components/business/cityNavForm'));
const CountyNavForm = lazy(() => import('@components/business/countyNavForm'));
const CenterNavForm = lazy(() => import('@app/components/business/centerNavForm'));
const NavTopBgForm = lazy(() => import('@app/components/business/NavTopBgForm'));
const OperateForm = lazy(() => import('@app/components/business/operateForm'));
const SecondNavForm = lazy(() => import('@app/components/business/SecondNavForm'));
const ChannelNavTopBgDrawer = lazy(() => import('@app/components/business/ChannelNavTopBgDrawer'));

type State = {
  loading: boolean;
  type: 'appNav' | 'secondNav' | 'cityNavOld' | 'centerNav' | 'cityNav' | 'provNav' | 'countyNav';
  onCount: number;
  selectType: 0 | 1;
  cityId: any;
  info: {
    [key in
      | 'appNav'
      | 'secondNav'
      | 'cityNavOld'
      | 'centerNav'
      | 'cityNav'
      | 'provNav'
      | 'countyNav']: {
      name: string;
      func: 'getAppNavList' | 'getCityNavList' | 'getCenterNavList';
      index: 'app_nav_list' | 'area_list' | 'county_list';
      permPrefix:
        | 'app_nav:'
        | 'area:city_'
        | 'app_nav:1:'
        | 'area:province_'
        | 'area:county_'
        | 'app_nav:2:';
      pagination: boolean;
      funcPrefix: 'AppNav' | 'CityNav' | 'CenterNav' | 'CountyNav' | 'SecondNav';
      detailIndex: 'app_nav' | 'area';
    };
  };
  privGroup: any[];
  provinceList: any[];
  countyList: any[];
  provinceId: any;
  appNavList: any;
  appNavId: any;
  editDetail: CommonObject;
  editBg: CommonObject;
  operate: CommonObject;
  channelNavTopBg: CommonObject;
};

type APITypes = keyof typeof api;

class NavManager extends BaseComponent<ITableProps, State> {
  constructor(props: any) {
    super(props);
    this.state = {
      loading: false,
      type: 'appNav',
      onCount: 0,
      selectType: 0,

      info: {
        appNav: {
          name: '首页分类',
          func: 'getAppNavList',
          index: 'app_nav_list',
          permPrefix: 'app_nav:',
          pagination: true,
          funcPrefix: 'AppNav',
          detailIndex: 'app_nav',
        },
        secondNav: {
          name: '首页二级分类',
          func: 'getAppNavList',
          index: 'app_nav_list',
          permPrefix: 'app_nav:2:',
          pagination: true,
          funcPrefix: 'SecondNav',
          detailIndex: 'app_nav',
        },

        cityNavOld: {
          name: '城市分类（旧）',
          func: 'getCityNavList',
          index: 'area_list',
          permPrefix: 'area:city_',
          pagination: false,
          funcPrefix: 'CityNav',
          detailIndex: 'area',
        },
        centerNav: {
          name: '频道分类',
          func: 'getCenterNavList',
          index: 'app_nav_list',
          permPrefix: 'app_nav:1:',
          pagination: true,
          funcPrefix: 'CenterNav',
          detailIndex: 'app_nav',
        },
        cityNav: {
          name: '城市分类',
          func: 'getCityNavList',
          index: 'area_list',
          permPrefix: 'area:city_',
          pagination: false,
          funcPrefix: 'CityNav',
          detailIndex: 'area',
        },
        provNav: {
          name: '省级分类',
          func: 'getCityNavList',
          index: 'area_list',
          permPrefix: 'area:province_',
          pagination: false,
          funcPrefix: 'CityNav',
          detailIndex: 'area',
        },
        countyNav: {
          name: '区县分类',
          func: 'getCityNavList',
          index: 'area_list',
          permPrefix: 'area:county_',
          pagination: false,
          funcPrefix: 'CountyNav',
          detailIndex: 'area',
        },
      },
      privGroup: [],
      provinceList: [],
      countyList: [],
      appNavList: [],
      appNavId: '',
      provinceId: '',
      cityId: '',
      editDetail: {
        key: Date.now(),
        visible: false,
      },
      editBg: {
        key: Date.now() + 1,
        visible: false,
      },
      operate: {
        key: Date.now() + 2,
        visible: false,
      },
      channelNavTopBg: {
        visible: false,
        editRecord: null,
      },
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    this.setMenu();
    this.getPrivGroup();
  }

  componentDidUpdate(prevProps: any) {
    if (prevProps.tableList.timestamp !== this.props.tableList.timestamp) {
      const { collapsed_count, enabled_count } = this.props.tableList.allData;
      if (['provNav', 'cityNav', 'cityNavOld', 'countyNav'].indexOf(this.state.type) > -1) {
        this.setState({
          onCount: this.props.tableList.records.filter((v: any) => v.enabled).length,
        });
      } else {
        this.setState({
          onCount: enabled_count || 0,
        });
      }

      // if (this.state.type === 'appNav' || this.state.type === 'centerNav') {
      //   this.setState({
      //     onCount: enabled_count || 0,
      //   });
      // } else {
      //   let onCount = 0;
      //   this.props.tableList.records.map((v: any) => {
      //     if (v.enabled) {
      //       onCount = onCount + 1;
      //     }
      //   });
      //   this.setState({
      //     onCount,
      //   });
      // }
    }
  }

  getProvinceList = () => {
    api.getCityNavList({ current: 1, size: 1000 }).then((res: any) => {
      if (res.data.area_list.length === 0) {
        message.error('暂无省级分类，无法查看城市列表');
        return;
      }
      this.setState(
        {
          provinceList: res.data.area_list,
          provinceId: res.data.area_list[0].id,
        },
        () => {
          if (this.state.type === 'cityNav') {
            this.getData({ current: 1, size: 10 });
          } else if (this.state.type === 'countyNav') {
            this.getCountyList();
          }
        }
      );
    });
  };
  // 获取首页导航
  getAppNavList = () => {
    api.getAppNavList({ current: 1, size: 1000 }).then((res: any) => {
      if (res.data.app_nav_list.records.length === 0) {
        message.error('暂无首页分类，无法查看');
        return;
      }
      // let newData = res.data.app_nav_list.records.filter((v: any) => {
      //   return v.uri_scheme.indexOf('tianmu') === -1;
      // });
      let newData = res.data.app_nav_list.records.filter((v: any) => {
        return v.uri_scheme.indexOf('chaoke') === -1;
      });
      newData = newData.filter((v: any) => {
        return v.uri_scheme.indexOf('area') === -1;
      });
      newData = newData.filter((v: any) => {
        return v.uri_scheme.indexOf('follow') === -1;
      });
      this.setState(
        {
          appNavList: newData,
          appNavId: res.data.app_nav_list.records[0].id,
        },
        () => {
          this.getData({ current: 1, size: 10 });
        }
      );
    });
  };
  getCountyList = () => {
    api
      .getCityNavList({ current: 1, size: 1000, parent_id: this.state.provinceId })
      .then((res: any) => {
        if (res.data.area_list.length === 0) {
          message.error('暂无区县分类，无法查看区县列表');
          this.setState({
            countyList: [],
            cityId: '',
          });
          return;
        }
        this.setState(
          {
            countyList: res.data.area_list,
            cityId: res.data.area_list[0].id,
          },
          () => {
            this.getData({ current: 1, size: 10 });
          }
        );
      });
  };
  getPrivGroup = () => {
    api.getPrivilegeGroupList().then((r: any) => {
      this.setState({
        privGroup: r.data.privilege_group_list,
      });
    });
  };

  getData = (overlap: CommonObject = {}, filter: any = this.getFilter()) => {
    const { info, type } = this.state;
    this.props.dispatch(getTableList(info[type].func, info[type].index, { ...filter, ...overlap }));
  };

  getFilter = () => {
    const { current, size } = this.props.tableList || {
      current: 1,
      size: 10,
    };
    if (this.state.type === 'cityNavOld') {
      return { current, size, parent_id: -1 };
    }
    if (this.state.type === 'cityNav') {
      return { current, size, parent_id: this.state.provinceId };
    }
    if (this.state.type === 'countyNav') {
      return { current, size, parent_id: this.state.cityId };
    }
    if (this.state.type === 'secondNav') {
      return { current, size, category: 2, parent_id: this.state.appNavId };
    }
    return { current, size };
  };

  getColumns = () => {
    const { info, type, onCount } = this.state;
    const { current, size } = this.props.tableList;
    const getDropdown = (record: any) => {
      const menu = (
        <Menu>
          {this.requirePerm(`${info[type].permPrefix}update`)(
            <Menu.Item onClick={this.editRecord.bind(this, record)}>编辑</Menu.Item>
          )}
          {this.requirePerm(`${info[type].permPrefix}update_state`)(
            <Menu.Item onClick={this.updateState.bind(this, record)}>
              {record.enabled ? '下线' : '上线'}
            </Menu.Item>
          )}
          {console.log(record)}
          {type === 'appNav' &&
            ['推荐', '城市'].indexOf(record.name) < 0 &&
            record.uri_scheme.indexOf('followed') === -1 &&
            this.requirePerm('app_nav:head_style_set')(
              <Menu.Item onClick={this.setupChannelHeadStyle.bind(this, record)}>
                设置头部样式
              </Menu.Item>
            )}
          {type === 'appNav' &&
            this.requirePerm(`${info[type].permPrefix}set_default`)(
              <Menu.Item
                onClick={this.updateDefault.bind(this, record)}
                disabled={record.account_set_default_status != 1}
              >
                {record.defaultable ? '取消默认首页' : '设为默认首页'}
              </Menu.Item>
            )}
          {this.requirePerm(`${info[type].permPrefix}delete`)(
            <Menu.Item onClick={this.deleteRecord.bind(this, record)}>删除</Menu.Item>
          )}
        </Menu>
      );
      return (
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };

    const getSeq = (i: number) => (current - 1) * size + i + 1;

    const columns: any = [
      {
        title: '排序',
        key: 'sort',
        render: (text: any, record: any, i: number) => (
          <span>
            {this.requirePerm(`${info[type].permPrefix}update_sort`)(
              <A
                disabled={getSeq(i) > onCount || record.collapsed || getSeq(i) === 1}
                className="sort-up"
                onClick={this.exchangeOrder.bind(this, record.id, '0')}
              >
                <Icon type="up-circle" theme="filled" />
              </A>
            )}{' '}
            {this.requirePerm(`${info[type].permPrefix}update_sort`)(
              <A
                disabled={getSeq(i) >= onCount || record.collapsed}
                className="sort-down"
                onClick={this.exchangeOrder.bind(this, record.id, '1')}
              >
                <Icon type="down-circle" theme="filled" />
              </A>
            )}
          </span>
        ),
        width: 70,
      },
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: this.state.type !== 'provNav' ? 'URL' : '',
        key: 'uri_scheme',
        dataIndex:
          ['appNav', 'centerNav', 'secondNav'].indexOf(this.state.type) > -1 ? 'uri_scheme' : 'url',
        render: (text: any) => (this.state.type !== 'provNav' ? text : ''),
      },
      {
        title: '分类名称',
        key: 'name',
        dataIndex: 'name',
      },
      {
        title: type === 'appNav' ? '属性' : '',
        dataIndex: 'collapsed',
        render: (text: any) => type === 'appNav' && (text ? '固定' : '可移动'),
        width: type === 'appNav' ? 90 : 0,
      },
      {
        title: '状态',
        key: 'enabled',
        dataIndex: 'enabled',
        render: (text: any) => <span>{text ? '上线' : '下线'}</span>,
        width: 80,
      },
      {
        title: '创建时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm')}</span>,
        width: 150,
      },
      {
        title: '创建人',
        key: 'creator',
        dataIndex: 'creator',
        width: 120,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => getDropdown(record),
        width: 70,
      },
    ];

    if (type == 'appNav' || type == 'secondNav') {
      columns.splice(4, 0, {
        title: '页面类型',
        key: 'nav_kind',
        dataIndex: 'nav_kind',
        render: (text: any) => (text == 1 ? 'H5' : '原生'),
        width: 90,
      });
    }
    return columns;
  };

  getRequestBody = (body: any) => {
    return { ...body };
  };

  editRecord = (record: any) => {
    const { info, type } = this.state;
    api[`get${info[type].funcPrefix}Detail` as APITypes]({ id: record.id }).then((r: any) => {
      this.setState({
        editDetail: {
          key: Date.now(),
          visible: true,
          edit: true,
          type: 1, //编辑
          provinceId: this.state.provinceId || '',
          ...r.data[`${info[type].detailIndex}`],
          belong: r.data[`${info[type].detailIndex}`].belong
            ? r.data[`${info[type].detailIndex}`].belong.split(',')
            : [],
          nav_selected_icon_url: r.data[`${info[type].detailIndex}`].nav_selected_icon_url,
          nav_unselected_icon_url: r.data[`${info[type].detailIndex}`].nav_unselected_icon_url,
        },
      });
    });
  };

  updateDefault = (record: any) => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .updateAppNavDefault({
        id: record.id,
        enabled: !record.defaultable,
      })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  setupChannelHeadStyle = (record: any) => {
    this.setState({
      channelNavTopBg: {
        visible: true,
        editRecord: record,
        key: Date.now(),
      },
    });
  };

  updateState = (record: any) => {
    const { info, type } = this.state;
    if (record.collapsed) {
      message.error('固定导航不允许下线，请先设置为可移动导航');
      return;
    }
    this.props.dispatch(setConfig({ loading: true }));
    api[`update${info[type].funcPrefix}State` as APITypes](
      this.getRequestBody({
        id: record.id,
        enabled: !record.enabled,
      })
    )
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  deleteRecord = (record: any) => {
    const { info, type } = this.state;
    Modal.confirm({
      width: 440,
      title: <p>确认删除频道“{record.name}”吗？</p>,
      content: (
        <p>
          删除后，将清空该频道下所有内容
          {this.state.type == 'appNav' && <span style={{ color: 'red' }}>及子频道相关内容</span>}
        </p>
      ),
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        api[`delete${info[type].funcPrefix}` as APITypes](
          this.getRequestBody({
            id: record.id,
            enabled: !record.enabled,
          })
        )
          .then(() => {
            message.success('操作成功');
            this.getData();
            this.props.dispatch(setConfig({ loading: false }));
          })
          .catch(() => this.props.dispatch(setConfig({ loading: false })));
      },
    });
  };

  exchangeOrder = (id: any, sortFlag: string) => {
    const { info, type } = this.state;
    this.props.dispatch(setConfig({ loading: true }));
    api[`sort${info[type].funcPrefix}` as APITypes](
      this.getRequestBody({
        id,
        sort_flag: sortFlag,
      })
    )
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  createRecord = () => {
    const appNavRecord: any = {
      name: '',
      uri_scheme: '',
      min_version_str: '',
      max_version_str: '',
      enabled: false,
      collapsed: false,
      selected: true,
      require_permission_id: '0',
      information: '',
      account_set_default_status: 0,
      nav_kind: 0,
    };
    const secondNavRecord: any = {
      name: '',
      uri_scheme: '',
      information: '',
      enabled: false,
      require_permission_id: '0',
      parent_id: '',
      belong: [],
      nav_kind: 0,
      min_version_str: '',
      max_version_str: '',
      nav_selected_icon_url: '',
      nav_unselected_icon_url: '',
    };
    const cityNavOldRecord: any = {
      name: '',
      url: '',
      enabled: false,
      sort_number: '',
      require_permission_id: '0',
      province_name: '',
    };
    const cityNavRecord: any = {
      name: '',
      url: '',
      enabled: false,
      sort_number: '',
      require_permission_id: '0',
      parent_id: '',
      belong: [],
    };
    const countyNavRecord: any = {
      name: '',
      url: '',
      enabled: false,
      sort_number: '',
      require_permission_id: '0',
      parent_id: '',
      provinceId: this.state.provinceId,
      belong: [],
    };
    const centerNavRecord = {
      name: '',
      uri_scheme: '',
      information: '',
      min_version_str: '',
      max_version_str: '',
      enabled: false,
      collapsed: false,
      selected: true,
      require_permission_id: '0',
    };
    const provNavRecord = {
      name: '',
      url: '',
      sort_number: '',
      require_permission_id: '0',
      belong: [],
    };
    const newData: any = {
      appNavRecord,
      cityNavOldRecord,
      centerNavRecord,
      provNavRecord,
      cityNavRecord,
      countyNavRecord,
      secondNavRecord,
    };
    this.setState({
      editDetail: {
        key: Date.now(),
        visible: true,
        edit: false,
        type: 2, //新建
        ...newData[`${this.state.type}Record`],
      },
    });
  };

  handleTypeChange = (value: any) => {
    this.props.dispatch(
      setTableList({
        total: 0,
        current: 1,
        size: this.props.tableList.size,
        records: [],
        allData: {},
      })
    );
    this.setState(
      {
        type: value,
        provinceId: '',
        cityId: '',
      },
      () => {
        if (value === 'cityNav' || value === 'countyNav') {
          this.getProvinceList();
        } else if (value === 'secondNav') {
          this.getAppNavList();
        } else {
          this.getData({
            current: 1,
            size: 10,
          });
        }
      }
    );
  };

  handleProvinceIdChange = (value: any) => {
    this.setState(
      {
        provinceId: value,
      },
      () => {
        if (this.state.type === 'cityNav') {
          this.getData({ current: 1 });
        } else {
          this.getCountyList();
        }
      }
    );
  };
  handleCountyIdChange = (value: any) => {
    this.setState(
      {
        cityId: value,
      },
      () => {
        this.getData({ current: 1 });
      }
    );
  };
  handleappNavIdChange = (value: any) => {
    this.setState(
      {
        appNavId: value,
      },
      () => {
        this.getData({ current: 1 });
      }
    );
  };
  handleSubmit = () => {
    this.handleSubmitForm(this.state.type);
  };

  onSubmitEnd = () => {
    this.getData();
    this.setState({ editDetail: { ...this.state.editDetail, visible: false }, loading: false });
  };

  closeDrawer = () => {
    this.setState({
      editDetail: { ...this.state.editDetail, visible: false },
    });
  };

  onBgEnd = () => {
    this.setState({ editBg: { ...this.state.editBg, visible: false }, loading: false });
  };

  onOperateEnd = () => {
    this.setState({ operate: { ...this.state.operate, visible: false }, loading: false });
  };

  channelNavTopBgEnd = () => {
    this.setState({ channelNavTopBg: { visible: false, editRecord: null } });
  };

  switchType = (e: any) => {
    this.setState({
      selectType: e.target.value,
    });
    console.log(this.state.selectType);
  };

  showArEntranceForm = () => {
    this.setState({ operate: { ...this.state.operate, visible: true } });
  };

  editConfigBg = () => {
    api.getNavTopBg().then((res: any) => {
      this.setState({
        editBg: {
          key: Date.now(),
          visible: true,
          ...res.data.config,
        },
      });
    });
  };

  render() {
    const { info, type, editDetail, editBg, operate, channelNavTopBg } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            <Select
              value={type}
              onChange={this.handleTypeChange}
              style={{ marginRight: 8, width: 130 }}
            >
              {this.requirePerm('app_nav:view')(
                <Select.Option value="appNav">首页分类</Select.Option>
              )}
              {this.requirePerm('app_nav:2:view')(
                <Select.Option value="secondNav">首页二级分类</Select.Option>
              )}
              {this.requirePerm('area:province_view')(
                <Select.Option value="provNav">省级分类</Select.Option>
              )}
              {this.requirePerm('area:city_view')(
                <Select.Option value="cityNav">城市分类</Select.Option>
              )}
              {this.requirePerm('area:county_view')(
                <Select.Option value="countyNav">区县分类</Select.Option>
              )}
              {this.requirePerm('area:city_view')(
                <Select.Option value="cityNavOld">城市分类(旧)</Select.Option>
              )}
              {this.requirePerm('app_nav:1:view')(
                <Select.Option value="centerNav">频道分类</Select.Option>
              )}
              {/* <Select.Option value="countyNav">区县分类</Select.Option> */}
            </Select>
            {(this.state.type === 'cityNav' || this.state.type === 'countyNav') && (
              <Select
                value={this.state.provinceId}
                onChange={this.handleProvinceIdChange}
                style={{ marginRight: 8, width: 130 }}
              >
                <Select.Option value="" disabled>
                  选择省级分类
                </Select.Option>
                {this.state.provinceList.map((v: any) => (
                  <Select.Option value={v.id} key={v.id}>
                    {v.name}
                  </Select.Option>
                ))}
              </Select>
            )}
            {this.state.type === 'countyNav' && (
              <Select
                value={this.state.cityId}
                onChange={this.handleCountyIdChange}
                style={{ marginRight: 8, width: 130 }}
              >
                <Select.Option value="" disabled>
                  选择区县分类
                </Select.Option>
                {this.state.countyList.map((v: any) => (
                  <Select.Option value={v.id} key={v.id}>
                    {v.name}
                  </Select.Option>
                ))}
              </Select>
            )}
            {this.state.type === 'secondNav' && (
              <Select
                value={this.state.appNavId}
                onChange={this.handleappNavIdChange}
                style={{ marginRight: 8, width: 130 }}
              >
                <Select.Option value="" disabled>
                  选择首页导航分类
                </Select.Option>
                {this.state.appNavList.map((v: any) => (
                  <Select.Option value={v.id} key={v.id}>
                    {v.name}
                  </Select.Option>
                ))}
              </Select>
            )}
            {this.requirePerm(`${info[type].permPrefix}create`)(
              <Button onClick={this.createRecord} style={{ verticalAlign: 'top', marginRight: 8 }}>
                <Icon type="plus-circle" /> 添加{info[type].name}
              </Button>
            )}
            {this.requirePerm('web_feature:custom_operate_switch')(
              <Button
                onClick={this.showArEntranceForm}
                style={{ verticalAlign: 'top', marginRight: 8 }}
              >
                运营位入口管理
              </Button>
            )}
            {/* {this.requirePerm('app_info_config:save')(
              <Button onClick={this.editConfigBg} style={{ verticalAlign: 'top' }}>
                全局头部样式
              </Button>
            )} */}

            <Button
              onClick={() => {
                this.props.history.push('/view/desktopGuide');
              }}
              style={{ verticalAlign: 'top', marginRight: 8 }}
            >
              添加到桌面
            </Button>
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func={info[type].func}
            index={info[type].index}
            columns={this.getColumns()}
            filter={this.getFilter()}
            pagination={info[type].pagination}
            rowKey="id"
          />
          <Drawer
            title="设置头部样式"
            visible={editBg.visible}
            skey={editBg.key}
            onClose={this.onBgEnd}
            onOk={() => this.formRefs.navBg.doSubmit()}
          >
            <NavTopBgForm
              formContent={editBg}
              onEnd={this.onBgEnd}
              wrappedComponentRef={this.setFormRef.bind(this, 'navBg')}
            />
          </Drawer>
          <Drawer
            title={editDetail.edit ? `编辑${info[type].name}` : `添加${info[type].name}`}
            visible={editDetail.visible}
            skey={editDetail.key}
            onClose={this.closeDrawer}
            onOk={this.handleSubmit}
          >
            {type === 'appNav' && (
              <AppNavForm
                privGroup={this.state.privGroup}
                formContent={{ ...editDetail }}
                onEnd={this.onSubmitEnd}
                wrappedComponentRef={this.setFormRef.bind(this, 'appNav')}
              />
            )}
            {type === 'centerNav' && (
              <CenterNavForm
                privGroup={this.state.privGroup}
                formContent={{ ...editDetail }}
                onEnd={this.onSubmitEnd}
                wrappedComponentRef={this.setFormRef.bind(this, 'centerNav')}
              />
            )}
            {type === 'provNav' && (
              <ProvinceNavForm
                privGroup={this.state.privGroup}
                formContent={{ ...editDetail }}
                onEnd={this.onSubmitEnd}
                wrappedComponentRef={this.setFormRef.bind(this, 'provNav')}
              />
            )}
            {type === 'cityNavOld' && (
              <OldCityNavForm
                privGroup={this.state.privGroup}
                formContent={{ ...editDetail }}
                onEnd={this.onSubmitEnd}
                wrappedComponentRef={this.setFormRef.bind(this, 'cityNavOld')}
              />
            )}
            {type === 'cityNav' && (
              <CityNavForm
                privGroup={this.state.privGroup}
                formContent={{ ...editDetail }}
                onEnd={this.onSubmitEnd}
                wrappedComponentRef={this.setFormRef.bind(this, 'cityNav')}
                provinceList={this.state.provinceList}
              />
            )}
            {type === 'countyNav' && (
              <CountyNavForm
                privGroup={this.state.privGroup}
                formContent={{ ...editDetail }}
                onEnd={this.onSubmitEnd}
                wrappedComponentRef={this.setFormRef.bind(this, 'countyNav')}
                provinceList={this.state.provinceList}
                countyList={this.state.countyList}
              />
            )}
            {type === 'secondNav' && (
              <SecondNavForm
                privGroup={this.state.privGroup}
                formContent={{ ...editDetail }}
                onEnd={this.onSubmitEnd}
                wrappedComponentRef={this.setFormRef.bind(this, 'secondNav')}
                appNavList={this.state.appNavList}
              />
            )}
          </Drawer>

          <Drawer
            title={
              <>
                运营位入口管理
                <span style={{ color: '#999', fontSize: 12, marginLeft: 10 }}>
                  仅针对7.5以下版本生效
                </span>
              </>
            }
            visible={operate.visible}
            skey={operate.key}
            onClose={this.onOperateEnd}
            onOk={() => this.formRefs.OperateForm.doSubmit()}
          >
            <OperateForm
              formContent={operate}
              onEnd={this.onOperateEnd}
              wrappedComponentRef={this.setFormRef.bind(this, 'OperateForm')}
            />
          </Drawer>

          <div key={channelNavTopBg.key}>
            <ChannelNavTopBgDrawer
              visible={channelNavTopBg.visible}
              record={channelNavTopBg.editRecord}
              onClose={this.channelNavTopBgEnd}
              onOk={this.channelNavTopBgEnd}
            />
          </div>
        </div>
      </>
    );
  }
}

export default withRouter(connect<any, any>()(NavManager));
