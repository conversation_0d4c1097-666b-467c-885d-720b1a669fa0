.permission-manager {
  .ant-checkbox-wrapper {
    font-size: 14px;
  }

  .ant-menu-item, .ant-submenu-title {
    padding: 0 4px!important;
  }

  .outside {
    background: #fff;
    height: 100%;
    overflow: auto;
    flex: 1;
  }

  .group-selector {
    background: #fff;
    height: 100%;
    margin-bottom: 0;
    overflow-y: auto;
    border-right: 1px solid #eee;
  }

  .permission-content {
    background: #fff;
    height: 100%;
  }

  .title-bar {
    margin: 16px 16px 0 16px;
    border-bottom: 1px solid #eee;
  }

  .permission-list{
    margin: 8px!important;
    font-size: 14px;
    flex: 1;
    height: 100%;
    display: flex;
    
    .left {
      border-right: 1px solid #eee;
      text-align: center;
      overflow-y: auto;
      height: 100%;

      
    }

    .type-1{
      text-align: left;
      padding: 8px;
      
      .panel {
        color: rgba(0,0,0,0.85);
      }

      .row {
        margin: 4px;
        text-align: center;
        padding: 8px;
        background: #fff;
        color: rgba(0,0,0,0.65);
      }

      .current {
        background: #e6f7ff;
        color: #1890ff;
      }
    }

    .left-selected {
      color: blue!important;
    }

    .right {
      margin: 8px 8px 0 8px;
      height: 100%;
      overflow-y: auto;

      .title {
        margin: 8px 8px 0 8px;
        padding-bottom: 8px;
        border-bottom: 1px solid #eee;
      }

      .content{
        margin: 8px 24px!important;
        line-height: 28px;
      }
    }
  }


}