import { setConfig } from '@action/config';
import { getTableList } from '@app/action/tableList';
import { sysApi as api } from '@app/api';
import { Table } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm } from '@utils/utils.tsx';
import { Button, Col, Form, Icon, Input, message, Modal, Row, Switch } from 'antd';
import React from 'react';
import { withRouter } from 'react-router';

@(withRouter as any)
@connect
class InterfaceManager extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      loading: false,
      form: {
        visible: false,
        key: Date.now(),
        name: '',
        uri: '',
      },
      commentSwitch: false,
    };
  }

  componentDidMount() {
    this.getData();
    this.props.dispatch(
      setConfig({
        selectKeys: this.props.selectKeys,
        openKeys: this.props.openKeys,
      })
    );
  }

  getData = () => {
    this.props.dispatch(getTableList('getInterfaceList', 'interface_switch_list', {}));
    api.getSwitchDetail({ feature: 'comment_sys_switch' }).then((r: any) => {
      this.setState({ commentSwitch: r.data.switch });
    });
  };

  changeStatus = (record: any, checked: boolean) => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .updateInterface({
        id: record.id,
        status: checked ? '0' : '1',
      })
      .then((r: any) => {
        message.success('操作成功');
        this.getData();
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  getColumns = () => {
    return [
      {
        title: '接口名称',
        key: 'name',
        dataIndex: 'name',
      },
      {
        title: '接口地址',
        key: 'uri',
        dataIndex: 'uri',
      },
      {
        title: '接口是否需要禁用',
        key: 'status',
        dataIndex: 'status',
        width: 150,
        render: (text: any, record: any) =>
          requirePerm(
            this,
            'interface_switch:update'
          )(
            // tslint:disable-next-line: jsx-wrap-multiline
            <Switch
              checked={text != 1}
              checkedChildren="开"
              unCheckedChildren="关"
              onChange={(checked: boolean) => this.changeStatus(record, checked)}
            />
          ),
      },
    ];
  };

  createInterface = () => {
    this.setState({
      form: {
        visible: true,
        key: Date.now(),
        name: '',
        uri: '',
      },
    });
  };

  valueChange = (key: any, value: any) => {
    this.setState({ form: { ...this.state.form, [key]: value } });
  };

  handleSubmit = () => {
    const { form } = this.state;
    if (form.name === '' || form.uri === '') {
      message.error('请填写所有内容');
      return;
    }
    this.setState({ loading: true });
    api
      .createInterface({
        name: form.name,
        uri: form.uri,
      })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setState({
          form: { ...form, visible: false },
          loading: false,
        });
      })
      .then(() => this.setState({ loading: false }));
  };

  updateComment = (status: boolean) => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .updateSwitch({
        status,
        feature: 'comment_sys_switch',
      })
      .then((r: any) => {
        message.success('操作成功');
        this.getData();
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  render() {
    const { form } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            {requirePerm(
              this,
              'interface_switch:save'
            )(
              <Button onClick={this.createInterface} style={{ marginRight: 8 }}>
                <Icon type="plus-circle" /> 添加接口
              </Button>
            )}
            评论接口统一关闭{' '}
            {requirePerm(
              this,
              'web_feature:comment_sys_switch'
            )(<Switch checked={this.state.commentSwitch} onChange={this.updateComment} />)}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getInterfaceList"
            index="interface_switch_list"
            rowKey="id"
            columns={this.getColumns()}
            filter={{}}
            pagination={false}
          />
          <Modal
            visible={form.visible}
            key={form.key}
            onOk={this.handleSubmit}
            onCancel={() => this.setState({ form: { ...form, visible: false } })}
            confirmLoading={this.state.loading}
            title="添加接口"
          >
            <Form.Item label="接口名称" required={true}>
              <Input
                placeholder="填写接口名称"
                value={form.name}
                onChange={(e: any) => this.valueChange('name', e.target.value)}
              />
            </Form.Item>
            <Form.Item label="接口地址" required={true}>
              <Input
                placeholder="填写接口地址"
                value={form.uri}
                onChange={(e: any) => this.valueChange('uri', e.target.value)}
              />
            </Form.Item>
          </Modal>
        </div>
      </>
    );
  }
}

export default InterfaceManager;
