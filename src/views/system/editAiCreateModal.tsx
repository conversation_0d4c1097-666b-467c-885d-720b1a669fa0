import { Button, Form, Icon, Input, InputNumber, Modal, Spin, Tooltip, message } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { sysApi } from '@app/api';
import { ImageUploader } from '@app/components/common';

const EditAiCreateModal = (props: any, ref: any) => {
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator } = props.form;

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true);
        const parmas = {
          id: props.record?.id,
          title: props.record?.title,
          sub_title: props.record?.sub_title,
          pic: props.record?.pic,
          extend: values.img1 + ',' + values.img2 + ',' + values.img3,
        };
        sysApi
          .editAiChannel(parmas)
          .then((res: any) => {
            message.success('操作成功');
            setLoading(false);
            props.onOk && props.onOk();
          })
          .catch(() => {
            // message.error('添加失败');
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
        setLoading(false);
      }
    });
  };
  const imgList = props.record?.extend?.split(',') || [];
  return (
    <Modal
      width={500}
      visible={props.visible}
      title="AI创作"
      key={props.key}
      onCancel={props.onCancel}
      onOk={handleSubmit}
      maskClosable={false}
      destroyOnClose={true}
      confirmLoading={loading}
    >
      <Spin spinning={loading}>
        <Form {...formLayout}>
          <Form.Item label="AI标题" extra="支持扩展名 .jpg .jpeg .png 等格式图片，比例为110:94">
            {getFieldDecorator('img1', {
              initialValue: imgList?.[0],
              rules: [
                {
                  required: true,
                  message: '请上传AI标题',
                },
              ],
            })(<ImageUploader ratio={110 / 94} />)}
          </Form.Item>
          <Form.Item label="AI封面图" extra="支持扩展名 .jpg .jpeg .png 等格式图片，比例为110:94">
            {getFieldDecorator('img2', {
              initialValue: imgList?.[1],
              rules: [
                {
                  required: true,
                  message: '请上传AI封面图',
                },
              ],
            })(<ImageUploader ratio={110 / 94} />)}
          </Form.Item>

          <Form.Item label="AI配文" extra="支持扩展名 .jpg .jpeg .png 等格式图片，比例为110:94">
            {getFieldDecorator('img3', {
              initialValue: imgList?.[2],
              rules: [
                {
                  required: true,
                  message: '请上传AI配文',
                },
              ],
            })(<ImageUploader ratio={110 / 94} />)}
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<any>({ name: 'EditAiCreateModal' })(
  forwardRef<any, any>(EditAiCreateModal)
);
