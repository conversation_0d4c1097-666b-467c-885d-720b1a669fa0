import { Button, Form, Icon, Input, InputNumber, Modal, Spin, Tooltip, message } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { sysApi } from '@app/api';
import { ImageUploader } from '@app/components/common';

const EditAiChannelModal = (props: any, ref: any) => {
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator } = props.form;

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true);
        const parmas = {
          ...values,
          id: props.record?.id,
        };
        sysApi
          .editAiChannel(parmas)
          .then((res: any) => {
            message.success('操作成功');
            setLoading(false);
            props.onOk && props.onOk();
          })
          .catch(() => {
            // message.error('添加失败');
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
        setLoading(false);
      }
    });
  };

  return (
    <Modal
      width={500}
      visible={props.visible}
      title="修改模块"
      key={props.key}
      onCancel={props.onCancel}
      onOk={handleSubmit}
      maskClosable={false}
      destroyOnClose={true}
      confirmLoading={loading}
    >
      <Spin spinning={loading}>
        <Form {...formLayout}>
          <Form.Item label="标题" extra="仅用于后台展示">
            {getFieldDecorator('title', {
              initialValue: props.record?.title,
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: '请输入标题',
                },
              ],
            })(<Input maxLength={6} placeholder="请输入标题，最多6字" />)}
          </Form.Item>
          <Form.Item
            label="标题图片"
            extra="支持扩展名 .jpg .jpeg .png 等格式图片，比例为不限制，建议高度60px"
          >
            {getFieldDecorator('pic', {
              initialValue: props.record?.pic,
              rules: [
                {
                  required: true,
                  message: '请上传标题图片',
                },
              ],
            })(<ImageUploader isCutting />)}
          </Form.Item>
          <Form.Item label="副标题">
            {getFieldDecorator('sub_title', {
              initialValue: props.record?.sub_title,
            })(<Input maxLength={10} placeholder="请输入副标题，最多10字" />)}
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<any>({ name: 'EditAiChannelModal' })(
  forwardRef<any, any>(EditAiChannelModal)
);
