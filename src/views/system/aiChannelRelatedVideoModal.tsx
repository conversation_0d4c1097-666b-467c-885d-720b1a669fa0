import { Button, Form, Icon, Input, InputNumber, Modal, Spin, Tooltip, message } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { sysApi } from '@app/api';
import SearchAndInput from '@components/common/newNewsSearchAndInput';
import { A, ImageUploader } from '@app/components/common';

const EditAiChannelModal = (props: any, ref: any) => {
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator } = props.form;

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true);
        const parmas = {
          id: props.record?.id,
          title: props.record?.title,
          sub_title: props.record?.sub_title,
          pic: props.record?.pic,
          articles: JSON.stringify(
            values.article_list.map((v: any) => {
              return {
                id: `${v.uuid || v.id}`,
                new_list_pic: v.new_list_pic,
                new_list_title: v.new_list_title,
              };
            })
          ),
        };
        sysApi
          .editAiChannel(parmas)
          .then((res: any) => {
            message.success('添加成功');
            setLoading(false);
            props.onOk && props.onOk();
          })
          .catch(() => {
            // message.error('添加失败');
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
        setLoading(false);
      }
    });
  };

  const editPic = (url: string, index: number) => {
    let pic: string = url;
    let modal: any;
    const picChange = (u: string) => {
      pic = u;
      modal.update({
        content: (
          <>
            <ImageUploader value={pic} onChange={picChange} ratio={3 / 4} imgMaxWidth={314} />
            <p>比例3:4</p>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '编辑图片',
      content: (
        <>
          <ImageUploader value={pic} onChange={picChange} ratio={3 / 4} imgMaxWidth={314} />
          <p>比例3:4</p>
        </>
      ),
      onOk: (destroy: Function) => {
        if (!pic) {
          message.error('请上传图片');
          return;
        }
        const articles = [...props.form.getFieldsValue().article_list];
        articles[index].new_list_pic = pic;
        props.form.setFieldsValue({ article_list: articles });
        destroy();
      },
    });
  };

  const editTitle = (value: string, index: number) => {
    let title: string = value || '';
    let modal: any;
    const titleChange = (e: any) => {
      title = e.target.value;
      modal.update({
        content: (
          <>
            <Input.TextArea
              placeholder="最多输入40字"
              value={title}
              maxLength={40}
              onPressEnter={(e) => e.preventDefault()}
              onChange={titleChange}
            ></Input.TextArea>
            <div style={{ textAlign: 'right' }}>{title?.length}/40</div>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '自定义标题',
      content: (
        <>
          <Input.TextArea
            placeholder="最多输入40字"
            value={title}
            maxLength={40}
            onPressEnter={(e) => e.preventDefault()}
            onChange={titleChange}
          ></Input.TextArea>
          <div style={{ textAlign: 'right' }}>{title?.length}/40</div>
        </>
      ),
      onOk: (destroy: Function) => {
        if (!title?.trim()) {
          message.error('请输入自定义标题');
          return;
        }
        const articles = [...props.form.getFieldsValue().article_list];
        articles[index].new_list_title = title?.trim();
        props.form.setFieldsValue({ article_list: articles });
        destroy();
      },
    });
  };

  const columns = [
    {
      title: '潮新闻ID',
      dataIndex: 'id',
      width: 80,
      render: (_: any, v: any) => v.uuid || v.id,
    },
    {
      title: '新闻频道',
      key: 'type',
      dataIndex: 'channel_name',
      width: 90,
    },
    {
      title: '新闻标题',
      key: 'list_title',
      dataIndex: 'list_title',
      width: 150,
    },
    {
      // width: 90,
      title: '自定义标题',
      key: 'new_list_title',
      dataIndex: 'new_list_title',
      render: (text: string, record: any, index: number) => {
        return (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <div>{record.new_list_title || record.list_title}</div>
            <a
              style={{ flex: 'none', marginLeft: '5px' }}
              onClick={() => editTitle(record.new_list_title || record.list_title, index)}
            >
              修改
            </a>
          </div>
        );
      },
    },
    {
      title: '列表图',
      align: 'center',
      // title: '列表图',
      dataIndex: 'new_list_pic',
      width: 90,
      render: (text: string, record: any, index: number) => {
        return (
          <div style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
            {text && <img src={text} style={{ height: 40, maxWidth: 70 }} />}
            <A onClick={() => editPic(text, index)}>{text ? '修改' : '上传'}</A>
          </div>
        );
      },
    },
  ];

  return (
    <Modal
      width={1000}
      visible={props.visible}
      title="关联视频"
      key={props.key}
      onCancel={props.onCancel}
      onOk={handleSubmit}
      maskClosable={false}
      destroyOnClose={true}
      confirmLoading={loading}
      bodyStyle={{
        height: 500,
        maxHeight: 500,
        overflow: 'auto',
      }}
    >
      <Spin spinning={loading}>
        <Form {...formLayout}>
          <Form.Item label="关联稿件">
            {getFieldDecorator('article_list', {
              initialValue: props.record?.article_list,
              validateFirst: true,
              rules: [
                {
                  required: true,
                  message: '请关联新闻',
                  type: 'array',
                },
                {
                  max: 50,
                  message: '最多关联50条新闻',
                  type: 'array',
                },
                {
                  min: 3,
                  message: `为保证客户端显示效果，关联新闻数不能少于3条！`,
                  type: 'array',
                },
                {
                  validator: (rule, val, callback) => {
                    if (!val) {
                      return callback('');
                      // } else if (val.filter((v) => !v.doc_title).length > 0) {
                      //   return callback('请填写自定义标题');
                    } else if (val.filter((v) => !v.new_list_pic).length > 0) {
                      return callback('请上传列表图');
                    } else {
                      return callback();
                    }
                  },
                },
              ],
            })(
              <SearchAndInput
                // key={style}
                max={50}
                func="listArticleRecommendSearch"
                columns={columns}
                placeholder="输入ID或标题关联稿件"
                body={{ doc_types: '9,10' }}
                order={true}
                addOnTop={true}
                map={(v: any) => {
                  return v;
                }}
                afix={
                  <Tooltip
                    title={
                      <p>
                        最多关联50条，可关联视频稿、小视频，且每条稿件必须上传推荐位使用的列表图，才能创建成功。
                      </p>
                    }
                  >
                    <Icon type="question-circle" />
                  </Tooltip>
                }
              />
            )}
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<any>({ name: 'EditAiChannelModal' })(
  forwardRef<any, any>(EditAiChannelModal)
);
