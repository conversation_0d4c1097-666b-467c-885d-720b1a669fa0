import { setConfig } from '@action/config';
import { sysApi as api } from '@app/api';
import { A, Table, BaseComponent } from '@components/common';
import { connectTable as connect } from '@utils/connect';
import { getCrumb, requirePerm } from '@utils/utils';
import { Button, Col, Divider, Icon, Input, message, Modal, Row, Transfer } from 'antd';
import React from 'react';
import { withRouter } from 'react-router';
import {
  TSysPermissionGroupRecord,
  IBaseProps,
  CommonObject,
  TSysAdminRecord,
  ITableProps,
  TSysAdminData,
  CommonResponse,
  TSysAdminRoleData,
} from '@app/types';
import { TransferItem } from 'antd/es/transfer';

type AdminListState = {
  loading: boolean;
  keyword: string;
  search: string;
  editRecord: {
    roleList: TSysPermissionGroupRecord[];
    userKeys: number[];
    selected: number[];
    id: string;
    visible: boolean;
    key: number;
  };
};

type Props = IBaseProps<ITableProps<TSysAdminRecord, TSysAdminData>>;

class AdminList extends BaseComponent<ITableProps<TSysAdminRecord, TSysAdminData>, AdminListState> {
  constructor(props: Props) {
    super(props);
    this.state = {
      loading: false,
      search: '',
      keyword: '',
      editRecord: {
        roleList: [],
        userKeys: [],
        selected: [],
        id: '',
        visible: false,
        key: Date.now(),
      },
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    this.setMenu();
  }

  getData = (overlap: CommonObject = {}, filter = this.getFilter()) => {
    this.dispatchTable('getAdminList', 'admin_list', { ...filter, ...overlap });
  };

  getFilter = () => {
    const { current, size } = this.props.tableList;
    return { current, size, keyword: this.state.keyword };
  };

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;

    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 80,
      },
      {
        title: '用户名',
        key: 'user_name',
        dataIndex: 'user_name',
        width: 180,
      },
      {
        title: '姓名',
        key: 'name',
        dataIndex: 'name',
        width: 120,
      },
      {
        title: '权限组',
        key: 'roles',
        dataIndex: 'roles',
        render: (text: any) => (
          <span>
            {Boolean(text) &&
              text.map((v: any, i: number) => (
                <span key={i}>
                  {i === 0 ? '' : '，'}
                  {v.name}
                </span>
              ))}
          </span>
        ),
      },
      {
        title: '状态',
        key: 'state',
        dataIndex: 'state',
        render: (text: any) => <span>{['正常', '停用'][text]}</span>,
        width: 60,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <span>
            {requirePerm(
              this,
              'admin_user:manage'
            )(<A onClick={this.managePermissions.bind(this, record)}>管理权限</A>)}
            <Divider type="vertical" />
            {requirePerm(
              this,
              'admin_user:update_state'
            )(
              <A onClick={this.changeState.bind(this, record)}>{['停用', '启用'][record.state]}</A>
            )}
          </span>
        ),
        width: 150,
      },
    ];
  };

  managePermissions = (record: TSysAdminRecord) => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .getPermissionGroupsForAdmin({ user_id: record.id })
      .then((r: CommonResponse<TSysAdminRoleData>) => {
        const keys = record.roles.map((v: TSysPermissionGroupRecord) => v.id);
        this.setState({
          editRecord: {
            roleList: r.data.unselected.concat(r.data.selected),
            userKeys: keys,
            selected: [],
            id: record.id,
            visible: true,
            key: Date.now(),
          },
        });
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  handleSubmit = () => {
    this.setState({ loading: true });
    api
      .updateAdminPermissionGroup({
        user_id: this.state.editRecord.id,
        role_ids: this.state.editRecord.userKeys.join(','),
      })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setState({
          loading: false,
          editRecord: {
            ...this.state.editRecord,
            visible: false,
          },
        });
      })
      .catch(() => this.setState({ loading: false }));
  };

  handleChange = (nextTargetKeys: string[]) => {
    setTimeout(() => {
      this.setState({
        editRecord: { ...this.state.editRecord, userKeys: nextTargetKeys as unknown as number[] },
      });
    }, 10);
  };

  handleSelectChange = (source: string[], target: string[]) => {
    // setTimeout(() => {
    this.setState({
      editRecord: {
        ...this.state.editRecord,
        selected: [...(source as unknown as number[]), ...(target as unknown as number[])],
      },
    });
    // },10);
  };

  changeState = (record: TSysAdminRecord) => {
    Modal.confirm({
      title: (
        <p>
          确认{['停用', '启用'][record.state]}账号“{record.user_name}”吗？
        </p>
      ),
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        api
          .updateAdminState({ id: record.id, state: ['1', '0'][record.state] })
          .then(() => {
            message.success('操作成功');
            this.getData();
            this.props.dispatch(setConfig({ loading: false }));
          })
          .catch(() => this.props.dispatch(setConfig({ loading: false })));
      },
    });
  };

  doSearch = () => {
    this.setState(
      {
        keyword: this.state.search,
      },
      () => this.getData({ current: 1 })
    );
  };

  handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    this.setState({
      search: e.target.value,
    });
  };

  cancelEdit = () => {
    this.setState({
      editRecord: { ...this.state.editRecord, visible: false },
    });
  };

  render() {
    const record = this.state.editRecord;
    const renderId = (r: TransferItem) => {
      return r.id;
    };

    const renderName = (r: TransferItem) => {
      return r.name;
    };
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            <Input
              value={this.state.search}
              style={{ width: 160, marginRight: 8, verticalAlign: 'bottom' }}
              placeholder="请输入用户名或姓名"
              onChange={this.handleSearchInputChange}
              onKeyPress={this.handleKey}
            />
            <Button onClick={this.doSearch}>
              <Icon type="search" /> 搜索
            </Button>
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            columns={this.getColumns()}
            rowKey="id"
            func="getAdminList"
            index="admin_list"
            filter={this.getFilter()}
            pagination={true}
          />
          <Modal
            key={record.key}
            visible={record.visible}
            onOk={this.handleSubmit}
            width={820}
            title="编辑权限"
            onCancel={this.cancelEdit}
            confirmLoading={this.state.loading}
          >
            <Row style={{ textAlign: 'center' }}>
              <Transfer
                rowKey={renderId}
                showSearch={true}
                render={renderName}
                dataSource={record.roleList as unknown as TransferItem[]}
                titles={['待选择权限', '已选择权限']}
                targetKeys={record.userKeys as unknown as string[]}
                selectedKeys={record.selected as unknown as string[]}
                onChange={this.handleChange}
                onSelectChange={this.handleSelectChange}
                listStyle={{ textAlign: 'left', width: 350, height: 500 }}
              />
            </Row>
          </Modal>
        </div>
      </>
    );
  }
}

export default withRouter(connect<TSysAdminRecord, TSysAdminData>()(AdminList));
