import React, { useState, useEffect } from "react";
import { useSelector, useDispatch, useStore } from 'react-redux';
import { setConfig } from '@action/config';
import { getCrumb } from '@utils/utils';
import { Button, Col, Row, Menu, Dropdown, Icon, Modal, message } from 'antd';
import { Table, OrderColumn } from '@components/common';
import { getTableList } from '@app/action/tableList';
import AddReadPreferenceModal from "@app/components/business/AddReadPreferenceModal";
import { sysApi as api } from '@app/api';

export default function ReadPreferenceManager(props: any) {
  const dispatch = useDispatch();
  const { permissions } = useStore().getState().session;
  const { total, current, size, records = [] } = useSelector((state: any) => state.tableList)
  const [addModalVisible, setAddModalVisible] = useState(false)
  const [editRecord, setEditRecord] = useState(null)

  const toggleStatusRequest = (record: any) => {
    dispatch(setConfig({ loading: true }))
    api.updateHobbyStatus({ id: record.id, status: record.status === 1 ? 0 : 1 })
      .then(() => {
        message.success('操作成功')
        dispatch(setConfig({ loading: false }))
        getData()
      }).catch(() => {
        dispatch(setConfig({ loading: false }))
      })
  }

  const deleteHobbyRequest = (record: any) => {
    dispatch(setConfig({ loading: true }))
    api.deleteHobby({ id: record.id })
      .then(() => {
        message.success('操作成功')
        dispatch(setConfig({ loading: false }))
        getData()
      }).catch(() => {
        dispatch(setConfig({ loading: false }))
      })
  }

  const toggleStatus = (record: any) => {
    if (record.status === 1) {
      // 下架
      Modal.confirm({
        title: <p>确定下架该标签？</p>,
        onOk: () => {
          toggleStatusRequest(record)
        },
      });
    } else {
      // 上架
      toggleStatusRequest(record)
    }
  }

  const deletePreference = (record: any) => {
    Modal.confirm({
      title: <p>确定删除该标签？</p>,
      onOk: () => {
        deleteHobbyRequest(record)
      },
    });
  }

  const handleCreateReadPreference = () => {
    setEditRecord(null)
    setAddModalVisible(true)
  }

  const editPreference = (record: any) => {
    setEditRecord(record)
    setAddModalVisible(true)
  }

  const exchangeOrder = (record: any, sort_flag: number) => {
    dispatch(setConfig({ loading: true }));
    api.updateHobbySort({ id: record.id, sort_flag })
      .then(() => {
        message.success('操作成功');
        getData();
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  }

  const canCreateHobby = permissions.indexOf('hobby:create') > 0;
  const canUpdateHobby = permissions.indexOf('hobby:update') > 0;
  const canUpdateStatus = permissions.indexOf('hobby:update_status') > 0;
  const canDeleteHobby = permissions.indexOf('hobby:delete') > 0;

  const getDropDown = (record: any) => {
    const menu = (
      <Menu>
        <Menu.Item onClick={() => editPreference(record)} disabled={!canUpdateHobby}>编辑</Menu.Item>
        <Menu.Item onClick={() => toggleStatus(record)} disabled={!canUpdateStatus}>{record.status === 1 ? '下架' : '上架'}</Menu.Item>
        <Menu.Item onClick={() => deletePreference(record)} disabled={!canDeleteHobby}>删除</Menu.Item>
      </Menu >);
    return (
      <Dropdown overlay={menu}>
        <a className="ant-dropdown-link">
          操作 <Icon type="down" />
        </a>
      </Dropdown>
    );
  };
  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const columns: any = [
    {
      title: '排序',
      key: 'order',
      render: (text: any, record: any, i: number) => {
        const pos = getSeq(i)
        return <OrderColumn
          pos={pos}
          start={1}
          end={total}
          perm="hobby:update_sort"
          disableUp={record.status === 0 || pos === 0}
          // i === records.length - 1 
          disableDown={record.status === 0 || (i < records.length - 1 && records[i + 1].status === 0)}
          onUp={() => exchangeOrder(record, 0)}
          onDown={() => exchangeOrder(record, 1)}
        />

      },
      width: 70,
    },
    {
      title: '标签ID',
      dataIndex: 'id',
    },
    {
      title: '关联标签ID',
      dataIndex: 'ref_id',
    },
    {
      title: '一级标签名',
      dataIndex: 'content',
    },
    {
      title: '展示状态',
      dataIndex: 'status',
      render(status: number) {
        return status === 1 ? '展示中' : '已下架'
      }
    },
    {
      title: '添加人',
      dataIndex: 'created_by',
    },
    {
      title: '添加时间',
      dataIndex: 'created_at',
    },
    {
      title: '操作',
      key: 'op',
      align: 'center',
      render: (text: any, record: any) => getDropDown(record),
    }
  ]

  const getData = (goToFirstPage = false) => {
    let cur = goToFirstPage ? 1 : current
    dispatch(getTableList('getHobbyList', 'records', { current: cur, size }));
  }

  const submitEnd = (goToFirstPage: boolean) => {
    getData(goToFirstPage)
    setAddModalVisible(false)
  }

  useEffect(() => {
    const { selectKeys, openKeys } = props
    dispatch(
      setConfig({ selectKeys, openKeys })
    );
    getData()
  }, [])

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button onClick={handleCreateReadPreference} disabled={!canCreateHobby}>添加一级标签</Button>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          func="getHobbyList"
          index="records"
          pagination={true}
          rowKey="id"
          columns={columns}
        />
        <AddReadPreferenceModal visible={addModalVisible} record={editRecord} onCancel={() => setAddModalVisible(false)} onEnd={submitEnd} />
      </div>
    </>
  )
}