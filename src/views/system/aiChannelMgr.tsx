import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  useImperativeHandle,
  forwardRef,
} from 'react';
import { useStore, useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, OrderColumn, PreviewMCN } from '@components/common';
import { CommonObject } from '@app/types';
import {
  getCrumb,
  objectToQueryString,
  searchToObject,
  setMenuHook,
  UserDetail,
} from '@app/utils/utils';
import {
  Row,
  Col,
  Divider,
  Icon,
  Modal,
  message,
  InputNumber,
  Tooltip,
  Popconfirm,
  Button,
  Select,
  Input,
  Tabs,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { sysApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@components/permItems';
import Radio from 'antd/es/radio';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import ImagePreviewColumn from '@app/components/common/imagePreviewColumn';
import EditAiChannelModal from './editAiChannelModal';
import AiChannelRelatedVideoModal from './aiChannelRelatedVideoModal';
import EditAiCreateModal from './editAiCreateModal';

export default function AiChannelMgr(props: any) {
  const dispatch = useDispatch();
  useEffect(() => {
    setMenuHook(dispatch, props);
  }, []);

  const store = useStore();
  const { run } = useXHR();
  const [filter, setFilter] = useState<any>({});
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const getSeq = (i: number) => (current - 1) * size + i + 1;
  const [relatedVideoModal, setRelatedVideoModal] = useState<any>({
    visible: false,
    record: null,
    key: 'relatedVideoModal',
  });

  const [editModal, setEditModal] = useState<any>({
    visible: false,
    record: null,
    key: 'edit',
  });

  const [editAiCreateModal, setEditAiCreateModal] = useState<any>({
    visible: false,
    record: null,
    key: 'editAiCreateModal',
  });

  const f = useMemo(() => {
    const x: CommonObject = { ...filter };
    return x;
  }, [filter]);

  const getList = (goFirstPage: boolean = false, overlap: CommonObject = {}) => {
    const { current, size = 10 } = store.getState().tableList;
    dispatch(
      getTableList('getAIChannelList', 'list', {
        ...f,
        current: goFirstPage ? 1 : current,
        size,
        ...overlap,
      })
    );
  };
  const listSort = (record: any, sort_flag: number, position: number = 0) => {
    let data: any = {
      id: record.id,
      sort_flag,
    };
    if (sort_flag == 2) {
      data.position = position;
    }
    sysApi.updateAiChannelOrder(data).then((res: any) => {
      message.success('操作成功');
      getList();
    });
  };

  useEffect(() => {
    getList(true);
  }, [f]);

  const updateRecordStatus = (record: any) => {
    run(sysApi.updateAiChannelStatus, { id: record.id, online: !record.status }, true).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  const columns = [
    {
      title: '排序',
      key: 'sort',
      render: (text: any, record: any, i: number) => (
        <OrderColumn
          perm="channel_ai:order"
          start={1}
          pos={getSeq(i)}
          end={records.filter((v: any) => v.status).length}
          disable={!record.status}
          onUp={() => listSort(record, 0)}
          onDown={() => listSort(record, 1)}
        />
      ),
      width: 70,
    },
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      width: 70,
    },
    {
      title: '模块标题',
      dataIndex: 'title',
      width: 150,
    },
    {
      title: '模块图片',
      key: 'pic',
      dataIndex: 'pic',
      width: 150,
      align: 'center',
      render: (text: any, record: any) => (
        <div style={{ height: 60, textAlign: 'center' }}>
          <ImagePreviewColumn text={text} imgs={[text]}></ImagePreviewColumn>
        </div>
      ),
    },
    {
      title: '模块描述',
      dataIndex: 'sub_title',
    },
    {
      title: '操作人',
      dataIndex: 'updated_by',
      width: 130,
    },
    {
      title: '操作时间',
      dataIndex: 'updated_at',
      width: 160,
      render: (text: any, record: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any, i: number) => (
        <span>
          <PermA
            perm="channel_ai:online"
            onClick={() => {
              updateRecordStatus(record);
            }}
          >
            {record.status ? '下架' : '上架'}
          </PermA>
          {record.type != 9 && (
            <>
              <Divider type="vertical" />
              <PermA
                perm="channel_ai:edit"
                onClick={() => setEditModal({ visible: true, record, key: Date.now() })}
              >
                编辑
              </PermA>
            </>
          )}
          <Divider type="vertical" />
          {(record.type == 6 || record.type == 8) && (
            <PermA
              perm="channel_ai:edit"
              onClick={() => {
                if (record.type == 6) {
                  setEditAiCreateModal({
                    visible: true,
                    record,
                    key: Date.now(),
                  });
                } else {
                  setRelatedVideoModal({
                    visible: true,
                    record,
                    key: Date.now(),
                  });
                }
              }}
            >
              管理
            </PermA>
          )}
        </span>
      ),
      width: 150,
    },
  ];

  return (
    <>
      <div className="component-content">
        <Table
          func={'getAIChannelList'}
          index="list"
          filter={f}
          columns={columns}
          rowKey="id"
          pagination={false}
        />
        <EditAiChannelModal
          {...editModal}
          onCancel={() => setEditModal({ visible: false, key: '' })}
          onOk={() => {
            setEditModal({ visible: false, key: '' });
            getList();
          }}
        ></EditAiChannelModal>
        <AiChannelRelatedVideoModal
          {...relatedVideoModal}
          onCancel={() => setRelatedVideoModal({ visible: false, key: '' })}
          onOk={() => {
            setRelatedVideoModal({ visible: false, key: '' });
            getList();
          }}
        ></AiChannelRelatedVideoModal>
        <EditAiCreateModal
          {...editAiCreateModal}
          onCancel={() => setEditAiCreateModal({ visible: false, key: '' })}
          onOk={() => {
            setEditAiCreateModal({ visible: false, key: '' });
            getList();
          }}
        ></EditAiCreateModal>
      </div>
    </>
  );
}
