import { setConfig } from '@action/config';
import { getTableList } from '@app/action/tableList';
import { sysApi as api } from '@app/api';
import { A, Table } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm } from '@utils/utils.tsx';
import { Button, Checkbox, Col, Icon, Input, message, Modal, Row } from 'antd';
import cloneDeep from 'lodash/cloneDeep';
import React from 'react';
import LazyLoad from 'react-lazy-load';
import { withRouter } from 'react-router';

import './styles/permissionGroupUsers.scss';

@(withRouter as any)
@connect
class PermissionGroupUsers extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      loading: false,
      keyword: '',
      search: '',
      listSelectedKeys: [],
      id: props.match.params.id,
      // ----- transfer -----
      adminList: {
        list: [],
        total: 0,
        pages: 1,
        current: 1,
      },
      adminFilter: {
        keyword: '',
        search: '',
      },
      selectedList: {},
      selectedIdKeys: [],
      mKey: Date.now(),
      sModal: false,
      lazyloadKey: Date.now(),
      row1Selected: {
        keys: [],
        lists: [],
      },
      row2Selected: {
        keys: [],
      },
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10, keyword: '', role_id: this.state.id });
    this.props.dispatch(setConfig({ openKeys: this.props.openKeys }));
    this.props.dispatch(setConfig({ selectKeys: this.props.selectKeys }));
  }

  componentDidUpdate(prevProps: any) {
    if (this.props.tableList.timestamp !== prevProps.tableList.timestamp) {
      this.setState({ listSelectedKeys: [] });
    }
  }

  getData = (filter = this.getFilter()) => {
    this.props.dispatch(getTableList('getPermissionGroupUserList', 'admin_list', filter));
  }

  getFilter = (keyword = this.state.keyword) => {
    const { current, size } = this.props.tableList.allData.admin_list || { current: 1, size: 10 };
    return { current, size, keyword, role_id: this.state.id };
  }

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 80,
      },
      {
        title: '用户名',
        key: 'user_name',
        dataIndex: 'user_name',
      },
      {
        title: '姓名',
        key: 'name',
        dataIndex: 'name',
      },
      {
        title: '状态',
        key: 'state',
        dataIndex: 'state',
        render: (text: number) => <span>{['正常', '停用'][text]}</span>,
        width: 80,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) =>
          requirePerm(this, 'role:remove_user')(
            <A onClick={() => this.removeUser(record)}>移除</A>
          ),
        width: 80,
      },
    ];
  }

  handleListSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    this.setState({ listSelectedKeys: selectedRowKeys });
  }

  removeUser = (record: any) => {
    if (!record && this.state.listSelectedKeys.length === 0) {
      message.error('请选择需要移除的用户');
      return;
    }
    Modal.confirm({
      title: !record ? <p>确认移除所选用户吗？</p> : <p>确认移除用户“{record.name}”吗？</p>,
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        api
          .removeGroupUser({
            user_id: !record ? this.state.listSelectedKeys.join(',') : record.id,
            role_id: this.state.id,
            del_flag: !record ? '1' : '0',
          })
          .then(() => {
            message.success('移除成功');
            this.getData();
            this.setState({
              listSelectedKeys: [],
            });
            this.props.dispatch(setConfig({ loading: false }));
          })
          .catch(() => this.props.dispatch(setConfig({ loading: false })));
      },
    });
  }

  handleKey = (e: any) => {
    if (e.which === 13) {
      this.setState({ keyword: this.state.search }, () =>
        this.getData({
          current: 1,
          size: this.props.tableList.size,
          keyword: this.state.search,
          role_id: this.state.id,
        })
      );
    }
  }

  handleUKey = (e: any) => {
    if (e.which === 13) {
      this.searchUser();
    }
  }

  searchUser = () => {
    this.setState(
      {
        adminFilter: { ...this.state.adminFilter, keyword: this.state.adminFilter.search },
        row1Selected: { keys: [], lists: [] },
        row2Selected: { keys: [], lists: [] },
      },
      () => this.getAdminList(1)
    );
  }

  nextPage = () => {
    if (this.state.loading || this.state.adminList.current === this.state.adminList.pages) {
      return;
    }
    this.getAdminList(this.state.adminList.current + 1);
  }

  addUser = () => {
    this.setState(
      {
        selectedList: {},
        selectedIdKeys: [],
        mKey: Date.now(),
        row1Selected: {
          keys: [],
          lists: [],
        },
        row2Selected: {
          keys: [],
          lists: [],
        },
        adminFilter: {
          searchType: 0,
          type: 0,
          inputKeyword: '',
          keyword: '',
        },
        sModal: true,
        adminList: {
          list: [],
          total: 0,
          pages: 1,
          current: 1,
        },
      },
      () => {
        this.getAdminList(1);
      }
    );
  }

  getAdminList = (current = this.state.adminList.current) => {
    this.setState({ loading: true });
    api
      .getAdminList({ current, size: 50, keyword: this.state.adminFilter.keyword })
      .then((r: any) => {
        this.setState(
          {
            adminList: {
              total: r.data.admin_list.total,
              pages: r.data.admin_list.pages,
              current: r.data.admin_list.current,
              list:
                r.data.admin_list.current === 1
                  ? r.data.admin_list.records
                  : this.state.adminList.list.concat(r.data.admin_list.records),
            },
            loading: false,
          },
          () =>
            this.setState({
              lazyloadKey: Date.now(),
            })
        );
      })
      .catch(() => this.setState({ loading: false }));
  }

  getRow1DisplayList = () => {
    const lists: any[] = [];
    this.state.adminList.list.map((v: any) => {
      if (this.state.selectedIdKeys.indexOf(v.id) === -1) {
        lists.push(v);
      }
    });
    return {
      list: lists,
      length: lists.length,
    };
  }

  selectRow1All = () => {
    const keys: any = [];
    const lists: any = [];
    if (this.state.adminList.list.length > this.state.row1Selected.keys.length) {
      this.state.adminList.list.map((v: any) => {
        if (this.state.selectedIdKeys.indexOf(v.id) === -1) {
          lists.push(v);
          keys.push(v.id);
        }
      });
      this.setState({
        row1Selected: { keys, lists },
      });
      return;
    }
    this.setState({
      row1Selected: { keys, lists },
    });
  }

  selectRow2All = () => {
    let keys = [];
    if (this.state.selectedIdKeys.length > this.state.row2Selected.keys.length) {
      keys = this.state.selectedIdKeys;
    }
    this.setState({
      row2Selected: { keys },
    });
  }

  checkRow1Item = (item: any) => {
    const row1 = cloneDeep(this.state.row1Selected);
    if (row1.keys.indexOf(item.id) > -1) {
      row1.keys.splice(row1.keys.indexOf(item.id), 1);
      let index = -1;
      row1.lists.map((v: any, i: number) => {
        if (v.id === item.id) {
          index = i;
        }
      });
      row1.lists.splice(index, 1);
    } else {
      row1.keys.push(item.id);
      row1.lists.push(item);
    }
    this.setState({
      row1Selected: row1,
    });
  }

  checkRow2Item = (item: any) => {
    const row2 = cloneDeep(this.state.row2Selected);
    if (row2.keys.indexOf(item) > -1) {
      row2.keys.splice(row2.keys.indexOf(item), 1);
    } else {
      row2.keys.push(item);
    }
    this.setState({ row2Selected: row2 });
  }

  toRight = () => {
    const right = cloneDeep(this.state.selectedList);
    this.state.row1Selected.lists.map((v: any) => {
      right[v.id] = v;
    });
    this.setState({
      selectedList: right,
      selectedIdKeys: Object.keys(right),
      row1Selected: { keys: [], lists: [] },
    });
  }

  toLeft = () => {
    const right = cloneDeep(this.state.selectedList);
    this.state.row2Selected.keys.map((v: any) => {
      delete right[v];
    });
    this.setState({
      selectedList: right,
      selectedIdKeys: Object.keys(right),
      row2Selected: { keys: [], lists: [] },
    });
  }

  handleSubmit = () => {
    if (this.state.selectedIdKeys.length === 0) {
      message.error('请选择用户');
      return;
    }
    this.setState({
      loading: true,
    });
    api
      .addGroupUser({
        role_id: this.state.id,
        admin_ids: this.state.selectedIdKeys.join(','),
      })
      .then((r: any) => {
        message.success('添加成功');
        this.setState({ loading: false, sModal: false });
        this.getData();
      })
      .catch(() => {
        this.setState({ loading: false });
      });
  }

  render() {
    const left = this.getRow1DisplayList();
    const bread = this.props.breadCrumb.concat([decodeURIComponent(this.props.match.params.name)]);
    return (
      <React.Fragment>
        <Row className="layout-infobar">
          <Col span={12}>
            <Button onClick={() => this.props.history.push('/view/permissionGroup')}>
              <Icon type="left-circle" /> 返回权限组列表
            </Button>
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(bread)}
          </Col>
        </Row>
        <div className="component-content permission-group-users" ref="mountpoint">
          <Row style={{ marginBottom: 16 }}>
            <Col span={12}>
              {requirePerm(this, 'role:batch_remove_user')(
                <Button onClick={() => this.removeUser(false)} style={{ marginRight: 8 }}>
                  <Icon type="minus-circle" /> 批量移除
                </Button>
              )}
              {requirePerm(this, 'role:add_user')(
                <Button onClick={this.addUser} style={{ marginRight: 8 }}>
                  <Icon type="plus-circle" /> 添加管理员
                </Button>
              )}
            </Col>
            <Col span={12} className="layout-breadcrumb">
              <Input
                style={{ width: 160, marginRight: 8 }}
                value={this.state.search}
                onKeyPress={this.handleKey}
                placeholder="请输入姓名搜索"
                onChange={(e: any) => this.setState({ search: e.target.value })}
              />
              <Button onClick={() => this.handleKey({ which: 13 })}>
                {' '}
                <Icon type="search" /> 搜索
              </Button>
            </Col>
          </Row>
          <Table
            multi={true}
            columns={this.getColumns()}
            filter={this.getFilter()}
            pagination={true}
            func="getPermissionGroupUserList"
            index="admin_list"
            rowKey="id"
            selectedRowKeys={this.state.listSelectedKeys}
            onSelectChange={this.handleListSelectChange}
          />

          <Modal
            key={this.state.mKey}
            visible={this.state.sModal}
            onOk={this.handleSubmit}
            onCancel={() => this.setState({ sModal: false })}
            title="添加管理员"
            getContainer={() => this.refs.mountpoint as any}
            width="820px"
            style={{ top: 50 }}
          >
            <Row style={{ marginBottom: 16 }}>
              <Input
                onKeyPress={this.handleUKey}
                placeholder="输入用户名或姓名"
                style={{ width: 160, marginRight: 8 }}
                value={this.state.adminFilter.search}
                onChange={(e: any) =>
                  this.setState({
                    adminFilter: { ...this.state.adminFilter, search: e.target.value },
                  })
                }
              />
              <Button onClick={() => this.searchUser()}>
                <Icon type="search" />
                搜索
              </Button>
            </Row>
            <Row style={{ textAlign: 'center', height: 500 }}>
              <div className="rox-transfer-box">
                <div className="title-part">
                  <Checkbox
                    indeterminate={
                      left.length > this.state.row1Selected.keys.length &&
                      this.state.row1Selected.keys.length > 0
                    }
                    checked={
                      left.length === this.state.row1Selected.keys.length &&
                      this.state.row1Selected.keys.length > 0
                    }
                    onChange={() => this.selectRow1All()}
                  >
                    已加载{this.state.adminList.list.length}条记录，已选择
                    {this.state.row1Selected.keys.length}
                  </Checkbox>
                </div>
                <div className="content">
                  {left.list.map((v, i) => (
                    <div className="line" key={i}>
                      <Checkbox
                        checked={this.state.row1Selected.keys.indexOf(v.id) > -1}
                        onChange={() => this.checkRow1Item(v)}
                      >
                        {v.name}({v.user_name})
                      </Checkbox>
                    </div>
                  ))}
                  {this.state.adminList.pages > this.state.adminList.current &&
                    this.state.adminList.list.length > 0 && (
                      <LazyLoad
                        key={this.state.lazyloadKey}
                        offsetVertical={0}
                        height={0}
                        onContentVisible={() => this.nextPage()}
                      >
                        <div style={{ height: 0 }} />
                      </LazyLoad>
                    )}
                  {this.state.adminList.pages > this.state.adminList.current &&
                    this.state.adminList.list.length > 0 && (
                      <div className="loading">正在加载...</div>
                    )}
                </div>
              </div>
              <div className="rox-transfer-operation">
                <Button
                  size="small"
                  style={{ marginBottom: 16 }}
                  type="primary"
                  disabled={this.state.row2Selected.keys.length === 0}
                  onClick={() => this.toLeft()}
                >
                  <Icon type="left" />
                </Button>
                <Button
                  size="small"
                  type="primary"
                  disabled={this.state.row1Selected.keys.length === 0}
                  onClick={() => this.toRight()}
                >
                  <Icon type="right" />
                </Button>
              </div>
              <div className="rox-transfer-box">
                <div className="title-part">
                  <Checkbox
                    indeterminate={
                      this.state.selectedIdKeys.length > this.state.row2Selected.keys.length &&
                      this.state.row2Selected.keys.length > 0
                    }
                    checked={
                      this.state.selectedIdKeys.length === this.state.row2Selected.keys.length &&
                      this.state.row2Selected.keys.length > 0
                    }
                    onChange={() => this.selectRow2All()}
                  >
                    已添加{this.state.selectedIdKeys.length}，已选择
                    {this.state.row2Selected.keys.length}
                  </Checkbox>
                </div>
                <div className="content">
                  {this.state.selectedIdKeys.map((v: any, i: number) => (
                    <div className="line" key={i}>
                      <Checkbox
                        checked={this.state.row2Selected.keys.indexOf(v) > -1}
                        onChange={() => this.checkRow2Item(v)}
                      >
                        {this.state.selectedList[v].name}({this.state.selectedList[v].user_name})
                      </Checkbox>
                    </div>
                  ))}
                </div>
              </div>
            </Row>
          </Modal>
        </div>
      </React.Fragment>
    );
  }
}

export default PermissionGroupUsers;
