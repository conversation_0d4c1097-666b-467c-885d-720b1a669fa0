import { setConfig } from '@action/config';
import { getTableList } from '@app/action/tableList';
import { sysApi as api } from '@app/api';
import { A, Table } from '@components/common';
import connect from '@utils/connectAll';
import { getCrumb, requirePerm } from '@utils/utils.tsx';
import {
  Button,
  Col,
  Dropdown,
  Icon,
  Input,
  Form,
  Menu,
  message,
  Modal,
  Row,
  Select,
  DatePicker,
  Tooltip,
} from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import { CommonObject } from '@app/types';

@(withRouter as any)
@connect
class AppCrashManager extends React.Component<any, any> {
  formRef: any;

  constructor(props: any) {
    super(props);
    this.state = {
      filters: {
        search_type: 0,
        keyword: '',
        device_platform: '',
        app_version: '',
        status: '',
        begin: false,
        end: false,
      },
      versionList: [],
      cType: 0,
      cKeyword: '',
      detail: {
        visible: false,
        key: Date.now(),
      },
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    this.getVersionList();
    this.props.dispatch(
      setConfig({
        selectKeys: this.props.selectKeys,
        openKeys: this.props.openKeys,
      })
    );
  }

  getVersionList = () => {
    api.getSimpleVersionList().then((res: any) => {
      this.setState({
        versionList: res.data.app_version_list,
      });
    });
  };

  getData = (overlap: CommonObject = {}, filter = this.getFilter()) => {
    this.props.dispatch(getTableList('getAppCrashList', 'list', { ...filter, ...overlap }));
  };

  getFilter = () => {
    const { current, size } = this.props.tableList;
    const { filters } = this.state;
    const body: CommonObject = { current, size, search_type: filters.search_type };
    Object.keys(filters).map((v: any) => {
      if (filters[v]) {
        body[v] = filters[v];
      }
    });
    return body;
  };

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    const getDropdown = (record: any) => {
      const menu = (
        <Menu>
          {requirePerm(
            this,
            'client_error_log:handle'
          )(
            <Menu.Item disabled={record.status === 1} onClick={() => this.processRecord(record)}>
              {record.status === 0 ? '标记已处理' : '已处理'}
            </Menu.Item>
          )}
          {requirePerm(
            this,
            'client_error_log:delete'
          )(<Menu.Item onClick={() => this.deleteRecord(record)}>删除</Menu.Item>)}
        </Menu>
      );

      return (
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };

    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '系统类型',
        key: 'device_platform',
        dataIndex: 'device_platform',
        width: 80,
      },
      {
        title: '版本名称',
        key: 'app_version',
        dataIndex: 'app_version',
        width: 80,
      },
      {
        title: '用户昵称',
        key: 'nick_name',
        dataIndex: 'nick_name',
      },
      {
        title: '小潮号',
        key: 'chao_id',
        dataIndex: 'chao_id',
        width: 120
      },
      {
        title: '手机号',
        key: 'phone_number',
        dataIndex: 'phone_number',
        width: 110,
      },
      {
        title: '上传时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm')}</span>,
        width: 150,
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'status',
        render: (text: any, record: any) =>
          this.props.session.permissions.indexOf('client_error_log:detail') > -1 ? (
            <A
              onClick={this.showDetail.bind(this, record)}
              style={
                text === 1
                  ? { color: '#353535', textDecoration: 'underline' }
                  : { textDecoration: 'underline' }
              }
            >
              {['待处理', '已处理'][text]}
            </A>
          ) : (
            <span>{['待处理', '已处理'][text]}</span>
          ),
        width: 70,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => getDropdown(record),
        width: 70,
      },
    ];
  };

  processRecord = (record: any) => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .processAppCrash({ id: record.id })
      .then((r: any) => {
        message.success('操作成功');
        this.getData();
        this.setState({
          detail: {
            ...this.state.detail,
            status: 1,
          },
        });
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  showDetail = (record: any) => {
    this.setState({
      detail: {
        visible: true,
        key: Date.now(),
        ...record,
      },
    });
  };

  deleteRecord = (record: any) => {
    Modal.confirm({
      title: '确定删除该日志？',
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        api
          .deleteAppCrash({ id: record.id })
          .then(() => {
            message.success('操作成功');
            this.props.dispatch(setConfig({ loading: false }));
            this.getData();
            this.closeDetail();
          })
          .catch(() => this.props.dispatch(setConfig({ loading: false })));
      },
    });
  };

  closeDetail = () => {
    this.setState({
      detail: {
        key: this.state.detail.key,
        visible: false,
      },
    });
  };

  filterChange = (key: string, value: any) => {
    let v = value;
    if (typeof value === 'object') {
      v = value.target.value;
    }
    this.setState(
      {
        filters: {
          ...this.state.filters,
          [key]: v,
        },
      },
      () => this.getData({ current: 1 })
    );
  };

  handleRangePickerChange = (dates: any) => {
    if (dates.length === 0) {
      this.setState(
        {
          filters: { ...this.state.filters, begin: false, end: false },
        },
        () => this.getData({ current: 1 })
      );
    } else {
      this.setState(
        {
          filters: {
            ...this.state.filters,
            begin: dates[0].format('YYYY-MM-DD'),
            end: dates[1].format('YYYY-MM-DD'),
          },
        },
        () => this.getData({ current: 1 })
      );
    }
  };

  handleKey = (e: any) => {
    if (e.which === 13) {
      this.doSearch();
    }
  };

  doSearch = () => {
    this.setState(
      {
        filters: {
          ...this.state.filters,
          search_type: this.state.cType,
          keyword: this.state.cKeyword,
        },
      },
      () => this.getData({ current: 1 })
    );
  };

  searchChange = (key: string, value: any) => {
    let v = value;
    if (typeof value === 'object') {
      v = value.target.value;
    }
    this.setState({
      [key]: v,
    });
  };

  render() {
    const { detail, filters } = this.state;
    const formLayout = {
      labelCol: { span: 5 },
      wrapperCol: { span: 18 },
    };
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12} />
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 8 }}>
            <Col span={15}>
              <Select
                style={{ width: 100, marginRight: 8 }}
                value={filters.device_platform}
                onChange={this.filterChange.bind(this, 'device_platform')}
              >
                <Select.Option value="">系统类型</Select.Option>
                <Select.Option value="Android">Android</Select.Option>
                <Select.Option value="IOS">iOS</Select.Option>
              </Select>
              <Select
                style={{ width: 100, marginRight: 8 }}
                value={filters.app_version}
                onChange={this.filterChange.bind(this, 'app_version')}
              >
                <Select.Option value="">版本号</Select.Option>
                {this.state.versionList.map((v: any) => (
                  <Select.Option key={v} value={v}>
                    {v}
                  </Select.Option>
                ))}
              </Select>
              <Select
                style={{ width: 100, marginRight: 8 }}
                value={filters.status}
                onChange={this.filterChange.bind(this, 'status')}
              >
                <Select.Option value="">状态</Select.Option>
                <Select.Option value="0">待处理</Select.Option>
                <Select.Option value="1">已处理</Select.Option>
              </Select>
              <DatePicker.RangePicker
                style={{ width: 240, marginRight: 8 }}
                format="YYYY-MM-DD"
                onChange={this.handleRangePickerChange}
                value={filters.begin ? [moment(filters.begin), moment(filters.end)] : []}
              />
              <Tooltip title="查看用户在客户端上传的出错日志">
                <Icon type="question-circle" />
              </Tooltip>
            </Col>
            <Col span={9} style={{ textAlign: 'right' }}>
              <Select
                style={{ width: 140, marginRight: 8 }}
                value={this.state.cType}
                onChange={this.searchChange.bind(this, 'cType')}
              >
                <Select.Option value={0}>搜索用户昵称</Select.Option>
                <Select.Option value={1}>搜索手机号</Select.Option>
                <Select.Option value={2}>搜索小潮号</Select.Option>
              </Select>
              <Input
                style={{ width: 120, marginRight: 8 }}
                value={this.state.cKeyword}
                onChange={this.searchChange.bind(this, 'cKeyword')}
                placeholder="输入搜索内容"
                onKeyPress={this.handleKey}
              />
              <Button style={{ verticalAlign: 'top' }} onClick={this.doSearch}>
                搜索
              </Button>
            </Col>
          </Row>
          <Table
            func="getAppCrashList"
            index="list"
            filter={this.getFilter()}
            columns={this.getColumns()}
            rowKey="id"
            pagination={true}
          />
          <Modal
            title="出错日志明细"
            width={600}
            key={detail.key}
            visible={detail.visible}
            onCancel={this.closeDetail}
            footer={[
              requirePerm(
                this,
                'client_error_log:handle'
              )(
                <Button
                  loading={this.props.config.loading}
                  key="process"
                  type="primary"
                  disabled={detail.status === 1}
                  onClick={this.processRecord.bind(this, detail)}
                >
                  {detail.status === 1 ? '已处理' : '标记已处理'}
                </Button>
              ),
              requirePerm(
                this,
                'client_error_log:delete'
              )(
                <Button
                  key="delete"
                  loading={this.props.config.loading}
                  onClick={this.deleteRecord.bind(this, detail)}
                >
                  删除
                </Button>
              ),
              <Button key="cancel" onClick={this.closeDetail}>
                关闭
              </Button>,
            ]}
          >
            <Form {...formLayout}>
              <Form.Item label="日志上传时间">
                {moment(detail.created_at).format('YYYY-MM-DD')}
              </Form.Item>
              <Form.Item label="用户昵称">{detail.nick_name}</Form.Item>
              <Form.Item label="用户手机号">{detail.phone_number}</Form.Item>
              <Form.Item label="设备ID">{detail.device_id}</Form.Item>
              <Form.Item label="推送ClientID">{detail.gt_client_id}</Form.Item>
              <Form.Item label="用户ID">{detail.account_id}</Form.Item>
              <Form.Item label="手机型号">{detail.device_model}</Form.Item>
              <Form.Item label="手机系统">{detail.device_platform}</Form.Item>
              <Form.Item label="版本号">{detail.app_version}</Form.Item>
              <Form.Item label="Crash信息">
                <a href={detail.crash_url}>{detail.crash_url}</a>
              </Form.Item>
            </Form>
          </Modal>
        </div>
      </>
    );
  }
}

export default AppCrashManager;
