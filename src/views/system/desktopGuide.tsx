import { setConfig } from '@action/config';
import { userApi as api } from '@app/api';
import { CardEditor } from '@components/common';
import connect from '@utils/connectSession';
import { getCrumb, requirePerm } from '@utils/utils';
import { Button, Col, Icon, message, Row } from 'antd';
import React from 'react';
import { withRouter } from 'react-router';

@(withRouter as any)
@connect
class DesktopGuide extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      editing: false,
    };
  }

  componentDidMount() {
    this.getData();
    this.props.dispatch(
      setConfig({
        selectKeys: this.props.selectKeys,
        openKeys: this.props.openKeys,
      })
    );
  }

  getData = () => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .getHContent(11)
      .then((r: any) => {
        this.props.dispatch(setConfig({ loading: false }));
        this.setState({ content: r.data.content, editing: false });
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  submitData = () => {
    const content = (this.refs.ce as any).getData();
    console.log(content);
    if (content === '') {
      message.error('请填写用户协议');
      return;
    }
    this.props.dispatch(setConfig({ loading: true }));
    api
      .updateHContent({ content, type: 11 })
      .then(() => {
        message.success('操作成功');
        this.getData();
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  render() {
    return (
      <React.Fragment>
        <Row className="layout-infobar">
          <Col span={12}>
            <Button onClick={this.props.history.goBack} style={{ marginRight: 8 }}>
              返回
            </Button>
            {this.state.editing ? (
              <React.Fragment>
                <Button key="x" onClick={this.submitData} type="primary" style={{ marginRight: 8 }}>
                  保存
                </Button>
                <Button key="y" onClick={() => this.setState({ editing: false })}>
                  取消
                </Button>
              </React.Fragment>
            ) : (
              requirePerm(
                this,
                'h5Content:update:1'
              )(
                <Button key="z" onClick={() => this.setState({ editing: true })}>
                  <Icon type="edit" /> 编辑
                </Button>
              )
            )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <CardEditor
            title="如何添加到桌面"
            value={this.state.content}
            editing={this.state.editing}
            ref="ce"
          />
        </div>
      </React.Fragment>
    );
  }
}

export default DesktopGuide;
