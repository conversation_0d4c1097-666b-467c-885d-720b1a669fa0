import { setConfig } from '@action/config';
import { getTableList } from '@app/action/tableList';
import { sysApi as api } from '@app/api';
import { A, Table } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm } from '@utils/utils.tsx';
import {
  Button,
  Col,
  Divider,
  Dropdown,
  Form,
  Icon,
  Input,
  InputNumber,
  Menu,
  message,
  Modal,
  Row,
} from 'antd';
import React from 'react';
import { withRouter } from 'react-router';

interface PermissionGroupState {
  loading: boolean;
  keyword: string;
  search: string;
  editRecord: {
    visible: boolean;
    key: string | number;
    id: string | number;
    name: string;
    remark: string;
    seq: number | undefined;
    title: string;
  };
  error: {
    validateStatus: '' | 'error' | 'success' | 'warning' | 'validating' | undefined;
    help: string;
  };
}

@(withRouter as any)
@connect
class PermissionGroupMgr extends React.Component<any, PermissionGroupState> {
  constructor(props: any) {
    super(props);
    this.state = {
      loading: false,
      keyword: '',
      search: '',
      editRecord: {
        visible: false,
        key: Date.now(),
        id: '',
        name: '',
        remark: '',
        seq: undefined,
        title: '',
      },
      error: {
        validateStatus: '',
        help: '',
      },
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10, keyword: '' });
    this.props.dispatch(setConfig({ openKeys: this.props.openKeys }));
    this.props.dispatch(setConfig({ selectKeys: this.props.selectKeys }));
  }

  componentDidUpdate(prevProps: any) {
    if (this.props.location !== prevProps.location) {
      this.setState({ keyword: '', search: '' });
      this.getData({ current: 1, size: 10, keyword: '' });
      this.props.dispatch(setConfig({ selectKeys: this.props.selectKeys }));
    }
  }

  getData = (filter = this.getFilter()) => {
    this.props.dispatch(getTableList('getPermissionGroupList', 'role_list', filter));
  }

  getFilter = () => {
    const { current, size } = this.props.tableList.allData.role_list || { current: 1, size: 10 };
    return { current, size, keyword: this.state.keyword };
  }

  getColumns = () => {
    const { current, size } = this.props.tableList.allData.role_list || { current: 1, size: 10 };
    const getSeq = (i: number) => (current - 1) * size + i + 1;

    const getDropdown = (record: any) => {
      const menu = (
        <Menu>
          {requirePerm(this, 'role:update')(
            <Menu.Item onClick={() => this.editItem(record)}>编辑</Menu.Item>,
          )}
          {requirePerm(this, 'role:list_all')(
            <Menu.Item
              onClick={() => this.props.history.push(`/view/permissionManager?roleId=${record.id}`)}
            >
              配置权限
            </Menu.Item>,
          )}
          {requirePerm(this, 'role:create_by_role')(
            <Menu.Item onClick={() => this.copyItem(record)}>复制</Menu.Item>,
          )}
          {requirePerm(this, 'role:delete')(
            <Menu.Item onClick={() => this.deleteItem(record)}>删除</Menu.Item>,
          )}
        </Menu>
      );

      return (
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };

    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 80,
      },
      {
        title: '权限组名称',
        key: 'name',
        dataIndex: 'name',
        render: (text: any, record: any) =>
          this.props.session.permissions.indexOf('role:view_single') > -1 ? (
            <a
              onClick={() =>
                this.props.history.push(
                  `/view/permissionGroupUsers/${record.id}/${encodeURIComponent(record.name)}`,
                )
              }
            >
              {text}
            </a>
          ) : (
            <span>{text}</span>
          ),
      },
      {
        title: '描述',
        key: 'remark',
        dataIndex: 'remark',
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => getDropdown(record),
        width: 70,
      },
    ];
  }

  editItem = (record: any) => {
    this.setState({
      editRecord: {
        visible: true,
        key: Date.now(),
        id: record.id || '',
        name: record.name || '',
        remark: record.remark || '',
        seq: record.sort_number || undefined,
        title: record.id ? '添加权限组' : '编辑权限组',
      },
    });
  }

  copyItem = (record: any) => {
    Modal.confirm({
      title: <p>确认复制权限组“{record.name}”吗？</p>,
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        api
          .copyPermissionGroup({ role_id: record.id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            this.props.dispatch(setConfig({ loading: false }));
          })
          .catch(() => this.props.dispatch(setConfig({ loading: false })));
      },
    });
  }

  deleteItem = (record: any) => {
    Modal.confirm({
      title: <p>确认删除权限组“{record.name}”吗？</p>,
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        api
          .deletePermissionGroup({ id: record.id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            this.props.dispatch(setConfig({ loading: false }));
          })
          .catch(() => this.props.dispatch(setConfig({ loading: false })));
      },
    });
  }

  handleSubmit = () => {
    const record = this.state.editRecord;
    if (record.name === '') {
      this.setState({ error: { validateStatus: 'error', help: '请输入权限组名称' } });
      message.error('请检查表单项');
      return;
    }
    if (record.name.length > 50) {
      this.setState({ error: { validateStatus: 'error', help: '权限组名称长度不能大于50个字' } });
      message.error('请检查表单项');
      return;
    }
    this.setState({ error: { validateStatus: '', help: '' } });

    this.setState({ loading: true });
    let func = 'createPermissionGroup';
    const body: any = {
      name: record.name,
      sort_number: record.seq || '',
      remark: record.remark,
    };
    if (record.id) {
      func = 'updatePermissionGroup';
      body.id = record.id;
    }
    api[func as keyof typeof api](body)
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setState({ editRecord: { ...record, visible: false }, loading: false });
      })
      .catch(() => this.setState({ loading: false }));
  }

  handleKey = (e: any) => {
    if (e.which === 13) {
      this.setState(
        {
          keyword: this.state.search,
        },
        () =>
          this.getData({ current: 1, size: this.props.tableList.size, keyword: this.state.search }),
      );
    }
  }

  handleValueChange = (key: string, value: any) => {
    this.setState({ editRecord: { ...this.state.editRecord, [key]: value } });
    if (key === 'name') {
      if (value === '') {
        this.setState({ error: { validateStatus: 'error', help: '请输入权限组名称' } });
      } else if (value.length > 50) {
        this.setState({ error: { validateStatus: 'error', help: '权限组名称长度不能大于50个字' } });
      } else {
        this.setState({ error: { validateStatus: '', help: '' } });
      }
    }
  }

  render() {
    const record = this.state.editRecord;
    return (
      <React.Fragment>
        <Row className="layout-infobar">
          <Col span={12}>
            {requirePerm(this, 'role:create')(
              <Button onClick={() => this.editItem({})}>
                <Icon type="plus-circle" /> 添加权限组
              </Button>,
            )}
            <Input
              onKeyPress={this.handleKey}
              placeholder="输入权限组名称"
              value={this.state.search}
              onChange={(e: any) => this.setState({ search: e.target.value })}
              style={{ width: 160, margin: '0 8px', verticalAlign: 'bottom' }}
            />
            <Button onClick={() => this.handleKey({ which: 13 })}>
              <Icon type="search" /> 搜索
            </Button>
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            columns={this.getColumns()}
            filter={this.getFilter()}
            pagination={true}
            func="getPermissionGroupList"
            index="role_list"
            rowKey="id"
          />

          <Modal
            visible={record.visible}
            key={record.key}
            title={record.title}
            confirmLoading={this.state.loading}
            onOk={this.handleSubmit}
            onCancel={() => this.setState({ editRecord: { ...record, visible: false } })}
          >
            <Form labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
              <Form.Item required={true} label="权限组名称" {...this.state.error}>
                <Input
                  value={record.name}
                  placeholder="请输入权限组名称"
                  onChange={(e: any) => this.handleValueChange('name', e.target.value)}
                />
              </Form.Item>
              <Form.Item label="排序">
                <InputNumber
                  value={record.seq}
                  placeholder="请输入序号"
                  min={1}
                  max={this.props.tableList.total + 1}
                  onChange={(e: any) => this.handleValueChange('seq', e || undefined)}
                  style={{ width: 180 }}
                />
              </Form.Item>
              <Form.Item label="描述">
                <Input
                  value={record.remark}
                  placeholder="请输入权限组描述"
                  onChange={(e: any) => this.handleValueChange('remark', e.target.value)}
                />
              </Form.Item>
            </Form>
          </Modal>
        </div>
      </React.Fragment>
    );
  }
}

export default PermissionGroupMgr;
