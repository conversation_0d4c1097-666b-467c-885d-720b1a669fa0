/* eslint-disable no-return-assign */
/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { A, Table, Drawer } from '@components/common';
import Form from '@components/business/thematicTemplateForm';
import { requirePerm } from '@app/utils/utils';
import { Row, Col, message, Icon, Modal, Divider } from 'antd';
import { getTableList } from '@app/action/tableList';
import { sysApi as api } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@app/components/permItems';
import { withRouter } from 'react-router';
import connect from '@utils/connectTable';

import moment from 'moment';
@(withRouter as any)
@connect
class thematiTemplate extends React.Component<any, any> {
  formRef: any;
  constructor(props: any) {
    super(props);
    this.state = {
      loading: false,
      form: {
        visible: false,
        key: Date.now(),
        isEdit: false,
        templateName: '',
        imgUrl: '',
        mlfTemplateId: '',
      },
    };
  }
  componentDidMount() {
    this.getData();
  }

  getData = () => {
    this.props.dispatch(getTableList('getSubjectTemplateConfig', 'subjectTemplateConfigList', {}));
  };

  editRecord = (record: any = {}) => {
    if (record.id) {
      api
        .editSubjectTemplateConfig({ id: record.id })
        .then((res: any) => {
          const { templateName, imgUrl, mlfTemplateId } = res;
          this.setState({
            form: {
              visible: true,
              key: Date.now(),
              isEdit: true,
              templateName,
              imgUrl,
              mlfTemplateId,
            },
          });
        })
        .catch();
    } else {
      this.setState({
        form: {
          visible: true,
          key: Date.now(),
          isEdit: false,
          templateName: '',
          imgUrl: '',
          mlfTemplateId: '',
        },
      });
    }
  };
  // 删除
  del = (record: any) => {
    this.setState({
      loading: true,
    });
    api
      .delSubjectTemplateConfig({ id: record.id })
      .then(() => {
        message.success('操作成功');
        this.setState({
          loading: false,
        });
        this.getData();
      })
      .catch(() => {
        this.setState({
          loading: false,
        });
      });
  };

  setRef = (instance: any) => {
    this['formRef'] = instance;
  };
  submitForm = () => {
    this['formRef'].doSubmit();
  };
  // 序号
  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '序号',
        key: 'seq',
        render: (a: any, b: any, c: number) => getSeq(c),
        width: 70,
      },
      {
        title: '模板名称',
        dataIndex: 'templateName',
      },
      {
        title: '模板预览',
        dataIndex: 'img_url',
        render: (text: any) => <img src={text} className="list-pic" />,
        width: 120,
      },
      {
        title: '关联媒立方模板ID',
        dataIndex: 'mlfTemplateId',
      },
      // {
      //   title: '模板状态',
      //   key: 'status',
      //   dataIndex: 'status',
      //   render: (text: any) => <span>{text ? '上线' : '下线'}</span>,
      //   width: 80,
      // },
      {
        title: '创建人',
        dataIndex: 'created_by',
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        render: (text: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
        width: 170,
      },
      {
        title: '操作',
        key: 'op',
        render: (record: any) => (
          <span>
            {requirePerm(
              this,
              'redPacketActivity:save'
            )(
              <PermA perm="subjectTemplateConfig:delete" onClick={() => this.del(record)}>
                删除
              </PermA>
            )}
          </span>
        ),
        width: 160,
      },
    ];
  };
  closeDrawer = () => {
    this.setState({
      form: { ...this.state.form, visible: false },
    });
  };
  submitSuccess = () => {
    this.closeDrawer();
    this.getData();
  };
  render() {
    const { form } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            {requirePerm(
              this,
              'web_feature:red_packet_switch'
            )(
              <PermButton perm="subjectTemplateConfig:save" onClick={() => this.editRecord()}>
                <Icon type="plus-circle" /> 新建专题模板
              </PermButton>
            )}
          </Col>
          {/* <div>{props}</div> */}
        </Row>
        <div className="component-content">
          <Table
            columns={this.getColumns()}
            rowKey="id"
            func="getSubjectTemplateConfig"
            index="subjectTemplateConfigList"
            pagination={true}
          />
          <Drawer
            visible={form.visible}
            skey={form.key}
            title={form.isEdit ? '编辑专题模板' : '添加专题模板'}
            onClose={this.closeDrawer}
            onOk={this.submitForm.bind(this)}
          >
            <Form
              formContent={form}
              wrappedComponentRef={this.setRef.bind(this)}
              onEnd={() => {
                this.submitSuccess();
              }}
            />
          </Drawer>
        </div>
      </>
    );
  }
}
export default thematiTemplate;
