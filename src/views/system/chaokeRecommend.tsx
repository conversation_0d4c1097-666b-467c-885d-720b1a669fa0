import React, { useState } from 'react';
import { BaseComponent, Table, Drawer, A, CKEditor } from '@components/common';
import { setConfig } from '@app/action/config';
import { ITableProps, IBaseProps, CommonObject } from '@app/types';
import ChaoyouApplication from '@components/business/ChaoyouApplication';
import {
  Button,
  Col,
  Icon,
  Form,
  Input,
  Select,
  DatePicker,
  Menu,
  Dropdown,
  AutoComplete,
  Collapse,
  message,
  Modal,
  Row,
  Divider,
  Radio,
} from 'antd';
import { requirePerm } from '@utils/utils';
import { withRouter } from 'react-router';
import { connectTable as connect } from '@utils/connect';
import { RadioChangeEvent } from 'antd/es/radio';
import moment from 'moment';
import { sysApi as api } from '@app/api';
import { cloneDeep } from 'lodash';
import { ProposalRecord, ProposalAllData } from '../operates/operates';

const InputGroup = Input.Group;
const { Panel } = Collapse;
const { confirm } = Modal;

enum TABSTATE {
  COMMENTPRESET = 1,
  COMMENTTIPS,
}

type State = {
  type: TABSTATE;
  commentForm: {
    show: boolean;
    key: number;
    id?: number;
    title: string;
    data: any;
  };
  commentTipsForm: {
    show: boolean;
    key: number;
    id?: number;
    title: string;
    data: any;
  };
  ModalForm: {
    show: boolean;
    data: any;
    id: any;
  };
};

type Props = IBaseProps<ITableProps<ProposalRecord, ProposalAllData>>;

class chaokeRecommend extends BaseComponent<ITableProps<ProposalRecord, ProposalAllData>, State> {
  constructor(props: Props) {
    console.log(props);
    super(props);
    this.state = {
      type: TABSTATE.COMMENTPRESET,
      // 应用窗口
      commentForm: {
        show: false,
        key: Date.now(),
        id: 0,
        title: '创建应用',
        data: {},
      },
      commentTipsForm: {
        show: false,
        key: Date.now(),
        id: 0,
        title: '评论提示',
        data: {},
      },
      ModalForm: {
        show: false,
        id: '',
        data: '',
      },
    };
  }

  componentDidMount() {
    this.setMenu();
    this.getData({ current: 1, size: 10 });
  }

  handleTypeChange = (e: RadioChangeEvent) => {
    if (e.target.value === this.state.type) {
      return;
    }
    this.setState(
      {
        type: e.target.value,
      },
      () => {
        console.log(this.state.type);
        // 切换
        if (e.target.value === TABSTATE.COMMENTPRESET) {
          this.getData({ current: 1, size: 10 });
        } else if (e.target.value === TABSTATE.COMMENTTIPS) {
          this.getData1({ current: 1, size: 10 });
        }
      }
    );
  };

  getData = (filters = this.getFilters()) => {
    this.dispatchTable('getRecommendsceneScenelist', 'scene_list', {
      ...filters,
    });
  };

  getData1 = (filters = {}) => {
    this.dispatchTable('getRecommendsceneDimensionlist', 'dimension_list', {
      ...filters,
    });
  };

  getFilters = () => {
    const { current, size } = this.props.tableList;
    const body: CommonObject = { current, size };
    return body;
  };

  // getFilters2 = () => {
  //   const data: any = cloneDeep(this.state.searchTips);
  //   if (data.text) {
  //     data[data.type] = data.text;
  //   }
  //   delete data.type;
  //   delete data.text;
  //   data.begin = data.begin ? data.begin : '';
  //   data.end = data.end ? data.end : '';
  //   return data;
  // };

  // 应用创建窗口 打开新建设置
  openDrawer = () => {
    this.setState({
      commentForm: {
        ...this.state.commentForm,
        title: '创建应用',
        show: true,
      },
    });
  };

  // 应用创建窗口-关闭
  closeDrawer = () => {
    this.setState({
      commentForm: { ...this.state.commentForm, show: false, data: {} },
    });
  };

  // 应用创建窗口关闭刷新数据
  commentSubmitEnd = () => {
    this.closeDrawer();
    this.getData();
  };

  getColumns = () => {
    const that = this;
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    const getDropDown = (record: any) => {
      // 编辑
      const editComment = () => {
        const seeData = record;
        seeData.disabled = false;
        this.setState({
          commentForm: {
            ...this.state.commentForm,
            title: '修改应用',
            data: seeData,
            show: true,
          },
        });
      };
      // 删除
      const deleteComment = () => {
        confirm({
          title: '确认删除应用吗？',
          content: `${record.scene_name}`,
          onOk() {
            api.deleteRecommendscene({ id: record.id }).then((res) => {
              message.success('删除成功');
              that.getData();
            });
          },
          onCancel() {},
        });
      };
      // 上下架
      const enableComment = () => {
        api
          .updatescenetypeRecommendscene({ id: record.id, status: record.status ? 0 : 1 })
          .then((res) => {
            if (record.state) {
              message.success('下架成功');
            } else {
              message.success('上架成功');
            }
            that.getData();
          });
      };

      const menu = (
        <span>
          {this.requirePerm('recommend_scene:update_displayed')(
            <A onClick={enableComment}>{record.status ? '下线' : '启用'}</A>
          )}
          <Divider type="vertical" />
          {this.requirePerm('recommend_scene:update')(<A onClick={editComment}>编辑</A>)}
          <Divider type="vertical" />
          {this.requirePerm('recommend_scene:delete')(<A onClick={deleteComment}>删除</A>)}
        </span>
      );
      return menu;
    };

    const columns = [
      {
        title: '序号',
        key: 'id',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 50,
      },
      {
        title: '应用场景',
        key: 'scene_name',
        dataIndex: 'scene_name',
        width: 120,
      },
      {
        title: '场景编码',
        key: 'scene_code',
        dataIndex: 'scene_code',
        width: 120,
      },
      {
        title: '推荐类型',
        key: 'recommend_type',
        dataIndex: 'recommend_type',
        render: (text: any) => (
          <span>
            {text &&
              JSON.parse(text).map((x: any, i: number) => {
                let str = '';
                switch (x) {
                  case '1':
                    str = '潮客';
                    break;
                  case '2':
                    str = '潮鸣号';
                    break;
                  case '3':
                    str = '栏目工作室';
                    break;
                  default:
                    str = '';
                }
                if (JSON.parse(text).length - 1 === i) {
                  return `${str}`;
                }
                return `${str} | `;
              })}
          </span>
        ),
        width: 160,
      },

      {
        title: '创建时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 120,
      },
      {
        title: '创建人',
        key: 'creator',
        dataIndex: 'creator',
        width: 90,
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'status',
        render: (text: any) => <span>{text ? '启用' : '未启用'}</span>,
        width: 60,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => getDropDown(record),
        width: 120,
      },
    ];
    return columns;
  };

  getColumns2 = () => {
    const that = this;
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    const getDropDown = (record: any) => {
      // 编辑
      const editTips = () => {
        this.setState({
          ModalForm: {
            ...this.state.ModalForm,
            data: record.reason,
            show: true,
            id: record.id,
          },
        });
      };
      // 上下架
      const enableTips = () => {
        api
          .UpdatedimensiontypeRecommenddimension({ id: record.id, status: record.status ? 0 : 1 })
          .then((res) => {
            if (record.status) {
              message.success('下架成功');
            } else {
              message.success('上架成功');
            }
            that.getData1();
          });
      };

      const menu = (
        <span>
          {this.requirePerm('recommend_scene:update_displayed')(
            <A onClick={enableTips}>{record.status ? '下线' : '启用'}</A>
          )}
          <Divider type="vertical" />
          {this.requirePerm('recommend_scene:update')(<A onClick={editTips}>编辑</A>)}
        </span>
      );
      return menu;
    };

    const columns2 = [
      {
        title: '序号',
        key: 'id',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 50,
      },
      {
        title: '维度名称',
        key: 'name',
        dataIndex: 'name',
        width: 120,
      },
      {
        title: '推荐理由',
        key: 'reason',
        dataIndex: 'reason',
        width: 200,
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'status',
        render: (text: any) => <span>{text ? '启用' : '未启用'}</span>,
        width: 100,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => getDropDown(record),
        width: 70,
      },
    ];
    return columns2;
  };

  // eslint-disable-next-line consistent-return
  TabContentDom = (tab: number) => {
    const TabDom1 = (
      <>
        <Table
          key="table1"
          func="getRecommendsceneScenelist"
          index="scene_list"
          pagination={true}
          filter={() => {}}
          rowKey="id"
          columns={this.getColumns()}
        />
      </>
    );

    const TabDom2 = (
      <>
        <Table
          key="table2"
          func="getRecommendsceneDimensionlist"
          index="dimension_list"
          pagination={true}
          filter={() => {}}
          rowKey="id"
          columns={this.getColumns2()}
        />
      </>
    );

    if (tab === TABSTATE.COMMENTPRESET) {
      return TabDom1;
    }
    if (tab === TABSTATE.COMMENTTIPS) {
      return TabDom2;
    }
  };

  ModalFormFn = (value: any) => {
    const { ModalForm } = this.state;
    if (!ModalForm.data) {
      return message.error('推荐理由不能为空');
    }
    api.updateRecommenddimension({ id: ModalForm.id, reason: ModalForm.data }).then((res) => {
      this.setState({
        ModalForm: {
          ...ModalForm,
          show: false,
        },
      });
      message.success('编辑成功');
      this.getData1();
    });
  };

  render() {
    const { type, commentForm, ModalForm } = this.state;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    };
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            <Radio.Group
              value={type}
              style={{ marginRight: 8 }}
              onChange={this.handleTypeChange}
              buttonStyle="solid"
            >
              <Radio.Button value={TABSTATE.COMMENTPRESET}>应用列表</Radio.Button>
              <Radio.Button value={TABSTATE.COMMENTTIPS}>维度管理</Radio.Button>
            </Radio.Group>
            {type === TABSTATE.COMMENTPRESET &&
              this.requirePerm('recommend_scene:create')(
                <Button onClick={this.openDrawer}>
                  <Icon type="plus-circle" />
                  创建应用 {/* 评论预设 */}
                </Button>
              )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {this.getCrumb()}
          </Col>
        </Row>
        <div className="component-content news-pages">{this.TabContentDom(type)}</div>
        <Drawer
          visible={commentForm.show}
          skey={commentForm.key}
          title={commentForm.title}
          onClose={this.closeDrawer}
          onOk={this.handleSubmitForm.bind(this, 'commentForm')}
        >
          <ChaoyouApplication
            formContent={commentForm.data}
            wrappedComponentRef={this.setFormRef.bind(this, 'commentForm')}
            onEnd={this.commentSubmitEnd}
            onClose={this.closeDrawer}
          />
        </Drawer>

        <Modal
          title="维度编辑"
          visible={ModalForm.show}
          onOk={this.ModalFormFn}
          onCancel={() => {
            this.setState({
              ModalForm: {
                ...ModalForm,
                show: false,
              },
            });
          }}
          okText="确认"
          cancelText="取消"
        >
          <Form {...formLayout}>
            <Form.Item required={true} label="推荐理由">
              <Col span={20}>
                <Input
                  value={ModalForm.data}
                  placeholder="请输入推荐理由，不超过10个汉字"
                  onChange={(e: any) =>
                    this.setState({ ModalForm: { ...ModalForm, data: e.target.value } })
                  }
                />
              </Col>
            </Form.Item>
          </Form>
        </Modal>
      </>
    );
  }
}

export default withRouter(connect<ProposalRecord, ProposalAllData>()(chaokeRecommend));
