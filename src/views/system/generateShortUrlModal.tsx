import {
  Button,
  Col,
  Form,
  Icon,
  Input,
  InputNumber,
  Modal,
  Radio,
  Row,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import ReactClipboard from 'react-clipboardjs-copy';

import { sysApi } from '@app/api';

const GenerateShortUrlModal = (props: any) => {
  const [loading, setLoading] = useState(false);
  const [value, setValue] = useState('');
  const [shortUrl, setShortUrl] = useState('');

  const handleSubmit = (e: any) => {
    e.preventDefault();
    if (/^https?:\/\//.test(value)) {
      setLoading(true);
      sysApi
        .generateShortUrl({ url: value })
        .then((result) => {
          setLoading(false);
          if (!!result?.data?.short_url) {
            message.success('短链已生成')
            setShortUrl(result?.data?.short_url || '');
            // props.onEnd?.(result?.data?.short_url || '');
          } else {
            message.error('生成失败');
          }
        })
        .catch((err) => {
          setLoading(false);
        });
    } else {
      message.error('请输入正确的链接');
    }
  };

  return (
    <Modal
      width={500}
      visible={props.visible}
      title="生成短链接"
      key={props.skey}
      onCancel={() => {
        if (!loading) {
          props.onCancel && props.onCancel();
        }
      }}
      cancelButtonProps={!!shortUrl ? { style: { display: 'none' } } : {}}
      onOk={
        !shortUrl
          ? handleSubmit
          : () => {
              props.onEnd?.();
            }
      }
      maskClosable={false}
      destroyOnClose={true}
    >
      <Spin spinning={false}>
        {!shortUrl && (
          <div>
            <span>原始链接:</span>
            <Input
              placeholder="请输入链接"
              value={value}
              style={{ marginTop: 10 }}
              onChange={(e) => {
                setValue(e.target.value);
              }}
            ></Input>
          </div>
        )}
        {!!shortUrl && (
          <div>
            <p>原始链接：{value}</p>
            <p>
              短链接：{shortUrl}&nbsp;
              <ReactClipboard
                action="copy"
                text={shortUrl}
                onSuccess={() => message.success('链接已复制')}
                onError={() => message.error('复制失败')}
              >
                <a>复制</a>
              </ReactClipboard>
            </p>
          </div>
        )}
      </Spin>
    </Modal>
  );
};

export default GenerateShortUrlModal;
