import { setConfig } from '@action/config';
import { getTableList } from '@app/action/tableList';
import { sysApi as api } from '@app/api';
import Form from '@components/business/appVersionForm';
import { Drawer, Table } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm } from '@utils/utils.tsx';
import { Button, Col, Dropdown, Icon, Input, InputNumber, Menu, message, Modal, Row, Select } from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';

@(withRouter as any)
@connect
class ReleaseManager extends React.Component<any, any> {
  formRef: any;
  constructor(props: any) {
    super(props);
    this.state = {
      loading: false,
      form: {
        visible: false,
        title: '编辑版本',
        key: Date.now(),
      },
      device: '-1',
    };
  }

  componentDidMount() {
    this.getData({ ...this.getFilter(), current: 1, size: 10 });
    this.props.dispatch(
      setConfig({
        selectKeys: this.props.selectKeys,
        openKeys: this.props.openKeys,
      })
    );
  }

  getData = (filter = this.getFilter()) => {
    this.props.dispatch(getTableList('getAppVersionList', 'app_version_list', filter));
  }

  getFilter = () => {
    const { current, size } = this.props.tableList;
    return { current, size, device_type: this.state.device };
  }

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    const getDropdown = (record: any) => {
      const menu = (
        <Menu>
          <Menu.Item>
            <a href={record.pkg_url} target="__blank">
              下载地址
            </a>
          </Menu.Item>
          {requirePerm(this, 'app_version:update')(<Menu.Item onClick={() => this.editRecord(record)}>编辑</Menu.Item>)}
          {requirePerm(
            this,
            'app_version:delete'
          )(<Menu.Item onClick={() => this.deleteRecord(record)}>删除</Menu.Item>)}
        </Menu>
      );

      return (
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };

    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '系统类型',
        key: 'device_type',
        dataIndex: 'device_type',
        render: (text: number) => <span>{['Android', 'iOS', '鸿蒙'][text]}</span>,
        width: 80,
      },
      {
        title: '版本名称',
        key: 'version',
        dataIndex: 'version',
        width: 80,
      },
      {
        title: '版本编号',
        key: 'version_code',
        dataIndex: 'version_code',
        width: 80,
      },
      {
        title: '发布时间',
        key: 'publish_time',
        dataIndex: 'publish_time',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm')}</span>,
        width: 150,
      },
      {
        title: '强制升级',
        key: 'force_upgraded',
        dataIndex: 'force_upgraded',
        render: (text: boolean) => <div style={{ textAlign: 'center' }}>{text ? '是' : '否'}</div>,
        width: 80,
      },
      {
        title: '版本说明',
        key: 'remark',
        dataIndex: 'remark',
        render: (text: any) => <div dangerouslySetInnerHTML={{ __html: text }} />,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => getDropdown(record),
        width: 70,
      },
    ];
  }

  editRecord = (record: any) => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .getAppVersionDetail({ id: record.id })
      .then((r: any) => {
        this.setState({
          form: {
            visible: true,
            title: '编辑版本',
            key: Date.now(),
            ...r.data.app_version,
          },
        });
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  }

  createRecord = (record: any) => {
    this.setState({
      form: {
        visible: true,
        title: '新建版本',
        key: Date.now(),
      },
    });
  }

  deleteRecord = (record: any) => {
    Modal.confirm({
      title: (
        <p>
          确认删除版本“{['Android', 'iOS', '鸿蒙'][record.device_type]}&nbsp;{record.version}”？
        </p>
      ),
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        api
          .deleteAppVersion({ id: record.id })
          .then(() => {
            message.success('操作成功');
            this.props.dispatch(setConfig({ loading: false }));
            this.getData();
          })
          .catch(() => this.props.dispatch(setConfig({ loading: false })));
      },
    });
  }

  handleTypeChange = (value: any) => {
    this.setState({ device: value }, () => {
      this.getData({ ...this.getFilter(), current: 1 });
    });
  }

  setLoading = (loading: boolean) => {
    this.setState({ loading });
  }

  closeDrawer = () => {
    this.setState({ form: { ...this.state.form, visible: false, loading: false } });
  }

  submitEnd = () => {
    this.getData();
    this.closeDrawer();
  }

  render() {
    const { form } = this.state;
    return (
      <React.Fragment>
        <Row className="layout-infobar">
          <Col span={12}>
            <Select
              value={this.state.device.toString()}
              style={{ width: 160, marginRight: 8 }}
              onChange={this.handleTypeChange}
            >
              <Select.Option value="-1">全部系统类型</Select.Option>
              <Select.Option value="1">iOS</Select.Option>
              <Select.Option value="0">Android</Select.Option>
              <Select.Option value="2">鸿蒙</Select.Option>
            </Select>
            {requirePerm(
              this,
              'app_version:create'
            )(
              <Button onClick={this.createRecord}>
                <Icon type="plus-circle" />
                新建版本
              </Button>
            )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getAppVersionList"
            index="app_version_list"
            filter={this.getFilter()}
            columns={this.getColumns()}
            rowKey="id"
            pagination={true}
          />
          <Drawer
            visible={form.visible}
            skey={form.key}
            title={form.title}
            onOk={() => this.formRef.doSubmit()}
            onClose={this.closeDrawer}
          >
            <Form
              formContent={form}
              onEnd={this.submitEnd}
              wrappedComponentRef={(instance: any) => (this.formRef = instance)}
            />
          </Drawer>
        </div>
      </React.Fragment>
    );
  }
}

export default ReleaseManager;
