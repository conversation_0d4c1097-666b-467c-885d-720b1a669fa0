import { setConfig } from '@action/config';
import { sysApi as api } from '@app/api';
import { CKEditor } from '@components/common';
import connect from '@utils/connectSession';
import { getCrumb, requirePerm } from '@utils/utils.tsx';
import { Button, Col, Form, Input, message, Row, Switch } from 'antd';
import React from 'react';
import { withRouter } from 'react-router';

@(Form.create({ name: 'noticeForm' }) as any)
class NoticeForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = { content: '', ...props.formContent };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        this.props.setLoading(true);
        api
          .updateNotice({ ...values, id: this.state.id })
          .then(() => {
            message.success('操作成功');
            this.props.onEnd();
          })
          .catch(() => this.props.setLoading(false));
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit}>
        <Form.Item label="公告标题">
          {getFieldDecorator('title', {
            initialValue: this.state.title,
            rules: [
              {
                required: true,
                message: '请输入公告标题',
              },
            ],
          })(<Input placeholder="请输入公告标题" />)}
        </Form.Item>
        <Form.Item label="值班电话">
          {getFieldDecorator('tel', {
            initialValue: this.state.tel,
            rules: [
              {
                required: true,
                message: '请输入值班电话',
              },
            ],
          })(<Input placeholder="请输入值班电话" />)}
        </Form.Item>
        <Form.Item label="公告内容">
          {getFieldDecorator('content', {
            initialValue: this.state.content,
            rules: [
              {
                required: true,
                message: '请输入公告内容',
              },
            ],
          })(<CKEditor />)}
        </Form.Item>
        <Form.Item label="公告显示">
          {getFieldDecorator('status', {
            initialValue: this.state.status,
            valuePropName: 'checked',
          })(<Switch checkedChildren="开" unCheckedChildren="关" />)}
        </Form.Item>
      </Form>
    );
  }
}

@(withRouter as any)
@connect
class NoticeManager extends React.Component<any, any> {
  formRef: any;

  constructor(props: any) {
    super(props);
    this.state = {
      form: {
        id: '',
        title: '',
        status: false,
        tel: '',
        content: '',
      },
      key: Date.now(),
      renderForm: false,
    };
  }

  componentDidMount() {
    this.getData();
    this.props.dispatch(
      setConfig({
        selectKeys: this.props.selectKeys,
        openKeys: this.props.openKeys,
      })
    );
    setTimeout(
      () =>
        this.setState({
          renderForm: true,
        }),
      1000
    );
  }

  getData = () => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .getNoticeDetail()
      .then((r: any) => {
        this.setState({
          form: { ...r.data.app_notice },
          key: Date.now(),
        });
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  handleSubmit = () => {
    this.formRef.doSubmit();
  };

  handleSubmitEnd = () => {
    this.getData();
  };

  setLoading = (loading: boolean) => {
    this.props.dispatch(setConfig({ loading }));
  };

  render() {
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            {requirePerm(
              this,
              'admin_notice:save'
            )(
              <Button type="primary" onClick={this.handleSubmit}>
                保存
              </Button>
            )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          {this.state.renderForm && (
            <NoticeForm
              formContent={this.state.form}
              key={this.state.key}
              onEnd={this.handleSubmitEnd}
              wrappedComponentRef={(instance: any) => (this.formRef = instance)}
              setLoading={this.setLoading}
            />
          )}
        </div>
      </>
    );
  }
}

export default NoticeManager;
