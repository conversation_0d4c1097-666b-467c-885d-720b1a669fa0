import { opApi } from '@app/api';
import { NewTable, OrderColumn } from '@app/components/common';
import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import useTable from '@app/utils/useTable';
import useXHR from '@app/utils/useXhr';
import {
  Button,
  Checkbox,
  Form,
  Icon,
  InputNumber,
  Modal,
  Radio,
  Spin,
  Table,
  Tabs,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useState } from 'react';
import { useStore } from 'react-redux';

const SecondFloorModal = (props: any, ref: any) => {
  const { tableList, loading, setLoading, getTableList } = useTable({
    api: opApi.secondFloorList,
    index: 'page',
  });

  const { loading: xhrLoading, run } = useXHR();

  const getSeq = (i: number) => (tableList?.current - 1) * tableList?.size + i + 1;

  const { session } = useStore().getState();

  const columns = [
    {
      title: '排序',
      dataIndex: 'seq',
      width: 100,
      render: (text, record, i) => {
        return (
          <OrderColumn
            pos={getSeq(i)}
            start={1}
            end={tableList?.total}
            perm="activity:update_second_sort_number"
            onUp={() => exchangeOrder(record.id, 0)}
            onDown={() => exchangeOrder(record.id, 1)}
          ></OrderColumn>
        );
      },
    },
    {
      title: '活动标题',
      dataIndex: 'title',
    },
    {
      title: '结束时间',
      dataIndex: 'end_time',
      width: 150,
    },
  ];

  const exchangeOrder = (id, sort_flag, position) => {
    const params = { id, sort_flag };
    if (sort_flag == 2) {
      params.position = position;
    }
    run(opApi.sortSecondFloor, params, true)
      .then((result) => {
        getTableList(tableList?.current);
      })
      .catch((err) => {});
  };

  const onDragEnd = (oldIndex: number, newIndex: number) => {
    const source = tableList?.records[oldIndex];
    const target = tableList?.records[newIndex];
    exchangeOrder(source.id, 2, getSeq(newIndex));
  };

  useEffect(() => {
    getTableList();
  }, []);

  return (
    <Modal
      width={700}
      visible={props.visible}
      title={'2楼活动排序'}
      key={props.key}
      onCancel={props.onCancel}
      maskClosable={false}
      destroyOnClose={true}
      footer={null}
    >
      <Spin spinning={loading || xhrLoading}>
        <div>已上传2楼列表图且状态为「进行中」的活动显示在下方列表，App上最多显示前8个活动</div>
        <NewTable
          rowKey={'id'}
          columns={columns}
          pagination={true}
          tableList={tableList}
          draggable={session.permissions.includes('activity:update_second_sort_number')}
          onDragEnd={onDragEnd}
          getTableList={getTableList}
        ></NewTable>
      </Spin>
    </Modal>
  );
};

export default Form.create<any>({ name: 'SecondFloorModal' })(
  forwardRef<any, any>(SecondFloorModal)
);
