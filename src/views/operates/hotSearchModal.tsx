import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import {
  Button,
  Form,
  Icon,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useReducer, useState } from 'react';
import { NewTable, OrderColumn } from '@app/components/common';
import { useDispatch, useSelector } from 'react-redux';
import { setConfig } from '@app/action/config';
import { PermA, PermButton } from '@app/components/permItems';
import { TableList } from '@app/types';
import useTable from '@app/utils/useTable';
import { opApi as api } from '@app/api';
import { connectSession } from '@app/utils/connect';

const HotSearchModal = (props: any) => {
  const dispatch = useDispatch();
  const [editIndex, setEditIndex] = useState(-1);
  const getFilter = () => {
    return {
      type: 2,
      size: 1000,
    };
  };
  const session = useSelector((state: any) => state.session);
  const { tableList, loading, setLoading, getTableList } = useTable({
    api: api.getHotWordList,
    filter: getFilter,
    index: 'hot_word_list',
  });

  useEffect(() => {
    if (props.visible) {
      getTableList();
    }
  }, [props.visible]);

  const getSeq = (i: number) => ((tableList?.current ?? 0) - 1) * (tableList?.size ?? 0) + i + 1;

  const exchangeOrder = (record: any, sort_flag: number) => {
    setLoading(true);
    api
      .sortHotWord({ id: record.id, sort_flag })
      .then(() => {
        message.success('操作成功');
        getTableList();
        setLoading(false);
        setEditIndex(-1);
      })
      .catch(() => setLoading(false));
  };

  // 切换编辑状态
  const toggleEdit = (i: number, type: boolean) => {
    if (type) {
      tableList.records[i].newContent = tableList.records[i].content;
    }
    setEditIndex(type ? i : -1);
  };

  const updateHotWord = (record: any) => {
    if (record.newContent?.length === 0) {
      message.error('请输入热搜词');
      return;
    }
    if (record.newContent?.length > 25) {
      message.error('热搜词长度不能超过25个字');
      return;
    }
    const body: any = { id: record.id, content: record.newContent?.trim() };
    setLoading(true);
    api
      .updateHotWord(body)
      .then(() => {
        message.success('操作成功');
        getTableList();
        setLoading(false);
        setEditIndex(-1);
      })
      .catch(() => setLoading(false));
  };

  const removeHotWord = (record: any) => {
    setLoading(true);
    api
      .deleteHotWord({ ids: record.id })
      .then(() => {
        message.success('操作成功');
        getTableList();
        setLoading(false);
      })
      .catch(() => setLoading(false));
  };

  const column = [
    {
      title: '排序',
      key: 'order',
      render: (text: any, record: any, i: number) => {
        const pos = getSeq(i);
        return (
          <OrderColumn
            pos={pos}
            start={1}
            end={tableList?.records?.length ?? 0}
            perm=""
            // disableUp={record.status === 0 || pos === 0}
            // // i === records.length - 1
            // disableDown={record.status === 0 || (i < records.length - 1 && records[i + 1].status === 0)}
            onUp={() => exchangeOrder(record, 0)}
            onDown={() => exchangeOrder(record, 1)}
          />
        );
      },
      width: 70,
    },
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      width: 70,
    },
    {
      title: '热搜词',
      key: 'content',
      dataIndex: 'content',
      render: (text: any, record: any, i: number) => {
        if (i == editIndex) {
          return (
            <Input
              maxLength={25}
              defaultValue={record.newContent}
              onChange={(event) => {
                record.newContent = event.target.value;
              }}
            ></Input>
          );
        } else {
          return <span>{text}</span>;
        }
      },
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any, index: number) => (
        <span>
          {index != editIndex ? (
            <PermA perm="hot_word:update" onClick={() => toggleEdit(index, true)}>
              编辑
            </PermA>
          ) : (
            <>
              {/* <Popconfirm
                title="确定要更新吗？"
                okText="确定"
                cancelText="取消"
                icon={<Icon type="close-circle" theme="filled" style={{ color: 'red' }} />}
                onConfirm={() => updateHotWord(record)}
              >
                
              </Popconfirm> */}
              <PermA perm="" style={{ marginRight: 5 }} onClick={() => updateHotWord(record)}>
                保存
              </PermA>
              <PermA perm="" onClick={() => toggleEdit(index, false)}>
                取消
              </PermA>
            </>
          )}

          {index != editIndex && (
            <Popconfirm
              title="确定要删除吗？"
              okText="确定"
              cancelText="取消"
              icon={<Icon type="close-circle" theme="filled" style={{ color: 'red' }} />}
              onVisibleChange={() => setEditIndex(-1)}
              onConfirm={() => removeHotWord(record)}
            >
              <PermA perm="hot_word:delete" style={{ marginLeft: 5 }}>
                删除
              </PermA>
            </Popconfirm>
          )}
        </span>
      ),
      width: 120,
    },
  ];

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    setEditIndex(-1);

    Modal.confirm({
      title: '确定要更新吗？',
      onOk(...args) {
        setLoading(true);
        api
          .onekeyUpdateHotWord({})
          .then(() => {
            message.success('操作成功');
            setLoading(false);
            props.onOk?.();
          })
          .catch(() => setLoading(false));
      },
    });
  };
  return (
    <Modal
      width={800}
      visible={props.visible}
      title="最新热搜词"
      key={props.key}
      onCancel={() => props.onCancel?.()}
      onOk={handleSubmit}
      maskClosable={false}
      destroyOnClose={true}
      okText="一键更新"
      confirmLoading={loading}
      okButtonProps={{ disabled: !session.permissions.includes('hot_word:onekey_update') }}
    >
      <Spin spinning={false}>
        <div style={{ maxHeight: 500, overflow: 'auto' }}>
          <NewTable
            columns={column}
            rowKey="id"
            pagination={false}
            getTableList={getTableList}
            loading={loading}
            tableList={tableList}
          ></NewTable>
        </div>
      </Spin>
    </Modal>
  );
};

export default connectSession(HotSearchModal);
