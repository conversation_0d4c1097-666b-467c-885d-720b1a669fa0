import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Divider,
  Form,
  Icon,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Radio,
  Select,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { opApi as api } from '@app/api';
import { A, Drawer, ImageUploader } from '@app/components/common';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import SearchAndInput from '@components/common/newNewsSearchAndInput';
import moment from 'moment';
import _ from 'lodash';
import NewNewsSearchAndInput from '@components/common/newNewsSearchAndInput';
import logger from 'redux-logger';

interface ProportionObject {
  divisor: number;
  dividend: number;
}

const AddCompilations = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const { getFieldDecorator, getFieldsValue, setFieldsValue, getFieldValue } = props.form;

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const editTitle = (value: string, index: number) => {
    let title: string = value || '';
    let modal: any;
    const titleChange = (e: any) => {
      title = e.target.value;
      modal.update({
        content: (
          <>
            <Input.TextArea
              placeholder="最多输入40字"
              value={title}
              maxLength={40}
              onPressEnter={(e) => e.preventDefault()}
              onChange={titleChange}
            ></Input.TextArea>
            <div style={{ textAlign: 'right' }}>{title?.length}/40</div>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '自定义标题',
      content: (
        <>
          <Input.TextArea
            placeholder="最多输入40字"
            value={title}
            maxLength={40}
            onPressEnter={(e) => e.preventDefault()}
            onChange={titleChange}
          ></Input.TextArea>
          <div style={{ textAlign: 'right' }}>{title?.length}/40</div>
        </>
      ),
      onOk: (destroy: Function) => {
        // if (!title?.trim()) {
        //   message.error('请输入自定义标题');
        //   return;
        // }
        const articles = [...props.form.getFieldsValue().articles];
        articles[index].new_list_title = title?.trim();
        setFieldsValue({ articles: articles });
        destroy();
      },
    });
  };

  const getImageRatio = async (imageUrl: string) => {
    return new Promise((resolve, reject) => {
      const img = new Image();

      img.onload = () => {
        const width = img.width;
        const height = img.height;
        const ratio = width / height;
        resolve(ratio);
      };

      img.onerror = () => {
        resolve(2);
      };

      img.src = imageUrl;
    });
  };

  const editPic = async (url: any, record: any, index: number) => {
    let pic: string = url || '';
    let modal: any;
    const { w, h } = { w: 4, h: 3 };
    let proportion = (w / h).toFixed(2);
    let consistentProportions = true;
    if (url) {
      const afterCalculation = await getImageRatio(url);
      if (Math.abs(afterCalculation - proportion) <= 0.05) {
        consistentProportions = false;
      } else {
        consistentProportions = true;
      }
    }

    const picChange = (u: string) => {
      pic = u;
      modal.update({
        content: (
          <>
            <ImageUploader value={pic} onChange={picChange} ratio={4 / 3} />
            <p>比例4:3</p>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '编辑图片',
      width: 500,
      content: (
        <>
          <ImageUploader value={pic} onChange={picChange} imgMaxWidth={400} ratio={w / h} />
          {consistentProportions && url ? (
            <span style={{ color: 'red' }}>
              该图片比例非{w}:{h}，图片将自动居中截取，建议重新上传。
            </span>
          ) : (
            <p>
              {record.page_type === 5 && '建议'}比例{w}:{h}
            </p>
          )}
        </>
      ),
      onOk: async (destroy: Function) => {
        console.log('pic ===' + pic);

        const articles = [...props.form.getFieldsValue().articles];
        articles[index].new_list_pic = pic;
        setFieldsValue({ articles: articles });
        destroy();
      },
    });
  };

  const handleSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        if (values.articles.length < 1) {
          message.error('至少关联1条稿件');
          return;
        }

        dispatch(setConfig({ mLoading: true }));

        if (props.record) {
          values.id = props.record.id ?? '';
        }

        const params = {
          ...values,
        };

        params.date = params.date.format('YYYYMMDD');

        const articleList = values.articles.map((item: any) => {
          return {
            id: item.id,
            new_list_title: item.new_list_title || '',
            new_list_pic: item.new_list_pic || '',
          };
        });
        params.articles = JSON.stringify(articleList);

        api
          .createDrawerEdit(params)
          .then((res: any) => {
            message.success(!props.record ? '新增成功' : '更新成功');
            dispatch(setConfig({ mLoading: false }));
            props.onEnd && props.onEnd();
          })
          .catch(() => {
            // message.error('添加失败');
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
        dispatch(setConfig({ mLoading: false }));
      }
    });
  };

  const columns: any = [
    {
      title: '潮新闻ID',
      key: 'id',
      dataIndex: 'id',
      width: 85,
    },
    {
      title: '新闻频道',
      key: 'channel_name',
      dataIndex: 'channel_name',
      width: 100,
    },
    {
      title: '新闻标题',
      key: 'list_title',
      dataIndex: 'list_title',
    },
    {
      // width: 90,
      title: '自定义标题',
      key: 'new_list_title',
      dataIndex: 'new_list_title',
      render: (text: string, record: any, index: number) => {
        return (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <div>{record.new_list_title}</div>
            <a
              style={{ flex: 'none', marginLeft: '5px' }}
              onClick={() => editTitle(record.new_list_title, index)}
            >
              修改
            </a>
          </div>
        );
      },
    },
    {
      title: '列表图',
      dataIndex: 'new_list_pic',
      key: 'new_list_pic',
      width: 114,
      render: (text: string, record: any, index: number) => (
        <div style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
          {text && <img src={text} style={{ height: 40, maxWidth: 70 }} />}

          <A
            style={{ flex: 'none', marginLeft: '5px' }}
            onClick={() => editPic(text, record, index)}
          >
            {text ? '修改' : '上传'}
          </A>
        </div>
      ),
    },
  ];

  const [detailData, setDetailData] = useState({
    date: null,
    articles: [],
  });

  const compilationsDetail = _.debounce((val: any) => {
    api
      .drawerDetail({ id: props.record.id })
      .then((data: any) => {
        const detail = data.data.detail;
        const date = detail.date;
        const article_list = detail.article_list;

        // console.log('detail ===' + JSON.stringify(detail));
        // console.log('date ===' + JSON.stringify(date));
        // console.log('article_list ===' + JSON.stringify(article_list));

        setDetailData({ date: date, articles: article_list });
      })
      .catch((e) => {});
  }, 500);

  useEffect(() => {
    if (props.visible) {
      if (props.record && props.record.id) {
        compilationsDetail('');
      }
    }
  }, [props.visible]);

  return (
    <Drawer
      title={!props.record ? '添加' : '编辑'}
      visible={props.visible}
      skey={props.key}
      onClose={props.onClose}
      maskClosable={false}
      width={'70%'}
      onOk={handleSubmit}
    >
      <Form {...formLayout}>
        <Form.Item label="日期">
          <Tooltip title="1个日期最多创建1条">
            <Icon type="question-circle" style={{ position: 'absolute', left: -70, top: 0 }} />
          </Tooltip>
          {getFieldDecorator('date', {
            initialValue: detailData.date ? moment(`${detailData.date}`, 'YYYYMMDD') : '',
            rules: [
              {
                required: true,
                message: '请选择日期',
              },
            ],
          })(<DatePicker style={{ width: '80' }} disabled={!!props.record} />)}
        </Form.Item>

        <Form.Item label="关联稿件">
          <Tooltip title="仅支持关联媒立方稿件">
            <Icon type="question-circle" style={{ position: 'absolute', left: -100, top: 12 }} />
          </Tooltip>
          {getFieldDecorator('articles', {
            initialValue: detailData.articles,
            rules: [
              {
                required: true,
                message: '请至少关联 1 条稿件',
                type: 'array',
              },
              {
                max: 30,
                message: '最多关联30条内容',
                type: 'array',
              },
            ],
          })(
            <NewNewsSearchAndInput
              min={1}
              max={30}
              func="listArticleRecommendSearch"
              columns={columns}
              placeholder="输入ID或标题关联稿件"
              body={{ doc_types: '2,3,4,5,8,9' }}
              order={true}
              // isMedia={true}
              addOnTop={true}
              draggable={true}
              map={(article: any) => {
                return {
                  ...article,
                  new_list_pic: '',
                  new_list_title: '',
                };
              }}
            />
          )}
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'AddCompilations' })(forwardRef<any, any>(AddCompilations));
