import React, { useEffect, useMemo, useState } from 'react';
import { Drawer, Form, Select, Button, Modal, message, Table, Pagination } from 'antd';
import { debounce } from 'lodash';
import { communityApi } from '@app/api';

const { confirm } = Modal;

interface ForbidJoinGroupProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (users: UserItem[]) => void;
  form: any;
}

// 与账号搜索返回结构对齐（参考 CreateGroupChat.tsx）
export interface UserItem {
  id: string;
  nick_name: string;
  chao_id: string;
}

const ForbidJoinGroup: React.FC<ForbidJoinGroupProps> = ({ visible, onCancel, onOk, form }) => {
  const { getFieldDecorator, resetFields } = form;

  // 搜索 UI 状态（完全复用 CreateGroupChat.tsx 的交互）
  const [selectedAccount, setSelectedAccount] = useState<string | undefined>();
  const [accountSuggestions, setAccountSuggestions] = useState<UserItem[]>([]);

  // 列表数据（使用本地状态管理）
  const [list, setList] = useState<UserItem[]>([]);

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 抽屉开启时加载初始数据（mock）
  useEffect(() => {
    if (visible) {
      // 模拟请求接口获取禁止加群列表
      const mock: UserItem[] = Array.from({ length: 23 }).map((_, i) => ({
        id: `${1000 + i}`,
        nick_name: `违规定义用户${i + 1}`,
        chao_id: `${1234500 + i}`,
      }));
      setList(mock);
      setPagination(prev => ({
        ...prev,
        total: mock.length,
        current: 1,
      }));
    }
  }, [visible]);

  // 关闭抽屉时复位
  const handleClose = () => {
    resetFields();
    setSelectedAccount(undefined);
    setAccountSuggestions([]);
    setList([]);
    setPagination({ current: 1, pageSize: 10, total: 0 });
    onCancel();
  };

  // 搜索（平移 CreateGroupChat.tsx 的 API 与节流逻辑）
  const handleAccountSearch = useMemo(
    () =>
      debounce((value: string) => {
        if (value) {
          communityApi
            .recommendAccount_Search({ keyword: value })
            .then((res: any) => {
              setAccountSuggestions(res?.data?.list || []);
            })
            .catch(() => setAccountSuggestions([]));
        } else {
          setAccountSuggestions([]);
        }
      }, 300),
    []
  );

  const handleAccountChange = (value?: string) => {
    setSelectedAccount(value);
    // 如果清空选择，也清空下拉数据
    if (!value) {
      setAccountSuggestions([]);
    }
  };

  // 选择某个账号时，将其加入列表顶部并清空下拉
  const handleAccountSelect = (value: string) => {
    const item = accountSuggestions.find((s) => `${s.id}` === `${value}`);
    if (!item) return;

    setList((prev) => {
      const exists = prev.some((x) => `${x.id}` === `${item.id}`);
      if (exists) {
        message.warning('该用户已在列表中');
        return prev;
      }
      const newList = [item, ...prev];

      // 更新分页总数
      setPagination(prevPagination => ({
        ...prevPagination,
        total: newList.length,
        current: 1, // 新增后回到第一页
      }));

      return newList;
    });

    // 清空搜索选中与下拉数据
    setSelectedAccount(undefined);
    setAccountSuggestions([]);

    // 清空表单字段值
    form.setFieldsValue({
      accountSearch: undefined
    });
  };

  // 移除
  const removeUser = (id: string) => {
    confirm({
      title: '确定移除该用户?',
      okText: '确定',
      cancelText: '取消',
      onOk: () =>
        new Promise<void>((resolve) => {
          setList((prev) => {
            const newList = prev.filter((x) => `${x.id}` !== `${id}`);

            // 更新分页总数，如果当前页没有数据则回到上一页
            setPagination(prevPagination => {
              const newTotal = newList.length;
              const maxPage = Math.ceil(newTotal / prevPagination.pageSize) || 1;
              const newCurrent = prevPagination.current > maxPage ? maxPage : prevPagination.current;

              return {
                ...prevPagination,
                total: newTotal,
                current: newCurrent,
              };
            });

            return newList;
          });
          resolve();
        }),
    });
  };

  // 处理分页变化
  const handlePaginationChange = (page: number, pageSize?: number) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize: pageSize || prev.pageSize,
    }));
  };

  // 处理页面大小变化
  const handlePageSizeChange = (_current: number, size: number) => {
    setPagination(prev => ({
      ...prev,
      current: 1, // 改变页面大小时回到第一页
      pageSize: size,
    }));
  };

  // 提交
  const handleSubmit = () => {
    // 仅收集数据并回传，不做实际提交
    onOk(list);
    handleClose();
  };

  // 计算当前页显示的数据
  const getCurrentPageData = () => {
    const { current, pageSize } = pagination;
    const startIndex = (current - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return list.slice(startIndex, endIndex);
  };

  // 表格列
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 80,
      render: (_: any, __: UserItem, index: number) => (pagination.current - 1) * pagination.pageSize + index + 1,
    },
    {
      title: '账号昵称',
      dataIndex: 'nick_name',
    },
    {
      title: '小潮号',
      dataIndex: 'chao_id',
      width: 160,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: any, record: UserItem) => (
        <a onClick={() => removeUser(record.id)}>移除</a>
      ),
    },
  ];

  return (
    <Drawer
      title="禁止加入圈子群聊"
      visible={visible}
      onClose={handleClose}
      width={800}
      maskClosable={false}
      destroyOnClose
    >
      {/* 说明文案 */}
      <div style={{ padding: '0 8px 16px', color: '#666' }}>
        对应账号将被移出圈子群聊，且无法再加入圈子任一群聊
      </div>

      {/* 搜索区域（保持与 CreateGroupChat.tsx 一致风格） */}
      <Form labelCol={{ span: 4 }} wrapperCol={{ span: 16 }}>
        <Form.Item label="账号" required={false} extra="输入昵称或小潮号查找账号">
          {getFieldDecorator('accountSearch')(
            <Select
              value={selectedAccount}
              showSearch
              allowClear
              placeholder="输入昵称进行搜索"
              filterOption={false}
              onChange={handleAccountChange}
              onSearch={handleAccountSearch as any}
              onSelect={handleAccountSelect}
            >
              {accountSuggestions.map((d: any) => (
                <Select.Option
                  key={d.id}
                  value={d.id}
                  style={{ whiteSpace: 'pre-wrap' }}
                >
                  {`${['潮客 - ', '潮鸣号 - ', '潮鸣号 - '][d.cert_type] || ''}${d.nick_name} | 小潮号：${d.chao_id}`}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>
      </Form>

      {/* 列表容器 */}
      <div style={{
        height: 'calc(100vh - 280px)',
        minHeight: '500px',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* 表格 */}
        <div style={{ flex: 1, overflow: 'hidden' }}>
          <Table
            rowKey="id"
            columns={columns}
            dataSource={getCurrentPageData()}
            pagination={false} // 禁用内置分页
            scroll={{ y: 'calc(100vh - 380px)' }}
            size="middle"
          />
        </div>

        {/* 分页组件 */}
        <div style={{
          padding: '16px 0',
          textAlign: 'right',
          borderTop: '1px solid #f0f0f0',
          background: '#fff'
        }}>
          <Pagination
            current={pagination.current}
            pageSize={pagination.pageSize}
            total={pagination.total}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`}
            pageSizeOptions={['10', '20', '50', '100']}
            onChange={handlePaginationChange}
            onShowSizeChange={handlePageSizeChange}
          />
        </div>
      </div>

      {/* 底部操作栏 */}
      <div
        style={{
          position: 'fixed',
          bottom: 0,
          right: 0,
          width: '800px',
          borderTop: '1px solid #e8e8e8',
          padding: '12px 24px',
          background: '#fff',
          textAlign: 'right',
          zIndex: 1000,
          boxShadow: '0 -2px 8px rgba(0,0,0,0.1)',
        }}
      >
        <Button onClick={handleClose} style={{ marginRight: 8 }}>
          取消
        </Button>
        <Button type="primary" onClick={handleSubmit}>
          确定
        </Button>
      </div>
    </Drawer>
  );
};

export default Form.create<ForbidJoinGroupProps>({ name: 'forbidJoinGroup' })(ForbidJoinGroup);

