import React, { useState, useEffect } from 'react';
import { Form, Input, Select, message, Modal, Button } from 'antd';
import { debounce } from 'lodash';
import { communityApi } from '@app/api';
import { iMReportTypeMap } from '@app/utils/utils';

const { TextArea } = Input;
const { Option } = Select;

interface AddGroupChatMuteModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
  form: any;
  existingMuteUsers?: string[]; // 已禁言用户ID列表，用于重复检查
}

interface FormData {
  account_id: string;
  mute_duration: string;
  reason_type: string;
  remark?: string;
}

interface UserOption {
  id: string;
  nick_name: string;
  chao_id: string;
  cert_type: number;
}

const AddGroupChatMuteModal: React.FC<AddGroupChatMuteModalProps> = ({ 
  visible, 
  onCancel, 
  onOk, 
  form,
  existingMuteUsers = []
}) => {
  const { getFieldDecorator, validateFields, resetFields, getFieldValue } = form;

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [userSuggestions, setUserSuggestions] = useState<UserOption[]>([]);

  // 表单布局
  const formLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };

  // 禁言时长选项（参考私信拉黑违规功能）
  const muteDurationOptions = [
    { value: '1', label: '1天' },
    { value: '3', label: '3天' },
    { value: '7', label: '7天' },
    { value: '30', label: '30天' },
    { value: '-1', label: '永久' },
  ];

  // 处理表单提交
  const handleSubmit = () => {
    validateFields((err: any, values: FormData) => {
      if (!err) {
        // 检查是否已在禁言状态
        if (existingMuteUsers.includes(values.account_id)) {
          message.error('该账号已禁言');
          return;
        }

        setLoading(true);

        // 处理数据
        const submitData = {
          ...values,
          remark: values.remark?.trim() || '',
        };

        // ✅ API调用预留
        console.log('添加群聊禁言账号', submitData);
        
        // 模拟API调用
        setTimeout(() => {
          setLoading(false);
          message.success('操作成功');
          onOk(submitData);
          handleCancel();
        }, 1000);
      }
    });
  };

  // 处理取消
  const handleCancel = () => {
    resetFields();
    setUserSuggestions([]);
    onCancel();
  };

  // 处理用户搜索建议（复用CreateGroupChat的账号选择功能）
  const handleUserSearch = debounce((value: string) => {
    if (value) {
      // 调用接口获取用户建议列表
      communityApi
        .recommendAccount_Search({ keyword: value })
        .then((res: any) => {
          setUserSuggestions(res.data?.list || []);
        })
        .catch(() => {
          setUserSuggestions([]);
        });
    } else {
      setUserSuggestions([]);
    }
  }, 300);

  // 自定义账号验证器
  const validateAccount = (rule: any, value: any, callback: any) => {
    if (!value) {
      callback('请输入昵称或小潮号');
      return;
    }
    callback();
  };

  // 自定义禁言时长验证器
  const validateMuteDuration = (rule: any, value: any, callback: any) => {
    if (!value) {
      callback('请选择禁言时长');
      return;
    }
    callback();
  };

  // 自定义禁言理由验证器
  const validateReason = (rule: any, value: any, callback: any) => {
    if (!value) {
      callback('请选择禁言理由');
      return;
    }
    callback();
  };

  return (
    <Modal
      title="添加群聊禁言账号"
      visible={visible}
      onCancel={handleCancel}
      width={500}
      maskClosable={false}
      destroyOnClose={true}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit} loading={loading}>
          确定
        </Button>,
      ]}
    >
      <Form {...formLayout}>
        {/* 禁言账号 */}
        <Form.Item label="禁言账号" required>
          {getFieldDecorator('account_id', {
            rules: [
              {
                required: true,
                validator: validateAccount,
              },
            ],
          })(
            <Select
              placeholder="输入昵称或小潮号查找"
              onSearch={handleUserSearch}
              showSearch
              allowClear={true}
              filterOption={false}
            >
              {userSuggestions.map((user: UserOption) => (
                <Select.Option
                  key={user.id}
                  value={user.id}
                  style={{
                    whiteSpace: 'pre-wrap',
                  }}
                >
                  {`${['潮客 - ', '潮鸣号 - ', '潮鸣号 - '][user.cert_type] || ''}${
                    user.nick_name
                  } | 小潮号：${user.chao_id}`}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>

        {/* 禁言时长 */}
        <Form.Item label="禁言时长" required>
          {getFieldDecorator('mute_duration', {
            initialValue: '1',
            rules: [
              {
                required: true,
                validator: validateMuteDuration,
              },
            ],
          })(
            <Select placeholder="请选择禁言时长">
              {muteDurationOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          )}
        </Form.Item>

        {/* 禁言理由 */}
        <Form.Item label="禁言理由" required>
          {getFieldDecorator('reason_type', {
            initialValue: '1',
            rules: [
              {
                required: true,
                validator: validateReason,
              },
            ],
          })(
            <Select placeholder="请选择禁言理由">
              {Object.keys(iMReportTypeMap).map((key) => (
                <Option key={key} value={key}>
                  {iMReportTypeMap[key]}
                </Option>
              ))}
            </Select>
          )}
        </Form.Item>

        {/* 备注 */}
        <Form.Item label="备注">
          {getFieldDecorator('remark')(
            <TextArea
              placeholder="添加备注信息，仅后台可见"
              rows={4}
              maxLength={200}
            />
          )}
        </Form.Item>
      </Form>

      {/* 底部说明文字 */}
      <div style={{
        color: '#666',
        fontSize: '14px',
        marginTop: '16px',
        textAlign: 'center'
      }}>
        禁言账号加入的所有群聊，在禁言期均不可发言。
      </div>
    </Modal>
  );
};

export default Form.create<AddGroupChatMuteModalProps>({ name: 'addGroupChatMuteModal' })(AddGroupChatMuteModal);
