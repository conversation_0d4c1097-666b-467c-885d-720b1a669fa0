import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setTableList } from '@app/action/tableList';
import { Table } from '@components/common';
import { getCrumb } from '@utils/utils';
import {
  Button,
  Divider,
  Col,
  Form,
  Icon,
  Input,
  message,
  Modal,
  Row,
  Select,
} from 'antd';
import { useRouteMatch } from 'react-router-dom';
import { CommonObject } from '@app/types';
import { setMenuHook } from '@app/utils/utils';
import CreateGroupChat from './components/CreateGroupChat';
import ForbidJoinGroup, { UserItem } from './components/ForbidJoinGroup';

interface FilterState {
  circle_id: string;
  search_type: number;
  keyword: string;
}



export default function GroupChatMgr(props: any) {
  const dispatch = useDispatch();
  const match = useRouteMatch();

  // Redux state
  const tableList = useSelector((state: any) => state.tableList);
  const tableCache = useSelector((state: any) => state.tableCache);
  const { current, size, records = [] } = useSelector((state: any) => state.tableList);

  // Local state
  const [filter, setFilter] = useState<FilterState>({
    circle_id: '',
    search_type: 1, // 1: 群名称, 2: 群ID
    keyword: '',
  });

  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const [circleList, setCircleList] = useState<any[]>([]);

  // 搜索状态
  const [search, setSearch] = useState<{
    keyword: string;
  }>({
    keyword: '',
  });

  // 编辑群聊弹窗
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editRecord, setEditRecord] = useState<any>({});

  // 排序弹窗
  const [sortModalVisible, setSortModalVisible] = useState(false);
  const [sortRecord, setSortRecord] = useState<any>({});

  // 创建群聊弹窗
  const [createModalVisible, setCreateModalVisible] = useState(false);

  // 禁止加群弹窗
  const [forbidJoinModalVisible, setForbidJoinModalVisible] = useState(false);

  // 初始化
  useEffect(() => {
    setMenuHook(dispatch, props);
    loadCircleList();

    if (tableCache?.beforeRoute === match?.path && tableCache.records.length > 0) {
      getData({ current: tableCache.current, size: tableCache.size });
    } else {
      getData({ current: 1 });
    }

    setIsInitialized(true);
  }, []);

  // 加载圈子列表
  const loadCircleList = () => {
    // ✅ API方法预留但不实现
    console.log('加载圈子列表');
    // 模拟数据
    setCircleList([
      { id: '1', name: '圈子1' },
      { id: '2', name: '圈子2' },
      { id: '3', name: '圈子3' },
    ]);
  };

  // 获取数据
  const getData = (overlap: CommonObject = {}) => {
    const params = { ...getFilter(), ...overlap };
    console.log('获取群聊列表数据', params);
    // ✅ API方法预留但不实现
    // dispatch(getTableList('getGroupChatList', 'list', params));

    // 模拟数据
    const mockData = {
      total: 5,
      current: 1,
      size: 10,
      allData: {},
      records: [
        {
          id: '1',
          group_id: '893',
          group_name: '聊天利水群',
          member_count: 123,
          owner_name: '小潮官方账号',
          circle_name: '口水楼市',
          creator_name: '张三',
          create_time: '2023-01-01 00:00:00',
          sort_order: 1,
        },
        {
          id: '2',
          group_id: '234',
          group_name: '小潮官方1群',
          member_count: 500,
          owner_name: '账号昵称',
          circle_name: '',
          creator_name: '张三',
          create_time: '2023-01-01 00:00:00',
          sort_order: 2,
        },
        {
          id: '3',
          group_id: '690',
          group_name: '小潮官方2群',
          member_count: 499,
          owner_name: '账号昵称',
          circle_name: '好摄之友',
          creator_name: '张三',
          create_time: '2023-01-01 00:00:00',
          sort_order: 3,
        },
        {
          id: '4',
          group_id: '456',
          group_name: '小潮官方3群',
          member_count: 100,
          owner_name: '账号昵称',
          circle_name: '',
          creator_name: '张三',
          create_time: '2023-01-01 00:00:00',
          sort_order: 4,
        },
        {
          id: '5',
          group_id: '678',
          group_name: '小潮粉丝福利群',
          member_count: 1,
          owner_name: '账号昵称',
          circle_name: '',
          creator_name: '张三',
          create_time: '2023-01-01 00:00:00',
          sort_order: 5,
        },
      ],
    };

    dispatch(setTableList(mockData));
  };

  // 获取过滤条件
  const getFilter = () => {
    const { current, size } = tableList;
    const filters: CommonObject = { current, size };

    // 添加所有筛选字段
    Object.keys(filter).forEach((key) => {
      const value = filter[key as keyof typeof filter];
      if (value !== '' && value !== undefined) {
        filters[key] = value;
      }
    });

    return filters;
  };

  // 处理过滤条件变化
  const handleFilterChange = (key: string, value: any) => {
    if (key === 'search_type') {
      // 搜索类型变化时，清空关键词
      setFilter({
        ...filter,
        [key]: value,
        keyword: '',
      });
      setSearch({
        keyword: '',
      });
    } else {
      setFilter({
        ...filter,
        [key]: value,
      });
    }
  };

  // 监听 filter 变化
  useEffect(() => {
    if (isInitialized) {
      getData({ current: 1 });
    }
  }, [filter]);

  // 处理搜索
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  // 处理搜索
  const handleSearch = () => {
    setFilter({
      ...filter,
      keyword: search.keyword,
    });
  };

  // 创建群聊
  const handleCreateGroup = () => {
    setCreateModalVisible(true);
  };

  // 处理创建群聊成功
  const handleCreateGroupSuccess = (values: any) => {
    console.log('创建群聊成功', values);
    // ✅ 这里可以调用API保存群聊信息
    // 刷新列表数据
    getData();
  };

  // 处理创建群聊取消
  const handleCreateGroupCancel = () => {
    setCreateModalVisible(false);
  };

  // 禁止加群
  const handleForbidJoin = () => {
    setForbidJoinModalVisible(true);
  };

  // 处理禁止加群确认
  const handleForbidJoinSuccess = (users: UserItem[]) => {
    console.log('禁止加群用户列表:', users);
    // ✅ 这里可以调用API保存禁止加群用户列表
    message.success(`已设置 ${users.length} 个用户禁止加群`);
  };

  // 处理禁止加群取消
  const handleForbidJoinCancel = () => {
    setForbidJoinModalVisible(false);
  };

  // 编辑群聊信息
  const handleEdit = (record: any) => {
    console.log('编辑群聊信息', record);
    setEditRecord(record);
    setEditModalVisible(true);
  };

  // 排序操作
  const handleSortAction = (record: any) => {
    console.log('排序操作', record);
    setSortRecord(record);
    setSortModalVisible(true);
  };

  // 删除群聊
  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '删除后群聊将被解散，不可恢复',
      content: '',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        console.log('删除群聊', record);
        // ✅ API调用预留
        message.success('删除成功');
        getData();
1      },
    });
  };

  // 复制链接
  const handleCopyLink = (record: any) => {
    console.log('复制群分享链接', record);
    // ✅ 复制群分享链接逻辑预留
    message.success('链接已复制');
  };

  // 上移
  const handleMoveUp = (record: any) => {
    console.log('上移', record);
    // ✅ 排序逻辑预留
  };

  // 下移
  const handleMoveDown = (record: any) => {
    console.log('下移', record);
    // ✅ 排序逻辑预留
  };

  // 处理批量选择
  const handleSelectChange = (selectedKeys: any[]) => {
    setSelectedRowKeys(selectedKeys);
  };

  // 获取序号
  const getSeq = (i: number) => (current - 1) * size + i + 1;

  // 是否显示排序功能（选择圈子后显示）
  const showSortFeature = filter.circle_id !== '';

  // 获取列配置
  const columns = [
    {
      title: '排序',
      key: 'sort',
      width: 80,
      render: (_: any, record: any, i: number) => {
        if (!showSortFeature) return null;

        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Button
              size="small"
              icon="up"
              onClick={() => handleMoveUp(record)}
              disabled={i === 0}
            />
            <Button
              size="small"
              icon="down"
              onClick={() => handleMoveDown(record)}
              disabled={i === records.length - 1}
            />
          </div>
        );
      },
    },
    {
      title: '序号',
      key: 'seq',
      render: (_: any, __: any, i: number) => <span>{getSeq(i)}</span>,
      width: 60,
    },
    {
      title: '群ID',
      dataIndex: 'group_id',
      key: 'group_id',
      width: 100,
    },
    {
      title: '群名称',
      dataIndex: 'group_name',
      key: 'group_name',
      width: 200,
      render: (text: string, record: any) => (
        <a onClick={() => handleEdit(record)}>{text}</a>
      ),
    },
    {
      title: '群成员数',
      dataIndex: 'member_count',
      key: 'member_count',
      width: 100,
    },
    {
      title: '群主',
      dataIndex: 'owner_name',
      key: 'owner_name',
      width: 150,
    },
    {
      title: '关联圈子',
      dataIndex: 'circle_name',
      key: 'circle_name',
      width: 120,
      render: (text: string) => text || '-',
    },
    {
      title: '操作人',
      dataIndex: 'creator_name',
      key: 'creator_name',
      width: 100,
    },
    {
      title: '最后操作时间',
      dataIndex: 'create_time',
      key: 'create_time',
      width: 160,
    },
    {
      title: '操作',
      key: 'op',
      width: 200,
      fixed: 'right',
      render: (_: any, record: any) => (
        <span>
          <a onClick={() => handleEdit(record)}>编辑</a>
          {showSortFeature && (
            <>
              <Divider type="vertical" />
              <a onClick={() => handleSortAction(record)}>排序</a>
            </>
          )}
          <Divider type="vertical" />
          <a onClick={() => handleDelete(record)}>删除</a>
          <Divider type="vertical" />
          <a onClick={() => handleCopyLink(record)}>复制链接</a>
        </span>
      ),
    },
  ].filter(col => {
    // 未选择圈子时隐藏排序列
    if (col.key === 'sort' && !showSortFeature) {
      return false;
    }
    return true;
  });

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button
            type="primary"
            onClick={handleCreateGroup}
            style={{ marginRight: 8 }}
          >
            <Icon type="plus-circle" /> 创建群聊
          </Button>
          <Button onClick={handleForbidJoin}>
            <Icon type="stop" /> 禁止加群
          </Button>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>

      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={16}>
            <Form layout="inline">
              <Form.Item>
                <Select
                  value={filter.circle_id}
                  onChange={(value) => handleFilterChange('circle_id', value)}
                  style={{ width: 150 }}
                  placeholder="全部圈子"
                >
                  <Select.Option value="">全部圈子</Select.Option>
                  {circleList.map((circle) => (
                    <Select.Option key={circle.id} value={circle.id}>
                      {circle.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Form>
          </Col>
          <Col span={8} style={{ textAlign: 'right' }}>
            <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
              <Select
                value={filter.search_type}
                onChange={(value) => handleFilterChange('search_type', value)}
                style={{ width: 100, marginRight: 8 }}
              >
                <Select.Option value={1}>群名称</Select.Option>
                <Select.Option value={2}>群ID</Select.Option>
              </Select>
              <Input
                value={search.keyword}
                onChange={(e) => setSearch({ ...search, keyword: e.target.value })}
                placeholder="请输入群名称或群ID"
                style={{ width: 200, marginRight: 8 }}
                onKeyDown={handleKeyDown}
              />
              <Button type="primary" onClick={handleSearch}>
                <Icon type="search" /> 搜索
              </Button>
            </div>
          </Col>
        </Row>

        <Table
          func="getGroupChatList"
          index="list"
          rowKey="id"
          filter={getFilter()}
          columns={columns}
          pagination={true}
          multi={false}
          selectedRowKeys={selectedRowKeys}
          onSelectChange={handleSelectChange}
          tableProps={{ scroll: { x: 1200 } }}
        />

        {/* 编辑群聊弹窗 */}
        <Modal
          visible={editModalVisible}
          title="编辑群聊信息"
          onCancel={() => setEditModalVisible(false)}
          onOk={() => {
            console.log('确认编辑', editRecord);
            setEditModalVisible(false);
            message.success('编辑成功');
          }}
        >
          <div>编辑群聊信息弹框内容</div>
        </Modal>

        {/* 排序弹窗 */}
        <Modal
          visible={sortModalVisible}
          title="排序"
          onCancel={() => setSortModalVisible(false)}
          onOk={() => {
            console.log('确认排序', sortRecord);
            setSortModalVisible(false);
            message.success('排序成功');
          }}
        >
          <div>排序弹框内容</div>
        </Modal>

        {/* 创建群聊弹窗 */}
        <CreateGroupChat
          visible={createModalVisible}
          onCancel={handleCreateGroupCancel}
          onOk={handleCreateGroupSuccess}
        />

        {/* 禁止加群弹窗 */}
        <ForbidJoinGroup
          visible={forbidJoinModalVisible}
          onCancel={handleForbidJoinCancel}
          onOk={handleForbidJoinSuccess}
        />
      </div>
    </>
  );
}