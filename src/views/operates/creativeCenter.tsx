import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useStore, useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, OrderColumn, PreviewMCN } from '@components/common';
import { CommonObject } from '@app/types';
import {
  getCrumb,
  objectToQueryString,
  requirePerm4Function,
  searchToObject,
  setMenuHook,
} from '@app/utils/utils';
import {
  Row,
  Col,
  Divider,
  Icon,
  Modal,
  message,
  InputNumber,
  Tooltip,
  Popconfirm,
  Button,
  Menu,
  Dropdown,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { opApi as api } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@components/permItems';
import Radio from 'antd/es/radio';
import moment from 'moment';
import ImagePreviewColumn from '@app/components/common/imagePreviewColumn';
import AddCreatorBanner from './addCreatorBanner';
import AddCreatorGuide from './addCreatorGuide';

export default function CreativeCenter(props: any) {
  const [filter, setFilter] = useState<any>({
    type: parseInt(searchToObject().type ?? 0),
  });

  const session = useSelector((state: any) => state.session);

  const [form, setForm] = useState(
    () =>
    ({
      visible: false,
      key: Date.now(),
    } as any)
  );

  const [guideForm, setGuideForm] = useState(
    () =>
    ({
      visible: false,
      key: Date.now(),
    } as any)
  );
  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);

  const { loading, run } = useXHR();

  const [preview, setPreview] = useState({
    visible: false,
    skey: 0,
    data: {},
  });

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const getList = useCallback(
    (first: boolean = false, overlap: CommonObject = {}) => {
      const { current, size = 10 } = store.getState().tableList;
      dispatch(
        getTableList(
          filter.type == 0 ? 'getCreativeCenterBannerList' : 'getCreativeCenterGuideList',
          'list',
          {
            ...filter,
            current: first ? 1 : current,
            size,
            ...overlap,
          }
        )
      );
    },
    [filter]
  );

  const editRecord = (record: any) => {
    setForm({
      visible: true,
      key: Date.now(),
      record,
    });
  };
  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确认删除吗？`,
      onOk: () => {
        const func = filter.type == 0 ? 'deleteCreativeCenterBanner' : 'deleteCreativeCenterGuide';
        run(api[func], { id: record.id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const listSort = (record: any, i: number, position: number = 1) => {
    let data: any = {
      id: record.id,
      sort_flag: i,
    };
    if (i == 2) {
      data.position = position;
    }
    const func = filter.type == 0 ? 'sortCreativeCenterBanner' : 'sortCreativeCenterGuide';
    api[func](data).then((res: any) => {
      message.success('操作成功');
      getList();
    });
  };

  const titleClick = (record: any) => {
    // setPreview({
    //   visible: true,
    //   skey: Date.now(),
    //   data: record,
    // });

    if (!!record.link) {
      window.open(record.link, '_blank');
    }

  };

  const columns1 = [
    {
      title: '排序',
      key: 'sort',
      render: (text: any, record: any, i: number) => (
        <OrderColumn
          perm="create_recommend:order"
          start={1}
          pos={getSeq(i)}
          end={records?.filter((v: any) => v.status == 1)?.length}
          disable={record.status == 0}
          onUp={() => listSort(record, 0)}
          onDown={() => listSort(record, 1)}
        />
      ),
      width: 70,
    },
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      width: 70,
    },
    {
      title: '图标',
      key: 'pic',
      dataIndex: 'pic',
      render: (text: string) => <img src={text} className="list-pic" alt="123" />,
      width: 120,
    },
    {
      title: '链接',
      key: 'link',
      dataIndex: 'link',
      render: (text: any, record: any) => (
        <a onClick={() => window.open(record.link, '_blank')}>{text}</a>
      ),
    },
    {
      title: '名称',
      dataIndex: 'name',
      width: 90,
    },
    {
      title: '操作人',
      dataIndex: 'updated_by',
      width: 90,
    },
    {
      title: '操作时间',
      dataIndex: 'updated_at',
      width: 100,
      render: (text: any, record: any) => {
        return (
          <div style={{ whiteSpace: 'normal' }}>
            <div>{moment(text).format('YYYY-MM-DD')}</div>
            <div>{moment(text).format('HH:mm:ss')}</div>
          </div>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 90,
      render: (text: any) => <span>{text == 1 ? '展示中' : '待展示'}</span>,
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any, i: number) => (
        <Dropdown
          overlay={
            <Menu>
              {requirePerm4Function(
                session,
                `create_recommend:edit`
              )(<Menu.Item onClick={() => editRecord(record)}>编辑</Menu.Item>)}
              {requirePerm4Function(
                session,
                `create_recommend:online`
              )(
                <Menu.Item onClick={() => updateStatus(record)}>
                  {record.status == 1 ? '下架' : '上架'}
                </Menu.Item>
              )}
              {requirePerm4Function(
                session,
                `create_recommend:delete`
              )(<Menu.Item onClick={() => deleteRecord(record)}>删除</Menu.Item>)}
            </Menu>
          }
        >
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      ),
      width: 110,
    },
  ];

  const columns2 = [
    {
      title: '排序',
      key: 'sort',
      render: (text: any, record: any, i: number) => (
        <OrderColumn
          perm="create_guide:order"
          start={1}
          pos={getSeq(i)}
          end={total}
          // disableUp={!record.enabled}
          // disableDown={!record.enabled}
          onUp={() => listSort(record, 0)}
          onDown={() => listSort(record, 1)}
        />
      ),
      width: 70,
    },
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      width: 70,
    },
    {
      title: '潮新闻ID',
      dataIndex: 'article_id',
      width: 90,
    },
    {
      title: '标题',
      key: 'title',
      dataIndex: 'title',
      render: (text: any, record: any) => (
        <a onClick={() => titleClick(record)} className="line-max-3 list-title">
          {(text && text.length > 0) ? text : "—"}
        </a>
      ),
    },
    {
      title: '封面图/配图',
      key: 'pic',
      dataIndex: 'pic',
      width: 150,
      align: 'center',
      render: (text: any, record: any) => (
        <div style={{ height: 60, textAlign: 'center' }}>
          <ImagePreviewColumn text={text} imgs={[text]}></ImagePreviewColumn>
        </div>
      ),
    },
    {
      title: '类型',
      dataIndex: 'doc_type',
      width: 70,
      render: (text: any) => ['小视频', '', '短图文', '长文章'][text - 10],
    },
    {
      title: '操作人',
      dataIndex: 'updated_by',
      width: 90,
    },
    {
      title: '操作时间',
      dataIndex: 'updated_at',
      width: 100,
      render: (text: any, record: any) => {
        return (
          <div style={{ whiteSpace: 'normal' }}>
            <div>{moment(text).format('YYYY-MM-DD')}</div>
            <div>{moment(text).format('HH:mm:ss')}</div>
          </div>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'op',
      width: 80,
      render: (text: any, record: any) => {
        return (
          <PermA perm="create_guide:delete" onClick={() => deleteRecord(record)}>
            删除
          </PermA>
        );
      },
    },
  ];

  useEffect(() => {
    setMenuHook(dispatch, props);
  }, []);

  useEffect(() => {
    getList(true);
  }, [filter]);

  const onChangeType = (v: any, name: any) => {
    let { pathname: path, search } = history.location;
    const obj = searchToObject();
    obj[name] = v;
    path = `${path}?${objectToQueryString(obj)}`;
    history.replace(path);
    setFilter({
      ...filter,
      [name]: v,
    });
  };

  const updateStatus = (record: any) => {
    run(
      api.updateCreativeCenterBannerStatus,
      { id: record.id, online: record.status == 1 ? 0 : 1 },
      true
    ).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Radio.Group
            defaultValue={filter.type}
            buttonStyle="solid"
            onChange={(e) => onChangeType(e.target.value, 'type')}
          >
            <Radio.Button value={0}>轮播图</Radio.Button>
            <Radio.Button value={1}>创作指南</Radio.Button>
          </Radio.Group>


          <PermButton perm={filter.type == 0 ? 'create_recommend:create' : "create_guide:create"} style={{ marginLeft: 8 }} onClick={() => {
            if (filter.type == 0) {
              editRecord(null);
            } else {
              setGuideForm({ visible: true, key: Date.now() });
            }
          }}>

            添加
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          func={filter.type == 0 ? 'getCreativeCenterBannerList' : 'getCreativeCenterGuideList'}
          index="list"
          filter={filter}
          columns={filter.type == 0 ? columns1 : columns2}
          rowKey="id"
          pagination={true}
        />
        <AddCreatorBanner
          {...form}
          onClose={() => {
            setForm({ visible: false });
          }}
          onEnd={() => {
            setForm({ visible: false });
            getList();
          }}
        ></AddCreatorBanner>
        <AddCreatorGuide
          {...guideForm}
          onClose={() => {
            setGuideForm({ visible: false });
          }}
          onEnd={() => {
            setGuideForm({ visible: false });
            getList();
          }}
        ></AddCreatorGuide>
        <PreviewMCN {...preview} onClose={() => setPreview({ ...preview, visible: false })} />
      </div>
    </>
  );
}
