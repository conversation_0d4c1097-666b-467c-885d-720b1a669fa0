import { setConfig } from '@action/config';
import { getTableList } from '@app/action/tableList';
import { opApi as api } from '@app/api';
import { CommonObject } from '@app/types';
import EntryForm from '@components/business/redPacketEntryForm';
import Form from '@components/business/redPacketInfoForm';
import { A, Drawer, Table, OrderColumn, BaseComponent, NavButton } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setLoading } from '@utils/utils.tsx';
import {
  Button,
  Col,
  DatePicker,
  Icon,
  Input,
  message,
  Modal,
  Row,
  Select,
  Divider,
  Tooltip,
  Switch,
  Radio,
} from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';

@(withRouter as any)
@connect
class RedPacketTotal extends BaseComponent<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      filters: {
        begin: '',
        end: '',
        nick_name: '',
      },
      sKey: 'nick_name',
      skeyword: '',
      type: 'recieve',
      typeDesc: {
        recieve: {
          columnName: '领取红包',
          dataIndex: 'money',
          sumName: '总领取金额',
        },
        cashout: {
          columnName: '提现金额',
          dataIndex: 'money',
          sumName: '总提现金额',
        },
      },
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    this.props.dispatch(
      setConfig({
        selectKeys: this.props.selectKeys,
        openKeys: this.props.openKeys,
      })
    );
  }

  getData = (overlap: CommonObject = {}) => {
    const { current, size } = this.props.tableList;
    const filters = this.getFilter();
    this.props.dispatch(
      getTableList('getPacketTotalList', 'data_total_list', { current, size, ...filters, ...overlap })
    );
  };

  getFilter = () => {
    const filters: CommonObject = {};
    Object.keys(this.state.filters).forEach((v: any) => {
      if (this.state.filters[v]) {
        filters[v] = this.state.filters[v];
      }
    });
    filters.type = this.state.type === 'recieve' ? 0 : 1;
    return filters;
  };

  getColumns = () => {
    const { type, typeDesc } = this.state;
    const { current, size, total } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;

    return [
      {
        title: '序号',
        key: 'seq',
        width: 90,
        render: (text: any, record: any, index: number) => getSeq(index),
      },
      {
        title: '时间',
        dataIndex: 'created_at',
        render: (text: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
        width: 170,
      },
      {
        title: '用户ID',
        dataIndex: 'account_id',
        width: 220,
      },
      {
        title: '用户昵称',
        dataIndex: 'nick_name',
      },
      {
        title: '手机号',
        dataIndex: 'phone_number',
        width: 110,
      },
      {
        title: typeDesc[type].columnName,
        dataIndex: typeDesc[type].dataIndex,
        render: (text: number) => (text ? text.toFixed(2) : ''),
        width: 90,
      },
    ];
  };

  handleRangeChange = (a: any, b: any) => {
    this.setState(
      {
        filters: { ...this.state.filters, begin: b[0], end: b[1] },
      },
      () => this.getData({ current: 1 })
    );
  };

  sKeyChange = (v: any) => {
    this.setState({
      sKey: v,
    });
  };

  sKeywordChange = (e: any) => {
    this.setState({
      sKeyword: e.target.value,
    });
  };

  doSearch = () => {
    const { begin, end } = this.state.filters;
    this.setState(
      {
        filters: {
          begin,
          end,
          [this.state.sKey]: this.state.sKeyword || '',
        },
      },
      () => this.getData({ current: 1 })
    );
  };

  typeChange = (e: any) => {
    this.setState(
      {
        type: e.target.value,
      },
      () => this.getData({ current: 1 })
    );
  };

  render() {
    const { filters, sKey, sKeyword, type, typeDesc } = this.state;
    const { allData } = this.props.tableList;
    const sum = allData.sum_money || 0;
    return (
      <React.Fragment>
        <Row className="layout-infobar">
          <Col span={12}>
            <NavButton style={{ marginRight: 8, verticalAlign: 'top' }} url="/view/redPacketMgr">
              <Icon type="left-circle" />
              返回
            </NavButton>
            <Radio.Group value={type} buttonStyle="solid" onChange={this.typeChange}>
              <Radio.Button value="recieve">领取金额</Radio.Button>
              <Radio.Button value="cashout">提现金额</Radio.Button>
            </Radio.Group>
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Row className="filters" style={{ marginBottom: 16 }}>
            <Col span={12}>
              <DatePicker.RangePicker
                style={{ marginRight: 8 }}
                value={filters.begin ? [moment(filters.begin), moment(filters.end)] : []}
                onChange={this.handleRangeChange}
              />
              {typeDesc[type].sumName}：{sum.toFixed(2)}
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <Select value={sKey} onChange={this.sKeyChange} style={{ width: 160, marginRight: 8 }}>
                <Select.Option value="nick_name">搜索用户昵称</Select.Option>
                <Select.Option value="account_id">搜索用户ID</Select.Option>
                <Select.Option value="phone_number">搜索用户手机号</Select.Option>
              </Select>
              <Input
                value={sKeyword}
                onChange={this.sKeywordChange}
                style={{ width: 200, marginRight: 8 }}
                placeholder="请输入关键词"
                onKeyPress={this.handleKey}
              />
              <Button type="primary" style={{ verticalAlign: 'top' }} onClick={this.doSearch}>
                <Icon type="search" />
                搜索
              </Button>
            </Col>
          </Row>
          <Table
            func="getPacketTotalList"
            index="data_total_list"
            columns={this.getColumns()}
            filter={this.getFilter()}
            rowKey="id"
            pagination={true}
          />
        </div>
      </React.Fragment>
    );
  }
}

export default RedPacketTotal;
