import { setConfig } from '@action/config';
import { getTableList } from '@app/action/tableList';
import { opApi as api } from '@app/api';
import { CommonObject } from '@app/types';
import { A, Table } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setMenu } from '@utils/utils.tsx';
import { Button, Col, Icon, Input, message, Modal, Row } from 'antd';
const { TextArea } = Input
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
@(withRouter as any)
@connect
class SensitiveWords extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      loading: false,
      word: '',
      visible: false,
      key: Date.now(),
      block_word: '',
      search: '',
      // 敏感词检测
      testingVisible: false,
      testingWrods:'',
      key2: Date.now() + 1,
    };
  }

  componentDidMount() {
    this.getData({ current: 1 });
    setMenu(this);
  }

  getData = (overlap: CommonObject = {}, filter = this.getFilter()) => {
    this.props.dispatch(getTableList('getSensitiveWords', 'block_word_list', { ...filter, ...overlap }));
  }

  getFilter = () => {
    const { current, size } = this.props.tableList;
    const filter: CommonObject = { current, size };
    if (this.state.block_word) {
      filter.block_word = this.state.block_word;
    }
    return filter;
  }

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;

    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      },
      {
        title: '敏感词内容',
        key: 'word',
        dataIndex: 'word',
      },
      {
        title: '创建时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <span>{requirePerm(this, 'block_word:delete')(<A onClick={() => this.deleteWord(record)}>删除</A>)}</span>
        ),
        width: 70,
      },
    ];
  }

  deleteWord = (record: any) => {
    Modal.confirm({
      title: <p>确认删除“{record.word}”吗</p>,
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        api
          .deleteSensitiveWord({ ids: record.id })
          .then(() => {
            message.success('操作成功');
            this.props.dispatch(setConfig({ loading: false }));
            this.getData();
          })
          .catch(() => this.props.dispatch(setConfig({ loading: false })));
      },
    });
  }

  createWord = () => {
    this.setState({
      visible: true,
      key: Date.now(),
      word: '',
    });
  }

  submitWord = () => {
    this.setState({ loading: true });
    if (this.state.word === '') {
      message.error('请输入敏感词内容');
      return;
    }
    if (this.state.word.length > 20) {
      message.error('敏感词内容不能超过20个字');
      return;
    }
    this.setState({ loading: true });
    api
      .createSensitiveWord({ block_word: this.state.word })
      .then(() => {
        message.success('操作成功');
        this.getData({ current: 1 });
        this.setState({
          loading: false,
          visible: false,
        });
      })
      .catch(() => this.setState({ loading: false }));
  }

  handleKey = (e: any) => {
    if (e.which === 13) {
      this.setState(
        {
          block_word: this.state.search,
        },
        () => this.getData({ current: 1 })
      );
    }
  }


  // 敏感词检测
  handleTesting = ()=>{
    this.setState(
      {
        testingVisible: true
      },
    );
  }
  submitTesting = () => {
    this.setState({ loading: true });
    if (this.state.testingWrods === '') {
      message.error('请输入要检测的敏感词');
      return;
    }
    if (this.state.testingWrods.length > 300) {
      message.error('敏感词内容不能超过300个字');
      return;
    }
    this.setState({ loading: true });
    api
      .getSensitiveWords2({ text: this.state.testingWrods })
      .then((res:any) => {
        if(!res.data.is_exist){
          Modal.success({
            title: '敏感词检测通过',
            okText:'确认'
          });
        }else{
          Modal.error({
            title: '检测到敏感词：',
            content: `${res.data.checked_word_list.join(',')}`,
            okText:'确认',
          });
        }
        this.setState({
          loading: false,
          testingVisible: false,
          testingWrods:''
        });
      })
      .catch(() => this.setState({ loading: false }));
  }
  render() {
    return (
      <React.Fragment>
        <Row className="layout-infobar">
          <Col span={12}>
            {requirePerm(
              this,
              'block_word:create'
            )(
              <Button onClick={this.createWord} style={{ marginRight: 8 }}>
                <Icon type="plus-circle" />
                添加敏感词
              </Button>
            )}
            <Input
              style={{ width: 160, marginRight: 8, verticalAlign: 'top' }}
              onKeyPress={this.handleKey}
              placeholder="输入搜索内容"
              value={this.state.search}
              onChange={(e: any) => this.setState({ search: e.target.value })}
            />
            <Button  style={{ marginRight: 8, verticalAlign: 'top' }}
             onClick={() => this.handleKey({ which: 13 })}>
              <Icon type="search" />
              搜索
            </Button>
            {requirePerm(
              this,
              'block_word:check'
            )(
              <Button onClick={() => this.handleTesting()}>
              敏感词检测
            </Button>
            )}
           
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getSensitiveWords"
            index="block_word_list"
            filter={this.getFilter()}
            columns={this.getColumns()}
            rowKey="id"
            pagination={true}
          />
          <Modal
            visible={this.state.visible}
            key={this.state.key}
            title="添加敏感词"
            onCancel={() => this.setState({ visible: false })}
            onOk={this.submitWord}
            confirmLoading={this.state.loading}
          >
            <Input
              value={this.state.word}
              placeholder="请输入敏感词"
              onChange={(e: any) => this.setState({ word: e.target.value })}
            />
          </Modal>
          <Modal
            visible={this.state.testingVisible}
            key={this.state.key2}
            title="敏感词检测"
            okText="开始检测"
            onCancel={() => this.setState({ testingVisible: false, testingWrods:''})}
            onOk={this.submitTesting}
            confirmLoading={this.state.loading}
          >
            <TextArea  value={this.state.testingWrods}  placeholder="请输入要检测文本 不超过300字"  maxLength={300} style={{ height: 120 }} onChange={(e: any) => this.setState({ testingWrods: e.target.value })}  />
          </Modal>
        </div>
      </React.Fragment>
    );
  }
}

export default SensitiveWords;
