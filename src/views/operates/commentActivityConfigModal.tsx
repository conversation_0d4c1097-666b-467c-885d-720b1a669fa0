import { opApi } from '@app/api';
import { ImageUploader } from '@app/components/common';
import { Checkbox, Form, Input, message, Modal, Spin } from 'antd';
import React, { useEffect, useState } from 'react';

export const CommentActivityConfigModal = (props: any) => {
  const {
    form: { getFieldDecorator },
  } = props;
  const [loading, setLoading] = useState(false);
  const [groups, setGroups] = useState([]);

  // useEffect(() => {
  //   if (props.visible) {
  //     getGroupsList();
  //   }
  // }, [props.visible]);

  // const getGroupsList = () => {
  // communityApi
  //   .getColumnGroupsList({})
  //   .then((res: any) => {})
  //   .catch((err: any) => {});
  // };

  const handleOkClick = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true);
        opApi
          .saveHudongAvatarConfig({
            ...(props.record || {}),
            ...values,
          })
          .then(() => {
            message.success('修改成功');
            setLoading(false);
            props.onEnd();
          })
          .catch(() => {
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  return (
    <Modal
      visible={props.visible}
      key="AddColumnGroupModal"
      title={''}
      confirmLoading={loading}
      width="600px"
      maskClosable={false}
      onCancel={props.onCancel}
      onOk={handleOkClick}
      destroyOnClose
    >
      <Spin tip="正在加载..." spinning={loading}>
        <Form {...formLayout}>
          {props.type == 1 ? (
            <>
              <h3>个人主页头像处</h3>

              <Form.Item label="文案">
                {getFieldDecorator('personal_head_text', {
                  initialValue: props.record?.personal_head_text || '',
                  // rules: [
                  //   {
                  //     required: true,
                  //     message: '请选择数据的时间范围',
                  //   },
                  // ],
                })(
                  <Input
                    placeholder="在个人主页预览头像挂件时，将显示该活动文案"
                    maxLength={20}
                  ></Input>
                )}
              </Form.Item>

              <Form.Item label="跳转链接">
                {getFieldDecorator('personal_head_link_url', {
                  initialValue: props.record?.personal_head_link_url || '',
                  rules: [
                    {
                      pattern: /^https?:\/\//,
                      message: '请输入正确的跳转链接',
                    },
                  ],
                })(<Input placeholder="在个人主页预览头像挂件时，点击可跳转的链接" />)}
              </Form.Item>
            </>
          ) : (
            <>
              <h3>
                评论区运营位&nbsp;
                <span
                  style={{
                    fontSize: '14px',
                    color: 'rgba(0, 0, 0, 0.45)',
                    fontWeight: '400',
                  }}
                >
                  运营位仅展示在新闻稿、长文章、短图文、报料等页面
                </span>
              </h3>
              <Form.Item label="跳转链接">
                {getFieldDecorator('comment_link_url', {
                  initialValue: props.record?.comment_link_url || '',
                  rules: [
                    {
                      pattern: /^https?:\/\//,
                      message: '请输入正确的跳转链接',
                    },
                  ],
                })(<Input placeholder="在所有内容评论区，点击可跳转的链接" />)}
              </Form.Item>
              <Form.Item label="图片" extra="支持.jpg .jpeg .png等格式，比例为 6.5 : 1">
                {getFieldDecorator('comment_pic_url', {
                  initialValue: props.record?.comment_pic_url || '',
                })(<ImageUploader ratio={6.5 / 1}></ImageUploader>)}
              </Form.Item>
            </>
          )}
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<any>({ name: 'CommentActivityConfigModal' })(CommentActivityConfigModal);
