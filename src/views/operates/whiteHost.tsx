import { getTableList } from '@action/tableList';
import { opApi as api } from '@app/api';
import { CommonObject } from '@app/types';
import { A, Table } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setLoading, setMenu } from '@utils/utils.tsx';
import { Button, Col, Divider, Form, Icon, Input, message, Modal, Row } from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';

@(withRouter as any)
@connect
class WhiteHostMgr extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      id: '',
      url: '',
      remark: '',
      visible: '',
      title: '',
      key: Date.now(),
      loading: false,
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    setMenu(this);
  }

  getData = (overlap: CommonObject = {}) => {
    const { current, size } = this.props.tableList;
    this.props.dispatch(
      getTableList('getWhiteHostList', 'white_list', { current, size, ...overlap })
    );
  };

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '域名',
        key: 'host',
        dataIndex: 'host',
      },
      {
        title: '备注',
        key: 'remark',
        dataIndex: 'remark',
        width: 350,
        render: (text: any, record: any) => <div style={{ width: 300 }} className='line-max'>{text}</div>
      },
      {
        title: '创建时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm')}</span>,
        width: 160,
      },
      {
        title: '状态',
        key: 'enabled',
        dataIndex: 'enabled',
        render: (text: boolean) => <span>{text ? '已启用' : '已停用'}</span>,
        width: 70,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <span>
            {requirePerm(
              this,
              'share_white_host:update'
            )(<A onClick={() => this.updateRecord(record)}>编辑</A>)}
            <Divider type="vertical" />
            {requirePerm(
              this,
              'share_white_host:update_state'
            )(<A onClick={() => this.updateState(record)}>{record.enabled ? '停用' : '启用'}</A>)}
            <Divider type="vertical" />
            {requirePerm(
              this,
              'share_white_host:delete'
            )(<A onClick={() => this.deleteRecord(record)}>删除</A>)}
          </span>
        ),
        width: 150,
      },
    ];
  };

  updateRecord = (record: any) => {
    this.setState({
      id: record.id || '',
      url: record.host || '',
      remark: record.remark || '',
      title: record.id ? '编辑域名' : '新增域名',
      visible: true,
      key: Date.now(),
    });
  };

  updateState = (record: any) => {
    setLoading(this, true);
    api
      .updateWhiteHostStatus({ id: record.id, enabled: !record.enabled })
      .then(() => {
        message.success('操作成功');
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  deleteRecord = (record: any) => {
    Modal.confirm({
      title: <p>确定删除吗？</p>,
      onOk: () => {
        setLoading(this, true);
        api
          .deleteWhiteHost({ id: record.id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };

  submit = () => {
    if (this.state.url === '') {
      message.error('请填写域名');
      return;
    }
    const body: any = { host: this.state.url, remark: this.state.remark };
    let func = 'createWhiteHost';
    if (this.state.id) {
      body.id = this.state.id;
      func = 'updateWhiteHost';
    }
    this.setState({ loading: true });
    api[func as keyof typeof api](body)
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setState({ loading: false, visible: false });
      })
      .catch(() => this.setState({ loading: false }));
  };

  render() {
    return (
      <React.Fragment>
        <Row className="layout-infobar">
          <Col span={12}>
            {requirePerm(
              this,
              'share_white_host:create'
            )(
              <Button onClick={() => this.updateRecord({})}>
                <Icon type="plus-circle" />
                新增域名
              </Button>
            )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getWhiteHostList"
            index="white_list"
            filter={{}}
            columns={this.getColumns()}
            rowKey="id"
            pagination={true}
          />
          <Modal
            visible={this.state.visible}
            title={this.state.title}
            confirmLoading={this.state.loading}
            onOk={this.submit}
            onCancel={() => this.setState({ visible: false })}
          >
            <Form.Item
              required={true}
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 18 }}
              label="域名"
            >
              <Input
                value={this.state.url}
                onChange={(e: any) => this.setState({ url: e.target.value })}
                placeholder="请输入域名"
              />
            </Form.Item>
            <Form.Item
              required={false}
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 18 }}
              label="备注"
            >
              <Input.TextArea
                maxLength={200}
                value={this.state.remark}
                onChange={(e: any) => this.setState({ remark: e.target.value })}
                placeholder="请输入备注，最多200字"
              />
            </Form.Item>
          </Modal>
        </div>
      </React.Fragment>
    );
  }
}

export default WhiteHostMgr;
