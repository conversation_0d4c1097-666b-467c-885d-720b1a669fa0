import React from 'react';
import { BaseComponent, Table, Drawer, A, CKEditor } from '@components/common';
import { setConfig } from '@app/action/config';
import { ITableProps, IBaseProps, CommonObject } from '@app/types';
import CreateFilmActivitiesForm from '@components/business/CreateFilmActivitiesForm';
import {
  Button,
  Col,
  Icon,
  Form,
  Input,
  Select,
  DatePicker,
  message,
  Modal,
  Row,
  Divider,
  Radio,
} from 'antd';
import { withRouter } from 'react-router';
import { connectTable as connect } from '@utils/connect';
import { RadioChangeEvent } from 'antd/es/radio';
import moment from 'moment';
import { opApi as api } from '@app/api';
import { cloneDeep } from 'lodash';
import { ProposalRecord, ProposalAllData } from './operates';

enum TABSTATE {
  ACTIVITY = 1,
  EXCHANGECODE,
  DETAILED,
  RULE,
}

type State = {
  type: TABSTATE;
  stateName: boolean;
  stateCode: boolean;
  titleActivi: string;
  formDrawer: {
    id?: number;
    activity_name: string;
    user_type: number;
    redeem_code?: number;
    taopp_old_id?: number;
    taopp_new_id?: number;
    maoy_old_id?: number;
    maoy_new_id?: number;
    activity_restrict_date: number;
    activity_restrict_count: number;
  };
  form: {
    show: boolean;
    key: number;
    id?: number;
    title: string;
  };
  searchActivity: {
    activity_name: string;
    redeem_code?: number | string;
    activity_type?: string;
  };
  searchCoupon: {
    activity_name: string;
    connect_no: string;
    coupon_state: string;
    begin: any;
    end: any;
  };
};

type Props = IBaseProps<ITableProps<ProposalRecord, ProposalAllData>>;

class MovieCouponsManager extends BaseComponent<
  ITableProps<ProposalRecord, ProposalAllData>,
  State
> {
  constructor(props: Props) {
    console.log(props);
    super(props);
    this.state = {
      type: TABSTATE.ACTIVITY,
      stateName: true,
      stateCode: true,
      titleActivi: '活动创建',
      formDrawer: {
        id: undefined,
        activity_name: '',
        user_type: 1,
        redeem_code: undefined,
        taopp_old_id: undefined,
        taopp_new_id: undefined,
        maoy_old_id: undefined,
        maoy_new_id: undefined,
        activity_restrict_date: 7,
        activity_restrict_count: 1,
      },
      form: {
        show: false,
        key: Date.now(),
        id: 0,
        title: '活动管理',
      },
      searchActivity: {
        activity_name: '',
        redeem_code: '',
        activity_type: '2',
      },
      searchCoupon: {
        activity_name: '',
        connect_no: '',
        coupon_state: '',
        begin: '',
        end: '',
      },
    };
    // eslint-disable-next-line react-hooks/rules-of-hooks
  }

  componentDidMount() {
    this.setMenu();
    this.getData1();
  }

  handleTypeChange = (e: RadioChangeEvent) => {
    if (e.target.value === this.state.type) {
      return;
    }
    this.setState(
      {
        type: e.target.value,
      },
      () => {
        // 切换
        if (e.target.value === TABSTATE.EXCHANGECODE) {
          this.getData();
        } else if (e.target.value === TABSTATE.ACTIVITY) {
          this.getData1();
        } else if (e.target.value === TABSTATE.DETAILED) {
          this.getData3();
        } else if (e.target.value === TABSTATE.RULE) {
          this.getData4();
        }
        console.log(e.target);
      }
    );
  };

  editRecord = (record: any = {}) => {
    const { type } = this.state;
    const keys = Object.keys(record);
    if (type === TABSTATE.EXCHANGECODE) {
      this.activityComponent(record);
      return;
    }
    if (type === TABSTATE.ACTIVITY) {
      // console.log(this.state.formDrawer);
      if (record.id) {
        this.setState({
          titleActivi: '活动编辑',
          formDrawer: record,
          form: {
            ...this.state.form,
            show: true,
          },
        });
      } else {
        this.setState({
          titleActivi: '活动创建',
          formDrawer: record,
          form: {
            ...this.state.form,
            show: true,
          },
        });
      }
    }
  };

  modifyState = (record: any = {}) => {
    this.props.dispatch(setConfig({ loading: true }));
    let { redeem_code_type } = record;
    const { id } = record;
    redeem_code_type = redeem_code_type === 0 ? 1 : 0;
    api
      .updateTypeMovieRedeemCode({ id, redeem_code_type })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  activityState(record: any = {}) {
    this.props.dispatch(setConfig({ loading: true }));
    let { activity_type } = record;
    const { id } = record;
    activity_type = activity_type === 0 ? 1 : 0;
    api
      .updateTypeMovieTicketActivity({ id, activity_type })
      .then(() => {
        message.success('操作成功');
        this.getData1(this.state.searchActivity);
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  }

  // 兑换码数据
  getData = (overlap: CommonObject = {}) => {
    // const { current, size } = this.props.tableList;
    this.dispatchTable('getMovieRedeemCodeList', 'code_list', {
      current: 1,
      size: 10,
      ...overlap,
    });
  };

  // 活动数据
  getData1 = (overlap: CommonObject = {}) => {
    // const { current, size } = this.props.tableList;
    const data = cloneDeep(overlap);
    if (data.activity_type === '2') {
      data.activity_type = undefined;
    }
    Object.keys(data).forEach((x) => {
      if (!data[x]) {
        delete data[x];
      }
    });
    this.dispatchTable('getMovieTicketActivityList', 'activity_list', {
      current: 1,
      size: 10,
      ...data,
    });
  };

  getData3 = (overlap: CommonObject = {}) => {
    // const { current, size } = this.props.tableList;
    const data: any = cloneDeep(this.state.searchCoupon);
    if (data.connect_no === '0') {
      data.connect_no = undefined;
    }
    if (data.coupon_state === '0') {
      data.coupon_state = undefined;
    }
    Object.keys(data).forEach((x: string) => {
      if (!data[x]) {
        delete data[x];
      }
    });
    this.dispatchTable('getMovieCouponUserList', 'coupon_list', {
      current: 1,
      size: 10,
      ...data,
      ...overlap,
    });
  };

  getData4 = (overlap: CommonObject = {}) => {
    // const { current, size } = this.props.tableList;
    this.dispatchTable('getMovieActivityRuleList', 'rule_list', {
      current: 1,
      size: 10,
      ...overlap,
    });
  };

  closeDrawer = () => {
    this.setState({
      form: { ...this.state.form, show: false },
    });
  };

  submitEnd = () => {
    this.closeDrawer();
    this.getData1();
  };

  // 活动组件
  activityComponent = (record: any) => {
    let conName = '创建兑换码';
    let { redeem_code, redeem_code_name } = record;
    if (redeem_code && redeem_code_name) {
      conName = '编辑兑换码';
    }
    const { id, redeem_code_type } = record;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      const name = v.target.value;
      console.log(name.length);
      if (name.length > 0 && name.length < 20) {
        this.setState({
          stateName: true,
        });
      } else {
        this.setState({
          stateName: false,
        });
      }
      redeem_code_name = v.target.value;
    };

    const positionChangeCode = (v: any) => {
      const code = v.target.value;
      if (code.length > 0 && code.length <= 6) {
        this.setState({
          stateCode: true,
        });
      } else {
        this.setState({
          stateCode: false,
        });
      }
      redeem_code = v.target.value;
    };

    Modal.confirm({
      title: <p>{conName}</p>,
      icon: <Icon type="info-circle" style={{ color: '#1890ff' }} />,
      width: 500,
      content: (
        <div>
          <Form>
            <Form.Item
              label="兑换码名称："
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 18 }}
              className={this.state.stateName ? '' : 'has-error'}
              required
            >
              <Input
                maxLength={20}
                placeholder="请输入兑换码名称，不超过20个汉字"
                defaultValue={redeem_code_name}
                onChange={positionChange}
              />
            </Form.Item>
            <Form.Item
              className={this.state.stateCode ? '' : 'has-error'}
              label="兑换码："
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 18 }}
              required
            >
              <Input
                type="text"
                placeholder="请输入兑换码码，由6位数字组成"
                maxLength={6}
                defaultValue={redeem_code}
                onChange={positionChangeCode}
              />
            </Form.Item>
          </Form>
        </div>
      ),
      onOk: (closeFunc: Function) => {
        this.props.dispatch(setConfig({ loading: true }));
        let name = false;
        let code = false;
        if (redeem_code_name) {
          name = true;
          this.setState({
            stateName: true,
          });
        } else {
          this.setState({
            stateName: false,
          });
        }
        if (redeem_code && redeem_code.toString().match(/^[0-9]*$/)) {
          code = true;
          this.setState({
            stateCode: true,
          });
        } else {
          this.setState({
            stateCode: false,
          });
        }

        if (name && code) {
          if (typeof id === 'number') {
            api
              .updateMovieRedeemCode({ redeem_code, redeem_code_name, id, redeem_code_type })
              .then(() => {
                message.success('操作成功');
                this.getData();
                setTimeout(() => {
                  this.props.dispatch(setConfig({ loading: false }));
                }, WAIT_TIME);
                closeFunc();
              })
              .catch(() => {
                this.props.dispatch(setConfig({ loading: false }));
              });
          } else {
            api
              .creatMovieRedeemCode({ redeem_code_name, redeem_code })
              .then(() => {
                message.success('操作成功');
                this.getData();
                setTimeout(() => {
                  this.props.dispatch(setConfig({ loading: false }));
                }, WAIT_TIME);
                closeFunc();
              })
              .catch(() => {
                this.props.dispatch(setConfig({ loading: false }));
              });
          }
        } else {
          this.props.dispatch(setConfig({ loading: false }));
          if (!code) {
            if (!redeem_code.match(/^[0-9]*$/)) {
              message.error('兑换码请输入数字');
            }
          } else {
            message.error('请输入正确的兑换码');
          }
        }
      },
    });
  };

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    console.log('????6666666');
  };

  // 规则组件
  RuleComponent = (record: any) => {
    console.log(record);
    const { page_number, page_name, id } = record;
    let { rule_content } = record;
    const WAIT_TIME = 1000;
    const positionChangeCode = (v: any) => {
      console.log(v);
      rule_content = v;
    };

    Modal.confirm({
      title: <p>活动规则</p>,
      icon: <Icon type="info-circle" style={{ color: '#1890ff' }} />,
      width: 1000,
      content: (
        <div>
          <Form onSubmit={this.handleSubmit}>
            <CKEditor value={rule_content} onChange={positionChangeCode} />
          </Form>
        </div>
      ),
      onOk: (closeFunc: Function) => {
        this.props.dispatch(setConfig({ loading: true }));
        api.updateMovieActivityRule({ id, page_number, rule_content, page_name }).then(() => {
          message.success('操作成功');
          this.getData4();
          setTimeout(() => {
            this.props.dispatch(setConfig({ loading: false }));
          }, WAIT_TIME);
          closeFunc();
        });
      },
    });
  };

  handleRangePickerChange = (dates: any) => {
    if (dates.length === 0) {
      this.setState({
        searchCoupon: { ...this.state.searchCoupon, begin: false, end: false },
      });
    } else {
      this.setState({
        searchCoupon: {
          ...this.state.searchCoupon,
          begin: dates[0].format('YYYY-MM-DD'),
          end: dates[1].format('YYYY-MM-DD'),
        },
      });
    }
  };

  inputSearchActivityName = (e: any) => {
    this.setState({
      searchActivity: { ...this.state.searchActivity, activity_name: e.target.value },
    });
  };

  inputSearchActivityCode = (e: any) => {
    this.setState({
      searchActivity: { ...this.state.searchActivity, redeem_code: e.target.value },
    });
  };

  selectSearchActivityType = (e: any) => {
    this.setState({
      searchActivity: { ...this.state.searchActivity, activity_type: e },
    });
  };

  inputSearchActivityNameConnect = (e: any) => {
    this.setState({
      searchCoupon: { ...this.state.searchCoupon, activity_name: e.target.value },
    });
  };

  selectSearchConnectNo = (e: any) => {
    this.setState({
      searchCoupon: { ...this.state.searchCoupon, connect_no: e },
    });
  };

  selectSearchCouponState = (e: any) => {
    this.setState({
      searchCoupon: { ...this.state.searchCoupon, coupon_state: e },
    });
  };

  getColumns = () => {
    const columns = [
      {
        title: '序号',
        key: 'id',
        render: (text: any, record: any, i: number) => <span>{i + 1}</span>,
        width: 50,
      },
      {
        title: '活动名称',
        key: 'activity_name',
        dataIndex: 'activity_name',
        width: 100,
      },
      {
        title: '面向用户',
        key: 'user_type',
        dataIndex: 'user_type',
        render: (t: number) => (
          <span>
            {t === 1 && '新用户'}
            {t === 2 && '老用户'}
            {t === 3 && '全部用户'}
          </span>
        ),
        width: 60,
      },
      {
        title: '兑换码',
        key: 'redeem_code',
        dataIndex: 'redeem_code',
        width: 80,
      },
      {
        title: '淘票票券池',
        key: 'taopp_new_id',
        dataIndex: 'taopp_new_id',
        render: (text: any, record: any) => (
          <span>
            {record.taopp_new_id && <p>【新】{record.taopp_new_id}</p>}
            {record.taopp_old_id && <p>【老】{record.taopp_old_id}</p>}
          </span>
        ),
        width: 120,
      },
      {
        title: '猫眼券池',
        key: 'maoy_new_id',
        dataIndex: 'maoy_new_id',
        render: (text: any, record: any) => (
          <span>
            {record.maoy_new_id && <p>【新】{record.maoy_new_id}</p>}
            {record.maoy_old_id && <p>【老】{record.maoy_old_id}</p>}
          </span>
        ),
        width: 120,
      },
      {
        title: '活动限制',
        key: 'activity_restrict_date',
        render: (text: any, record: any) => (
          <span>
            {record.activity_restrict_date} 天 {record.activity_restrict_count} 次
          </span>
        ),
        width: 90,
      },
      {
        title: '创建人',
        key: 'creator',
        dataIndex: 'creator',
        width: 90,
      },
      {
        title: '创建时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 120,
      },
      {
        title: '状态',
        key: 'activity_type',
        dataIndex: 'activity_type',
        render: (text: number) => <span>{text === 0 ? '已下架' : '已上架'}</span>,
        width: 60,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <span>
            {this.requirePerm(`movie_ticket_activity:update_displayed`)(
              <A onClick={() => this.activityState(record)}>
                {record.activity_type ? '下架' : '上架'}
              </A>
            )}
            <Divider type="vertical" />
            {this.requirePerm(`movie_ticket_activity:update${record.activity_type ? '1' : ''}`)(
              <A onClick={() => this.editRecord(record)}>编辑</A>
            )}
          </span>
        ),
        width: 90,
      },
    ];
    return columns;
  };

  getColumns2 = () => {
    const columns = [
      {
        title: '序号',
        key: 'id',
        dataIndex: 'id',
        render: (text: any, record: any, i: number) => <span>{i + 1}</span>,
        width: 70,
      },
      {
        title: '兑换码名称',
        key: 'redeem_code_name',
        dataIndex: 'redeem_code_name',
        width: 150,
      },
      {
        title: '兑换码',
        key: 'redeem_code',
        dataIndex: 'redeem_code',
        width: 90,
      },
      {
        title: '创建人',
        key: 'creator',
        dataIndex: 'creator',
        width: 90,
      },
      {
        title: '创建时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 95,
      },
      {
        title: '状态',
        key: 'redeem_code_type',
        dataIndex: 'redeem_code_type',
        render: (text: number) => <span>{text === 0 ? '已下架' : '已上架'}</span>,
        width: 80,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <span>
            {this.requirePerm(`movie_redeem_code:update_displayed`)(
              <A onClick={() => this.modifyState(record)}>
                {record.redeem_code_type ? '下架' : '上架'}
              </A>
            )}
            <Divider type="vertical" />
            {this.requirePerm(`movie_redeem_code:update${record.redeem_code_type ? '1' : ''}`)(
              <A onClick={() => this.editRecord(record)}>编辑</A>
            )}
          </span>
        ),
        width: 70,
      },
    ];
    return columns;
  };

  getColumns3 = () => {
    const columns = [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{i + 1}</span>,
        width: 50,
      },
      {
        title: '用户昵称',
        key: 'nick_name',
        dataIndex: 'nick_name',
        width: 120,
      },
      {
        title: '活动名称',
        key: 'activity_name',
        dataIndex: 'activity_name',
        width: 120,
      },
      {
        title: '领券渠道',
        key: 'connect_no',
        dataIndex: 'connect_no',
        render: (text: number) => <span>{text === 1 ? '淘票票' : '猫眼'}</span>,
        width: 60,
      },
      {
        title: '消费券码',
        key: 'coupon_code',
        dataIndex: 'coupon_code',
        width: 90,
      },
      {
        title: '券面额',
        key: 'coupon_money',
        dataIndex: 'coupon_money',
        width: 60,
      },
      {
        title: '核销金额',
        key: 'coupon_verify_money',
        dataIndex: 'coupon_verify_money',
        width: 60,
      },
      {
        title: '券开始时间',
        key: 'coupon_begin_time',
        dataIndex: 'coupon_begin_time',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 120,
      },
      {
        title: '券结束时间',
        key: 'coupon_end_time',
        dataIndex: 'coupon_end_time',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 120,
      },
      {
        title: '更新时间',
        key: 'updated_at',
        dataIndex: 'updated_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 120,
      },
      {
        title: '状态',
        key: 'coupon_state',
        dataIndex: 'coupon_state',
        render: (text: number) => (
          <span>
            {text === 1 && '已领取'}
            {text === 2 && '已使用'}
            {text === 3 && '退票退券'}
          </span>
        ),
        width: 80,
      },
    ];
    return columns;
  };

  getColumns4 = () => {
    const columns = [
      {
        title: '页面名称',
        key: 'page_name',
        dataIndex: 'page_name',
        width: 150,
      },
      {
        title: '活动规则',
        key: 'rule_content',
        dataIndex: 'rule_content',
        render: (text: any) => <span dangerouslySetInnerHTML={{ __html: text }} />,
      },
      {
        title: '操作',
        key: 'id3',
        render: (text: any, record: any) => (
          <span>
            {this.requirePerm(`movie_activity_rule:update`)(
              <A onClick={() => this.RuleComponent(record)}>编辑</A>
            )}
          </span>
        ),
        width: 100,
      },
    ];
    return columns;
  };

  filters = (Obj: any) => {
    const data = cloneDeep(Obj);
    if (data.activity_type === '2') {
      data.activity_type = undefined;
    }
    if (data.connect_no === '0') {
      data.connect_no = undefined;
    }
    if (data.coupon_state === '0') {
      data.coupon_state = undefined;
    }
    Object.keys(data).forEach((x) => {
      if (!data[x]) {
        delete data[x];
      }
    });
    return data;
  };

  TabContentDom = (tab: number) => {
    const { type, searchActivity, searchCoupon } = this.state;
    const TabDom1 = (
      <>
        <Form labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
          <Row justify="start">
            <Col span={6}>
              <Form.Item label="活动名称">
                <Input
                  placeholder="请输入活动全称，或关键词"
                  value={searchActivity.activity_name}
                  onChange={this.inputSearchActivityName}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="兑换码">
                <Input
                  placeholder="请输入完整兑换码"
                  value={searchActivity.redeem_code}
                  onChange={this.inputSearchActivityCode}
                />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item label="状态">
                <Select
                  defaultValue="2"
                  value={searchActivity.activity_type}
                  onChange={this.selectSearchActivityType}
                >
                  <Select.Option value="2">全部</Select.Option>
                  <Select.Option value="1">已上架</Select.Option>
                  <Select.Option value="0">已下架</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={2} offset={6}>
              <Form.Item>
                <Button type="primary" onClick={() => this.getData1(this.state.searchActivity)}>
                  查找
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <Table
          func="getMovieTicketActivityList"
          index="activity_list"
          pagination={true}
          filter={this.filters(this.state.searchActivity)}
          rowKey="id"
          columns={this.getColumns()}
        />
      </>
    );

    const TabDom2 = (
      <>
        <Table
          func="getMovieRedeemCodeList"
          index="code_list"
          pagination={true}
          filter={{}}
          rowKey="id"
          columns={this.getColumns2()}
        />
      </>
    );

    const TabDom3 = (
      <>
        <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
          <Row justify="start">
            <Col span={6}>
              <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }}>
                <DatePicker.RangePicker
                  format="YYYY-MM-DD"
                  onChange={this.handleRangePickerChange}
                  value={
                    searchCoupon.begin ? [moment(searchCoupon.begin), moment(searchCoupon.end)] : []
                  }
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="活动名称">
                <Input
                  placeholder="请输入活动全称，或关键词"
                  value={searchCoupon.activity_name}
                  onChange={this.inputSearchActivityNameConnect}
                />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item label="领券渠道" labelCol={{ span: 10 }} wrapperCol={{ span: 14 }}>
                <Select
                  defaultValue="0"
                  value={searchCoupon.connect_no}
                  onChange={this.selectSearchConnectNo}
                >
                  <Select.Option value="0">全部</Select.Option>
                  <Select.Option value="1">淘票票</Select.Option>
                  <Select.Option value="2">猫眼</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item label="状态">
                <Select
                  defaultValue="0"
                  value={searchCoupon.coupon_state}
                  onChange={this.selectSearchCouponState}
                >
                  <Select.Option value="0">全部</Select.Option>
                  <Select.Option value="1">已领取</Select.Option>
                  <Select.Option value="2">已使用</Select.Option>
                  <Select.Option value="3">退票</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={2} offset={2}>
              <Form.Item>
                <Button type="primary" onClick={() => this.getData3()}>
                  查找
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <Table
          func="getMovieCouponUserList"
          index="coupon_list"
          pagination={true}
          filter={this.filters(this.state.searchCoupon)}
          rowKey="id"
          columns={this.getColumns3()}
        />
      </>
    );

    const TabDom4 = (
      <>
        <Table
          func="getMovieActivityRuleList"
          index="rule_list"
          pagination={true}
          filter={{ type }}
          rowKey="id"
          columns={this.getColumns4()}
        />
      </>
    );

    if (tab === 1) {
      return TabDom1;
    }
    if (tab === 2) {
      return TabDom2;
    }
    if (tab === 3) {
      return TabDom3;
    }
    return TabDom4;
  };

  render() {
    const { type, form, formDrawer } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            <Radio.Group
              value={type}
              style={{ marginRight: 8 }}
              onChange={this.handleTypeChange}
              buttonStyle="solid"
            >
              <Radio.Button value={TABSTATE.ACTIVITY}>活动管理</Radio.Button>
              <Radio.Button value={TABSTATE.EXCHANGECODE}>兑换码管理</Radio.Button>
              <Radio.Button value={TABSTATE.DETAILED}>消费券领取明细</Radio.Button>
              <Radio.Button value={TABSTATE.RULE}>活动规则</Radio.Button>
            </Radio.Group>
            {type === TABSTATE.ACTIVITY &&
              this.requirePerm(`movie_ticket_activity:create`)(
                <Button onClick={this.editRecord}>
                  <Icon type="plus-circle" />
                  创建
                </Button>
              )}
            {type === TABSTATE.EXCHANGECODE &&
              this.requirePerm(`movie_redeem_code:create`)(
                <Button onClick={this.editRecord}>
                  <Icon type="plus-circle" />
                  创建
                </Button>
              )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {this.getCrumb()}
          </Col>
        </Row>
        <div className="component-content news-pages">{this.TabContentDom(type)}</div>
        <Drawer
          visible={form.show}
          skey={form.key}
          title={this.state.titleActivi}
          onClose={this.closeDrawer}
          onOk={this.handleSubmitForm.bind(this, 'form')}
        >
          <CreateFilmActivitiesForm
            formContent={formDrawer}
            wrappedComponentRef={this.setFormRef.bind(this, 'form')}
            onEnd={this.submitEnd}
          />
        </Drawer>
      </>
    );
  }
}

export default withRouter(connect<ProposalRecord, ProposalAllData>()(MovieCouponsManager));
