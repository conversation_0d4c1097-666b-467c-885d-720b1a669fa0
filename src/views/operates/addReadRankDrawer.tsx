import {
  But<PERSON>,
  Col,
  DatePicker,
  Form,
  Icon,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Radio,
  Select,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { opApi } from '@app/api';
import { A, Drawer, ImageUploader } from '@app/components/common';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import ImgUploadList from '@app/components/common/imgUploadList';
import moment from 'moment';
import uuid from 'uuid';
import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import { getImageRatio } from '@app/utils/utils';
import { range } from 'lodash';

const AddReadRankDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();

  const [page_list, setPageList] = useState<any>([]);
  const [recordInfo, setRecordInfo] = useState<any>({
    visible: false,
    titleName: '',
    key: '',
    pid: '',
    index: '',
    value: '',
    sizeMax: 40,
  });

  const { getFieldDecorator, getFieldsValue, setFieldsValue, getFieldValue } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  // 初始化页面数据
  const intPageList = () => {
    if (props.record) {
      return (
        props.record.book_list?.map((group: any) => {
          const pid = uuid();
          return {
            pid,
            ...group,
          };
        }) || []
      );
    } else {
      return [
        {
          pid: uuid(),
          book_name: '',
          cover_url: '',
          author: '',
          press_company: '',
          introduce: '',
          link_url: '',
        },
      ];
    }
  };

  useEffect(() => {
    setPageList(intPageList());
  }, [props.visible]);

  useEffect(() => {
    const { page_list: arr } = getFieldsValue();
    if (arr) {
      setTimeout(() => {
        setFieldsValue({ page_list: page_list });
      }, 0);
    }
  }, [page_list]);

  const findPageWithPID = (list: any, id: any): any => {
    return list?.filter((item: any) => item.pid == id)?.[0];
  };

  const handleSubmit = () => {
    // e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        dispatch(setConfig({ mLoading: true }));
        let promise = null;
        const parmas: any = {
          ref_ids2: values.ref_ids2?.trim() || '',
          ref_ids3: values.ref_ids3?.trim() || '',
          ref_extensions: JSON.stringify(values.page_list),
        };
        parmas.type = values.type;
        if (values.type == 43) {
          parmas.ref_ids = values.month.format('YYYY-MM');
        } else {
          parmas.ref_ids = values.year;
        }

        if (!props.record) {
          // 新增
          promise = opApi.addBookRank(parmas);
        } else {
          parmas.id = props.record.id;
          promise = opApi.updateBookRank(parmas);
        }
        promise
          .then((res: any) => {
            message.success(!props.record ? '新增成功' : '更新成功');
            dispatch(setConfig({ mLoading: false }));
            props.onEnd && props.onEnd();
          })
          .catch(() => {
            // message.error('添加失败');
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
        dispatch(setConfig({ mLoading: false }));
      }
    });
  };

  const delectList = (id: any) => {
    const { page_list: arr } = getFieldsValue();
    const arrayConment = arr.filter((item: { pid: any }) => {
      return item.pid !== id;
    });
    setTimeout(() => {
      // console.log(arrayConment)
      setFieldsValue({ page_list: arrayConment });
      // console.log(getFieldsValue())
    }, 0);
    setPageList(arrayConment);
  };

  const handleMove = (data: {}, index: number) => {
    let position = index + 1;
    Modal.confirm({
      width: 250,
      content: (
        <>
          位置:{' '}
          <InputNumber
            min={1}
            max={page_list.length}
            defaultValue={position}
            onChange={(e: any) => {
              position = e;
            }}
          />
        </>
      ),
      icon: null,
      onOk() {
        const { page_list: arr } = getFieldsValue();
        let item = arr[index];
        let newArray = [...arr];
        newArray.splice(index, 1);
        newArray.splice(position - 1, 0, item);
        setPageList(newArray);
      },
      onCancel() {},
    });
  };

  const addGroup = () => {
    const { page_list: articleList = [] } = getFieldsValue();
    const result = [
      ...articleList,
      {
        pid: uuid(),
        book_name: '',
        cover_url: '',
        author: '',
        press_company: '',
        introduce: '',
        link_url: '',
      },
    ];
    setPageList(result);
  };

  const month = props.record?.type != 48 ? props.record?.ref_ids : '';
  const year = props.record?.type == 48 ? props.record?.ref_ids : '';
  return (
    <Drawer
      title={!props.record ? '添加榜单' : '编辑榜单'}
      visible={props.visible}
      skey={props.skey}
      onClose={props.onClose}
      maskClosable={false}
      // width={500}
      onOk={handleSubmit}
      okText="保存"
    >
      <Form {...formLayout}>
        <Form.Item label="类型">
          {getFieldDecorator('type', {
            initialValue: `${props.record?.type || 43}`,
            rules: [
              {
                required: true,
                message: '请选择类型',
              },
            ],
          })(
            <Radio.Group disabled={!!props.record}>
              <Radio value={'43'}>月榜</Radio>
              <Radio value={'48'}>半年榜</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {getFieldValue('type') == 43 && (
          <Form.Item label="月份">
            {getFieldDecorator('month', {
              initialValue: props.record ? moment(`${month}`, 'YYYY-MM') : undefined,
              rules: [
                {
                  required: true,
                  message: '请选择月份',
                },
              ],
            })(<DatePicker.MonthPicker style={{ width: '100%' }} />)}
          </Form.Item>
        )}

        {getFieldValue('type') == 48 && (
          <Form.Item label="年份">
            {getFieldDecorator('year', {
              initialValue: `${year}` || undefined,
              rules: [
                {
                  required: true,
                  message: '请选择年份',
                },
              ],
            })(
              <Select placeholder="请选择年份">
                {range(2024, 2034).map((item: any) => (
                  <Select.Option key={item} value={`${item}`}>
                    {item}
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>
        )}

        {page_list.map((item: any, index: number) => {
          return (
            <div key={item.pid + `${index}`}>
              <Form.Item label="id" style={{ display: 'none' }}>
                {getFieldDecorator(`page_list[${index}].pid`, {
                  initialValue: item.pid,
                })(<Input disabled />)}
              </Form.Item>
              <Form.Item>
                <h3 style={{ marginLeft: 50 }}>书本{index + 1}</h3>
              </Form.Item>

              <Form.Item label="书名">
                {getFieldDecorator(`page_list[${index}].book_name`, {
                  initialValue: item.book_name,
                  validateTrigger: 'onChange',
                  rules: [
                    {
                      required: true,
                      message: '请输入书名',
                      whitespace: true,
                    },
                  ],
                })(<Input placeholder="最多输入20字" maxLength={20} />)}
                {page_list.length > 1 && (
                  <div
                    style={{
                      display: 'flex',
                      position: 'absolute',
                      width: 60,
                      top: '-12px',
                      right: '-60px',
                    }}
                  >
                    <div
                      style={{
                        height: '40px',
                        lineHeight: '40px',
                        width: '40px',
                        textAlign: 'center',
                        cursor: 'pointer',
                      }}
                    >
                      <Icon
                        type="swap"
                        style={{ transform: 'rotate(90deg)' }}
                        onClick={() => {
                          handleMove(item, index);
                        }}
                      />
                    </div>

                    <div
                      onClick={() => {}}
                      style={{
                        height: '40px',
                        lineHeight: '40px',
                        width: '40px',
                        textAlign: 'center',
                        cursor: 'pointer',
                      }}
                    >
                      <Popconfirm
                        placement="top"
                        title="确定要删除吗？"
                        okText="确定"
                        cancelText="取消"
                        icon={<Icon type="exclamation-circle" theme="twoTone" twoToneColor="red" />}
                        onConfirm={() => {
                          delectList(item.pid);
                        }}
                      >
                        <Icon type="delete" />
                      </Popconfirm>
                    </div>
                  </div>
                )}
              </Form.Item>
              <Form.Item label="封面图" extra="支持.jpg .jpeg .png等格式，比例为 105:142">
                {getFieldDecorator(`page_list[${index}].cover_url`, {
                  initialValue: item.cover_url,
                  rules: [
                    {
                      required: true,
                      message: '请上传封面图',
                    },
                  ],
                })(<ImageUploader ratio={105 / 142}></ImageUploader>)}
              </Form.Item>
              <Form.Item label="作者信息">
                {getFieldDecorator(`page_list[${index}].author`, {
                  initialValue: item.author,
                  rules: [
                    {
                      required: true,
                      message: '请输入作者信息',
                      whitespace: true,
                    },
                    {
                      max: 20,
                      message: '最多输入20字',
                    },
                  ],
                })(<Input placeholder="请输入最多作者/译者/校对人员信息，最多输入20字"></Input>)}
              </Form.Item>
              <Form.Item label="出版社">
                {getFieldDecorator(`page_list[${index}].press_company`, {
                  initialValue: item.press_company,
                  rules: [
                    {
                      required: true,
                      message: '请输入出版社信息',
                      whitespace: true,
                    },
                    {
                      max: 20,
                      message: '最多输入20字',
                    },
                  ],
                })(<Input placeholder="请输入出版社信息，最多输入20字"></Input>)}
              </Form.Item>
              <Form.Item label="推荐介绍">
                {getFieldDecorator(`page_list[${index}].introduce`, {
                  initialValue: item.introduce,
                  rules: [
                    {
                      max: 30,
                      message: '最多输入30字',
                    },
                  ],
                })(<Input placeholder="最多输入30字"></Input>)}
              </Form.Item>
              <Form.Item label="购买链接">
                {getFieldDecorator(`page_list[${index}].link_url`, {
                  initialValue: item.link_url,
                  rules: [
                    {
                      validator: (rule: any, value: any, callback: any) => {
                        const regex = /^https?:\/\//;
                        if (value.length > 0 && !regex.test(value)) {
                          callback('请正确填写关联链接');
                          return;
                        }
                        callback();
                      },
                    },
                  ],
                })(<Input placeholder="请输入购买链接"></Input>)}
              </Form.Item>
            </div>
          );
        })}

        {page_list.length < 20 && (
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 20,
            }}
          >
            <Button style={{ width: 500, marginRight: 8 }} onClick={addGroup}>
              <Icon type="plus-circle-o" />
              添加书本
            </Button>
            <Tooltip title="最多添加20本书">
              <Icon type="question-circle" />
            </Tooltip>
          </div>
        )}
        <p style={{ height: '40px', lineHeight: '40px', fontWeight: 600 }}>分享信息</p>
        <Form.Item label="分享标题">
          {getFieldDecorator('ref_ids2', {
            initialValue: props.record?.ref_ids2 || '',
            rules: [
              {
                required: false,
                message: '请填写分享标题',
              },
            ],
          })(
            <Input placeholder="最多24字，如不填写，默认显示「潮新闻春风悦读榜」" maxLength={24} />
          )}
        </Form.Item>
        <Form.Item label="分享副标题">
          {getFieldDecorator('ref_ids3', {
            initialValue: props.record?.ref_ids3 || '',
            rules: [
              {
                required: false,
                message: '请填写分享副标题',
              },
            ],
          })(
            <Input placeholder="最多30字，如不填写，默认显示「来自潮新闻客户端」" maxLength={30} />
          )}
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'AddReadRankDrawer' })(
  forwardRef<any, any>(AddReadRankDrawer)
);
