import { setConfig } from '@action/config';
import { opApi as api } from '@app/api';
import { CKEditor, ImageUploader } from '@components/common';
import connect from '@utils/connectSession';
import { getCrumb, requirePerm } from '@utils/utils';
import { Button, Col, Form, Input, message, Row } from 'antd';
import React from 'react';
import { withRouter } from 'react-router';

@(Form.create({ name: 'aboutManagerForm' }) as any)
class AboutManagerForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = { ...props.formContent };
  }

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        this.props.setLoading(true);
        api
          .updateAboutDetail({ ...values, qr_code: values.qr_code_url })
          .then(() => {
            message.success('操作成功');
            this.props.onEnd();
          })
          .catch(() => this.props.setLoading(false));
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit} ref="form">
        {/* <Form.Item label="官网">
          {getFieldDecorator('official_web_url', {
            initialValue: this.state.official_web_url,
            rules: [
              {
                required: true,
                message: '必须输入官网',
              },
              {
                pattern: /^https?:\/\//,
                message: '官网链接格式不正确',
              },
            ],
          })(<Input placeholder="请输入官网" />)}
        </Form.Item> */}
        <Form.Item label="二维码" extra="图片大小：152*152">
          {getFieldDecorator('qr_code_url', {
            initialValue: this.state.qr_code_url,
            rules: [
              {
                required: true,
                message: '必须上传二维码',
              },
            ],
          })(<ImageUploader ratio={1 / 1} />)}
        </Form.Item>
        <Form.Item label="版权声明">
          {getFieldDecorator('copyright_notice', {
            initialValue: this.state.copyright_notice,
            rules: [
              {
                required: true,
                message: '必须输入版权声明',
              },
            ],
          })(<CKEditor />)}
        </Form.Item>
      </Form>
    );
  }
}

@(withRouter as any)
@connect
class AboutManager extends React.Component<any, any> {
  formRef: any;

  constructor(props: any) {
    super(props);
    this.state = {
      form: {
        official_web_url: '',
        qr_code_url: '',
        copyright_notice: '',
      },
      key: Date.now(),
    };
  }

  componentDidMount() {
    this.getData();
    this.props.dispatch(
      setConfig({
        selectKeys: this.props.selectKeys,
        openKeys: this.props.openKeys,
      })
    );
  }

  getData = () => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .getAboutDetail()
      .then((r: any) => {
        setTimeout(() => {
          this.setState({
            form: { ...r.data.about },
            key: Date.now(),
          });
          this.props.dispatch(setConfig({ loading: false }));
        }, 1000);
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  handleSubmit = () => {
    this.formRef.doSubmit();
  };

  handleSubmitEnd = () => {
    this.getData();
  };

  setLoading = (loading: boolean) => {
    this.props.dispatch(setConfig({ loading }));
  };

  render() {
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            {requirePerm(
              this,
              'about:create'
            )(
              <Button type="primary" onClick={this.handleSubmit}>
                保存
              </Button>
            )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <AboutManagerForm
            formContent={this.state.form}
            key={this.state.key}
            onEnd={this.handleSubmitEnd}
            wrappedComponentRef={(instance: any) => (this.formRef = instance)}
            setLoading={this.setLoading}
          />
        </div>
      </>
    );
  }
}

export default AboutManager;
