import React, {useEffect, useState, useRef} from 'react';
import {useDispatch, useSelector, useStore} from 'react-redux';
import moment from 'moment';
import {opApi, opApi as api} from "@app/api";
import { getTableList } from '@action/tableList';
import {getCrumb, setMenuHook} from '@utils/utils';
import {
    Button,
    Col,
    Divider,
    Icon,
    Row,
    Popconfirm,
    message,
    Input, Form, Tooltip, Modal,
} from 'antd';
import { Table, Drawer} from '@components/common';
import {PermA, PermButton} from "@components/permItems";
import HomeRemindForm from '@components/business/homeRemindForm';
import {useHistory} from "react-router";

const HomeRemind: React.FC<{ selectKeys: string; openKeys: string; breadCrumb: any }> = (
    props
) => {
    const dispatch = useDispatch();
    const history = useHistory()
    const {  current, size, } = useSelector((state: any) => state.tableList)
    const formRef = useRef({} as any);
    // 筛选条件
    const [param, setParam] = useState({
        current: 1,
        keyword: '',
        status: '',
        type: 0,
    });
    const [keyword,setKeyword] = useState('')

    const [drawer, setDrawer] = useState({
        visible: false,
        key: Date.now(),
        type: 'create',
    });
    const [formContent, setFormContent] = useState(null)
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    const getColumns = () => {
        return [
            {
                title: '序号',
                key: 'order',
                render(text: any, record: any, i: number) {
                    return getSeq(i);
                },
                width: 60
            },
            {
                title: '主题ID',
                dataIndex: 'theme_id',
                key: 'theme_id',
                width: 100,
            },
            {
                title: '主题名称',
                key: 'theme_name',
                dataIndex: 'theme_name',
            },
            {
                title: '提醒图',
                key: 'top_img_url',
                dataIndex: 'top_img_url',
                width: 120,
                render: (text: any, record: any) => (<div style={{ height: 60, textAlign: 'left' }}>
                    <img src={text} className='list-pic' style={{ height: '100%', }}></img>
                </div>)
            },
            {
                title: '状态',
                key: 'status',
                dataIndex: 'status',
                width:80,
                render: (text: any, record: any) => (<span>{['下架','上架'][text]}</span>)
            },
            {
                title: '最后修改时间',
                dataIndex: 'updated_at',
                render: (text: number) => <span
                >{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
                width: 160
            },
            {
                title: '最后操作人',
                key: 'updated_by',
                dataIndex: 'updated_by',
                width: 200,
            },
            {
                title: '操作',
                dataIndex: 'date12',
                width: 140,
                render(text: any, record: any) {
                    return (
                        <>
                            <PermA perm="app:theme:remind:edit"
                                   onClick={() => { editHomeRemind(record) }}
                            >编辑</PermA>
                            <Divider type="vertical" />
                            <PermA perm="app:theme:remind:change_status"
                                   onClick={() => { changeStatus(record) }}
                            >{['上架', '下架'][record.status]}</PermA>
                            <Divider type="vertical" />
                            <PermA perm="app:theme:remind:delete" >
                                <Popconfirm
                                    placement="left"
                                    title="确定删除该提醒？"
                                    okText="确定"
                                    cancelText="取消"
                                    icon={<Icon type="exclamation-circle" theme="twoTone" twoToneColor="red" />}
                                    onConfirm={() => { handleDelete(record) }}
                                >删除
                                </Popconfirm>
                            </PermA>
                        </>
                    );
                },
            },
        ];
    };

    const getData = () => {
        dispatch(getTableList('getThemeRemindList', 'list', {...param,current, size }));
    };
    const backToTopic = () => {
        history.go(-1);
    };
    const handleDelete = (record: any) => {
        opApi.deleteThemeRemind({id: record.id}).then(() => {
            message.success('操作成功');
            getData();
        })
    };
    // 初始化
    useEffect(() => {
        setMenuHook(dispatch, props);
        getData();
    }, [param]);

    const changeStatus = (record: any) => {
        const reqData = {
            id: record.id,
            status: record.status === 1 ? 0 : 1,
        }
        Modal.confirm({
            title: record.status == 0 ? '该提醒将面向所有用户显示，确定上架？' : '确定下架该提醒？',
            content: (
                <>
                    {record.status ? '' : <div>（如果此时已开启全局主题，用户自选主题将无法生效，请勿配置提醒）</div>}
                </>
            ),
            onOk: (closeFunc: Function) => {
                api.updateThemeRemindStatus(reqData).then((res) => {
                    message.success('操作成功');
                    closeFunc()
                    getData()
                })
            },
        });
    }
    const editHomeRemind = (record:any) =>{
            setFormContent(record)
            setDrawer({
                visible: true,
                key: Date.now(),
                type: 'edit'
            })
    }


    const getFilter = () => {
        return param;
    };
    const handleTypeChange = (e: any) =>{
        setParam({
            ...param,
            type: e.target.value
        })
    }
    const keywordChange = (event:any) => {
        const value = event.target.value;
        setKeyword(value);
    };

    const keywordSearch = (e: any) => {
        setParam({
            ...param,
            keyword,
        })
    };
    const onSubmitEnd = () => {
        setDrawer({ ...drawer, visible: false });
    };
    return (
        <>
            <Row className="layout-infobar">
                <Col span={12}>
                    <Button onClick={backToTopic} style={{ marginRight: 8 }}>
                        <Icon type="left-circle-o" />
                        主题管理
                    </Button>
                    <PermButton
                        perm="app:theme:remind:add"
                        onClick={() => {
                            setFormContent(null)
                            setDrawer({ ...drawer, visible: true, type: 'create' });
                        }}
                        style={{ marginRight: 8 }}
                    >
                        <Icon type="plus-circle" />添加提醒
                    </PermButton>
                    <Tooltip
                        overlayStyle={{ maxWidth: 390 }}
                        title={<div>
                        功能说明：<br />
                        1、新主题提醒，仅适用于用户自选主题，请先添加主题并上架后，再配置提醒
                        <br/>
                        2、开启全局主题期间，请勿配置新主题提醒；因为全局主题的优先级高于用户自选主题，用户设置了也无法显示。
                    </div>}
                             placement="top"
                    >
                        <Icon style={{ marginLeft: 8 }} type="question-circle" />
                    </Tooltip>
                </Col>
                <Col span={12} className="layout-breadcrumb">
                    {getCrumb(props.breadCrumb)}
                </Col>
            </Row>
            <div className="component-content news-pages">
                <Row style={{ marginBottom: 16 }} type="flex" align="middle">
                    <Col span={14} >
                    </Col>
                    <Col span={10} style={{ textAlign: 'right' }}>
                        <Input
                            placeholder="按主题名称搜索"
                            style={{ width: 150 }}
                            onChange={keywordChange}
                        />
                        <Button
                            style={{ marginLeft: 8, verticalAlign: 'top' }}
                            onClick={keywordSearch}
                        >
                            <Icon type="search" />
                            搜索
                        </Button>
                    </Col>
                </Row>
                <Table
                    func="getThemeRemindList"
                    index="list"
                    pagination={true}
                    rowKey="auto_pk"
                    columns={getColumns()}
                    filter={getFilter()}
                />
                <Drawer
                    visible={drawer.visible}
                    skey={drawer.key}
                    title={drawer.type === 'create' ? '添加提醒' : '编辑提醒'}
                    onClose={() => {
                        setDrawer({ ...drawer, visible: false })
                    }}
                    onOk={() => {formRef.current.doSubmit()}}
                >
                    <HomeRemindForm
                        type={param.type}
                        drawer={drawer}
                        onEnd={onSubmitEnd}
                        formContent={formContent}
                        wrappedComponentRef={(instance: any) => (formRef.current = instance)}
                        getData={() => { getData() }}
                    />
                </Drawer>
            </div>
        </>
    );
};

export default Form.create({})(HomeRemind);
