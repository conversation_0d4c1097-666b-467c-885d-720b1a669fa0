/* eslint-disable no-return-assign */
/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useStore, useSelector } from 'react-redux';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { A, PreviewMCN, SearchAndInput, Table, ImageUploader, Drawer } from '@components/common';
import Form from '@components/business/arSceneForm';
import { getCrumb, setMenuHook } from '@app/utils/utils';
import {
  Row,
  Col,
  Button,
  message,
  Icon,
  Modal,
  Select,
  Tooltip,
  InputNumber,
  Radio,
  Divider,
  Input,
} from 'antd';
import { getTableList, clearTableList } from '@app/action/tableList';
import { opApi as api } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@app/components/permItems';
import moment from 'moment';

export default function ArSceneManager(props: any) {
  const [form, setForm] = useState(
    () =>
      ({
        visible: false,
        key: Date.now(),
      } as any)
  );
  const dispatch = useDispatch();

  const { loading, run } = useXHR();

  const formRef = useRef({} as any);

  const fullRecords = useSelector((state: any) => state.tableList.records);

  const getList = () => {
    dispatch(getTableList('getArSceneList', 'ar_list', {}));
  };

  const editRecord = (record: any = {}) => {
    if (record.ar_id) {
      run(api.getArSceneDetail, { ar_id: record.ar_id }).then((res: any) => {
        setForm({
          visible: true,
          key: Date.now(),
          isEdit: true,
          ar_id: record.ar_id,
          identify_method: res.data.ar_content.identify_method || 1,
          title: res.data.ar_content.title || '',
          json: JSON.parse(res.data.ar_content.json).map((v: any) => ({
            event_id: v.event_id.toString(),
            name: v.name,
            jump_url: v.jump_url || '',
            ar_content_type: v.ar_content_type || 0,
            share_title: v.share_title || '',
            share_info: v.share_info || '',
            share_image_url: v.share_image_url || '',
            share_url: v.share_url || '',
          })),
          arIds: [],
        });
      });
    } else {
      setForm({
        visible: true,
        key: Date.now(),
        isEdit: false,
        arIds: fullRecords.map((v: any) => (v.ar_id ? v.ar_id.toString() : '')),
      });
    }
  };

  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: '确定删除吗',
      onOk: () => {
        run(api.deleteArScene, { ar_id: record.ar_id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const updateStatus = (record: any) => {
    run(api.updateArSceneStatus, { id: record.id, status: [1, 0][record.status] }, true).then(
      () => {
        message.success('操作成功');
        getList();
      }
    );
  };

  const columns = [
    {
      title: '标题',
      dataIndex: 'title',
    },
    {
      title: 'AR内容ID',
      dataIndex: 'ar_id',
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (text: number) => ['已下架', '已上架'][text],
      width: 70,
    },
    {
      title: '创建人',
      dataIndex: 'created_by',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      render: (text: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 170,
    },
    {
      title: '操作',
      key: 'op',
      render: (record: any) => (
        <span>
          <PermA perm="ar_content:edit" onClick={() => editRecord(record)}>
            编辑
          </PermA>
          <Divider type="vertical" />
          <PermA perm="ar_content:update_status" onClick={() => updateStatus(record)}>
            {['上架', '下架'][record.status]}
          </PermA>
          <Divider type="vertical" />
          <PermA perm="ar_content:delete" onClick={() => deleteRecord(record)}>
            删除
          </PermA>
        </span>
      ),
      width: 160,
    },
  ];

  useEffect(() => {
    getList();
    setMenuHook(dispatch, props);
  }, []);

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton perm="ar_content:save" onClick={() => editRecord()}>
            <Icon type="plus-circle" /> 创建AR
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Table filter={{}} columns={columns} rowKey="ar_id" func="getArSceneList" index="ar_list" />
        <Drawer
          visible={form.visible}
          skey={form.key}
          title={form.isEdit ? '编辑AR事件' : '创建AR事件'}
          onClose={() => setForm((s: any) => ({ ...s, visible: false }))}
          onOk={() => formRef.current.doSubmit()}
        >
          <Form
            formContent={form}
            allIds={form.arIds}
            wrappedComponentRef={(instance: any) => (formRef.current = instance)}
            onEnd={() => {
              setForm((s: any) => ({ ...s, visible: false }));
              getList();
            }}
          />
        </Drawer>
      </div>
    </>
  );
}
