import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useStore, useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, OrderColumn, PreviewMCN } from '@components/common';
import { CommonObject } from '@app/types';
import { getCrumb, objectToQueryString, searchToObject, setMenuHook } from '@app/utils/utils';
import {
  Row,
  Col,
  Divider,
  Icon,
  Modal,
  message,
  InputNumber,
  Tooltip,
  Popconfirm,
  Select,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { opApi as api, communityApi, opApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@components/permItems';
import Radio from 'antd/es/radio';
import AddArticleModal from './addArticleModal';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import HotDiscussionDrawer from '@app/components/business/hotDiscussionDrawer';

export default function HotTopicMgr(props: any) {
  const [listSelectedKeys, setListSelectedKeys] = useState([]);

  const [filter, setFilter] = useState<any>({
    publish_type: parseInt(searchToObject().publish_type ?? 0),
    circle_id: '',
  });

  const [form, setForm] = useState(
    () =>
      ({
        visible: false,
        key: Date.now(),
      } as any)
  );
  const [keyword, setKeyword] = useState('');
  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const { loading, run } = useXHR();
  const addRef = useRef<any>(null);

  const [preview, setPreview] = useState({
    visible: false,
    skey: 0,
    data: {},
  });

  const [hotDrawer, setHotDrawer] = useState({
    visible: false,
    key: 0,
    record: null,
  });

  const editHotRecord = (record: any = null) => {
    setHotDrawer({
      visible: true,
      key: Date.now(),
      record,
    });
  };

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const updateKeyword = useCallback((e: any) => {
    setKeyword(e.target.value);
  }, []);

  const f = useMemo(() => {
    const x: CommonObject = { ...filter };
    return x;
  }, [filter]);

  const getList = useCallback(
    (overlap: CommonObject = {}) => {
      setListSelectedKeys([]);
      const { current, size = 20 } = store.getState().tableList;
      dispatch(
        getTableList('getHotRecommendTopicList', 'list', {
          ...f,
          current,
          size,
          ...overlap,
        })
      );
    },
    [f, setListSelectedKeys]
  );

  const editRecord = (record: any) => {
    setForm({
      visible: true,
      key: Date.now(),
      id: record.id,
      name: record.name,
      class_id: record.class_id,
    });
  };
  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确认删除“${record.title}”？`,
      onOk: () => {
        run(api.deleteHotRecommendTopic, { id: record.id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const listSort = (record: any, current: number, offset: number) => {
    let data: any = {
      id: record.id,
      current,
      offset,
    };
    opApi.sortHotRecommendTopic(data).then((res: any) => {
      message.success('操作成功');
      getList();
    });
  };

  const changeOrder = (record: any, i: any) => {
    let position = i;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      position = v;
    };

    Modal.confirm({
      title: <p>排序：《{record.title}》</p>,
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber min={1} max={total} defaultValue={position} onChange={positionChange} />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!position) {
          message.error('请填写位置');
          return;
        }

        dispatch(setConfig({ loading: true }));
        opApi
          .sortHotRecommendTopic({ id: record.id, current: i, offset: position - i })
          .then((res: any) => {
            message.success('操作成功');
            getList();
            dispatch(setConfig({ loading: false }));
            closeFunc();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const titleClick = (record: any) => {
    // setPreview({
    //   visible: true,
    //   skey: Date.now(),
    //   data: record
    // })

    history.push(
      `/view/ugcTopicArticle/${record.ref_ids}/${encodeURIComponent(record.title)}/${record.type}`
    );
  };

  const columns = [
    {
      title: '排序',
      key: 'sort',
      render: (text: any, record: any, i: number) => (
        <OrderColumn
          perm="recommend_hot_topic:sort"
          start={1}
          pos={getSeq(i)}
          end={total}
          // disableUp={!record.enabled}
          // disableDown={!record.enabled}
          onUp={() => listSort(record, getSeq(i), -1)}
          onDown={() => listSort(record, getSeq(i), 1)}
        />
      ),
      width: 70,
    },
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      width: 70,
    },
    {
      title: '话题名称',
      width: 150,
      key: 'title',
      dataIndex: 'title',
      render: (text: any, record: any) => (
        // onClick={this.toMlf.bind(this, record.id, 'mlf_detail_url')}
        <Tooltip title={<div>{text}</div>}>
          {/* <div className="line-max-2" dangerouslySetInnerHTML={{ __html: text }}></div> */}
          <a onClick={() => titleClick(record)} className="line-max-3 list-title">
            {text}
          </a>
        </Tooltip>
      ),
    },
    {
      title: '关联圈子',
      dataIndex: 'ref_ids3',
      width: 90,
      render: (text: any, record: any) => {
        return <span>{record.ref_ids2 == 0 ? '全部圈子' : text}</span>;
      },
    },
    {
      title: (
        <span>
          新增内容数 &emsp;
          <Tooltip title={<span>话题下近72小时新发布的过审且未沉底内容数</span>}>
            <Icon type="question-circle" />
          </Tooltip>
        </span>
      ),
      dataIndex: 'show_article_count',
      width: 120,
    },
    {
      title: '来源',
      dataIndex: 'ref_type',
      width: 90,
      render: (text: any, record: any) => {
        return <span>{text == 0 ? '人工添加' : '系统计算'}</span>;
      },
    },
    {
      title: '操作人',
      dataIndex: 'updated_by',
      width: 160,
    },
    {
      title: (
        <span>
          更新或添加时间 &emsp;
          <Tooltip
            title={
              <span>
                来源为系统计算的，为更新时间<br></br>来源为人工添加的，为添加时间
              </span>
            }
          >
            <Icon type="question-circle" />
          </Tooltip>
        </span>
      ),
      dataIndex: 'updated_at',
      width: 160,
      render: (text: any, record: any) => {
        return (
          <div style={{ whiteSpace: 'normal' }}>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</div>
        );
      },
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any, i: number) => (
        <span>
          <PermA perm="recommend_hot_topic:update" onClick={() => editHotRecord(record)}>
            编辑
          </PermA>
          <Divider type="vertical" />
          <PermA perm="recommend_hot_topic:sort" onClick={() => changeOrder(record, getSeq(i))}>
            排序
          </PermA>
          <Divider type="vertical" />
          <PermA perm="recommend_hot_topic:delete" onClick={() => deleteRecord(record)}>
            移除
          </PermA>
        </span>
      ),
      width: 140,
    },
  ];

  const publishedColumns = [
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      width: 70,
    },
    {
      title: '话题名称',
      width: 150,
      key: 'title',
      dataIndex: 'title',
      render: (text: any, record: any) => (
        // onClick={this.toMlf.bind(this, record.id, 'mlf_detail_url')}
        <Tooltip title={<div>{text}</div>}>
          {/* <div className="line-max-2" dangerouslySetInnerHTML={{ __html: text }}></div> */}
          <a onClick={() => titleClick(record)} className="line-max-3 list-title">
            {text}
          </a>
        </Tooltip>
      ),
    },
    {
      title: '关联圈子',
      dataIndex: 'ref_ids3',
      width: 90,
      render: (text: any, record: any) => {
        return <span>{record.ref_ids2 == 0 ? '全部圈子' : text}</span>;
      },
    },
    {
      title: (
        <span>
          新增内容数 &emsp;
          <Tooltip title={<span>话题下近72小时新发布的过审且未沉底内容数</span>}>
            <Icon type="question-circle" />
          </Tooltip>
        </span>
      ),
      dataIndex: 'show_article_count',
      width: 120,
    },
    {
      title: '发布人',
      dataIndex: 'updated_by',
      width: 150,
    },
    {
      title: '发布时间',
      dataIndex: 'updated_at',
      width: 160,
      render: (text: any, record: any) => {
        return (
          <div style={{ whiteSpace: 'normal' }}>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</div>
        );
      },
    },
  ];

  useEffect(() => {
    getCircleList();
    // getList({ current: 1, size: 10 });
    // setMenuHook(dispatch, props);
  }, []);

  useEffect(() => {
    getList({ current: 1, size: 20 });
  }, [f]);

  useEffect(() => {
    setListSelectedKeys([]);
  }, [current]);

  const handleListSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    setListSelectedKeys(selectedRowKeys);
  };

  const batchDelete = () => {
    run(opApi.deleteHotRecommendTopic, { id: listSelectedKeys.join(',') }, true).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  const formChange = (v: any) => {
    if (typeof v === 'object') {
      setForm({
        ...form,
        name: v.target.value,
      });
    } else {
      setForm({
        ...form,
        class_id: v,
      });
    }
  };

  const onChangeType = (v: any, name: any) => {
    let { pathname: path, search } = history.location;
    const obj = searchToObject();
    obj[name] = v;
    path = `${path}?${objectToQueryString(obj)}`;
    history.replace(path);
    setFilter({
      ...filter,
      [name]: v,
    });
  };

  const addRecord = () => {
    addRef.current.showModal();
  };

  const handleAddArticleOk = () => {
    getList({ current: 1 });
  };

  const publish = () => {
    if (current != 1) {
      message.error('请翻到第一页，发布前20条话题');
      return;
    }

    if (records.length < 20) {
      message.error('话题少于20条，无法发布');
      return;
    }
    const ids = records
      .slice(0, 20)
      .map((v: any) => v.id)
      .join(',');

    Modal.confirm({
      title: '确定发布前20条话题吗？',
      onOk: () => {
        run(opApi.publishHotTopic, { data_ids: ids }, true).then(() => {
          message.success('操作成功');

          setFilter({
            ...filter,
            publish_type: 1,
          });
        });
      },
    });
  };

  const [circleOptions, setCircleOptions] = useState([]);
  const getCircleList = () => {
    communityApi
      .getCircleList({ current: 1, size: 100 })
      .then((res) => {
        const { list = [] } = res.data as any;
        setCircleOptions(list.records);
      })
      .catch(() => {});
  };

  return (
    <>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={18}>
            <Radio.Group
              style={{ marginRight: 8 }}
              value={filter.publish_type}
              buttonStyle="solid"
              onChange={(e) => onChangeType(e.target.value, 'publish_type')}
            >
              <Radio.Button value={1}>已发布</Radio.Button>
              <Radio.Button value={0}>待发布</Radio.Button>
            </Radio.Group>

            <Select
              value={filter.circle_id}
              style={{ width: 130, marginLeft: 8 }}
              onChange={(v) => onChangeType(v, 'circle_id')}
            >
              <Select.Option value="">关联圈子</Select.Option>
              {circleOptions.map((item: any) => (
                <Select.Option key={item.id} value={item.id}>
                  {item.name}
                </Select.Option>
              ))}
            </Select>

            {Boolean(filter.publish_type == 0 && allData.update_time > 0) && (
              <span>
                最近更新时间：{moment(allData.update_time).format('YYYY-MM-DD HH:mm:ss')}
                &emsp;
                <Tooltip title="每隔1小时更新一次系统计算的热议话题">
                  <Icon type="question-circle" />
                </Tooltip>
                &emsp;
                <span style={{ color: 'red' }}>更新的数据在第20条之后哦~</span>
              </span>
            )}
            {filter.publish_type == 0 && (
              <Popconfirm
                title="确定要批量移除选中内容吗？"
                onConfirm={batchDelete}
                okText="确定"
                cancelText="取消"
                icon={<Icon type="exclamation-circle" theme="twoTone" twoToneColor="red" />}
              >
                <PermButton
                  type="primary"
                  perm="recommend_hot_topic:delete"
                  style={{ marginLeft: 8 }}
                  disabled={listSelectedKeys.length == 0}
                >
                  批量移除
                </PermButton>
              </Popconfirm>
            )}
          </Col>

          {filter.publish_type == 0 && (
            <Col span={6} style={{ textAlign: 'right' }}>
              <PermButton
                perm="recommend_hot_topic:create"
                style={{ marginRight: 8 }}
                onClick={() => editHotRecord()}
              >
                <Icon type="plus-circle" /> 添加话题
              </PermButton>
              <PermButton
                type="primary"
                perm="recommend_hot_topic:publish"
                onClick={() => publish()}
                style={{ marginRight: 8 }}
              >
                一键发布
              </PermButton>
              <Tooltip
                title={
                  <>
                    <p>1、热议话题榜，显示已发布的20个话题；</p>
                    <p>
                      2、潮圈首页「热议话题」模块，优先显示榜单中关联了全部圈子/用户已加入圈子的话题，再显示其他热议话题；
                    </p>
                  </>
                }
              >
                <Icon type="question-circle" />
              </Tooltip>
            </Col>
          )}
        </Row>
        <Table
          func={'getHotRecommendTopicList'}
          index="list"
          filter={f}
          columns={f.publish_type == 0 ? columns : publishedColumns}
          rowKey="id"
          pagination={filter.publish_type == 0}
          pageSizeOptions={['20', '50', '100']}
          multi={f.publish_type == 0}
          selectedRowKeys={listSelectedKeys}
          onSelectChange={handleListSelectChange}
        />
        <AddArticleModal
          wrappedComponentRef={addRef}
          maxPosition={total + 1}
          type={filter.type}
          onOk={handleAddArticleOk}
        ></AddArticleModal>
        <PreviewMCN {...preview} onClose={() => setPreview({ ...preview, visible: false })} />

        <HotDiscussionDrawer
          {...hotDrawer}
          maxPosition={total + 1}
          onClose={() => setHotDrawer({ ...hotDrawer, visible: false })}
          onEnd={() => {
            setHotDrawer({ ...hotDrawer, visible: false });
            getList({ current: 1, size: 20 });
          }}
        ></HotDiscussionDrawer>
      </div>
    </>
  );
}
