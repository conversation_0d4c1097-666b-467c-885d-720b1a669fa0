import { setConfig } from '@action/config';
import { getTableList } from '@app/action/tableList';
import { opApi as api } from '@app/api';
import { CommonObject } from '@app/types';
import Form from '@components/business/musicForm';
import { A, Drawer, Table, OrderColumn } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setLoading } from '@utils/utils';
import { Button, Col, Icon, message, Modal, Row, Select, Divider } from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';

@(withRouter as any)
@connect
class RedPacket extends React.Component<any, any> {
  formRef: any;

  constructor(props: any) {
    super(props);
    this.state = {
      form: {
        visible: false,
        key: Date.now() + 1,
      },
      classId: '',
      classList: [],
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    this.getClassList();
    this.props.dispatch(
      setConfig({
        selectKeys: this.props.selectKeys,
        openKeys: this.props.openKeys,
      })
    );
  }

  getClassList = () => {
    api
      .getMusicCategoryList({
        size: 10000,
        current: 1,
        type: 0,
      })
      .then((res: any) => {
        this.setState({
          classList: res.data.class_list.records,
        });
      });
  };

  getData = (overlap: CommonObject = {}) => {
    const { current, size } = this.props.tableList;
    const { classId } = this.state;
    const body: any = { current, size };
    if (classId) {
      body.class_id = classId;
    }
    this.props.dispatch(getTableList('getMusicList', 'music_list', { ...body, ...overlap }));
  };

  getColumns = () => {
    const { current, size, total } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    const orderColumn = [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) => (
          <OrderColumn
            start={1}
            end={total}
            pos={getSeq(i)}
            perm="music:exchange_order"
            onUp={this.exchangeOrder.bind(this, record.id, getSeq(i), 0)}
            onDown={this.exchangeOrder.bind(this, record.id, getSeq(i), 1)}
          />
        ),
        width: 70,
      },
    ];
    const columns: any = [
      {
        title: '序号',
        key: 'id',
        render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
        width: 90,
      },
      {
        title: '封面图',
        dataIndex: 'pic_url',
        render: (text: string) => <img src={text} className="list-pic" alt="" />,
        width: 100,
      },
      {
        title: '歌曲名',
        key: 'name',
        render: (record: any) => (
          <a href={record.url} download={record.name} target="_blank" rel="noreferrer">
            {record.name}
          </a>
        ),
      },
      {
        title: '歌手',
        dataIndex: 'singer',
        width: 200,
      },
      {
        title: '所属分类',
        dataIndex: 'belong_classes',
        width: 200,
      },
      {
        title: '上传时间',
        dataIndex: 'created_at',
        render: (text: number) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 170,
      },
      {
        title: '音乐时长',
        dataIndex: 'play_duration',
       
        width: 100,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <span>
            {requirePerm(this, 'music:update')(<A onClick={() => this.editRecord(record)}>编辑</A>)}
            <Divider type="vertical" />
            {requirePerm(
              this,
              'music:delete'
            )(<A onClick={() => this.deleteRecord(record)}>删除</A>)}
          </span>
        ),
        width: 100,
      },
    ];
    if (this.state.classId) {
      return orderColumn.concat(columns);
    }
    return columns;
  };

  exchangeOrder = (id: any, pos: any, sortFlag: 0 | 1) => {
    setLoading(this, true);
    api
      .exchangeMusicOrder({
        id,
        offset: sortFlag,
        class_id: this.state.classId,
        current: pos,
      })
      .then(() => {
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  editRecord = (record: any = {}) => {
    this.setState({
      form: {
        visible: true,
        key: Date.now(),
        id: record.id || '',
        name: record.name || '',
        singer: record.singer || '',
        pic_url: record.pic_url || '',
        class_ids: record.belong_class_ids ? record.belong_class_ids.split(',') : [],
        music_url: record.url || '',
        play_duration: record.play_duration || '0:00'
      },
    });
  };

  deleteRecord = (record: any) => {
    Modal.confirm({
      title: '确定删除吗？',
      onOk: () => {
        setLoading(this, true);
        api
          .deleteMusic({
            id: record.id,
            class_id: record.belong_class_ids,
          })
          .then(() => {
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };

  classIdChange = (id: string) => {
    this.setState(
      {
        classId: id,
      },
      () => this.getData({ current: 1 })
    );
  };

  closeDrawer = () => {
    this.setState({
      form: { ...this.state.form, visible: false },
    });
  };

  toCategoryManager = () => {
    this.props.history.push('/view/musicCategoryMgr');
  };

  onSubmitEnd = () => {
    this.closeDrawer();
    this.getData();
  };

  setRef = (ref: 'formRef', instance: any) => {
    this[ref] = instance;
  };

  submitForm = (ref: 'formRef') => {
    this[ref].doSubmit();
  };

  render() {
    const { form } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={14}>
            {requirePerm(
              this,
              'music:create'
            )(
              <Button onClick={this.editRecord} style={{ marginRight: 8 }}>
                <Icon type="plus-circle" />
                添加音乐
              </Button>
            )}
            {requirePerm(
              this,
              'music:class_list'
            )(
              <Button onClick={this.toCategoryManager} style={{ marginRight: 8 }}>
                音乐分类管理
              </Button>
            )}
          </Col>
          <Col span={10} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <Select style={{ width: 150 }} value={this.state.classId} onChange={this.classIdChange}>
              <Select.Option value="">全部分类</Select.Option>
              {this.state.classList.map((v: any) => (
                <Select.Option value={v.id} key={v.id}>
                  {v.class_name}
                </Select.Option>
              ))}
            </Select>
          </Row>
          <Table
            func="getMusicList"
            index="music_list"
            columns={this.getColumns()}
            filter={{ class_id: this.state.classId }}
            rowKey={(record: any) => record.id + Math.random()}
            pagination={true}
          />
          <Drawer
            visible={form.visible}
            skey={form.key}
            onClose={this.closeDrawer}
            onOk={this.submitForm.bind(this, 'formRef')}
            title={`${form.id ? '编辑' : '新建'}音乐`}
          >
            <Form
              formContent={form}
              wrappedComponentRef={this.setRef.bind(this, 'formRef')}
              onEnd={this.onSubmitEnd}
              classList={this.state.classList}
            />
          </Drawer>
        </div>
      </>
    );
  }
}

export default RedPacket;
