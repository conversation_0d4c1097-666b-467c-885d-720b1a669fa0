import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useStore, useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, OrderColumn, PreviewMCN } from '@components/common';
import { CommonObject } from '@app/types';
import { getCrumb, objectToQueryString, searchToObject, setMenuHook } from '@app/utils/utils';
import { Row, Col, Divider, Icon, Modal, message, InputNumber, Tooltip, Popconfirm } from 'antd';
import { getTableList } from '@app/action/tableList';
import { opApi as api } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@components/permItems';
import Radio from 'antd/es/radio';
import AddArticleModal from './addArticleModal';
import { setConfig } from '@app/action/config';
import moment from 'moment';

export default function ServiceKeywords(props: any) {
  const [listSelectedKeys, setListSelectedKeys] = useState([]);

  const [filter, setFilter] = useState<any>({
    type: parseInt(searchToObject().type ?? 0),
    status: parseInt(searchToObject().status ?? 1),
  });

  const [form, setForm] = useState(
    () =>
      ({
        visible: false,
        key: Date.now(),
      } as any)
  );
  const [keyword, setKeyword] = useState('');
  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const { loading, run } = useXHR();
  const addRef = useRef<any>(null);

  const [preview, setPreview] = useState({
    visible: false,
    skey: 0,
    data: {},
  });

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const updateKeyword = useCallback((e: any) => {
    setKeyword(e.target.value);
  }, []);

  const f = useMemo(() => {
    const x: CommonObject = { ...filter };
    return x;
  }, [filter]);

  const getList = useCallback(
    (overlap: CommonObject = {}) => {
      setListSelectedKeys([]);
      const { current, size = 20 } = store.getState().tableList;
      dispatch(
        getTableList(f.status == 0 ? 'getPublishRankingList' : 'getSourceRankingList', 'list', {
          ...f,
          current,
          size,
          ...overlap,
        })
      );
    },
    [f, setListSelectedKeys]
  );

  const editRecord = (record: any) => {
    setForm({
      visible: true,
      key: Date.now(),
      id: record.id,
      name: record.name,
      class_id: record.class_id,
    });
  };
  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确认删除“${record.article_title}”？`,
      onOk: () => {
        run(api.deleteRankingArticle, { id: record.id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const listSort = (record: any, i: number, position: number = 1) => {
    let data: any = {
      id: record.id,
      sort_flag: i,
    };
    if (i == 2) {
      data.position = position;
    }
    api.rankingSort(data).then((res: any) => {
      message.success('操作成功');
      getList();
    });
  };

  const changeOrder = (record: any, i: any) => {
    let position = i;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      position = v;
    };

    Modal.confirm({
      title: <p>排序：《{record.article_title}》</p>,
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber min={1} max={total} defaultValue={position} onChange={positionChange} />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!position) {
          message.error('请填写位置');
          return;
        }

        dispatch(setConfig({ loading: true }));
        api
          .rankingSort({ id: record.id, sort_flag: 2, position: position })
          .then((res: any) => {
            message.success('操作成功');
            getList();
            dispatch(setConfig({ loading: false }));
            closeFunc();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const titleClick = (record: any) => {
    // setPreview({
    //   visible: true,
    //   skey: Date.now(),
    //   data: record
    // })
    if (!!record.url) {
      window.open(record.url, '_blank');
    }
  };

  const columns = [
    {
      title: '排序',
      key: 'sort',
      render: (text: any, record: any, i: number) => (
        <OrderColumn
          perm="ranking:sort"
          start={1}
          pos={getSeq(i)}
          end={total}
          // disableUp={!record.enabled}
          // disableDown={!record.enabled}
          onUp={() => listSort(record, 0)}
          onDown={() => listSort(record, 1)}
        />
      ),
      width: 70,
    },
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      width: 70,
    },
    {
      title: '潮新闻ID',
      dataIndex: 'article_id',
      width: 90,
    },
    {
      title: '媒立方ID',
      dataIndex: 'mlf_id',
      width: 90,
      render: (text: any, record: any) => {
        return <span>{record.article_type == 1 ? '' : text}</span>;
      },
    },
    {
      title: '稿件标题',
      key: 'article_title',
      dataIndex: 'article_title',
      render: (text: any, record: any) => (
        // onClick={this.toMlf.bind(this, record.id, 'mlf_detail_url')}
        <Tooltip title={<div>{text}</div>}>
          {/* <div className="line-max-2" dangerouslySetInnerHTML={{ __html: text }}></div> */}
          <a onClick={() => titleClick(record)} className="line-max-3 list-title">
            {text}
          </a>
        </Tooltip>
      ),
    },
    {
      title: '浏览量',
      dataIndex: 'read_count',
      width: 90,
    },
    {
      title: '签发时间',
      dataIndex: 'published_at',
      width: 100,
      render: (text: any, record: any) => {
        return (
          <div style={{ whiteSpace: 'normal' }}>
            <div>{moment(text).format('YYYY-MM-DD')}</div>
            <div>{moment(text).format('HH:mm:ss')}</div>
          </div>
        );
      },
    },
    {
      title: '来源',
      dataIndex: 'source',
      width: 90,
      render: (text: any, record: any) => {
        return <span>{text == 0 ? '系统计算' : '人工添加'}</span>;
      },
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      width: 90,
    },
    {
      title: (
        <span>
          更新或添加时间 &emsp;
          <Tooltip
            title={
              <span>
                来源为系统计算的，为更新时间<br></br>来源为人工添加的，为添加时间
              </span>
            }
          >
            <Icon type="question-circle" />
          </Tooltip>
        </span>
      ),
      dataIndex: 'created_at',
      width: 160,
      render: (text: any, record: any) => {
        return (
          <div style={{ whiteSpace: 'normal' }}>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</div>
        );
      },
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any, i: number) => (
        <span>
          <PermA perm="ranking:sort" onClick={() => changeOrder(record, getSeq(i))}>
            排序
          </PermA>
          <Divider type="vertical" />
          <PermA perm="ranking:delete" onClick={() => deleteRecord(record)}>
            移除
          </PermA>
        </span>
      ),
      width: 110,
    },
  ];

  const publishedColumns = [
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      width: 70,
    },
    {
      title: '潮新闻ID',
      dataIndex: 'article_id',
      width: 90,
    },
    {
      title: '媒立方ID',
      dataIndex: 'mlf_id',
      width: 90,
      render: (text: any, record: any) => {
        return <span>{record.article_type == 1 ? '' : text}</span>;
      },
    },
    {
      title: '稿件标题',
      key: 'article_title',
      dataIndex: 'article_title',
      render: (text: any, record: any) => (
        // onClick={this.toMlf.bind(this, record.id, 'mlf_detail_url')}
        <Tooltip title={<div>{text}</div>}>
          {/* <div className="line-max-2" dangerouslySetInnerHTML={{ __html: text }}></div> */}
          <a onClick={() => titleClick(record)} className="line-max-3 list-title">
            {text}
          </a>
        </Tooltip>
      ),
    },
    {
      title: '浏览量',
      dataIndex: 'read_count',
      width: 70,
    },
    {
      title: '签发时间',
      dataIndex: 'published_at',
      width: 160,
      render: (text: any, record: any) => {
        return (
          <div style={{ whiteSpace: 'normal' }}>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</div>
        );
      },
    },
    {
      title: '发布人',
      dataIndex: 'operator',
      width: 150,
    },
    {
      title: '发布时间',
      dataIndex: 'created_at',
      width: 160,
      render: (text: any, record: any) => {
        return (
          <div style={{ whiteSpace: 'normal' }}>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</div>
        );
      },
    },
  ];

  useEffect(() => {
    // getList({ current: 1, size: 10 });
    setMenuHook(dispatch, props);
  }, []);

  useEffect(() => {
    getList({ current: 1, size: 20 });
  }, [f]);

  useEffect(() => {
    setListSelectedKeys([]);
  }, [current]);

  const handleListSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    setListSelectedKeys(selectedRowKeys);
  };

  const batchDelete = () => {
    run(api.deleteRankingArticle, { id: listSelectedKeys.join(',') }, true).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  const formChange = (v: any) => {
    if (typeof v === 'object') {
      setForm({
        ...form,
        name: v.target.value,
      });
    } else {
      setForm({
        ...form,
        class_id: v,
      });
    }
  };

  const onChangeType = (v: any, name: any) => {
    let { pathname: path, search } = history.location;
    const obj = searchToObject();
    obj[name] = v;
    path = `${path}?${objectToQueryString(obj)}`;
    history.replace(path);
    setFilter({
      ...filter,
      [name]: v,
    });
  };

  const addRecord = () => {
    addRef.current.showModal();
  };

  const handleAddArticleOk = () => {
    getList({ current: 1, size: 20 });
  };

  const handleErrorRecords = (list: any[]) => {
    const ids = list.map((v: any) => v.article_id).join('、');
    const id = list.map((v: any) => v.id).join(',');
    Modal.confirm({
      title: `内容ID: ${ids}状态异常，请批量移除后再发布。`,
      okText: '批量移除',
      cancelText: '取消',
      onOk: () => {
        run(api.deleteRankingArticle, { id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const publish = () => {
    if (current != 1) {
      message.error('请翻到第一页，发布前20条稿件');
      return;
    }

    if (records.length < 20) {
      message.error('稿件少于20条，无法发布');
      return;
    }
    const errorRecords = records.filter((v: any) => !v.show);
    if (errorRecords.length > 0) {
      handleErrorRecords(errorRecords);
      return;
    }

    const ids = records
      .slice(0, 20)
      .map((v: any) => v.id)
      .join(',');

    Modal.confirm({
      title: '确定发布前20条稿件吗？',
      onOk: () => {
        run(api.rankingPublish, { data_ids: ids, type: filter.type }, true).then(() => {
          message.success('操作成功');

          setFilter({
            ...filter,
            status: 0,
          });
        });
      },
    });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Radio.Group
            defaultValue={filter.type}
            buttonStyle="solid"
            onChange={(e) => onChangeType(e.target.value, 'type')}
          >
            <Radio.Button value={0}>24h热榜</Radio.Button>
            <Radio.Button value={1}>市县热闻榜</Radio.Button>
            <Radio.Button value={2}>视频热榜</Radio.Button>
          </Radio.Group>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={18}>
            <Radio.Group
              style={{ marginRight: 8 }}
              value={filter.status}
              buttonStyle="solid"
              onChange={(e) => onChangeType(e.target.value, 'status')}
            >
              <Radio.Button value={0}>已发布</Radio.Button>
              <Radio.Button value={1}>待发布</Radio.Button>
            </Radio.Group>
            {Boolean(filter.status == 1 && allData.latest_sync_time > 0) && (
              <span>
                最近更新时间：{moment(allData.latest_sync_time).format('YYYY-MM-DD HH:mm:ss')}
                &emsp;
                <Tooltip
                  title={
                    [
                      '每隔1小时更新近24小时发布的热门稿件',
                      '每隔1小时更新近24小时发布的市县热门稿件',
                      '每隔1小时更新近24小时发布的热门视频稿',
                    ][filter.type]
                  }
                >
                  <Icon type="question-circle" />
                </Tooltip>
                &emsp;
                <span style={{ color: 'red' }}>更新的稿件在第20条之后哦~</span>
              </span>
            )}
            {filter.status == 1 && (
              <Popconfirm
                title="确定要批量移除选中内容吗？"
                onConfirm={batchDelete}
                okText="确定"
                cancelText="取消"
                icon={<Icon type="exclamation-circle" theme="twoTone" twoToneColor="red" />}
              >
                <PermButton
                  type="primary"
                  perm="ranking:delete"
                  style={{ marginLeft: 8 }}
                  disabled={listSelectedKeys.length == 0}
                >
                  批量移除
                </PermButton>
              </Popconfirm>
            )}
          </Col>

          {filter.status == 1 && (
            <Col span={6} style={{ textAlign: 'right' }}>
              <PermButton perm="ranking:add" style={{ marginRight: 8 }} onClick={() => addRecord()}>
                <Icon type="plus-circle" /> 添加稿件
              </PermButton>
              <PermButton type="primary" perm="ranking:publish" onClick={() => publish()}>
                一键发布
              </PermButton>
            </Col>
          )}
        </Row>
        <Table
          func={f.status == 0 ? 'getPublishRankingList' : 'getSourceRankingList'}
          index="list"
          filter={f}
          columns={f.status == 0 ? publishedColumns : columns}
          rowKey="id"
          pagination={filter.status == 1}
          pageSizeOptions={['20', '50', '100']}
          multi={f.status == 1}
          selectedRowKeys={listSelectedKeys}
          onSelectChange={handleListSelectChange}
        />
        <AddArticleModal
          wrappedComponentRef={addRef}
          maxPosition={total + 1}
          type={filter.type}
          onOk={handleAddArticleOk}
        ></AddArticleModal>
        <PreviewMCN {...preview} onClose={() => setPreview({ ...preview, visible: false })} />
      </div>
    </>
  );
}
