import {
  Form,
  Icon,
  Input,
  Radio,
  Switch,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';

import { opApi } from '@app/api';
import { Drawer, FileUploader } from '@app/components/common';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';

const ConfigGPTStyleDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();

  const { getFieldDecorator, getFieldValue } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = () => {
    // e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        dispatch(setConfig({ mLoading: true }));
        let parmas: any = {}
        if (values.status) {
          parmas.status = 1
          parmas.ref_ids = values.color.join(',')
          parmas.pic_url = values.pic_url
        } else {
          parmas.status = 0
          parmas.ref_ids = ''
          parmas.pic_url = ''
        }

        opApi.saveGptStyle(parmas)
          .then((res: any) => {
            message.success('保存成功');
            dispatch(setConfig({ mLoading: false }));
            props.onEnd && props.onEnd();
          })
          .catch(() => {
            // message.error('添加失败');
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
        dispatch(setConfig({ mLoading: false }));
      }
    });
  };

  // 输入框字体校验
  const validateLogoColor = (
    rule: object,
    value: string,
    callback: any,
    formTitle: string,
    size: number = 6
  ) => {
    const regex = new RegExp(`^#[0-9a-zA-Z]{${size}}$`);
    if (!value) {
      callback(`请输入${formTitle}`);
    } else if (!regex.test(value)) {
      callback(`格式错误，请输入以#开头，后面跟着${size}位数字或英文字母的内容`);
    } else {
      callback();
    }
  };

  // 下载模板
  const handleDownload = (url: string) => {
    const a = document.createElement('a');
    a.setAttribute('href', url);
    a.target = '_self';
    a.href = url;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  return (
    <Drawer
      title={
        <>
          <span>设置智能助手样式</span>
          <span style={{ color: '#999', marginLeft: 10, fontSize: 14 }}>
            注：针对6.4及以上版本客户端生效
          </span>
        </>
      }
      visible={props.visible}
      skey={props.skey}
      onClose={props.onClose}
      maskClosable={false}
      // width={500}
      onOk={handleSubmit}
      okPerm='gpt_question:style_save'
      okText="保存"
    >
      <Form {...formLayout}>
        <Form.Item label="自定义样式">
          {getFieldDecorator('status', {
            initialValue: props.record?.status,
            valuePropName: 'checked',
            rules: [
              {
                required: true,
              },
            ],
          })(<Switch></Switch>)}
        </Form.Item>

        {!!getFieldValue('status') && (
          <>
            <Form.Item
              label="图片资源"
              extra={
                <span>
                  支持zip文件格式，请将图片文件按要求命名后打包上传，
                  <a
                    onClick={() => {
                      handleDownload('https://app-stc.zjol.com.cn/static/custom/cbb_gpt_style_template.xlsx');
                    }}
                  >
                    点击下载模板
                  </a>
                </span>
              }
            >
              {getFieldDecorator('pic_url', {
                initialValue: props.record?.pic_url,
                rules: [
                  {
                    required: true,
                    message: '请上传图片资源',
                  },
                ],
              })(<FileUploader accept=".zip" download={true} />)}
            </Form.Item>
            <Form.Item label="文字颜色">
              {getFieldDecorator('color[0]', {
                initialValue: props.record?.color?.[0] || undefined,
                rules: [
                  {
                    required: true,
                    message: '请选择文字颜色',
                  },
                ],
              })(
                <Radio.Group onChange={() => { }}>
                  <Radio value={'0'}>浅色风格</Radio>
                  <Radio value={'1'}>深色风格</Radio>
                </Radio.Group>
              )}
            </Form.Item>

            <Form.Item label="主题色">
              {getFieldDecorator('color[1]', {
                initialValue: props.record?.color?.[1] || '',
                rules: [
                  {
                    required: true,
                    validator: (rule: any, value: string, callback: any) =>
                      validateLogoColor(rule, value, callback, '选中字体颜色', 6),
                  },
                ],
              })(<Input placeholder="输入色值" style={{ width: '50%' }} maxLength={9} />)}
              <span style={{ width: '50%', color: 'rgb(0 0 0 / 45%)' }}>
                &nbsp;&nbsp;示例：#000000
              </span>
            </Form.Item>

            <Form.Item
              label={
                <span style={{ position: 'relative' }}>
                  <Tooltip
                    title={<img src={`/assets/gpt_bubble_tip.png`} width={148} height={53} />}
                  >
                    <Icon
                      type="question-circle"
                      style={{ position: 'absolute', left: -30, top: 0 }}
                    />
                  </Tooltip>
                  对话框底色-起
                </span>
              }
            >
              {getFieldDecorator('color[2]', {
                initialValue: props.record?.color?.[2] || '',
                rules: [
                  {
                    required: true,
                    validator: (rule: any, value: string, callback: any) =>
                      validateLogoColor(rule, value, callback, '选中字体颜色', 8),
                  },
                ],
              })(<Input placeholder="输入色值" style={{ width: '50%' }} maxLength={9} />)}
              <span style={{ width: '50%', color: 'rgb(0 0 0 / 45%)' }}>
                &nbsp;&nbsp;示例：#FF000000，前两位为透明度&nbsp;
                <Tooltip
                  title={
                    '透明度的对应色值如下：100% : FF，95% : F2，90% : E6，85% : D9，80% : CC，75% : BF，70% : B3，65% : A6，60% : 99，55% : 8C，50% : 80，45% : 73，40% : 66，35% : 59，30% : 4D，25% : 40，20% : 33，15% : 26，10% : 1A，5% : 0D，0% : 00'
                  }
                >
                  <Icon type="question-circle" />
                </Tooltip>
              </span>
            </Form.Item>

            <Form.Item label="对话框底色-止">
              {getFieldDecorator('color[3]', {
                initialValue: props.record?.color?.[3] || '',
                rules: [
                  {
                    required: true,
                    validator: (rule: any, value: string, callback: any) =>
                      validateLogoColor(rule, value, callback, '选中字体颜色', 8),
                  },
                ],
              })(<Input placeholder="输入色值" style={{ width: '50%' }} maxLength={9} />)}
              <span style={{ width: '50%', color: 'rgb(0 0 0 / 45%)' }}>
                &nbsp;&nbsp;示例：#FF000000，前两位为透明度
              </span>
            </Form.Item>

            <Form.Item
              label={
                <span style={{ position: 'relative' }}>
                  <Tooltip
                    title={<img src={`/assets/gpt_shortcutbar_tip.png`} width={228} height={120} />}
                  >
                    <Icon
                      type="question-circle"
                      style={{ position: 'absolute', left: -30, top: 0 }}
                    />
                  </Tooltip>
                  快捷栏底色-起
                </span>
              }
            >
              {getFieldDecorator('color[4]', {
                initialValue: props.record?.color?.[4] || '',
                rules: [
                  {
                    required: true,
                    validator: (rule: any, value: string, callback: any) =>
                      validateLogoColor(rule, value, callback, '选中字体颜色', 8),
                  },
                ],
              })(<Input placeholder="输入色值" style={{ width: '50%' }} maxLength={9} />)}
              <span style={{ width: '50%', color: 'rgb(0 0 0 / 45%)' }}>
                &nbsp;&nbsp;示例：#FF000000，前两位为透明度
              </span>
            </Form.Item>

            <Form.Item label="快捷栏底色-中">
              {getFieldDecorator('color[5]', {
                initialValue: props.record?.color?.[5] || '',
                rules: [
                  {
                    required: true,
                    validator: (rule: any, value: string, callback: any) =>
                      validateLogoColor(rule, value, callback, '选中字体颜色', 8),
                  },
                ],
              })(<Input placeholder="输入色值" style={{ width: '50%' }} maxLength={9} />)}
              <span style={{ width: '50%', color: 'rgb(0 0 0 / 45%)' }}>
                &nbsp;&nbsp;示例：#FF000000，前两位为透明度
              </span>
            </Form.Item>

            <Form.Item label="快捷栏底色-止">
              {getFieldDecorator('color[6]', {
                initialValue: props.record?.color?.[6] || '',
                rules: [
                  {
                    required: true,
                    validator: (rule: any, value: string, callback: any) =>
                      validateLogoColor(rule, value, callback, '选中字体颜色', 8),
                  },
                ],
              })(<Input placeholder="输入色值" style={{ width: '50%' }} maxLength={9} />)}
              <span style={{ width: '50%', color: 'rgb(0 0 0 / 45%)' }}>
                &nbsp;&nbsp;示例：#FF000000，前两位为透明度
              </span>
            </Form.Item>

            <Form.Item
              label={
                <span style={{ position: 'relative' }}>
                  <Tooltip
                    title={<img src={`/assets/gpt_input_tip.png`} width={220} height={68} />}
                  >
                    <Icon
                      type="question-circle"
                      style={{ position: 'absolute', left: -30, top: 0 }}
                    />
                  </Tooltip>
                  输入框底色-起
                </span>
              }
            >
              {getFieldDecorator('color[7]', {
                initialValue: props.record?.color?.[7] || '',
                rules: [
                  {
                    required: true,
                    validator: (rule: any, value: string, callback: any) =>
                      validateLogoColor(rule, value, callback, '选中字体颜色', 8),
                  },
                ],
              })(<Input placeholder="输入色值" style={{ width: '50%' }} maxLength={9} />)}
              <span style={{ width: '50%', color: 'rgb(0 0 0 / 45%)' }}>
                &nbsp;&nbsp;示例：#FF000000，前两位为透明度
              </span>
            </Form.Item>

            <Form.Item label="输入框底色-止">
              {getFieldDecorator('color[8]', {
                initialValue: props.record?.color?.[8] || '',
                rules: [
                  {
                    required: true,
                    validator: (rule: any, value: string, callback: any) =>
                      validateLogoColor(rule, value, callback, '选中字体颜色', 8),
                  },
                ],
              })(<Input placeholder="输入色值" style={{ width: '50%' }} maxLength={9} />)}
              <span style={{ width: '50%', color: 'rgb(0 0 0 / 45%)' }}>
                &nbsp;&nbsp;示例：#FF000000，前两位为透明度
              </span>
            </Form.Item>
          </>
        )}
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'ConfigGPTStyleDrawer' })(
  forwardRef<any, any>(ConfigGPTStyleDrawer)
);
