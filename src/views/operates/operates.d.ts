import { ICommonTableList } from '@app/types';

export type ProposalRecord = {
  id: number;
  pic_url: string;
  url: string;
  status: 1 | 0;
  created_by: string;
  created_at: number;
  title?: string;
};

export type ProposalAllData = {
  on_show_count: number;
  recommend_list: ICommonTableList<ProposalRecord>;
};

export type TopicRecord = {
  id: string;
  name: string;
  type: 0 | 1;
  description: string;
  article_count: number;
  class_names: string[];
  enabled: boolean;
  created_user_name: string;
  created_at: number;
  show_style: 0 | 1 | 2;
  logo_url: string;
};

export type TopicAllData = {
  on_show_count: number;
  list: ICommonTableList<TopicRecord>;
};

export type RecommendTopicRecord = {
  id: number;
  name: string;
  article_count: number;
  class_names: string[];
  created_at: number;
  enabled: boolean;
};

export type RecommendTopicAllData = {
  on_show_count: number;
  recommend_list: ICommonTableList<RecommendTopicRecord>;
  min_count: number;
  min_count_id: number;
};

export type TopicArticleRecord = {
  id: string;
  metadata_id: number;
  video_url: string;
  list_pics: string;
  list_title: string;
  account_nick_name: string;
  published_timestamp: number;
  top: number;
};

export type TopicArticleAllData = {
  list: ICommonTableList<TopicArticleRecord>;
  top_count: number;
};
