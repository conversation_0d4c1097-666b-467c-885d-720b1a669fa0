import { opApi as api } from '@app/api';
import { IAllProps, IBaseProps, CommonObject } from '@app/types';
import { A, BaseComponent } from '@components/common';
import { connectAll as connect } from '@utils/connect';
import { Button, Col, Icon, message, Modal, Row, Input, Table } from 'antd';
import React from 'react';
import { withRouter } from 'react-router';
import { SortOrder } from 'antd/es/table';
import { TopicRecord, TopicAllData } from './operates';

type State = {
  form: {
    visible: boolean;
    key: number;
    name: string;
  };
  records: any[];
  page: number;
  pageSize: number;
};

type Props = IBaseProps<IAllProps<TopicRecord, TopicAllData>>;

class TopicCategoryMgr extends BaseComponent<IAllProps<TopicRecord, TopicAllData>, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      form: {
        visible: false,
        key: Date.now(),
        name: '',
      },
      records: [],
      page: 1,
      pageSize: 10,
    };
  }

  componentDidMount() {
    this.setMenu();
    this.getData();
  }

  getData = (overlap: CommonObject = {}) => {
    this.setLoading(true);
    api
      .getTopicCategoryList()
      .then((res: any) => {
        this.setState({
          records: res.data.list,
        });
        this.setLoading(false);
      })
      .catch(() => {
        this.setLoading(false);
      });
  };

  pageChange = (page: number, pageSize: number | undefined) => {
    console.log(page, pageSize);
    this.setState({ page, pageSize: pageSize || 10 });
  };

  getColumns = () => {
    const { page, pageSize } = this.state;
    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{(page - 1) * pageSize + i + 1}</span>,
        width: 70,
      },
      {
        title: '话题分类',
        key: 'name',
        dataIndex: 'name',
        sorter: (a: any, b: any) => a.name.localeCompare(b.name),
        sortDirections: ['ascend'] as SortOrder[],
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) =>
          this.requirePerm('topic_class:delete')(<A onClick={this.deleteRecord.bind(this, record)}>删除</A>),
        width: 70,
      },
    ];
  };

  editRecord = (record: any = {}) => {
    this.setState({
      form: {
        visible: true,
        key: Date.now(),
        name: record.name || '',
      },
    });
  };

  deleteRecord = (record: any) => {
    Modal.confirm({
      title: '确定删除吗？',
      onOk: () => {
        this.setLoading(true);
        api
          .deleteTopicCategory({
            id: record.id,
          })
          .then(() => {
            message.success('操作成功');
            this.getData();
            this.setLoading(false);
          })
          .catch(() => {
            this.setLoading(false);
          });
      },
    });
  };

  closeForm = () => {
    this.setState({
      form: { ...this.state.form, visible: false },
    });
  };

  submitForm = () => {
    if (this.state.form.name.length > 15) {
      message.error('分类名称不能多于15个字');
      return;
    }
    this.setConfirmLoading(true);
    api
      .createTopicCategory({
        name: this.state.form.name,
      })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setConfirmLoading(false);
        this.closeForm();
      })
      .catch(() => {
        this.setConfirmLoading(false);
      });
  };

  nameChange = (e: any) => {
    this.setState({
      form: { ...this.state.form, name: e.target.value },
    });
  };

  render() {
    const { form } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            {this.requirePerm('topic_class:create')(
              <Button onClick={this.editRecord}>
                <Icon type="plus-circle-o" />
                添加话题分类
              </Button>
            )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {this.getCrumb()}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            columns={this.getColumns()}
            pagination={{
              pageSizeOptions: ['10', '20', '50', '100'],
              showSizeChanger: true,
              onChange: this.pageChange,
              onShowSizeChange: this.pageChange,
            }}
            rowKey="id"
            dataSource={this.state.records}
          />
          <Modal
            title="创建分类"
            visible={form.visible}
            onCancel={this.closeForm}
            onOk={this.submitForm}
            confirmLoading={this.props.config.mLoading}
          >
            <Row>
              <Col span={4} style={{ textAlign: 'right', lineHeight: '32px' }}>
                分类名称：
              </Col>
              <Col span={20}>
                <Input value={form.name} onChange={this.nameChange} placeholder="请输入分类名称，不超过15个字" />
              </Col>
            </Row>
          </Modal>
        </div>
      </>
    );
  }
}

export default withRouter(connect<TopicRecord, TopicAllData>()(TopicCategoryMgr));
