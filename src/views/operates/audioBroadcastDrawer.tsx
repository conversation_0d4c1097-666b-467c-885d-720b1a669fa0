import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Divider,
  Form,
  Icon,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Select,
  Spin,
  Switch,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { opApi } from '@app/api';
import { A, Drawer, ImageUploader } from '@app/components/common';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import uuid from 'uuid';

const AudioBroadcastDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();

  const [page_list, setPageList] = useState<any>([]);
  const [recordInfo, setRecordInfo] = useState<any>({
    visible: false,
    titleName: '',
    key: '',
    pid: '',
    index: '',
    value: '',
    sizeMax: 40,
  });

  const { getFieldDecorator, getFieldsValue, setFieldsValue, getFieldValue } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  // 初始化页面数据
  const intPageList = () => {
    if (props.record?.ref_extensions) {
      const dataList = JSON.parse(props.record?.ref_extensions);
      return (
        dataList?.map((group: any) => {
          const pid = uuid();
          return {
            pid,
            ...group,
          };
        }) || []
      );
    }

    return [
      {
        pid: uuid(),
        broadcast_name: '',
        broadcast_img: '',
        audio_url: '',
      },
    ];
  };

  useEffect(() => {
    setPageList(intPageList());
  }, [props.visible]);

  useEffect(() => {
    const { page_list: arr } = getFieldsValue();
    if (arr) {
      setTimeout(() => {
        setFieldsValue({ page_list: page_list });
      }, 0);
    }
  }, [page_list]);

  const findPageWithPID = (list: any, id: any): any => {
    return list?.filter((item: any) => item.pid == id)?.[0];
  };

  const handleSubmit = () => {
    // e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        if (values.page_list.length < 5) {
          message.error('至少添加5条广播');
          dispatch(setConfig({ mLoading: false }));
          return;
        }
        dispatch(setConfig({ mLoading: true }));
        const parmas: any = {
          ref_extensions: JSON.stringify(values.page_list),
          status: values.status ? 1 : 0,
        };

        opApi
          .saveBroadcast(parmas)
          .then((res: any) => {
            message.success('保存成功');
            dispatch(setConfig({ mLoading: false }));
            props.onOk && props.onOk();
          })
          .catch(() => {
            // message.error('添加失败');
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
        dispatch(setConfig({ mLoading: false }));
      }
    });
  };

  const delectList = (id: any) => {
    const { page_list: arr } = getFieldsValue();
    const arrayConment = arr.filter((item: { pid: any }) => {
      return item.pid !== id;
    });
    setTimeout(() => {
      // console.log(arrayConment)
      setFieldsValue({ page_list: arrayConment });
      // console.log(getFieldsValue())
    }, 0);
    setPageList(arrayConment);
  };

  // const handleMove = (data: {}, index: number) => {
  //   let position = index + 1;
  //   Modal.confirm({
  //     width: 250,
  //     content: (
  //       <>
  //         位置:{' '}
  //         <InputNumber
  //           min={1}
  //           max={page_list.length}
  //           defaultValue={position}
  //           onChange={(e: any) => {
  //             position = e;
  //           }}
  //         />
  //       </>
  //     ),
  //     icon: null,
  //     onOk() {
  //       const { page_list: arr } = getFieldsValue();
  //       let item = arr[index];
  //       let newArray = [...arr];
  //       newArray.splice(index, 1);
  //       newArray.splice(position - 1, 0, item);
  //       setPageList(newArray);
  //     },
  //     onCancel() {},
  //   });
  // };

  const addGroup = () => {
    const { page_list: articleList = [] } = getFieldsValue();
    const result = [
      ...articleList,
      {
        pid: uuid(),
        broadcast_name: '',
        broadcast_img: '',
        author: '',
        audio_url: '',
        introduce: '',
        link_url: '',
      },
    ];
    setPageList(result);
  };

  const handleMove = (data: any, index: number, type: 1 | -1) => {
    const { page_list: arr } = getFieldsValue();
    let item = arr[index];
    let newIndex = index + type;
    if (newIndex < 0) {
      newIndex = 0;
    }
    if (newIndex > arr.length - 1) {
      newIndex = arr.length - 1;
    }
    let newArray = [...arr];
    newArray.splice(index, 1);
    newArray.splice(newIndex, 0, item);
    setPageList(newArray);
  };

  return (
    <Drawer
      title={'听广播'}
      visible={props.visible}
      skey={props.skey}
      onClose={props.onCancel}
      maskClosable={false}
      // width={500}
      onOk={handleSubmit}
      okText="保存"
    >
      <Form {...formLayout}>
        <Form.Item label="广播开关">
          {getFieldDecorator(`status`, {
            initialValue: props.record ? props.record.status : 1,
            valuePropName: 'checked',
          })(<Switch checkedChildren="开" unCheckedChildren="关" />)}
        </Form.Item>

        {page_list.map((item: any, index: number) => {
          return (
            <div key={item.pid + `${index}`}>
              <Row>
                <Col span={6} style={{ paddingLeft: 50 }}>
                  {page_list.length && index == 0 ? (
                    <Button
                      onClick={() => handleMove(item, index, -1)}
                      className="btn_mar"
                      disabled
                      type="primary"
                      icon="up"
                      style={{ marginRight: 5 }}
                    />
                  ) : (
                    <Button
                      onClick={() => handleMove(item, index, -1)}
                      className="btn_mar"
                      type="primary"
                      icon="up"
                      style={{ marginRight: 5 }}
                    />
                  )}
                  {page_list.length && index != page_list.length - 1 ? (
                    <Button
                      onClick={() => handleMove(item, index, 1)}
                      className="btn_mar"
                      type="primary"
                      icon="down"
                    />
                  ) : (
                    <Button
                      onClick={() => handleMove(item, index, 1)}
                      className="btn_mar"
                      disabled
                      type="primary"
                      icon="down"
                    />
                  )}
                  {/* <span className="title">
                    {'广播'}
                    {index + 1}
                  </span> */}
                  {/* {index === 0 && <span className="listLengthTip">{this.props.listLengthTip}</span>} */}
                </Col>
                <Col span={18} style={{ textAlign: 'right' }}>
                  <Button
                    type="danger"
                    onClick={() => delectList(item.pid)}
                    disabled={page_list.length <= 1}
                  >
                    删除
                  </Button>
                </Col>
                {/* <Col span={4} style={{ textAlign: 'right' }}>
                  <h3>广告位{i + 1}</h3>
                </Col>
                <Col span={18} style={{ textAlign: 'right' }}>
                  <Button
                    type="danger"
                    onClick={() => this.removeItem(i)}
                    disabled={list.length <= 1}
                  >
                    删除广告位{i + 1}
                  </Button>
                </Col> */}
              </Row>

              <Form.Item label="id" style={{ display: 'none' }}>
                {getFieldDecorator(`page_list[${index}].pid`, {
                  initialValue: item.pid,
                })(<Input disabled />)}
              </Form.Item>
              <Form.Item>
                <h3 style={{ marginLeft: 50 }}>广播{index + 1}</h3>
              </Form.Item>

              <Form.Item label="广播名称">
                {getFieldDecorator(`page_list[${index}].broadcast_name`, {
                  initialValue: item.broadcast_name,
                  validateTrigger: 'onChange',
                  rules: [
                    {
                      required: true,
                      message: '请输入广播名称',
                      whitespace: true,
                    },
                  ],
                })(<Input placeholder="最多输入15字" maxLength={15} />)}
              </Form.Item>

              <Form.Item label="来源">
                {getFieldDecorator(`page_list[${index}].broadcast_source`, {
                  initialValue: item.broadcast_source,
                })(<Input placeholder="请输入来源，最多15字" maxLength={15} />)}
              </Form.Item>

              <Form.Item label="音频url">
                {getFieldDecorator(`page_list[${index}].audio_url`, {
                  initialValue: item.audio_url,
                  rules: [
                    {
                      required: true,
                      message: '请输入音频url',
                      whitespace: true,
                    },
                  ],
                })(<Input placeholder="请输入音频url"></Input>)}
              </Form.Item>

              <Form.Item label="图片" extra="支持.jpg .jpeg .png等格式，比例为1:1">
                {getFieldDecorator(`page_list[${index}].broadcast_img`, {
                  initialValue: item.broadcast_img,
                  rules: [
                    {
                      required: true,
                      message: '请上传图片',
                    },
                  ],
                })(<ImageUploader ratio={1}></ImageUploader>)}
              </Form.Item>
            </div>
          );
        })}

        {page_list.length < 20 && (
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 20,
            }}
          >
            <Button style={{ width: 500, marginRight: 8 }} onClick={addGroup}>
              <Icon type="plus-circle-o" />
              添加广播
            </Button>
          </div>
        )}
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'AddReadRankDrawer' })(
  forwardRef<any, any>(AudioBroadcastDrawer)
);
