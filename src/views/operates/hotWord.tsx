import { setConfig } from '@action/config';
import { getTableList } from '@action/tableList';
import { opApi as api } from '@app/api';
import { A, Table } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm } from '@utils/utils';
import { Button, Col, Divider, Form, Icon, Input, message, Modal, Row } from 'antd';
import React from 'react';
import { withRouter } from 'react-router';
import HotSearchModal from './hotSearchModal';
import { PermButton } from '@app/components/permItems';

@(withRouter as any)
@connect
class HotWord extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      editWord: '',
      id: '',
      visible: false,
      key: Date.now(),
      title: '',
      loading: false,
      hotSearchModal: {
        visible: false,
        key: null,
      },
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    this.props.dispatch(
      setConfig({
        selectKeys: this.props.selectKeys,
        openKeys: this.props.openKeys,
      })
    );
  }

  getData(overlap: any = {}) {
    const { current, size } = this.props.tableList;
    this.props.dispatch(
      getTableList('getHotWordList', 'hot_word_list', { current, size, ...overlap })
    );
  }

  exchangeOrder = (record: any, sortFlag: string) => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .sortHotWord({ id: record.id, sort_flag: sortFlag })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  getColumns = () => {
    const { on_show_count: total } = this.props.tableList.allData;
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '排序',
        key: 'sort',
        render: (text: any, record: any, i: number) => (
          <span>
            {requirePerm(
              this,
              'hot_word:exchange'
            )(
              <A
                disabled={getSeq(i) === 1 || getSeq(i) > total}
                className="sort-up"
                onClick={() => this.exchangeOrder(record, '0')}
              >
                <Icon type="up-circle" theme="filled" />
              </A>
            )}{' '}
            {requirePerm(
              this,
              'hot_word:exchange'
            )(
              <A
                disabled={getSeq(i) > total - 1}
                className="sort-down"
                onClick={() => this.exchangeOrder(record, '1')}
              >
                <Icon type="down-circle" theme="filled" />
              </A>
            )}
          </span>
        ),
        width: 70,
      },
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '热搜词',
        key: 'content',
        dataIndex: 'content',
      },
      {
        title: '状态',
        dataIndex: 'status',
        render: (text: number) => ['待展示', '展示中'][text],
        width: 80,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <span>
            {requirePerm(
              this,
              'hot_word:update'
            )(<A onClick={() => this.editWord(record)}>编辑</A>)}
            <Divider type="vertical" />
            {requirePerm(
              this,
              'hot_word:on_off'
            )(
              <A onClick={() => this.updateStatus(record)}>
                {record.status === 1 ? '下线' : '上线'}
              </A>
            )}
          </span>
        ),
        width: 90,
      },
    ];
  };

  updateStatus = (record: any) => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .updateHotWordStatus({ id: record.id, status: record.status === 1 ? 'off' : 'on' })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  editWord = (record: any = {}) => {
    // if (!record.id && this.props.tableList.total >= 10) {
    //   message.error('最多添加10个热搜词，请先删除再添加');
    //   return;
    // }
    this.setState({
      editWord: record.content || '',
      id: record.id || '',
      visible: true,
      key: Date.now(),
      title: record.id ? '编辑热搜词' : '添加热搜词',
    });
  };

  deleteWord = (record: any) => {
    Modal.confirm({
      title: <p>确认删除热搜词“{record.content}”吗？</p>,
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        api
          .deleteHotWord({ ids: record.id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            this.props.dispatch(setConfig({ loading: false }));
          })
          .catch(() => this.props.dispatch(setConfig({ loading: false })));
      },
    });
  };

  handleSubmit = () => {
    if (this.state.editWord.length === 0) {
      message.error('请输入热搜词');
      return;
    }
    if (this.state.editWord.length > 25) {
      message.error('热搜词长度不能超过25个字');
      return;
    }
    let func = 'createHotWord';
    const body: any = { content: this.state.editWord };
    if (this.state.id) {
      func = 'updateHotWord';
      body.id = this.state.id;
    }
    this.setState({ loading: true });
    api[func as keyof typeof api](body)
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setState({ loading: false, visible: false });
      })
      .catch(() => this.setState({ loading: false }));
  };

  render() {
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            {requirePerm(
              this,
              'hot_word:create'
            )(
              <Button onClick={() => this.editWord()}>
                <Icon type="plus-circle" /> 添加热搜词
              </Button>
            )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <span>最后更新时间：{this.props.tableList.allData.updated_at}</span>
            <PermButton
              perm="hot_word:list"
              style={{ marginLeft: 10 }}
              onClick={() => this.setState({ hotSearchModal: { visible: true, key: Date.now() } })}
            >
              最新热搜词
            </PermButton>
            <span style={{ marginLeft: 10, color: 'red' }}>App上展示仅前10条热搜词。</span>
          </Row>
          <Table
            columns={this.getColumns()}
            func="getHotWordList"
            index="hot_word_list"
            filter={{}}
            rowKey="id"
            pagination={true}
          />
          <Modal
            visible={this.state.visible}
            title={this.state.title}
            key={this.state.key}
            onOk={this.handleSubmit}
            onCancel={() => this.setState({ visible: false })}
            confirmLoading={this.state.loading}
          >
            <Form.Item>
              <Input
                placeholder="请输入热搜词"
                value={this.state.editWord}
                onChange={(e: any) => this.setState({ editWord: e.target.value })}
              />
            </Form.Item>
          </Modal>
          <HotSearchModal
            {...this.state.hotSearchModal}
            onCancel={() => {
              this.setState({
                ...this.state,
                hotSearchModal: {
                  visible: false,
                  key: null,
                },
              });
            }}
            onOk={() => {
              this.setState({
                ...this.state,
                hotSearchModal: {
                  visible: false,
                  key: null,
                },
              });
              this.getData({ current: 1, size: 10 });
            }}
          ></HotSearchModal>
        </div>
      </>
    );
  }
}

export default HotWord;
