/* eslint-disable no-return-assign */
/* eslint-disable no-nested-ternary */
import React from 'react';
import { setConfig } from '@app/action/config';
import { Table } from '@components/common';
import { requirePerm, HzMaterialText } from '@app/utils/utils';
import { Row, Col, message, Icon, Modal, Input, Button, Tooltip, Tag } from 'antd';
import { getTableList } from '@app/action/tableList';
import { opApi as api } from '@app/api';
import { PermA } from '@app/components/permItems';
import { withRouter } from 'react-router';
import connect from '@utils/connectTable';

@(withRouter as any)
@connect
class commercialAd extends React.Component<any, any> {
  formRef: any;
  constructor(props: any) {
    super(props);
    this.state = {
      loading: false,
    };
  }
  componentDidMount() {
    this.getData();
    this.props.dispatch(
      setConfig({
        selectKeys: this.props.selectKeys,
        openKeys: this.props.openKeys,
      })
    );
  }

  getData = () => {
    this.props.dispatch(getTableList('getCommercialAdList', 'result', {}));
  };

  // 下架
  updated = (record: any) => {
    let reason = '';
    let sticky = false;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      reason = v.target.value;
    };
    Modal.confirm({
      title: <p>下架原因</p>,
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <Input defaultValue={reason} onChange={positionChange} />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!reason) {
          message.error('请填写下架原因');
          return;
        }
        this.props.dispatch(setConfig({ loading: true }));
        api
          .downCommercialAd({ ad_id: record.ad_type === 1 ? record.ad_slot_no : record.id, reason: reason, ad_type: record.ad_type })
          .then(() => {
            message.success('操作成功');
            setTimeout(() => {
              this.getData();
              this.props.dispatch(setConfig({ loading: false }));
            }, WAIT_TIME);
            closeFunc();
          })
          .catch(() => {
            setTimeout(() => {
              this.getData();
              this.props.dispatch(setConfig({ loading: false }));
            }, WAIT_TIME);
          });
      },
    });
  };

  // 序号
  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '序号',
        key: 'seq',
        render: (a: any, b: any, c: number) => getSeq(c),
        width: 70,
      },
      {
        title: <div>
          广告位标题&nbsp;
          <Tooltip title="该标题仅用于后台展示。洪泽系统取「营销计划名称」">
            <Icon type="question-circle" />
          </Tooltip>
        </div>,
        dataIndex: 'title',
        render(text: string, record: any) {
          return <span>{text}{record.no_display && <>&nbsp;<Tag color="orange">未展示</Tag></>}</span>
        }
      },
      {
        title: '素材类型',
        key: 'material_types',
        dataIndex: 'material_types',
        render: (material_types: any, record: any) => {
          if (record.ad_type === 1) {
            return HzMaterialText[material_types] || '未知'
          } else {
            return material_types == 1 ? '长文章' : material_types == 2 ? '视频' : '长文章'
          }
        }
      },
      {
        title: '广告来源',
        dataIndex: 'ad_type',
        render: (ad_type: number) => ad_type === 1 ? '洪泽' : '潮新闻'
      },
      {
        title: '广告位名称',
        dataIndex: 'name',
      },
      {
        title: '系统类型',
        dataIndex: 'os_type',
        render: (os_type: number = 0) => ['\\', '安卓', 'iOS'][os_type]
      },
      {
        title: '广告位编码',
        dataIndex: 'ad_slot_no',
      },
      {
        title: '广告位位置',
        dataIndex: 'position',
        align: 'center',
        render: (position: any) => position || '\\'
      },
      {
        title: '操作',
        key: 'op',
        render: (record: any) => (
          <span>
            {requirePerm(
              this,
              'advertise:offline'
            )(
              <PermA perm="advertise:offline" onClick={() => this.updated(record)}>
                下架
              </PermA>
            )}
          </span>
        ),
        width: 160,
      },
    ];
  };

  changeRoute = (url: string) => {
    this.props.history.push(url);
  };

  render() {
    const { permissions } = this.props.session;
    const showAdvertiseCode = permissions.indexOf('advertise:list_space') > 0;
    return (
      <>
        {showAdvertiseCode && <Row className="layout-infobar">
          <Col span={24}>
            <Button onClick={this.changeRoute.bind(this, '/view/advertiseCode')}>广告位编码配置</Button>
          </Col>
        </Row>}
        <div className="component-content">
          <Table
            columns={this.getColumns()}
            rowKey="id"
            func="getCommercialAdList"
            index="result"
            pagination={true}
          />
        </div>
      </>
    );
  }
}
export default commercialAd;
