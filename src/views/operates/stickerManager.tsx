import { setConfig } from '@action/config';
import { getTableList } from '@app/action/tableList';
import { opApi as api } from '@app/api';
import { CommonObject } from '@app/types';
import Form from '@components/business/stickerForm';
import { A, Table, OrderColumn, Drawer } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setMenu, setLoading } from '@utils/utils.tsx';
import { Button, Col, Icon, message, Modal, Row, Divider } from 'antd';
import React from 'react';
import { withRouter } from 'react-router';

@(withRouter as any)
@connect
class StickerManager extends React.Component<any, any> {
  formRef: any;

  constructor(props: any) {
    super(props);
    this.state = {
      form: {
        visible: false,
        key: Date.now(),
        id: '',
        name: '',
        pic_url: '',
        resource_url: '',
      },
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    setMenu(this);
  }

  getData = (overlap: CommonObject = {}, filter = this.getFilter()) => {
    this.props.dispatch(
      getTableList('getStickerList', 'ugc_sticker_list', { ...filter, ...overlap })
    );
  };

  getFilter = () => {
    const { current, size } = this.props.tableList;
    const filter: CommonObject = { current, size };
    return filter;
  };

  getColumns = () => {
    const { current, size, total } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;

    return [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) => (
          <OrderColumn
            perm="ugc_sticker:exchange_order"
            start={1}
            end={total}
            pos={getSeq(i)}
            onUp={this.exchangeOrder.bind(this, record, getSeq(i), 1)}
            onDown={this.exchangeOrder.bind(this, record, getSeq(i), -1)}
          />
        ),
        width: 70,
      },
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '封面图',
        key: 'word',
        dataIndex: 'pic_url',
        render: (text: any) => <img src={text} className="list-pic" />,
        width: 200,
      },
      {
        title: '动效贴纸名称',
        key: 'created_at',
        dataIndex: 'name',
      },
      {
        title: '下载地址',
        key: 'download',
        render: (record: any) => (
          <span>
            <a href={record.ios_resource_url}>iOS</a>
            <Divider type="vertical" />
            <a href={record.android_resource_url}>安卓</a>
          </span>
        ),
        width: 120,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <span>
            {requirePerm(
              this,
              'ugc_sticker:update'
            )(<A onClick={() => this.editSticker(record)}>编辑</A>)}
            <Divider type="vertical" />
            {requirePerm(
              this,
              'ugc_sticker:delete'
            )(<A onClick={() => this.deleteSticker(record)}>删除</A>)}
          </span>
        ),
        width: 100,
      },
    ];
  };

  exchangeOrder = (record: any, current: number, offset: number) => {
    setLoading(this, true);
    api
      .sortSticker({
        current,
        offset,
        id: record.id,
      })
      .then(() => {
        message.success('操作成功');
        this.getData();
        setLoading(this, false);
      })
      .catch(() => {
        setLoading(this, false);
      });
  };

  deleteSticker = (record: any) => {
    Modal.confirm({
      title: <p>确认删除？</p>,
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        api
          .deleteSticker({ id: record.id })
          .then(() => {
            message.success('操作成功');
            this.props.dispatch(setConfig({ loading: false }));
            this.getData();
          })
          .catch(() => this.props.dispatch(setConfig({ loading: false })));
      },
    });
  };

  createSticker = () => {
    this.setState({
      form: {
        visible: true,
        key: Date.now(),
        id: '',
        name: '',
        pic_url: '',
        ios_resource_url: '',
        android_resource_url: '',
      },
    });
  };

  editSticker = (record: any) => {
    this.setState({
      form: {
        visible: true,
        key: Date.now(),
        id: record.id,
        name: record.name,
        pic_url: record.pic_url,
        ios_resource_url: record.ios_resource_url,
        android_resource_url: record.android_resource_url,
      },
    });
  };

  setRef = (instance: any) => {
    this.formRef = instance;
  };

  handleSubmit = () => {
    this.formRef.doSubmit();
  };

  closeDrawer = () => {
    this.setState({
      form: { ...this.state.form, visible: false },
    });
  };

  submitEnd = () => {
    this.getData();
    this.closeDrawer();
  };

  render() {
    const { form } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            {requirePerm(
              this,
              'ugc_sticker:create'
            )(
              <Button onClick={this.createSticker} style={{ marginRight: 8 }}>
                <Icon type="plus-circle" />
                添加动效贴纸
              </Button>
            )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getStickerList"
            index="ugc_sticker_list"
            filter={this.getFilter()}
            columns={this.getColumns()}
            rowKey="id"
            pagination={true}
          />
          <Drawer
            visible={form.visible}
            skey={form.key}
            onClose={this.closeDrawer}
            onOk={this.handleSubmit}
            title={form.id ? '编辑动态贴纸' : '创建动态贴纸'}
          >
            <Form
              formContent={form}
              wrappedComponentRef={this.setRef}
              onEnd={this.submitEnd}
              type={1}
            />
          </Drawer>
        </div>
      </>
    );
  }
}

export default StickerManager;
