import { opApi as api, releaseListApi, communityApi } from '@app/api';
import { ITableProps, IBaseProps, CommonObject } from '@app/types';
import { A, Table, BaseComponent, Drawer } from '@components/common';
import { PermA } from '@components/permItems';
import { connectTable as connect } from '@utils/connect';
import { Button, Col, Icon, Row, Input, Select, DatePicker, message, Radio, Divider } from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import { RangePickerValue } from 'antd/es/date-picker/interface';
import { TopicArticleRecord, TopicArticleAllData } from './operates';
import ChangeColumnGroupModal from './changeColumnGroupModal';
import { searchToObject } from '@app/utils/utils';

type State = {
  filter: {
    begin: string;
    end: string;
    search_type: 1 | 2 | 3 | 4 | 5;
    keyword: string;
    group_id: string;
  };
  cType: 1 | 2 | 3 | 4 | 5;
  cKeyword: string;
  id: string;
  name: string;
  preview: {
    visible: boolean;
    skey: number;
    create_date: number;
    author: string;
    content: string;
    title: string;
  };
  groupList: any[];
  changeGroupModal: any;
};

type Props = IBaseProps<
  ITableProps<TopicArticleRecord, TopicArticleAllData>,
  { id: string; name: string }
>;

class NewsTopicArticleList extends BaseComponent<
  ITableProps<TopicArticleRecord, TopicArticleAllData>,
  State,
  { id: string; name: string }
> {
  constructor(props: Props) {
    super(props);
    const { group_id = '0' } = searchToObject();
    this.state = {
      filter: {
        begin: '',
        end: '',
        search_type: 3,
        keyword: '',
        group_id: `${group_id}`,
      },
      cType: 3,
      cKeyword: '',
      id: props.match.params.id,
      name: decodeURIComponent(props.match.params.name),
      preview: {
        visible: false,
        skey: Date.now() + 1,
        create_date: 0,
        author: '',
        content: '',
        title: '',
      },
      groupList: [],
      changeGroupModal: {
        visible: false,
        record: null,
        columnId: '',
        columnName: '',
      },
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    this.getGroupList();
  }

  getGroupList = () => {
    communityApi
      .getColumnGroupsList({ topic_label_id: this.state.id, permission_type: '1' })
      .then((result) => {
        const {
          data: { group_list = [] },
        } = result as any;
        this.setState({
          ...this.state,
          groupList: group_list.map((item: any) => ({ value: item.id, label: item.name })),
        });
      })
      .catch((err) => {});
  };

  getData = (overlap: CommonObject = {}, filters = this.getFilters()) => {
    this.dispatchTable('getNewsTopicArticleList', 'list', { ...filters, ...overlap });
  };

  getFilters = () => {
    const { current, size } = this.props.tableList;
    const { filter, id } = this.state;
    const filters: CommonObject = { current, size, ...filter, topic_id: id };
    Object.keys(filter).map((k: string) => {
      if (!filters[k]) {
        delete filters[k];
      }
    });
    return filters;
  };

  preview = (record: any) => {
    this.setState({
      preview: {
        visible: true,
        skey: Date.now(),
        create_date: record.published_at,
        author: record.creator,
        content: record.content,
        title: record.list_title,
      },
    });
  };

  toMlf = (id: number, type: 'mlf_edit_url' | 'mlf_detail_url') => {
    releaseListApi
      .toMlf(type, { id })
      .then((r: any) => {
        window.open(r.data.url);
      })
      .catch();
  };

  getColumns = () => {
    const {
      current,
      size,
      total,
      records,
      allData: { top_count },
    } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    const disableSort = this.state.filter.begin || this.state.filter.keyword;
    return [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) => (
          <span>
            {this.requirePerm('topic_label:exchange_order')(
              <A
                disabled={
                  getSeq(i) === 1 || record.top || (i > 0 && records[i - 1].top) || disableSort
                }
                className="sort-up"
                onClick={this.exchangeOrder.bind(this, record.id, getSeq(i), 1)}
              >
                <Icon type="up-circle" theme="filled" />
              </A>
            )}{' '}
            {this.requirePerm('topic_label:exchange_order')(
              <A
                disabled={getSeq(i) >= total || record.top || disableSort}
                className="sort-down"
                onClick={this.exchangeOrder.bind(this, record.id, getSeq(i), -1)}
              >
                <Icon type="down-circle" theme="filled" />
              </A>
            )}
          </span>
        ),
        width: 70,
      },
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '稿件标题',
        key: 'list_title',
        dataIndex: 'list_title',
        render: (text: any, record: any) => (
          <a onClick={this.toMlf.bind(this, record.id, 'mlf_detail_url')} className="list-title">
            {text}
          </a>
        ),
      },
      {
        title: '发稿人',
        key: 'creator',
        dataIndex: 'creator',
        width: 110,
      },
      {
        title: '签发时间',
        key: 'published_at',
        dataIndex: 'published_at',
        render: (text: any, record: any) => (
          <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>
        ),
        width: 180,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <>
            <PermA perm="topic_label:0:update_group" onClick={() => this.handleChangeGroup(record)}>
              修改分组
            </PermA>
            <Divider type="vertical"></Divider>
            <PermA
              perm={record.top ? 'topic_article:un_top' : 'topic_article:top'}
              disabled={!record.top && top_count >= 5}
              onClick={() => this.handleChangeTop(record)}
            >
              {record.top ? '取消置顶' : '置顶'}
            </PermA>
          </>
        ),
        width: 180,
      },
    ];
  };

  handleChangeTop = (record: any) => {
    this.setLoading(true);
    api[record.top ? 'topicArticleUnTop' : 'topicArticleTop']({
      id: record.id,
      topic_label_id: this.state.id,
      group_id: this.state.filter.group_id,
    })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setLoading(false);
      })
      .catch(() => {
        this.setLoading(false);
      });
  };

  handleChangeGroup = (record: any) => {
    this.setState({
      ...this.state,
      changeGroupModal: {
        visible: true,
        record,
        columnId: this.state.id,
        columnName: this.state.name,
      },
    });
  };

  exchangeOrder = (id: number, current: number, offset: number) => {
    this.setLoading(true);
    api
      .sortNewsTopicArticle({ id, current, offset, topic_label_id: this.state.id, group_id: this.state.filter.group_id })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setLoading(false);
      })
      .catch(() => {
        this.setLoading(false);
      });
  };

  handleRangePickerChange = (dates: RangePickerValue) => {
    if (dates.length === 0) {
      this.setState(
        {
          filter: { ...this.state.filter, begin: '', end: '' },
        },
        () => this.getData({ current: 1 })
      );
    } else {
      this.setState(
        {
          filter: {
            ...this.state.filter,
            begin: dates[0] ? dates[0].format('YYYY-MM-DD') : '',
            end: dates[1] ? dates[1].format('YYYY-MM-DD') : '',
          },
        },
        () => this.getData({ current: 1 })
      );
    }
  };

  handleGroupChange = (e: any) => {
    this.setState(
      {
        filter: {
          ...this.state.filter,
          group_id: e.target.value,
        },
      },
      () => this.getData({ current: 1 })
    );
  };

  handleCTypeChange = (value: 1 | 2 | 3 | 4 | 5) => {
    this.setState({
      cType: value,
    });
  };

  handleKeywordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    this.setState({
      cKeyword: e.target.value,
    });
  };

  doSearch = () => {
    this.setState(
      {
        filter: {
          ...this.state.filter,
          search_type: this.state.cType,
          keyword: this.state.cKeyword,
        },
      },
      () => this.getData({ current: 1 })
    );
  };

  backToTopic = () => {
    // this.props.history.push('/view/columnManager');
    this.props.history.goBack();
  };

  closePreview = () => {
    this.setState({
      preview: { ...this.state.preview, visible: false },
    });
  };

  render() {
    const { filter, cType, cKeyword, preview, name } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            <Button onClick={this.backToTopic} style={{ marginRight: 8 }}>
              <Icon type="left-circle-o" />
              栏目管理
            </Button>
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {this.getCrumb(['运营管理', '栏目管理', name])}
          </Col>
        </Row>
        <div className="component-content">
          {this.state.groupList?.length > 0 && (
            <Row style={{ marginBottom: 16 }}>
              <Radio.Group
                value={filter.group_id}
                buttonStyle="solid"
                onChange={this.handleGroupChange}
              >
                <Radio.Button value="0">全部</Radio.Button>
                {/* <Radio.Button value={0}>无版块</Radio.Button> */}
                {this.state.groupList?.map((item: any) => (
                  <Radio.Button key={item.value} value={`${item.value}`}>
                    {item.label}
                  </Radio.Button>
                ))}
              </Radio.Group>
            </Row>
          )}
          <Row style={{ marginBottom: 16 }}>
            <Col span={12}>
              <DatePicker.RangePicker
                format="YYYY-MM-DD"
                onChange={this.handleRangePickerChange}
                value={
                  filter.begin
                    ? [moment(this.state.filter.begin), moment(this.state.filter.end)]
                    : []
                }
              />
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <Select
                value={cType}
                onChange={this.handleCTypeChange}
                style={{ marginRight: 8, width: 120 }}
              >
                <Select.Option value={2}>搜索媒立方ID</Select.Option>
                <Select.Option value={1}>搜索潮新闻ID</Select.Option>
                <Select.Option value={3}>搜索标题</Select.Option>
                <Select.Option value={4}>搜索发稿人</Select.Option>
              </Select>
              <Input
                value={cKeyword}
                onChange={this.handleKeywordChange}
                style={{ width: 180, marginRight: 8 }}
                onKeyPress={this.handleKey}
                placeholder="请输入搜索内容"
              />
              <Button onClick={this.doSearch}>
                <Icon type="search" />
                搜索
              </Button>
            </Col>
          </Row>
          <Table
            func="getNewsTopicArticleList"
            index="list"
            columns={this.getColumns()}
            pagination={true}
            rowKey="id"
            filter={this.getFilters()}
          />
          <Drawer
            visible={preview.visible}
            skey={preview.skey}
            title="预览稿件"
            closeText="关闭"
            onClose={this.closePreview}
          >
            <Row style={{ textAlign: 'center', fontSize: 24 }}>{preview.title}</Row>
            <Row style={{ textAlign: 'center' }}>
              作者：{preview.author}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 稿件发布时间：
              {moment(preview.create_date).format('YYYY-MM-DD HH:mm')}
            </Row>
            <Row style={{ textAlign: 'center' }}>
              <div dangerouslySetInnerHTML={{ __html: preview.content }} />
            </Row>
          </Drawer>
          <ChangeColumnGroupModal
            {...this.state.changeGroupModal}
            onCancel={() => this.setState({ ...this.state, changeGroupModal: { visible: false } })}
            onEnd={() => {
              this.getData();
              this.setState({ ...this.state, changeGroupModal: { visible: false } });
            }}
          ></ChangeColumnGroupModal>
        </div>
      </>
    );
  }
}

export default withRouter(connect<TopicArticleRecord, TopicArticleAllData>()(NewsTopicArticleList));
