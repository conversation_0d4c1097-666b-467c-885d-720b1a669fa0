/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, useMemo } from 'react';
import { useDispatch, useStore, useSelector } from 'react-redux';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { A, PreviewMCN, SearchAndInput, Table, ImageUploader } from '@components/common';
import { getCrumb, setMenuHook } from '@app/utils/utils';
import {
  Row,
  Col,
  Button,
  message,
  Icon,
  Modal,
  Form,
  Select,
  Tooltip,
  InputNumber,
  Radio,
  Divider,
  Input,
} from 'antd';
import { getTableList, clearTableList } from '@app/action/tableList';
import { opApi as api } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@app/components/permItems';

export default function ServiceKeywords(props: any) {
  const [form, setForm] = useState(
    () =>
      ({
        visible: false,
        key: Date.now(),
      } as any)
  );
  const dispatch = useDispatch();

  const { loading, run } = useXHR();

  const getList = () => {
    dispatch(getTableList('getSignList', 'sign_list', { current: 1, size: 10 }));
  };

  const formChange = (key: string, value: any) => {
    setForm({
      ...form,
      [key]: value,
    });
  };

  const editRecord = (record: any = {}) => {
    setForm({
      visible: true,
      key: Date.now(),
      id: record.id || '',
      title: record.title || '',
      pic_url: record.pic_url || '',
    });
  };

  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: '确定删除吗',
      onOk: () => {
        run(api.deleteSign, { id: record.id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const updateStatus = (record: any) => {
    run(api.updateSignStatus, { id: record.id, status: [1, 0][record.status] }, true).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 70,
    },
    {
      title: '名称',
      dataIndex: 'title',
    },
    {
      title: '背景图',
      dataIndex: 'pic_url',
      render: (text: any) => <img src={text} className="list-pic" />,
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (text: any) => ['下线', '上线'][text],
    },
    {
      title: '操作',
      key: 'op',
      render: (record: any) => (
        <span>
          <PermA perm="sign:update_status" onClick={() => updateStatus(record)}>
            {['上线', '下线'][record.status]}
          </PermA>
          <Divider type="vertical" />
          <PermA perm="sign:update" onClick={() => editRecord(record)}>
            编辑
          </PermA>
          <Divider type="vertical" />
          <PermA perm="sign:delete" onClick={() => deleteRecord(record)}>
            删除
          </PermA>
        </span>
      ),
      width: 140,
    },
  ];

  useEffect(() => {
    getList();
    setMenuHook(dispatch, props);
  }, []);

  const submit = () => {
    if (!form.title || !form.pic_url) {
      message.error('请填写表单项');
      return;
    }
    let service = api.createSign;
    const body: any = { title: form.title, pic_url: form.pic_url };
    if (form.id) {
      body.id = form.id;
      service = api.updateSign;
    }
    run(service, body).then(() => {
      message.success('操作成功');
      formChange('visible', false);
      getList();
    });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton perm="sign:create" onClick={() => editRecord()}>
            <Icon type="plus-circle" /> 新建背景图
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          filter={{}}
          columns={columns}
          rowKey="id"
          pagination
          func="getSignList"
          index="sign_list"
        />
        <Modal
          visible={form.visible}
          key={form.key}
          title={form.id ? '编辑背景图' : '新建背景图'}
          confirmLoading={loading}
          onCancel={() => formChange('visible', false)}
          onOk={submit}
        >
          <Form labelCol={{ span: 4 }} wrapperCol={{ span: 16 }}>
            <Form.Item label="名称" required>
              <Input
                placeholder="请输入名称"
                value={form.title}
                onChange={(e) => formChange('title', e.target.value)}
              />
            </Form.Item>
            <Form.Item label="图片" required extra="支持jpg,png 比例为6:5">
              <ImageUploader
                ratio={30 / 25}
                value={form.pic_url}
                onChange={(url: any) => formChange('pic_url', url)}
              />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </>
  );
}
