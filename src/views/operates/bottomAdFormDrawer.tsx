import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import { Button, Checkbox, Col, DatePicker, Dropdown, Form, Icon, Input, InputNumber, Menu, Modal, Radio, Row, Spin, Tooltip, message } from 'antd'
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react'

import { opApi } from '@app/api';
import '@components/business/styles/business.scss'
import { setMLoading } from '@app/utils/utils';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { Drawer, ImageUploader, VideoUploader } from '@app/components/common';
import ImgUploadList from '@app/components/common/imgUploadList';
import { PermButton } from '@app/components/permItems';
import moment from 'moment';
import { initial } from 'lodash';

const BottomAdFormDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();

  const { title, pic_url, url, ref_ids2, ref_ids3, ref_ids = '1', origin_user = '' } = props.formContent || {}

  const [imgs, setImgs] = useState([])
  const [videoURL, setVideoURL] = useState<any>('')
  const { getFieldDecorator, getFieldValue } = props.form;

  const [fieldsList, setFieldsList] = useState([])
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
      doSubmit
    }),
    []
  );


  useEffect(() => {
    if (!!props.formContent) {
      let video: any = null
      const imgs = props.formContent?.medias?.filter((item: any) => {
        if (item.type == 0) {
          return true
        }
        video = item
        return false
      }).map((v: any) => `${v.media_id},${v.url}`) || []

      setVideoURL(!!video ? `${video.media_id},${video.url}` : '')
      setImgs(imgs)
    }
  }, [props.formContent])

  const doSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body: any = { ...values };
        body.ref_ids2 = moment(body.time[0]).format('YYYY-MM-DD HH:mm:ss')
        body.ref_ids3 = moment(body.time[1]).format('YYYY-MM-DD HH:mm:ss')
        delete body.time
        if (body.ref_ids == '2') {
          body.origin_user = ''
        }
        let api
        if (props.formContent) {
          body.id = props.formContent.id
          api = opApi.bottomRecommendUpdate
        } else {
          api = opApi.bottomRecommendCreate
        }

        dispatch(setConfig({ mLoading: true }))
        api(body)
          .then(() => {
            dispatch(setConfig({ mLoading: false }))
            message.success('操作成功');
            props.onEnd(values.type);
          })
          .catch(() => {
            dispatch(setConfig({ mLoading: false }))
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  }

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };
  // const menu = (
  //   <Menu onClick={props.onClose}>
  //     <Menu.Item key="1" style={{ textAlign: 'center' }}>
  //       仅保存修改
  //     </Menu.Item>
  //     <Menu.Item key="2" style={{ textAlign: 'center' }}>
  //       保存并通过
  //     </Menu.Item>
  //   </Menu>
  // );
  return <Drawer
    title={!!props.formContent ? '编辑广告' : '添加广告'}
    visible={props.visible}
    skey={props.skey}
    onClose={props.onClose}
    onOk={doSubmit}
    okText='确定'
  >
    <Form {...formLayout}>
      <Form.Item label="名称">
        {getFieldDecorator('title', {
          initialValue: title,
          rules: [{
            required: true,
            message: '请输入名称',
            whitespace: true
          }, {
            max: 20,
            message: '最多20字'
          }
          ],
        })(<Input placeholder='请输入名称，不超过20个字'></Input>)}
      </Form.Item>

      <Form.Item label="图片" extra="支持扩展名：.jpg .jpeg .png，比例 4.3:1">
        {getFieldDecorator('pic_url', {
          initialValue: pic_url,
          rules: [
            {
              required: true,
              message: '请上传图片',
            },
          ],
        })(<ImageUploader ratio={4.3} />)}
      </Form.Item>
      <Form.Item label="链接">
        {getFieldDecorator('url', {
          initialValue: url,
          rules: [
            {
              required: true,
              message: '请填写链接',
            },
            {
              validator: (rule: any, value: any, callback: any) => {
                const regex = /^https?:\/\//;
                if (value?.length > 0 && !regex.test(value)) {
                  callback('请正确填写关联链接');
                  return;
                }
                callback();
              },
            },
          ],
        })(<Input placeholder="请输入链接" />)}
      </Form.Item>
      <Form.Item label="展示时间">
        {getFieldDecorator('time', {
          initialValue: !!props.formContent ? [moment(ref_ids2), moment(ref_ids3)] : [],
          rules: [
            {
              required: true,
              message: '请设置展示时间',
            },
          ],
        })(
          <DatePicker.RangePicker
            format="YYYY-MM-DD HH:mm:ss"
            showTime={{ format: 'HH:mm:ss' }}
          />
        )}
      </Form.Item>

      <Form.Item label="展示设置">
        {getFieldDecorator('ref_ids', {
          initialValue: ref_ids,
          rules: [{
            required: true,
            message: '请选择展示设置',
          },],
        })(<Radio.Group>
          <Radio value={`1`}>部分稿件不展示</Radio>
          <Radio value={`2`}>所有稿件都展示&nbsp;<Tooltip title='所有稿件的范围是新闻稿、视频稿'>
            <Icon type="question-circle" />
          </Tooltip></Radio>
        </Radio.Group>)}
      </Form.Item>

      {getFieldValue('ref_ids') == '1' && <Form.Item label="" style={{ paddingLeft: 134, marginTop: -20 }}>
        <div>媒立方中设置不展示广告位的稿子不会展示，如有其他稿件可填入下方输入框</div>
        {getFieldDecorator('origin_user', {
          initialValue: origin_user || '',
          // preserve: true,
          rules: [
            // {
            //   required: true,
            //   message: '请填写黑名单',
            // },
            // {
            //   max: 2000,
            //   message: '用户昵称不能超过2000个字',
            // },
          ],
        })(<Input.TextArea rows={8} placeholder="请输入稿件id，用英文逗号隔开" />)}
      </Form.Item>}

    </Form>
  </Drawer>
}

export default Form.create<any>({ name: 'BottomAdFormDrawer' })(forwardRef<any, any>(BottomAdFormDrawer));
