import { Button, Form, Input, message, Modal } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { opApi as api } from '@app/api';
import { PermButton } from '@app/components/permItems';
import { debounce } from 'lodash';

export const SyntheticAudioDialog = (props: any) => {
  const [value, setValue] = useState('');
  const interval = useRef<any>(null);
  const [loading, setLoading] = useState(false);

  const [detail, setDetail] = useState<any>(props.record);
  useEffect(() => {
    setDetail(props.record);
    getDetail();
  }, [props.record]);

  useEffect(() => {
    return () => {
      if (interval.current) {
        clearInterval(interval.current);
        interval.current = null;
      }
    };
  }, []);

  const startInterval = () => {
    interval.current = setInterval(() => {
      getDetail(true);
    }, 2000);
  };

  const getDetail = (query = false) => {
    const reqData = {
      pub_date: props.record.pub_date,
      time_type: props.record.time_type,
    };
    api.detailReadPaper(reqData).then((res: any) => {
      const { detail } = res.data;
      setDetail(detail);
      const status = detail?.paper_audio_status ?? 0;

      if (status == 1 && interval.current == null) {
        startInterval();
      }

      if (query) {
        if (status != 2 && status != 3) {
        } else {
          if (interval.current) {
            clearInterval(interval.current);
            interval.current = null;
          }
          if (status == 2) {
            message.success('合成音频成功');
          } else {
            message.error('合成音频失败');
          }
        }
      } else {
        setValue(detail?.paper_audio_prefix || '');
      }
    });
  };

  const mergeAudio = debounce(
    () => {
      setLoading(true);
      api
        .mergeAudio({
          id: props.record?.auto_pk,
          paper_audio_prefix: value,
        })
        .then((res) => {
          startInterval();
          setDetail({
            ...detail,
            paper_audio_status: 1,
          });
        })
        .catch((err) => {
          message.error('合成音频失败');
        })
        .finally(() => {
          setLoading(false);
        });
    },
    2000,
    { leading: true, trailing: false }
  );

  const paper_audio_status = detail?.paper_audio_status ?? 0;
  return (
    <Modal
      visible={props.visible}
      // key={props.key}
      title=""
      width="500px"
      maskClosable={false}
      onCancel={props.onCancel}
      onOk={props.onOk}
      footer={null}
      destroyOnClose
    >
      <Form.Item label="请输入音频开头文字（打招呼语、天气等）" required>
        <Input.TextArea
          value={value}
          onChange={(e) => setValue(e.target.value)}
          rows={4}
          placeholder="最多输入 100 字"
          maxLength={100}
        />
        <div style={{ textAlign: 'right' }}>{value.length}/100</div>

        <div>
          <PermButton
            type="primary"
            perm="read_paper:merge_audios"
            onClick={() => {
              mergeAudio();
            }}
            style={{ marginRight: 10 }}
            disabled={paper_audio_status == 1 || !value || loading}
          >
            {loading
              ? '合成中...'
              : ['合成音频', '合成中...', '合成音频', '合成音频'][paper_audio_status]}
          </PermButton>
          {!!detail?.paper_audio_url && (
            <Button
              type="primary"
              onClick={() => {
                fetch(detail?.paper_audio_url)
                  .then((response) => response.blob())
                  .then((blob) => {
                    const a = document.createElement('a');
                    a.href = URL.createObjectURL(blob);
                    a.download = '早晚报音频.mp3';
                    a.click();
                    URL.revokeObjectURL(a.href);
                  })
                  .catch(() => {
                    message.error('下载音频失败');
                  });
              }}
            >
              下载音频
            </Button>
          )}
        </div>

        <div style={{ color: 'orange' }}>
          说明：<br></br>
          1. 系统针对开头文字和稿件的标题摘要，自动合成一个音频。<br></br>
          2. 若修改了标题或摘要，请再次重新合成音频。
        </div>
      </Form.Item>
    </Modal>
  );
};

export default SyntheticAudioDialog;
