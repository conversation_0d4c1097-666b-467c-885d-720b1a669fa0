import {
  <PERSON><PERSON>,
  Col,
  DatePicker,
  Form,
  Icon,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Radio,
  Select,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { opApi } from '@app/api';
import { A, Drawer, ImageUploader } from '@app/components/common';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';

const AddCreatorBanner = (props: any, ref: any) => {
  const dispatch = useDispatch();

  const { getFieldDecorator, getFieldsValue, setFieldsValue, getFieldValue } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = () => {
    // e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        dispatch(setConfig({ mLoading: true }));

        if (props.record) {
          values.id = props.record.id ?? '';
        }

        opApi
          .editCreativeCenterBanner(values)
          .then((res: any) => {
            message.success(!props.record ? '新增成功' : '更新成功');
            dispatch(setConfig({ mLoading: false }));
            props.onEnd && props.onEnd();
          })
          .catch(() => {
            // message.error('添加失败');
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
        dispatch(setConfig({ mLoading: false }));
      }
    });
  };

  return (
    <Drawer
      title={!props.record ? '添加轮播图' : '编辑轮播图'}
      visible={props.visible}
      skey={props.key}
      onClose={props.onClose}
      maskClosable={false}
      // width={500}
      onOk={handleSubmit}
      okText="保存"
      okPerm={!props.record ? "create_recommend:create" : "create_recommend:edit"}
    >
      <Form {...formLayout}>
        <Form.Item label="名称">
          {getFieldDecorator(`name`, {
            initialValue: props.record?.name,
            rules: [
              {
                required: true,
                message: '请输入轮播图名称',
                whitespace: true,
              },
              {
                max: 20,
                message: '最多输入20字',
              },
            ],
          })(<Input placeholder="请输入轮播图名称"></Input>)}
        </Form.Item>
        <Form.Item label="链接">
          {getFieldDecorator(`link`, {
            initialValue: props.record?.link,
            rules: [
              {
                required: true,
                message: '请输入链接',
              },
              {
                validator: (rule: any, value: any, callback: any) => {
                  const regex = /^https?:\/\//;
                  if (value?.length > 0 && !regex.test(value)) {
                    callback('请正确填写链接');
                    return;
                  }
                  callback();
                },
              },
            ],
          })(<Input placeholder="请输入链接"></Input>)}
        </Form.Item>
        <Form.Item label="图片" extra="支持.jpg .jpeg .png等格式，推荐尺寸750*174">
          {getFieldDecorator(`pic`, {
            initialValue: props.record?.pic,
            rules: [
              {
                required: true,
                message: '请上传图片',
              },
            ],
          })(<ImageUploader ratio={750 / 174}></ImageUploader>)}
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'AddCreatorBanner' })(
  forwardRef<any, any>(AddCreatorBanner)
);
