import { setConfig } from '@app/action/config';
import { opApi } from '@app/api';
import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import {
  Button,
  Checkbox,
  Form,
  Icon,
  Input,
  InputNumber,
  Modal,
  Radio,
  Spin,
  Table,
  Tabs,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useState } from 'react';
import { PhotoSlider } from 'react-photo-view';
import { useDispatch, useSelector } from 'react-redux';

const EditColumnClassifyModal = (props: any, ref: any) => {
  const { getFieldDecorator } = props.form;
  const dispatch = useDispatch();
  const loading = useSelector((state: any) => state.config.loading);
  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const submit = () => {
    props.form.validateFields((err: any, values: any) => {
      if (!err) {
        dispatch(setConfig({ loading: true }));
        const api = props.record ? 'updateColumnClass' : 'createColumnClass';
        const params = {
          ...values,
          type: 0,
        };
        if (props.record) {
          params.id = props.record.id;
        }
        opApi[api](params)
          .then((res: any) => {
            dispatch(setConfig({ loading: false }));
            message.success('操作成功');
            props.onOk();
          })
          .catch(() => dispatch(setConfig({ loading: false })));
      } else {
        console.log(err);
      }
    });
  };
  return (
    <Modal
      width={500}
      visible={props.visible}
      title={props.record ? '编辑分类' : '添加分类'}
      key={props.key}
      onCancel={props.onCancel}
      onOk={submit}
      maskClosable={false}
      destroyOnClose={true}
    >
      <Spin spinning={loading}>
        <Form {...formLayout}>
          <Form.Item label="分类名称">
            {getFieldDecorator('name', {
              initialValue: props.record?.name,
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: '请输入分类名称',
                },
              ],
            })(<Input placeholder="请输入分类名称，最多6字" maxLength={6} />)}
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<any>({ name: 'EditColumnClassifyModal' })(
  forwardRef<any, any>(EditColumnClassifyModal)
);
