import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useStore, useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, A, OrderColumn, Drawer } from '@components/common';
import { CommonObject } from '@app/types';
import { getCrumb, setMenuHook, UserDetail } from '@app/utils/utils';
import {
  Row,
  Col,
  Input,
  Divider,
  Button,
  Select,
  Icon,
  Modal,
  message,
  InputNumber,
  Tooltip,
  Timeline,
  Switch,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { opApi as api, communityApi, releaseListApi, userApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@components/permItems';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import '@components/business/styles/business.scss';
import _ from 'lodash';
import CommonAddUserModal from '@app/components/common/commonAddUserModal';

const defaultSize = 10;
export default function ImWhiteListManager(props: any) {
  const [accountOptions, setAccountOptions] = useState([]);

  const [filter, setFilter] = useState<any>({});

  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const { loading, run } = useXHR();
  const addRef = useRef<any>(null);
  const [drawer, setDrawer] = useState({
    visible: false,
    key: 0,
    record: null,
  });

  const [userDetail, setUserDetail] = useState({
    key: null,
    visible: false,
    detail: null,
  });

  const [addUserModal, setAddUserModal] = useState({
    visible: false,
    key: '',
  });

  const [logs, setLogs] = useState<any>({
    visible: false,
    logs: [],
    title: '',
  });

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const getList = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current;

    dispatch(getTableList('getIMWhiteList', 'list', { current: cur, size, ...newFilter }));
  };

  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确定要删除吗？`,
      onOk: () => {
        run(api.deleteIMWhiteList, { id: record.id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const getColumns = () => {
    let values = [
      {
        title: '账号昵称',
        dataIndex: 'account_name',
        width: 150,
        render: (text: any, record: any) => (
          <A onClick={() => showUserDetailModal(record.account_id, true)}>{text}</A>
        ),
      },
      {
        title: '小潮号',
        dataIndex: 'chao_id',
        width: 150,
      },
      {
        title: '操作人',
        dataIndex: 'updated_by',
        width: 150,
      },
      {
        title: '操作时间',
        dataIndex: 'updated_at',
        width: 160,
        render: (text: any, record: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any, i: number) => (
          <span>
            <PermA perm="im_whitelist:delete" onClick={() => deleteRecord(record)}>
              删除
            </PermA>
          </span>
        ),
        width: 140,
      },
    ];
    return values;
  };

  useEffect(() => {
    // getList({ current: 1, size: 10 });
    setMenuHook(dispatch, props);
    getList(true);
  }, []);

  const addWhiteList = () => {
    setAddUserModal({ ...addUserModal, visible: true });
  };

  // 显示用户详情
  const showUserDetailModal = (id: any, visible: boolean) => {
    run(userApi.getUserDetail, { accountId: id }, true).then((r: any) => {
      setUserDetail({
        key: Date.now(),
        visible,
        detail: r.data.account,
      });
    });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button onClick={() => history.goBack()}>
            <Icon type="left-circle" />
            返回
          </Button>

          <PermButton perm="im_whitelist:create" style={{ marginLeft: 8 }} onClick={addWhiteList}>
            添加白名单
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          func="getIMWhiteList"
          index="list"
          filter={filter}
          columns={getColumns()}
          rowKey="id"
          pagination={true}
        />
        <CommonAddUserModal
          title="添加私信白名单账号"
          addApi={api.addIMWhiteList}
          {...addUserModal}
          onEnd={() => {
            setAddUserModal({ ...addUserModal, visible: false });
            getList();
          }}
          onCancel={() => setAddUserModal({ ...addUserModal, visible: false })}
        ></CommonAddUserModal>
      </div>

      <Modal
        visible={userDetail.visible}
        key={userDetail.key}
        title="用户详情"
        width={800}
        onCancel={() => setUserDetail({ ...userDetail, visible: false })}
        onOk={() => setUserDetail({ ...userDetail, visible: false })}
      >
        {userDetail.visible && <UserDetail detail={userDetail.detail} />}
      </Modal>
    </>
  );
}
