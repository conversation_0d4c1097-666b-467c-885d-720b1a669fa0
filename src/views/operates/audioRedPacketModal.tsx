import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import {
  Button,
  Form,
  Icon,
  Input,
  InputNumber,
  Modal,
  Spin,
  Switch,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useState } from 'react';

import { opApi } from '@app/api';
import { Drawer } from '@app/components/common';

const AudioRedPacketModal = (props: any, ref: any) => {
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator } = props.form;

  const formLayout = {
    labelCol: { span: 5 },
    wrapperCol: { span: 19 },
  };

  const handleSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true);
        const parmas = {
          ...values,
          redpacket_rule: values.redpacket_rule || '',
          redpacket_info_list: JSON.stringify(values.redpacket_info_list),
        };
        opApi
          .addAudioPlazaRedpacket(parmas)
          .then((res: any) => {
            message.success('添加成功');
            setLoading(false);
            props.onOk && props.onOk();
          })
          .catch(() => {
            // message.error('添加失败');
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
        setLoading(false);
      }
    });
  };
  const redpacket_info_list =
    props.record?.redpacket_info_list?.length > 0 && JSON.parse(props.record?.redpacket_info_list);
  return (
    <Drawer
      // width={600}
      visible={props.visible}
      title="红包活动"
      skey={props.key}
      onClose={props.onCancel}
      onOk={handleSubmit}
      maskClosable={false}
      // destroyOnClose={true}
      // confirmLoading={loading}
      okPerm={'audio_home:redpacket_save'}
    >
      <Spin spinning={loading}>
        <Form {...formLayout} onSubmit={handleSubmit}>
          <Form.Item label="红包开关">
            {getFieldDecorator('redpacket_switch', {
              valuePropName: 'checked',
              initialValue: props.record?.redpacket_switch,
            })(<Switch checkedChildren="开" unCheckedChildren="关"></Switch>)}
          </Form.Item>
          <Form.Item label="活动规则" extra="最多1000字">
            {getFieldDecorator('redpacket_rule', {
              initialValue: props.record?.redpacket_rule || '',
            })(<Input.TextArea rows={4} placeholder="请输入活动规则" maxLength={1000}></Input.TextArea>)}
          </Form.Item>

          {(redpacket_info_list || [1, 2, 3]).map((v: any, index: number) => {
            return (
              <div key={index}>
                <h3>红包{index + 1}</h3>
                <Form.Item label="红包id">
                  {getFieldDecorator(`redpacket_info_list[${index}].redpacket_id`, {
                    initialValue: redpacket_info_list?.[index]?.redpacket_id,
                    rules: [
                      {
                        required: true,
                        message: '请输入红包ID',
                        whitespace: true,
                      },
                    ],
                  })(
                    <Input
                      style={{
                        width: '400px',
                      }}
                      placeholder="请输入红包活动管理页创建的红包ID"
                    ></Input>
                  )}
                </Form.Item>

                <Form.Item label="听新闻时长">
                  {getFieldDecorator(`redpacket_info_list[${index}].single_redpacket_time`, {
                    initialValue: redpacket_info_list?.[index]?.single_redpacket_time,
                    rules: [
                      {
                        required: true,
                        message: '请输入听新闻时长',
                      },
                    ],
                  })(
                    <InputNumber
                      style={{
                        width: '400px',
                        marginRight: 10,
                      }}
                      placeholder="请输入听新闻时长"
                      precision={0}
                    ></InputNumber>
                  )}
                  <span>分钟</span>
                </Form.Item>
              </div>
            );
          })}

          <h3>
            低版本红包
            <span style={{ fontSize: 12, color: '#999', marginLeft: 10 }}>
              在v6.8.0、6.8.1中生效
            </span>
          </h3>

          <Form.Item label="红包id">
            {getFieldDecorator(`redpacket_id`, {
              initialValue: props.record?.redpacket_id,
              rules: [
                {
                  required: true,
                  message: '请输入红包ID',
                  whitespace: true,
                },
              ],
            })(
              <Input
                style={{
                  width: '400px',
                }}
                placeholder="请输入红包活动管理页创建的红包ID"
              ></Input>
            )}
          </Form.Item>
          <Form.Item label="单个红包听新闻时长">
            {getFieldDecorator(`single_redpacket_time`, {
              initialValue: props.record?.single_redpacket_time,
              rules: [
                {
                  required: true,
                  message: '请输入听新闻时长',
                },
              ],
            })(
              <InputNumber
                style={{
                  width: '400px',
                  marginRight: 10,
                }}
                placeholder="请输入听新闻时长"
                precision={0}
              ></InputNumber>
            )}
            <span>分钟</span>
          </Form.Item>
          <Form.Item label="单日拆红包个数">
            {getFieldDecorator(`redpacket_day_count`, {
              initialValue: props.record?.redpacket_day_count,
              rules: [
                {
                  required: true,
                  message: '请输入单日拆红包个数',
                },
              ],
            })(
              <InputNumber
                style={{
                  width: '400px',
                  marginRight: 10,
                }}
                placeholder="请输入红包个数"
                precision={0}
              ></InputNumber>
            )}
          </Form.Item>
        </Form>
      </Spin>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'AudioRedPacketModal' })(
  forwardRef<any, any>(AudioRedPacketModal)
);
