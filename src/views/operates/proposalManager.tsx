/* eslint-disable no-nested-ternary */
import { opApi as api } from '@app/api';
import { ITableProps, IBaseProps, CommonObject } from '@app/types';
import { A, Table, BaseComponent, Drawer } from '@components/common';
import Form from '@components/business/proposalForm';
import IndexForm from '@components/business/indexProposalForm';
import NavRecommend from '@components/business/navRecommend';
import NavIcon from '@components/business/navIcon';

import { connectTable as connect } from '@utils/connect';
import { Button, Col, Icon, message, Modal, Row, Menu, Dropdown, Radio, Divider, Form as AForm } from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import { RadioChangeEvent } from 'antd/es/radio';
import { isArLink, getArInfo } from '@components/common/arLinkInput';
import { ProposalRecord, ProposalAllData } from './operates';
import { setConfig } from '@app/action/config';

type State = {
  type: 3 | 4 | 19 | 999 | 9999;
  buttonState: Boolean,
  form: {
    show: boolean;
    key: number;
    id?: number;
    title: string;
    pic_url: string;
    url: string;
    visible?: 0 | 1;
    visible_org?: 0 | 1;
    to_all?: 0 | 1;
    origin_user?: string;
  }
  preview: {
    show: boolean;
    key: number;
    to_all: boolean;
    origin_user: string;
    fail_visible_user: string;
    visible_org: boolean;
    visible: boolean;
    title: string;
    pic_url: string;
    url: string;
  };
  recommend: {
    show: boolean;
    id: number;
    key: number;
    nav_position: string;
    bubble_type: number;
    bubble_content: string;
    pop_rule: number;
    disappear_rule: number;
  };
  icons: {
    show: boolean;
    id: number;
    key: number;
    nav_position: String;
    select_icon: String;
    unselect_icon: String;
    select_color: String;
    unselect_color: String;
    max_size: boolean;
    config_type: number;
    show_rule: number;
    file: object | null;
    fileTwo: object | null;
  };
  onCount: number;
  routeInfo: any;
};

type Props = IBaseProps<ITableProps<ProposalRecord, ProposalAllData>>;

class ProposalManager extends BaseComponent<ITableProps<ProposalRecord, ProposalAllData>, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      type: 4,
      buttonState: false,
      form: {
        show: false,
        key: Date.now(),
        title: '',
        pic_url: '',
        url: '',
        to_all: 1,
        visible: 1,
        visible_org: 0,
        origin_user: '',
      },
      preview: {
        show: false,
        key: Date.now() + 1,
        to_all: false,
        origin_user: '',
        fail_visible_user: '',
        visible_org: false,
        visible: false,
        title: '',
        pic_url: '',
        url: '',
      },
      recommend: {
        show: false,
        id: 1,
        key: Date.now() + 2,
        nav_position: "",
        bubble_type: 0,
        bubble_content: "",
        pop_rule: 1,
        disappear_rule: 1,
      },
      icons: {
        show: false,
        id: 1,
        key: Date.now() + 3,
        nav_position: '',
        select_icon: "",
        unselect_icon: "",
        select_color: '',
        unselect_color: '',
        max_size: false,
        config_type: 0,
        show_rule: 0,
        file: null
      },
      onCount: 0,
      routeInfo: {
        isFloatWindow: props.match.url.startsWith('/view/floatWindow/'),
        ...props.match.params
      }
    };
  }

  componentDidMount() {
    const { isFloatWindow, id, from } = this.state.routeInfo
    if (isFloatWindow) {
      this.props.dispatch(
        setConfig({ openKeys: this.props.openKeys, selectKeys: [`/view/${from}?channel_id=${id}`] })
      );
    } else {
      this.setMenu();
    }
    this.getData({ current: 1, size: 10 });
  }

  componentDidUpdate(prevProps: Props) {
    if (
      prevProps.tableList.timestamp !== this.props.tableList.timestamp &&
      Boolean(this.props.tableList.allData.recommend_list)
    ) {
      this.setState({
        onCount: this.props.tableList.allData.on_show_count,
      });
    }
  }

  getData = (overlap: CommonObject = {}) => {
    const { isFloatWindow, id } = this.state.routeInfo
    const { current, size } = this.props.tableList;
    if (isFloatWindow) {
      this.dispatchTable('getChannelFloatWindowList', 'recommend_list', {
        current,
        size,
        channel_id: id,
        ...overlap,
      });
    } else {
      this.dispatchTable('getProposalList', 'recommend_list', {
        current,
        size,
        type: this.state.type,
        ...overlap,
      });
    }
  };
  getData2 = (overlap: CommonObject = {}) => {
    const { current, size } = this.props.tableList;
    this.dispatchTable('getNavRecommendlList', 'nav_recommend_list', {
      current,
      size,
      ...overlap,
    });
  };
  getData3 = (overlap: CommonObject = {}) => {
    const { current, size } = this.props.tableList;

    this.dispatchTable('getNavIconList', 'nav_icon_config_list', {
      current,
      size,
      ...overlap,
    }, this.tableCallBack);
  };

  // 列表返回调
  tableCallBack = () => {
    // alert(222)
    this.setState({
      buttonState: this.props.tableList.records[0].status === 1
    })
  }
  getColumns2 = () => {
    const columns = [
      {
        title: '位置',
        key: 'nav_name',
        dataIndex: 'nav_name',
        width: 120,
      },
      {
        title: '气泡类型',
        key: 'bubble_type',
        dataIndex: 'bubble_type',
        render: (bubble_type: number) => (
          <span>{(bubble_type === 1 ? '图片' : '文本')}</span>
        ),
        width: 120,
      },
      {
        title: '气泡内容',
        key: 'bubble_content',
        dataIndex: 'bubble_content',
        render: (bubble_type: number, data: any) => (
          <span>{(data.bubble_type === 1 ? <img src={data.bubble_content} className="list-pic" alt="123" /> : data.bubble_content)}</span>
        ),
        width: 180,
      },
      // {
      //   title: type === 4 ? '名称' : '',
      //   key: 'title',
      //   dataIndex: 'title',
      //   width: type === 4 ? 200 : 0,
      // },
      {
        title: '弹出规则',
        key: 'pop_rule',
        dataIndex: 'pop_rule',
        render: (pop_rule: boolean) => (
          <span>{((pop_rule || 1) + '天1次')}</span>
        ),
        width: 120,
      },
      {
        title: '消失规则',
        key: 'disappear_rule',
        dataIndex: 'disappear_rule',
        width: 200,
        render: (bubble_type: Number) => (
          <span>{(bubble_type == 1 ? '用户点击后消失' : '展示5秒后自动消失')}</span>
        ),
      },
      {
        title: '修改人',
        key: 'updated_by',
        dataIndex: 'updated_by',
        width: 90,
      },
      {
        title: '修改时间',
        key: 'created_at',
        dataIndex: 'updated_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 95,
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'status',
        render: (text: number) => <span>{text === 0 ? '未展示' : '展示中'}</span>,
        width: 80,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <span>
            {this.requirePerm(
              'nav_recommend:update'
            )(
              <A disabled={record.status === 1} onClick={this.editRecord2.bind(this, record)}>
                编辑
              </A>
            )}

            <Divider type="vertical" />

            {this.requirePerm(
              'nav_recommend:update_displayed'
            )(
              <A onClick={this.updateStatus2.bind(this, record)}>
                {record.status === 1 ? '下架' : '上架'}
              </A>
            )}

          </span>
        ),
        width: 180,
      },
    ];
    return columns;
  };
  getColumns = () => {
    const { current, size, total } = this.props.tableList;
    const { type, onCount, routeInfo } = this.state;
    const { isFloatWindow, id } = routeInfo
    const getSeq = (i: number) => (current - 1) * size + i + 1;

    const getDropdown = (record: any) => {
      const menu = (
        <Menu>
          {this.requirePerm(isFloatWindow ? `channel_win_recommend:${id}:update` : `type_recommend:${type}:update`)(
            <Menu.Item onClick={this.editRecord.bind(this, record)}>编辑</Menu.Item>
          )}
          {this.requirePerm(isFloatWindow ? `channel_win_recommend:${id}:update_status` : `type_recommend:${type}:update_status`)(
            <Menu.Item onClick={this.updateStatus.bind(this, record)}>
              {record.status === 0 ? '上架' : '下架'}
            </Menu.Item>
          )}
          {this.requirePerm(isFloatWindow ? `channel_win_recommend:${id}:delete` : `type_recommend:${type}:delete`)(
            <Menu.Item onClick={this.deleteRecord.bind(this, record)}>删除</Menu.Item>
          )}
          {type === 4 &&
            this.requirePerm(isFloatWindow ? '' : 'type_recommend:view_float_win')(
              <Menu.Item onClick={this.getIndexProposalDetail.bind(this, record)}>
                查看详情
              </Menu.Item>
            )}
        </Menu>
      );
      return (
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };

    const orderColumn = [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) => (
          <span>
            {this.requirePerm(isFloatWindow ? `channel_win_recommend:${id}:update_sort` : `type_recommend:${type}:update_sort`)(
              <A
                disabled={getSeq(i) === 1 || record.status === 0}
                className="sort-up"
                onClick={this.exchangeOrder.bind(this, record.id, 0)}
              >
                <Icon type="up-circle" theme="filled" />
              </A>
            )}{' '}
            {this.requirePerm(isFloatWindow ? `channel_win_recommend:${id}:update_sort` : `type_recommend:${type}:update_sort`)(
              <A
                disabled={getSeq(i) >= onCount || record.status === 0}
                className="sort-down"
                onClick={this.exchangeOrder.bind(this, record.id, 1)}
              >
                <Icon type="down-circle" theme="filled" />
              </A>
            )}
          </span>
        ),
        width: 70,
      },
    ];
    const columns = [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: 'ID',
        key: 'id',
        dataIndex: 'id',
        width: 70,
      },
      {
        title: '图标',
        key: 'icon',
        dataIndex: 'pic_url',
        render: (text: string) => <img src={text} className="list-pic" alt="123" />,
        width: 120,
      },
      {
        title: '链接',
        key: 'url',
        dataIndex: 'url',
        render: (text: string) =>
          isArLink(text) ? (
            `EazyAR ID: ${getArInfo(text).arId}`
          ) : (
            <a href={text} target="_blank" rel="noreferrer">
              {text}
            </a>
          ),
      },
      // {
      //   title: type === 4 ? '名称' : '',
      //   key: 'title',
      //   dataIndex: 'title',
      //   width: type === 4 ? 200 : 0,
      // },
      {
        title: '名称',
        key: 'title',
        dataIndex: 'title',
        width: 150,
      },
      {
        title: type === 4 ? '面向用户' : '',
        key: 'to_all',
        dataIndex: 'to_all',
        width: type === 4 ? 120 : 0,
        render: (text: boolean) => (
          <span>{type === 4 ? (text ? '全部用户' : '部分用户') : ''}</span>
        ),
      },
      {
        title: '创建人',
        key: 'created_by',
        dataIndex: 'created_by',
        width: 90,
      },
      {
        title: '修改时间',
        key: 'created_at',
        dataIndex: 'updated_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 95,
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'status',
        render: (text: number) => <span>{text === 0 ? '未展示' : '展示中'}</span>,
        width: 80,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => getDropdown(record),
        width: 70,
      },
    ];
    // if (type !== 4) {
    return [...orderColumn, ...columns];
    // }
    // return columns;
  };
  getColumns3 = () => {
    const columns = [
      {
        title: '导航位置',
        key: 'nav_position',
        dataIndex: 'nav_position',
        width: 120,
      },
      {
        title: '选中图标',
        key: 'select_icon',
        dataIndex: 'select_icon',
        render: (bubble_type: number, data: any) => (
          data.select_icon && <img src={data.select_icon} className="list-pic" />
        ),
        width: 180,
      },
      {
        title: '未选中图标',
        key: 'unselect_icon',
        dataIndex: 'unselect_icon',
        render: (bubble_type: number, data: any) => (
          data.select_icon && <img src={data.unselect_icon} className="list-pic" />
        ),
        width: 180,
      },
      {
        title: '图标类型',
        key: 'file_json_url',
        dataIndex: 'file_json_url',
        render: (text: any) => <span>{text ? '动画' : '图片'}</span>,
        // width: 95,
      },
      {
        title: '修改人',
        key: 'updated_by',
        dataIndex: 'updated_by',
        // width: 90,
      },
      {
        title: '修改时间',
        key: 'created_at',
        dataIndex: 'updated_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 95,
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'status',
        render: (text: number) => <span>{text === 0 ? '未展示' : '展示中'}</span>,
        // width: 80,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <span>
            {this.requirePerm(
              'nav_icon_config:update'
            )(
              <A disabled={record.status === 1} onClick={this.editRecord3.bind(this, record)}>
                编辑
              </A>
            )}
            <Divider type="vertical" />
            {this.requirePerm(
              'nav_icon_config:update_displayed'
            )(
              <A onClick={this.updateStatus3.bind(this, record)}>
                {record.status === 1 ? '下架' : '上架'}
              </A>
            )}
          </span>
        ),
        width: 180,
      },
    ];
    return columns;
  };
  getIndexProposalDetail = (record: any) => {
    const { routeInfo: { isFloatWindow } } = this.state;
    this.setLoading(true);
    api[isFloatWindow ? 'getChannelFloatWindowDetail' : 'getIndexProposalDetail']({ id: record.id })
      .then((r: any) => {
        this.setState({
          preview: {
            show: true,
            key: Date.now(),
            to_all: r.data.float_win_detail.to_all,
            origin_user: r.data.float_win_detail.origin_user,
            fail_visible_user: r.data.float_win_detail.fail_visible_user,
            visible_org: r.data.float_win_detail.visible_org,
            visible: r.data.float_win_detail.visible,
            title: r.data.float_win_detail.title,
            pic_url: r.data.float_win_detail.pic_url,
            url: r.data.float_win_detail.url,
          },
        });
        this.setLoading(false);
      })
      .catch(() => this.setLoading(false));
  };

  exchangeOrder = (id: number, sortFlag: number) => {
    const { routeInfo: { isFloatWindow } } = this.state;
    this.setLoading(true);
    api[isFloatWindow ? 'sortChannelFloatWindow' : 'sortProposal']({ id, sort_flag: sortFlag })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setLoading(false);
      })
      .catch(() => {
        this.setLoading(false);
      });
  };

  editRecord = (record: any = {}) => {
    const keys = Object.keys(record);
    this.setState({
      form: {
        show: true,
        key: Date.now(),
        title: record.title || '',
        pic_url: record.pic_url || '',
        url: record.url || '',
        id: record.id || '',
        to_all: keys.indexOf('to_all') > -1 ? (record.to_all ? 1 : 0) : 1,
        visible: keys.indexOf('visible') > -1 ? (record.visible ? 1 : 0) : 1,
        visible_org: record.visible_org ? 1 : 0,
        origin_user: record.origin_user || '',
      },
    });
  };

  editRecord2 = (record: any = {}) => {
    this.setState({
      recommend: {
        show: true,
        key: Date.now() + 2,
        id: record.id || '',
        nav_position: record.nav_position || '',
        bubble_type: record.bubble_type || '',
        bubble_content: record.bubble_content || '',
        pop_rule: record.pop_rule || '',
        disappear_rule: record.disappear_rule || '',
        nav_type: record.nav_type || '',
        nav_name: record.nav_name || '',
      },
    });
  };

  editRecord3 = (record: any = {}) => {
    const { file_json_url, unselect_file_json_url } = record
    this.setState({
      icons: {
        show: true,
        key: Date.now() + 3,
        id: record.id || '',
        nav_position: record.nav_position || '',
        select_icon: record.select_icon || '',
        unselect_icon: record.unselect_icon || '',
        select_color: record.select_color || '',
        unselect_color: record.unselect_color || '',
        max_size: record.max_size,
        config_type: file_json_url ? 1 : 0,
        show_rule: record.show_rule === 0 ? 0 : 1,
        fileTwo: unselect_file_json_url ? (() => {
          const pathComponents = unselect_file_json_url.split('/')
          const name = pathComponents[pathComponents.length - 1]
          return { name, file_json_url: unselect_file_json_url }
        })() : null,
        file: file_json_url ? (() => {
          const pathComponents = file_json_url.split('/')
          const name = pathComponents[pathComponents.length - 1]
          return { name, file_json_url }
        })() : null,
      },
    });
  };
  updateStatus = (record: any) => {
    const { type, routeInfo: { isFloatWindow } } = this.state;
    const total = this.state.onCount;
    if (isFloatWindow) {
      if (record.status === 0 && total >= 3) {
        message.error('频道浮窗已达上限3个，请先下架再上架');
        return;
      }
    } else if (record.status === 0 && (type === 3 || type === 19) && total >= 5) {
      message.error('个人中心推荐位已达上限5个，请先下架再上架');
      return;
    }
    this.setLoading(true);
    api[isFloatWindow ? 'updateChannelFloatWindowStatus' : 'updateProposalStatus']({ id: record.id, status: record.status === 0 ? 1 : 0 })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setLoading(false);
      })
      .catch(() => {
        this.setLoading(false);
      });
  };
  updateStatus2 = (record: any) => {
    this.setLoading(true);
    api
      .updateNavRecommend({ id: record.id, status: record.status === 0 ? 1 : 0 })
      .then(() => {
        message.success('操作成功');
        this.getData2();
        this.setLoading(false);
      })
      .catch(() => {
        this.setLoading(false);
      });
  };
  updateStatus3 = (record: any) => {
    this.setLoading(true);
    api
      .updateNaveIcons({ id: record.id, status: record.status === 0 ? 1 : 0 })
      .then(() => {
        message.success('操作成功');
        this.getData3({ current: 1, size: 10 });
        this.setLoading(false);
      })
      .catch(() => {
        this.setLoading(false);
      });
  };
  deleteRecord = (record: any) => {
    const { routeInfo: { isFloatWindow } } = this.state;
    Modal.confirm({
      title: '确定删除吗？',
      onOk: () => {
        api[isFloatWindow ? 'deleteChannelFloatWindow' : 'deleteProposal']({
          id: record.id,
        }).then(() => {
          message.success('操作成功');
          this.getData();
          this.setLoading(false);
        }).catch(() => {
          this.setLoading(false);
        });
      },
    });
  };

  handleTypeChange = (e: RadioChangeEvent) => {
    if (e.target.value === this.state.type) {
      return;
    }
    this.setState(
      {
        type: e.target.value,
      },
      () => {
        if (e.target.value === 999) {
          this.getData2({ current: 1, size: 10 })
        } else if (e.target.value === 9999) {
          this.getData3({ current: 1, size: 10 })
        } else {
          this.getData()
        }
      }
    );
  };

  closePreview = () => {
    this.setState({
      preview: { ...this.state.preview, show: false },
    });
  };

  closeDrawer = () => {
    this.setState({
      form: { ...this.state.form, show: false },
      recommend: { ...this.state.recommend, show: false },
      icons: { ...this.state.icons, show: false },
    });
  };
  closeDrawer2 = () => {
    this.setState({
      recommend: { ...this.state.recommend, show: false },
    });
  };
  closeDrawer3 = () => {
    this.setState({
      icons: { ...this.state.icons, show: false },
    });
  };
  submitEnd = () => {
    this.closeDrawer();
    this.getData()
  };
  submitEnd2 = () => {
    this.closeDrawer2();
    this.getData2({ current: 1, size: 10 })
  };
  submitEnd3 = () => {
    this.closeDrawer3();
    this.getData3({ current: 1, size: 10 })
  };
  render() {
    const { routeInfo } = this.state
    const { isFloatWindow, id, from } = routeInfo
    const { form, type, buttonState, preview, recommend, icons } = this.state;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    };
    return (
      <>
        <Row className="layout-infobar">
          <Col span={18}>
            {isFloatWindow && <Button
              // onClick={this.changeRoute.bind(
              //   this,
              //   `/view/${from}?channel_id=${id}`
              // )}
              onClick={() => this.props.history.goBack()}
            >
              <Icon type="left-circle" /> 返回
            </Button>}
            {
              !isFloatWindow && <Radio.Group
                value={type}
                onChange={this.handleTypeChange}
                buttonStyle="solid"
              >
                {this.requirePerm('type_recommend:4:view')(
                  <Radio.Button value={4}>首页浮窗</Radio.Button>
                )}
                {this.requirePerm('type_recommend:3:view')(
                  <Radio.Button value={3}>个人中心推荐位</Radio.Button>
                )}
                {this.requirePerm('type_recommend:19:view')(
                  <Radio.Button value={19}>个人中心推荐位（底部）</Radio.Button>
                )}
                {this.requirePerm('nav_recommend:list')(
                  <Radio.Button value={999}>气泡推荐位</Radio.Button>
                )}
                {this.requirePerm('nav_icon_config:list')(
                  <Radio.Button value={9999}>导航图标自定义</Radio.Button>
                )}

              </Radio.Group>
            }
            {this.requirePerm(isFloatWindow ? `channel_win_recommend:${id}:create` : `type_recommend:${type}:create`)(
              <Button onClick={this.editRecord} style={{ marginLeft: 8 }}>
                <Icon type="plus-circle" />
                {isFloatWindow ? '创建浮窗' : '添加推荐位'}
              </Button>
            )}
          </Col>
          <Col span={6} className="layout-breadcrumb">
            {this.getCrumb()}
          </Col>
        </Row>
        <div className="component-content">
          {type === 9999 ? <div style={{ marginTop: -14, marginBottom: 8, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {/*<div style={{ color: '#999' }}>注意：导航图标、文案的配置，仅针对客户端V5.3.0以上版本生效。</div>*/}
            <div style={{ color: '#999' }}>此处配置仅针对客户端V5.3.0~V6.0.0版本生效，如需修改新版本样式请前往【主题管理】添加全局生效主题</div>
            {/* <div>{
              buttonState ? <span>  {this.requirePerm(`nav_icon_config:update_displayed`)(
                <Button onClick={this.updateStatus3.bind(this, buttonState)}>
                  一键下架
                </Button>
              )} </span> : <span>{this.requirePerm(`nav_icon_config:update_displayed`)(
                <Button onClick={this.updateStatus3.bind(this, buttonState)} style={{ marginRight: 20 }}>
                  一键上架
                </Button>
              )}</span>

            }</div> */}
          </div> : ''}
          {type === 999 ? <Table
            func="getNavRecommendlList"
            index="nav_recommend_list"
            pagination={true}
            filter={{}}
            rowKey="id"
            columns={this.getColumns2()}
          /> : type === 9999 ? <Table
            func="getNavIconList"
            index="nav_icon_config_list"
            pagination={true}
            filter={{ type }}
            rowKey="id"
            columns={this.getColumns3()}
          /> : <Table
            func={isFloatWindow ? 'getChannelFloatWindowList' : 'getProposalList'}
            index="recommend_list"
            pagination={true}
            filter={isFloatWindow ? { channel_id: id } : { type }}
            rowKey="id"
            columns={this.getColumns()}
          />}
          <Drawer
            visible={form.show}
            skey={form.key}
            title={`${form.id ? '编辑' : '创建'}${type === 4 ? `${isFloatWindow ? '' : '首页'}浮窗` : '个人中心推荐位'}${type === 19 ? '（底部）' : ''
              }`}
            onClose={this.closeDrawer}
            onOk={this.handleSubmitForm.bind(this, 'form')}
          >
            {type !== 4 ? (
              <Form
                wrappedComponentRef={this.setFormRef.bind(this, 'form')}
                formContent={form}
                type={type}
                onEnd={this.submitEnd}
              />
            ) : (
              <IndexForm
                wrappedComponentRef={this.setFormRef.bind(this, 'form')}
                formContent={form}
                type={type}
                onEnd={this.submitEnd}
                routeInfo={routeInfo}
              />
            )}
          </Drawer>
          <Drawer
            visible={recommend.show}
            skey={recommend.key}
            title={'编辑'}
            onClose={this.closeDrawer}
            onOk={this.handleSubmitForm.bind(this, 'recommend')}
          >
            <NavRecommend
              wrappedComponentRef={this.setFormRef.bind(this, 'recommend')}
              formContent={recommend}
              onEnd={this.submitEnd2}
            />
          </Drawer>
          <Drawer
            visible={icons.show}
            skey={icons.key}
            title={'编辑'}
            onClose={this.closeDrawer}
            onOk={this.handleSubmitForm.bind(this, 'icons')}
          >
            <NavIcon
              wrappedComponentRef={this.setFormRef.bind(this, 'icons')}
              formContent={icons}
              onEnd={this.submitEnd3}
            />
          </Drawer>
          <Modal
            title={`${isFloatWindow ? '' : '首页'}浮窗详情`}
            visible={preview.show}
            key={preview.key}
            okText={null}
            onCancel={this.closePreview}
          >
            <AForm {...formLayout}>
              <AForm.Item label="名称">{preview.title}</AForm.Item>
              <AForm.Item label="图标">
                <img src={preview.pic_url} style={{ width: 200 }} alt="fd" />
              </AForm.Item>
              <AForm.Item label="链接">
                <a href={preview.url} target="_blank" rel="noreferrer">
                  {preview.url}
                </a>
              </AForm.Item>
              <AForm.Item label="面向用户">
                {preview.to_all ? (
                  '全部用户'
                ) : (
                  <>
                    <Row>
                      <Col span={5}>{preview.visible ? '给谁看' : '不给谁看'}：</Col>
                      <Col span={19}>
                        {preview.visible_org && <Row>机构用户</Row>}
                        {preview.origin_user.split(',').map((v: any) => (
                          <Row key={v}>
                            <a href={v}>{v}</a>
                          </Row>
                        ))}
                      </Col>
                    </Row>
                    {preview.fail_visible_user && (
                      <Row>
                        <Col span={5}>无效用户：</Col>
                        <Col span={19}>
                          {preview.fail_visible_user.split(',').map((v: any) => (
                            <Row key={v}>
                              <a href={v}>{v}</a>
                            </Row>
                          ))}
                        </Col>
                      </Row>
                    )}
                  </>
                )}
              </AForm.Item>
            </AForm>
          </Modal>
        </div>
      </>
    );
  }
}

export default withRouter(connect<ProposalRecord, ProposalAllData>()(ProposalManager));
