import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useStore, useDispatch } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, A, Drawer, OrderColumn } from '@components/common';
import Form from '@components/business/activityForm';
import { getCrumb, setMenuHook } from '@app/utils/utils';
import {
  Row,
  Col,
  Input,
  Button,
  message,
  Icon,
  Modal,
  Dropdown,
  Menu,
  DatePicker,
  Tag,
  Radio,
  Tooltip,
} from 'antd';
import { PermMenuItem, PermButton, PermA } from '@components/permItems';
import { getTableList } from '@app/action/tableList';
import { opApi as api } from '@app/api';
import useXHR from '@utils/useXhr';
import moment from 'moment';
import { useSelector } from 'react-redux';
import SecondFloorModal from './secondFloorModal';

export default function ServiceKeywords(props: any) {
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const history = useHistory();
  const dataRef: any = useRef();
  const [form, setForm] = useState(
    () =>
      ({
        visible: false,
        key: Date.now(),
      } as any)
  );
  const [filter, setFilter] = useState(() => ({
    timeRange: [] as any,
    title: '',
    status: 1 as any,
    enabled: true,
  }));
  const [keyword, setKeyword] = useState('');
  const [positionValue, setPositionValue]: any = useState('');
  const formRef: any = useRef({});
  const dispatch = useDispatch();
  const store = useStore();
  const [sortIsShow, setSortIsShow] = useState(false);
  const { loading, run } = useXHR();

  const [secondFloorModal, setSecondFloorModal] = useState({
    visible: false,
    data: null,
  });

  const f = useMemo(() => {
    let body: any = { ...filter };

    if (filter.enabled) {
      if (filter.status === 'top') {
        delete body.status;
        body.top = true;
      } else {
        body.top = false;
      }
    } else {
      body = { enabled: filter.enabled, top: false };
    }

    if (filter.timeRange.length > 0) {
      body.begin = filter.timeRange[0].format('YYYY-MM-DD');
      body.end = filter.timeRange[1].format('YYYY-MM-DD');
    }
    if (filter.title) {
      body.title = filter.title;
    }
    return body;
  }, [filter]);
  const getList = (overlap: any = {}) => {
    const { current, size = 10 } = store.getState().tableList;
    let data = {
      ...f,
      current,
      size,
      ...overlap,
      enabled: filter.enabled,
    };

    if (filter.enabled) {
      if (filter.status === 'top') {
        data.top = true;
        delete data.status;
      } else {
        data.top = false;
      }
    } else {
      data = { current, size, enabled: filter.enabled, title: filter.title, top: false };
    }

    if (filter.timeRange.length > 0) {
      data.begin = filter.timeRange[0].format('YYYY-MM-DD');
      data.end = filter.timeRange[1].format('YYYY-MM-DD');
    }

    dispatch(getTableList('getActivityList', 'list', data));
  };

  const editRecord = (record: any) => {
    setForm({
      visible: true,
      key: Date.now(),
      id: record.id,
      title: record.title,
      time: [moment(record.begin), moment(record.end)],
      list_pic: record.list_pic,
      base_count: record.base_count,
      url: record.url,
      show_number: record.show_number,
      second_floor_list_pic: record.second_floor_list_pic,
    });
  };

  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确认删除吗？`,
      onOk: () => {
        run(api.deleteActivity, { id: record.id }, true).then(() => {
          message.success('操作成功');
          getList({ current: 1 });
        });
      },
    });
  };

  const handleChangeStatus = (record: any) => {
    run(api.updateActivityStatus, { id: record.id, enabled: !record.enabled }, true).then(() => {
      message.success('操作成功');
      getList();
    });
  };

  const handleChangeTop = (record: any) => {
    // const { top_count = 0 } = store.getState().tableList.allData;
    // if (!record.top && top_count >= 5) {
    //   message.error('置顶的活动大于5条，请先取消置顶再置顶');
    //   return;
    // }
    run(api.updateActivityTop, { id: record.id, top: !record.top }, true).then(() => {
      message.success('操作成功');
      getList();
    });
  };
  const positionChange = (e: any) => {
    setPositionValue(e.target.value);
    if (e.target.value >= total) {
      dataRef.current = total;
      return;
    }
    dataRef.current = e.target.value;
  };
  const getSeq = (i: number) => {
    const { current, size } = store.getState().tableList;
    return (current - 1) * size + i + 1;
  };
  function sortNum(record: any, b: any, i: any) {
    dataRef.current = getSeq(i);
    Modal.confirm({
      title: ` 排序：${record.title}`,
      content: (
        <div className="example-input">
          <div>
            请输入排序位置
            <Input
              defaultValue={getSeq(i)}
              type="number"
              maxLength={2}
              style={{ width: '40%', marginLeft: '10px' }}
              onChange={(e) => positionChange(e)}
              placeholder=""
            />
          </div>
          {/* <Input placeholder="default size" />
               <Input size="small" placeholder="small size" /> */}
        </div>
      ),
      onOk() {
        listSort(record, 2, dataRef.current || (getSeq(i) >= 99 ? 99 : getSeq(i)));
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  }
  const listSort = (val: any, num: any, position: any) => {
    console.log(position, 'position');

    const data = position
      ? {
          id: val.id,
          sort_flag: num,
          position: position,
        }
      : {
          id: val.id,
          sort_flag: num,
        };
    api.activitySort(data).then((res) => {
      getList();
    });
  };
  const columns = [
    {
      title: '排序',
      key: 'order',
      render: (text: any, record: any, i: number) => {
        return (
          <OrderColumn
            pos={getSeq(i)}
            start={1}
            end={total}
            perm={`activity:update_sort`}
            disableUp={!record.enabled || sortIsShow}
            disableDown={!record.enabled || sortIsShow}
            onUp={() => listSort(record, 0, '')}
            onDown={() => listSort(record, 1, '')}
          />
        );
      },
      width: 70,
    },
    {
      title: '序号',
      key: 'seq',
      render: (a: any, b: any, c: number) => getSeq(c),
      width: 70,
    },
    {
      title: '活动标题',
      dataIndex: 'title',
      render: (text: any, record: any) => (
        <span>
          {record.top && <Tag color="red">置顶</Tag>}
          {text}
        </span>
      ),
    },
    {
      title: '开始时间',
      dataIndex: 'begin',
      render: (text: number) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 180,
    },
    {
      title: '结束时间',
      dataIndex: 'end',
      render: (text: number) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 180,
    },
    {
      title: '活动状态',
      dataIndex: 'status',
      render: (text: number) => (
        <span style={{ display: 'flex', alignItems: 'center' }}>
          <span
            style={{
              color: ['blue', 'green', 'grey'][text],
              fontSize: 30,
            }}
          >
            &#8226;
          </span>
          {['未开始', '进行中', '已结束'][text]}
        </span>
      ),
      width: 80,
    },
    {
      title: '上下线',
      dataIndex: 'enabled',
      render: (text: boolean) => (text ? '上线' : '下线'),
      width: 70,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      render: (text: number) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 180,
    },
    {
      title: '操作',
      key: 'op',
      render: (record: any, b: any, i: number) => (
        <Dropdown
          overlay={
            <Menu>
              <PermMenuItem perm="activity:update" onClick={() => editRecord(record)}>
                编辑
              </PermMenuItem>
              <PermMenuItem
                perm="activity:update_sort"
                onClick={() => sortNum(record, b, i)}
                disabled={!record.enabled || record.status == 2 || sortIsShow}
              >
                排序
              </PermMenuItem>
              <PermMenuItem
                perm="activity:change_enabled"
                onClick={() => handleChangeStatus(record)}
              >
                {record.enabled ? '下线' : '上线'}
              </PermMenuItem>
              <PermMenuItem
                perm="activity:change_top"
                disabled={!record.enabled}
                onClick={() => handleChangeTop(record)}
              >
                {record.top ? '取消置顶' : '置顶'}
              </PermMenuItem>
              <PermMenuItem perm="activity:delete" onClick={() => deleteRecord(record)}>
                删除
              </PermMenuItem>
            </Menu>
          }
        >
          <a className="dropdown-link">
            操作
            <Icon type="down" />
          </a>
        </Dropdown>
      ),
      width: 70,
    },
  ];

  const getColumns = () => {
    return columns.filter((item) => {
      if (filter.enabled) {
        if (filter.status == 2) {
          return item.key !== 'order';
        }
        return true;
      } else {
        return item.key !== 'order' && item.dataIndex !== 'status';
      }
    });
  };

  useEffect(() => {
    getList({ current: 1 });
    setMenuHook(dispatch, props);
  }, [f]);
  useEffect(() => {
    if (!!filter.title || !!filter.timeRange.length) {
      setSortIsShow(true);
    } else {
      setSortIsShow(false);
    }
  }, [filter]);
  function submitEnd() {
    getList();
    setForm((s: any) => ({ ...s, visible: false }));
  }

  const showSecondFloorModal = () => {
    setSecondFloorModal({
      visible: true,
      data: null,
      key: Date.now()
    });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton
            perm="activity:create"
            style={{ marginLeft: 8 }}
            onClick={() => setForm({ visible: true, key: Date.now() })}
          >
            <Icon type="plus-circle" /> 新建活动
          </PermButton>
          <PermButton
            perm={'type_recommend:34:view'}
            style={{ marginLeft: 8 }}
            onClick={() => history.push('/view/adamantineMgr')}
          >
            金刚位管理
          </PermButton>
          <PermButton
            perm={'activity:second_floor_list'}
            // perm=""
            style={{ marginLeft: 8 }}
            onClick={showSecondFloorModal}
          >
            2楼活动排序
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 10 }}>
          <Radio.Group
            value={filter.enabled}
            onChange={(e) => setFilter({ ...filter, enabled: e.target.value })}
          >
            <Radio.Button value={true}>上线</Radio.Button>
            <Radio.Button value={false}>下线</Radio.Button>
          </Radio.Group>
        </Row>
        <Row style={{ marginBottom: 16 }}>
          {filter.enabled && (
            <Col span={7}>
              <Radio.Group
                value={filter.status}
                onChange={(e) => setFilter({ ...filter, status: e.target.value })}
              >
                <Radio.Button value={'top'}>置顶</Radio.Button>
                <Radio.Button value={1}>进行中</Radio.Button>
                <Radio.Button value={0}>未开始</Radio.Button>
                <Radio.Button value={2}>已结束</Radio.Button>
              </Radio.Group>
            </Col>
          )}
          <Col span={11}>
            <Tooltip title={'筛选同时包含开始时间和结束时间的活动'}>
              <Icon type="question-circle" />
            </Tooltip>
            <DatePicker.RangePicker
              style={{ marginLeft: 5 }}
              value={filter.timeRange}
              onChange={(timeRange) => setFilter({ ...filter, timeRange })}
            />
          </Col>
          <Col span={6} style={{ textAlign: 'right' }}>
            <Input
              value={keyword}
              style={{ width: 180 }}
              placeholder="输入搜索关键词"
              onChange={(e: any) => setKeyword(e.target.value)}
              onKeyPress={(e) => e.which === 13 && setFilter({ ...filter, title: keyword })}
            />
            <Button
              type="primary"
              style={{ marginLeft: 8, verticalAlign: 'top' }}
              onClick={() => setFilter({ ...filter, title: keyword })}
            >
              搜索
            </Button>
          </Col>
        </Row>
        <Table
          func="getActivityList"
          index="list"
          filter={f}
          columns={getColumns()}
          rowKey="id"
          pagination={true}
        />
        <Drawer
          visible={form.visible}
          title={form.id ? '编辑活动' : '新建活动'}
          skey={form.key}
          onClose={() => setForm((s: any) => ({ ...s, visible: false }))}
          onOk={() => formRef.current.doSubmit()}
        >
          <Form
            wrappedComponentRef={(ref: any) => {
              formRef.current = ref;
            }}
            formContent={form}
            onEnd={submitEnd}
          />
        </Drawer>
        <SecondFloorModal
          {...secondFloorModal}
          onCancel={() => {
            setSecondFloorModal({
              ...secondFloorModal,
              visible: false,
            });
          }}
        ></SecondFloorModal>
      </div>
    </>
  );
}
