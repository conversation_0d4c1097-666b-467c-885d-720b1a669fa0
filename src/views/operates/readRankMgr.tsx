import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useStore, useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, A, OrderColumn, Drawer } from '@components/common';
import { CommonObject } from '@app/types';
import { getCrumb, objectToQueryString, searchToObject, setMenuHook } from '@app/utils/utils';
import {
  Row,
  Col,
  Input,
  Divider,
  Button,
  Select,
  Icon,
  Modal,
  message,
  InputNumber,
  Tooltip,
  DatePicker,
  Timeline,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { opApi as api, releaseListApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@components/permItems';
import Radio from 'antd/es/radio';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import AssistantForm from './assistantForm';
import { render } from 'react-dom';
import ReactClipboard from 'react-clipboardjs-copy';
import AddPartyNewsDrawer from './addPartyNewsDrawer';
import ImagePreviewColumn from '@app/components/common/imagePreviewColumn';
import AddReadRankDrawer from './addReadRankDrawer';

const defaultSize = 10;
export default function PartyNewsMgr(props: any) {
  const [filter, setFilter] = useState<any>({
    // title: '',
    status: '',
    type: parseInt(searchToObject().type ?? 43),
  });

  const [search, setSearch] = useState('');
  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const {
    total,
    current,
    size = 10,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const { loading, run } = useXHR();
  const [logs, setLogs] = useState<any>({
    visible: false,
    logs: [],
    title: '',
  });

  const [drawer, setDrawer] = useState<any>({
    visible: false,
    skey: 0,
    record: null,
  });

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const getList = (goFirstPage: boolean = false, overlap: CommonObject = {}) => {
    const relCurrent = goFirstPage ? 1 : current;
    const map: any = {};
    for (const key in filter) {
      if (filter[key]) {
        map[key] = filter[key];
      }
    }
    dispatch(
      getTableList('getBookRankList', 'subject_list', {
        ...map,
        current: relCurrent,
        size,
        ...overlap,
      })
    );
  };

  const editRecord = (record: any) => {
    run(api.bookRankDetail, { id: record.id }, true)
      .then((data: any) => {
        const {
          data: { recommend, book_list },
        } = data;
        const body = {
          ...recommend,
          book_list,
        };

        setDrawer({
          skey: Date.now(),
          visible: true,
          record: body,
        });
      })
      .catch((e: any) => {});
  };
  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确定要删除吗？`,
      onOk: () => {
        run(api.deleteBookRank, { id: record.id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const updateRecordStatus = (record: any) => {
    run(api.updateBookRankStatus, { id: record.id, status: record.status == 1 ? 0 : 1 }, true).then(
      () => {
        message.success('操作成功');
        getList();
      }
    );
  };

  const getColumns = () => {
    let values = [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: filter.type == 48 ? '年度' : '日期',
        dataIndex: 'ref_ids',
        width: 120,
      },
      {
        title: '状态',
        dataIndex: 'status',
        width: 80,
        render: (text: any) => (text == 0 ? '待展示' : '展示中'),
      },
      {
        title: '操作人',
        dataIndex: 'updated_by',
        width: 130,
      },
      {
        title: '操作时间',
        dataIndex: 'updated_at',
        width: 100,
        render: (text: any, record: any) => {
          // onClick={() => getOperateLog(record)}
          return (
            <div style={{ whiteSpace: 'normal' }}>
              <div>{moment(text).format('YYYY-MM-DD')}</div>
              <div>{moment(text).format('HH:mm:ss')}</div>
            </div>
          );
        },
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any, i: number) => (
          <span>
            <PermA
              perm={`type_recommend:43:update_status`}
              onClick={() => updateRecordStatus(record)}
            >
              {record.status == 1 ? '下架' : '上架'}
            </PermA>
            <Divider type="vertical" />
            <PermA perm={`type_recommend:43:update`} onClick={() => editRecord(record)}>
              编辑
            </PermA>
            <Divider type="vertical" />
            <PermA perm={`type_recommend:43:delete`} onClick={() => deleteRecord(record)}>
              删除
            </PermA>
            <Divider type="vertical" />
            <ReactClipboard
              action="copy"
              text={record.url}
              onSuccess={() => message.success('链接已复制')}
              onError={() => message.error('复制失败')}
            >
              <a>复制链接</a>
            </ReactClipboard>
          </span>
        ),
        width: 220,
      },
    ];

    return values;
  };

  useEffect(() => {
    // getList({ current: 1, size: 10 });
    setMenuHook(dispatch, props);
  }, []);

  useEffect(() => {
    getList(true);
  }, [filter]);

  const getOperateLog = (record: any) => {
    releaseListApi
      .getRecommendOperateLog({ id: record.id, type: 'SubjectRecommend' })
      .then((r: any) => {
        setLogs({
          visible: true,
          logs: r.data.logs,
          title: record.title,
          key: Date.now(),
        });
      })
      .catch();
  };

  const addRecord = () => {
    setDrawer({
      skey: Date.now(),
      visible: true,
      record: null,
    });
  };

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      setFilter({
        ...filter,
        title: search,
      });
    }
  };

  const handleRangePickerChange = (dates: any) => {
    setFilter({
      ...filter,
      begin_date: dates.length != 0 ? dates[0].format('YYYY-MM-DD') : '',
      end_date: dates.length != 0 ? dates[1].format('YYYY-MM-DD') : '',
    });
  };

  const onChangeType = (v: any, name: any) => {
    let { pathname: path, search } = history.location;
    const obj = searchToObject();
    obj[name] = v;
    path = `${path}?${objectToQueryString(obj)}`;
    history.replace(path);
    setFilter({
      ...filter,
      [name]: v,
    });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton
            perm="type_recommend:43:create"
            style={{ marginRight: 8 }}
            onClick={addRecord}
          >
            <Icon type="plus-circle" /> 添加榜单
          </PermButton>

          <Select
            value={filter.status}
            style={{ marginLeft: 8, width: 120 }}
            onChange={(v) => setFilter({ ...filter, status: v })}
          >
            <Select.Option value="">全部状态</Select.Option>
            <Select.Option value={'0'}>待展示</Select.Option>
            <Select.Option value={'1'}>展示中</Select.Option>
          </Select>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={12}>
            <Radio.Group
              defaultValue={filter.type}
              buttonStyle="solid"
              onChange={(e) => onChangeType(e.target.value, 'type')}
            >
              <Radio.Button value={43}>月榜</Radio.Button>
              <Radio.Button value={48}>半年榜</Radio.Button>
            </Radio.Group>
          </Col>
        </Row>

        <Table
          func="getBookRankList"
          index="subject_list"
          filter={filter}
          columns={getColumns()}
          rowKey="id"
          pagination={true}
        />

        <AddReadRankDrawer
          {...drawer}
          onClose={() => setDrawer({ visible: false })}
          onEnd={() => {
            setDrawer({ visible: false });
            getList();
          }}
        ></AddReadRankDrawer>

        <Modal
          visible={logs.visible}
          title="操作日志"
          key={logs.key}
          cancelText={null}
          onCancel={() => setLogs({ visible: false })}
          onOk={() => setLogs({ visible: false })}
        >
          <p>标题：{logs.title}</p>
          <br />
          <div>
            <Timeline>
              {logs?.logs?.map((v: any, i: number) => [
                <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                  &nbsp;
                </Timeline.Item>,
                v.actions.map((action: any, index: number) => (
                  <Timeline.Item
                    className="timeline-dot"
                    data-show={action.time}
                    key={`time${i}-action${index}`}
                  >
                    {action.user}&emsp;&emsp;{action.action}&emsp;&emsp;
                  </Timeline.Item>
                )),
              ])}
            </Timeline>
          </div>
        </Modal>
      </div>
    </>
  );
}
