import { getTableList } from '@action/tableList';
import { opApi as api } from '@app/api';
import { CommonObject } from '@app/types';
import { A, Table } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setLoading, setMenu } from '@utils/utils.tsx';
import { Button, Col, Divider, Icon, Input, message, DatePicker, Row, Select, Modal } from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import { RangePickerValue } from 'antd/es/date-picker/interface';
import TopicAuditModal from './topicAuditModal';

@(withRouter as any)
@connect
class TopicAudit extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      filter: {
        begin: '',
        end: '',
        search_type: 1,
        keyword: '',
        audit_status: 0,
      },
      cType: 1,
      cKeyword: '',
      listSelectedKeys: [],
      auditModal: {
        visible: false,
        skey: 0,
        account_id: '',
        created_user_name: '',
        id: ''
      }
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    setMenu(this);
  }

  componentDidUpdate(prevProps: any) {
    if (this.props.tableList.timestamp !== prevProps.tableList.timestamp) {
      this.setState({ listSelectedKeys: [] });
    }
  }

  getData = (overlap: CommonObject = {}, filter = this.getFilter()) => {
    this.props.dispatch(getTableList('getTopicAuditList', 'list', { ...filter, ...overlap }));
  }

  getFilter = () => {
    const { current, size } = this.props.tableList;
    const { filter } = this.state;
    const filters: CommonObject = { current, size, ...filter };
    Object.keys(filter).map((k: string) => {
      if (filters[k] === '') {
        delete filters[k];
      }
    });
    return filters;
  }

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 80,
      },
      {
        title: '话题名称',
        key: 'title',
        dataIndex: 'name',
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'audit_status',
        render: (text: any) => <span>{text === 0 ? '待审核' : '已通过'}</span>,
        width: 80,
      },
      {
        title: '创建人（用户昵称）',
        key: 'created_user_name',
        dataIndex: 'created_user_name',
      },
      {
        title: '创建时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: number) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 95,
      },
      {
        title: this.state.filter.audit_status === 0 ? '操作' : '',
        key: 'op',
        dataIndex: 'id',
        render: (text: any, record: any) =>
          this.state.filter.audit_status === 0 ? (
            <span>
              {requirePerm(this, 'ugc_topic_audit:pass')(
                <A onClick={this.passSingle.bind(this, record)}>通过</A>
              )}
              <Divider type="vertical" />
              {requirePerm(this, 'topic_label:delete')(
                <A onClick={this.deleteRecord.bind(this, text)}>删除</A>
              )}
            </span>
          ) : null,
        width: 100,
      },
    ];
  }

  handleKey = (e: any) => {
    if (e.which === 13) {
      this.setState(
        {
          filter: {
            ...this.state.filter,
            search_type: this.state.cType,
            keyword: this.state.cKeyword,
          },
        },
        () => this.getData({ current: 1 })
      );
    }
  }

  handleListSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    this.setState({ listSelectedKeys: selectedRowKeys });
  }

  handleRangePickerChange = (dates: RangePickerValue) => {
    if (dates.length === 0) {
      this.setState(
        {
          filter: { ...this.state.filter, begin: '', end: '' },
        },
        () => this.getData({ current: 1 })
      );
    } else {
      this.setState(
        {
          filter: {
            ...this.state.filter,
            begin: dates[0] ? dates[0].format('YYYY-MM-DD') : '',
            end: dates[1] ? dates[1].format('YYYY-MM-DD') : '',
          },
        },
        () => this.getData({ current: 1 })
      );
    }
  }

  handleAuditStatusChange = (value: number) => {
    this.setState(
      {
        filter: { ...this.state.filter, audit_status: value },
      },
      () => this.getData({ current: 1 })
    );
  }

  handleCTypeChange = (value: 1 | 2) => {
    this.setState({
      cType: value,
    });
  }

  handleKeywordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    this.setState({
      cKeyword: e.target.value,
    });
  }

  passSingle = (record: any) => {
    this.setState({
      ...this.state,
      auditModal: {
        visible: true,
        skey: Date.now(),
        account_id: record.account_id,
        created_user_name: record.created_user_name,
        id: record.id
      }
    })
    // setLoading(this, true);
    // api
    //   .passTopicAudit({ ids: id })
    //   .then(() => {
    //     message.success('操作成功');
    //     setLoading(this, false);
    //     this.getData();
    //   })
    //   .catch(() => setLoading(this, false));
  }

  batchPass = () => {
    if (this.state.listSelectedKeys.length === 0) {
      message.error('请选择数据');
      return;
    }
    setLoading(this, true);
    api
      .passTopicAudit({ ids: this.state.listSelectedKeys.join(',') })
      .then(() => {
        message.success('操作成功');
        setLoading(this, false);
        this.getData();
      })
      .catch(() => setLoading(this, false));
  }

  deleteRecord = (id: number) => {
    Modal.confirm({
      title: '确定删除吗？',
      onOk: () => {
        setLoading(this, true);
        api
          .deleteTopicAudit({ ids: id })
          .then(() => {
            message.success('操作成功');
            setLoading(this, false);
            this.getData();
          })
          .catch(() => setLoading(this, false));
      },
    });
  }

  batchDelete = () => {
    if (this.state.listSelectedKeys.length === 0) {
      message.error('请选择数据');
      return;
    }
    Modal.confirm({
      title: '确定删除吗？',
      onOk: () => {
        setLoading(this, true);
        api
          .deleteTopicAudit({ ids: this.state.listSelectedKeys.join(',') })
          .then(() => {
            message.success('操作成功');
            setLoading(this, false);
            this.getData();
          })
          .catch(() => setLoading(this, false));
      },
    });
  }

  backToTopic = () => {
    this.props.history.push('/view/ugcTopicMgr');
  }

  render() {
    const { filter, cType, cKeyword, listSelectedKeys } = this.state;
    return (
      <React.Fragment>
        <Row className="layout-infobar">
          <Col span={16}>
            <Button onClick={this.backToTopic} style={{ marginRight: 8 }}>
              <Icon type="left-circle-o" />
              话题管理
            </Button>
            {requirePerm(this, 'ugc_topic_audit:pass')(
              <Button onClick={this.batchPass} style={{ marginRight: 8 }}>
                批量通过
              </Button>
            )}
            {requirePerm(this, 'topic_label:delete')(
              <Button onClick={this.batchDelete} style={{ marginRight: 8 }}>
                批量删除
              </Button>
            )}
          </Col>
          <Col span={8} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <Col span={12}>
              <DatePicker.RangePicker
                format="YYYY-MM-DD"
                onChange={this.handleRangePickerChange}
                value={
                  filter.begin
                    ? [moment(this.state.filter.begin), moment(this.state.filter.end)]
                    : []
                }
              />
              <Select
                value={filter.audit_status}
                onChange={this.handleAuditStatusChange}
                style={{ marginLeft: 8, width: 120 }}
              >
                <Select.Option value={0}>待审核</Select.Option>
                <Select.Option value={1}>已审核</Select.Option>
              </Select>
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <Select
                value={cType}
                onChange={this.handleCTypeChange}
                style={{ marginRight: 8, width: 120 }}
              >
                <Select.Option value={1}>话题名称</Select.Option>
                <Select.Option value={2}>创建人</Select.Option>
              </Select>
              <Input
                value={cKeyword}
                onChange={this.handleKeywordChange}
                style={{ width: 180, marginRight: 8 }}
                onKeyPress={this.handleKey}
                placeholder="请输入搜索内容"
              />
              <Button onClick={this.handleKey.bind(this, { which: 13 })}>
                <Icon type="search" />
                搜索
              </Button>
            </Col>
          </Row>
          <Table
            multi={filter.audit_status === 0}
            func="getTopicAuditList"
            index="list"
            filter={this.getFilter()}
            columns={this.getColumns()}
            rowKey="id"
            pagination={true}
            selectedRowKeys={listSelectedKeys}
            onSelectChange={this.handleListSelectChange}
          />
          <TopicAuditModal
            {...this.state.auditModal}
            onCancel={() => { this.setState({ ...this.state, auditModal: { visible: false } }) }}
            onEnd={() => {
              this.setState({ ...this.state, auditModal: { visible: false } })
              this.getData();
            }}
          ></TopicAuditModal>
        </div>
      </React.Fragment>
    );
  }
}

export default TopicAudit;
