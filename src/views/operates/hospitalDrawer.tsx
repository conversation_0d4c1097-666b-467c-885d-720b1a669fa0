import {
  Button,
  DatePicker,
  Form,
  Icon,
  Input,
  Modal,
  Radio,
  Row,
  Switch,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import _, { set } from 'lodash';
import { opApi } from '@app/api';
import { A, Drawer, FileUploader, ImageUploader } from '@app/components/common';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import TMFormList, { FormListTitle } from '@app/components/common/TMFormList';
import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import moment from 'moment';

const defaultItem = {
  name: '',
  key_word: '',
  service_url: '',
  start_time: '',
  end_time: '',
};

// 数字主理人气泡
const HospitalDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const channel_id = props.channel_id ?? '';

  const platformListRef = useRef<any>(null);
  const [platformList, setPlatformList] = useState<any[]>([defaultItem]);

  const { getFieldDecorator, getFieldValue, setFieldsValue } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  useEffect(() => {
    if (props.visible) {
      gptQuestionBubble('');
    }
  }, [props.visible]);

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = () => {
    // e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        dispatch(setConfig({ mLoading: true }));
        let parmas: any = {
          hospitals: values.list.map((item: any) => {
            const result: any = {
              ...item,
              start_time: item.time[0].format('YYYY-MM-DD'),
              end_time: item.time[1].format('YYYY-MM-DD'),
            };
            delete result.time;
            return result;
          }),
        };

        opApi
          .gptHospitalSave(parmas)
          .then((res: any) => {
            message.success('保存成功');
            dispatch(setConfig({ mLoading: false }));
            props.onEnd && props.onEnd();
          })
          .catch(() => {
            // message.error('添加失败');
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
        dispatch(setConfig({ mLoading: false }));
      }
    });
  };

  const [detailData, setDetailData] = useState<any>(null);

  const gptQuestionBubble = _.debounce((val: any) => {
    dispatch(setConfig({ mLoading: true }));

    opApi
      .gptHospitalDetail({ channel_id: channel_id })
      .then((res) => {
        dispatch(setConfig({ mLoading: false }));
        const detail = res.data;
        setDetailData(detail);
        setPlatformList(detail?.list || [defaultItem]);
      })
      .catch(() => {
        dispatch(setConfig({ mLoading: false }));
      });
  }, 500);

  return (
    <Drawer
      title="合作医院"
      visible={props.visible}
      skey={props.skey}
      onClose={props.onClose}
      maskClosable={false}
      // width={500}
      onOk={handleSubmit}
      okPerm=""
      okText="保存"
    >
      <p>该配置的目的，是为了大模型判断答案底部是否显示合作医院。最多配置10家医院。</p>
      <Form {...formLayout}>
        <TMFormList
          ref={platformListRef}
          dataList={platformList}
          form={props.form}
          fromItem={() => defaultItem}
          filed="list"
        >
          {(item, index, length) => {
            return (
              <div key={`${item.name}-${index}`}>
                <FormListTitle
                  total={length}
                  i={index}
                  title="医院"
                  upMove={() => platformListRef.current?.upMove(index)}
                  downMove={() => platformListRef.current?.downMove(index)}
                  removeItem={() => platformListRef.current?.removeItem(index)}
                  min={1}
                />

                <Form.Item label="医院名称">
                  {getFieldDecorator(`list[${index}].name`, {
                    initialValue: item.name,
                    rules: [
                      {
                        required: true,
                        message: '请输入医院名称',
                        whitespace: true,
                      },
                      {
                        max: 30,
                        message: '最多可输入30字',
                      },
                    ],
                  })(<Input placeholder="最多可输入30字"></Input>)}
                </Form.Item>
                <Form.Item label="选择日期">
                  {getFieldDecorator(`list[${index}].time`, {
                    initialValue: item.start_time
                      ? [moment(item.start_time), moment(item.end_time)]
                      : undefined,
                    rules: [
                      {
                        required: true,
                        message: '请选择日期',
                      },
                    ],
                  })(<DatePicker.RangePicker style={{ width: 300 }} format="YYYY-MM-DD" />)}
                </Form.Item>
                <Form.Item label="关键词">
                  {getFieldDecorator(`list[${index}].key_word`, {
                    initialValue: item.key_word,
                    rules: [
                      {
                        required: true,
                        message: '请输入关键词',
                        whitespace: true,
                      },
                      {
                        max: 100,
                        message: '最多可输入100字',
                      },
                    ],
                  })(<Input.TextArea rows={3} placeholder="最多可输入100字"></Input.TextArea>)}
                </Form.Item>
                <Form.Item label="跳转链接">
                  {getFieldDecorator(`list[${index}].service_url`, {
                    initialValue: item.service_url,
                    rules: [
                      {
                        required: true,
                        message: '请输入跳转链接',
                        whitespace: true,
                      },
                      {
                        pattern: /^https?:\/\/.+/,
                        message: '请输入正确的跳转链接',
                      },
                    ],
                  })(<Input placeholder="请输入跳转链接"></Input>)}
                </Form.Item>
              </div>
            );
          }}
        </TMFormList>

        <Row style={{ textAlign: 'center', marginBottom: 16 }} id="add-btn-line">
          <Button
            onClick={() => platformListRef.current?.addItem()}
            disabled={(platformListRef.current?.total ?? 0) >= 10}
          >
            添加医院
          </Button>
        </Row>
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'HospitalDrawer' })(forwardRef<any, any>(HospitalDrawer));
