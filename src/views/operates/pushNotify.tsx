/* eslint-disable no-param-reassign */
import { setConfig } from '@action/config';
import { getTableList } from '@app/action/tableList';
import { opApi as api, searchApi } from '@app/api';
import { CommonObject, CommonResponse, IOperationActionData, IOperationLogRes } from '@app/types';
import NotifyForm from '@components/business/pushNotifyForm';
import _ from 'lodash';
import { A, Drawer, Table } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, searchToObject } from '@utils/utils';
import {
  Button,
  Col,
  DatePicker,
  Form,
  Icon,
  Input,
  message,
  Modal,
  Row,
  Select,
  Divider,
  Tooltip,
  Table as ATable,
  Timeline,
} from 'antd';
import moment from 'moment';
import React from 'react';
interface recordA {
  gt_task_id: number;
}
import { withRouter } from 'react-router';
@(withRouter as any)
@connect
class PushNotifyList extends React.Component<any, any> {
  formRef: any;

  constructor(props: any) {
    super(props);
    this.state = {
      filter: {
        start_date: null,
        end_date: null,
        searchType: 1,
        keyword: '',
        status: '',
      },
      search: '',
      drawer: {
        visible: false,
        key: Date.now(),
      },
      detail: {
        visible: false,
        key: Date.now(),
      },
      operateLog: {
        visible: false,
        title: '',
        content: '',
        logs: [],
        key: Date.now(),
      } as any,
      detailData: {
        visible: false,
        key: Date.now(),
        columns: [
          {
            title: '类别',
            dataIndex: 'type',
            key: 'type',
            render: (text: any) => <span>{text === -1 ? '-' : text}</span>,
          },
          {
            title: '成功下发数',
            dataIndex: 'target_num',
            key: 'target_num',
            render: (text: any) => <span>{text === -1 ? '-' : text}</span>,
          },
          {
            title: '到达数',
            dataIndex: 'receive_num',
            key: 'receive_num',
            render: (text: any) => <span>{text === -1 ? '-' : text}</span>,
          },
          {
            title: '展示数',
            dataIndex: 'display_num',
            key: 'display_num',
            render: (text: number) => <span>{text === -1 ? '-' : text}</span>,
          },
          {
            title: '点击数',
            dataIndex: 'click_num',
            key: 'click_num',
            render: (text: number) => <span>{text === -1 ? '-' : text}</span>,
          },
        ],
        data: [],
      },
      formContent: {},
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    if (searchToObject().channel_id && searchToObject().tmId) {
      this.getCommentNewsList();
    }

    this.props.dispatch(
      setConfig({
        selectKeys: this.props.selectKeys,
        openKeys: this.props.openKeys,
      })
    );
  }

  // // 查询稿件详情getCommentNewsList

  getCommentNewsList = () => {
    this.props.dispatch(setConfig({ loading: true }));
    searchApi
      .pushSearch({ channel_id: searchToObject().channel_id, keyword: searchToObject().tmId })
      .then((r: any) => {
        this.setState({
          drawer: {
            visible: true,
            key: Date.now(),
          },
          formContent: {
            title: r.data.article_list[0].list_title,
            channel_article_ids: [r.data.article_list[0].id],
            channelArticles: r.data.article_list,
            // area_names: ['全局'],
          },
        });
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  getData = (overlap: CommonObject = {}, filter = this.getFilter()) => {
    this.props.dispatch(
      getTableList('getPushNotifyList', 'push_notify_list', { ...filter, ...overlap })
    );
  };

  getFilter = () => {
    const { current, size } = this.props.tableList;
    const { filter } = this.state;
    const result: CommonObject = { current, size };
    Object.keys(filter).forEach((k: string) => {
      if (k === 'start_date' || k === 'end_date') {
        if (filter[k]) {
          result[k] = filter[k].format('YYYY-MM-DD');
        }
      } else if (filter[k]) {
        result[k] = filter[k];
      }
    });
    if (result.searchType === 2) {
      if (!!result.keyword) {
        result.title = result.keyword;
      }
      delete result.keyword;
    }
    return result;
  };

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const { permissions } = this.props.session;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    const taskColumn = searchToObject().showtask
      ? [
          {
            title: 'taskId',
            dataIndex: 'gt_task_id',
          },
        ]
      : [];
    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 50,
      },
      {
        title: '标题',
        dataIndex: 'title',
        key: 'title',
        render: (text: any, record: any) => (
          <div
            className="line-max-8"
            style={{ minWidth: 120 }}
            onClick={() => this.showDetail(record)}
            title={text}
          >
            {text}
          </div>
        ),
      },
      {
        title: '推送内容',
        dataIndex: 'content',
        key: 'content',
        render: (text: any, record: any) => (
          <a
            className="line-max-8"
            style={{ minWidth: 120 }}
            onClick={() => this.showDetail(record)}
            title={text}
          >
            {text}
          </a>
        ),
      },
      {
        title: '推送人',
        dataIndex: 'creator',
        key: 'creator',
        width: 80,
      },
      {
        title: '图片',
        dataIndex: 'pic_url',
        key: 'pic_url',
        render: (text: any) => <img src={text} className="list-pic" />,
        width: 80,
      },
      {
        title: '推送范围',
        dataIndex: 'area_name',
        key: 'area_name',
        width: 80,
        render: (text: any, record: object) => {
          const value = ['全部', '指定手机号', '指定人群'][record.receiver_type || 0];
          return value;
        },
      },
      {
        title: '创建或推送时间',
        dataIndex: 'created_at',
        key: 'created_at',
        render: (text: any, record: any) => (
          <div style={{ cursor: 'pointer' }} onClick={this.getOperateLog.bind(this, record)}>
            <div>{moment(text).format('YYYY-MM-DD')}</div>
            <div>{moment(text).format('HH:mm:ss')}</div>
          </div>
        ),
        width: 120,
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (text: number) => (
          <span>{['未推送', '推送失败', '已推送'][text - 1] || '未推送'}</span>
        ),
        width: 80,
      },
      {
        title: '打开数',
        dataIndex: 'open_count',
        key: 'open_count',
        width: 80,
      },
      {
        title: '个推taskId',
        dataIndex: 'gt_task_id',
        key: 'gt_task_id',
        width: 100,
      },
      {
        title: '操作',
        key: 'opt',
        render: (text: any, record: any) => (
          <div>
            {requirePerm(
              this,
              'push_notify:update'
            )(
              <A disabled={record.status === 3} onClick={() => this.editPush(record)}>
                编辑
              </A>
            )}
            <Divider type="vertical" />
            {requirePerm(
              this,
              'push_notify:push'
            )(
              <A disabled={record.status === 3} onClick={() => this.rePush(record)}>
                {record.status === 2 ? '重新发送' : '发送'}
              </A>
            )}
            <Divider type="vertical" />
            {requirePerm(
              this,
              'push_notify:delete'
            )(<A onClick={() => this.deletePush(record)}>删除</A>)}

            {record.status == 3 ? <Divider type="vertical" /> : null}
            {record.status == 3
              ? requirePerm(
                  this,
                  'push_notify:delete'
                )(
                  <A
                    disabled={!permissions.includes('push_notify:stat')}
                    onClick={() => this.showData(record)}
                  >
                    数据
                  </A>
                )
              : null}
          </div>
        ),
        width: 110,
      },
      ...taskColumn,
    ];
  };

  editPush = (record: any) => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .getPushNotifyDetail({ id: record.id })
      .then((r: any) => {
        const ids = r.data.push_notify_list.article_news.map((v: any, i: number) => {
          r.data.push_notify_list.article_news[i].list_title = v.title;
          r.data.push_notify_list.article_news[i].id = v.article_id;
          r.data.push_notify_list.article_news[i].channel_name = v.channel_name;
          return v.article_id;
        });

        this.setState({
          drawer: {
            visible: true,
            key: Date.now(),
          },
          formContent: {
            ...r.data.push_notify_list,
            channel_article_ids: ids,
            channelArticles: r.data.push_notify_list.article_news,
            area_names: r.data.push_notify_list.area_name
              ? r.data.push_notify_list.area_name.split(',')
              : ['全局'],
          },
        });
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  showDetail = (record: any) => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .getPushNotifyDetail({ id: record.id })
      .then((r: any) => {
        this.setState({
          detail: { visible: true, key: Date.now(), ...r.data.push_notify_list },
        });
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  rePush = (record: any) => {
    Modal.confirm({
      title: <p>是否确认{record.status === 2 ? '重新发送' : '发送'}该推送？</p>,
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        api
          .rePushNotify({ push_notify_id: record.id })
          .then(() => {
            message.success('操作成功');
            this.props.dispatch(setConfig({ loading: false }));
            this.getData();
          })
          .catch(() => this.props.dispatch(setConfig({ loading: false })));
      },
    });
  };

  deletePush = (record: any) => {
    Modal.confirm({
      title: (
        <p>
          是否删除推送：<a onClick={() => this.showDetail(record)}>{record.content}</a>
        </p>
      ),
      content: <p>删除后，用户消息中心将同步删除推送</p>,
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        api
          .deletePushNotify({ ids: record.id })
          .then(() => {
            message.success('操作成功');
            this.props.dispatch(setConfig({ loading: false }));
            this.getData();
          })
          .catch(() => this.props.dispatch(setConfig({ loading: false })));
      },
    });
  };
  showData = _.debounce((record: recordA) => {
    api.getPushNotify({ task_id: record.gt_task_id }).then((res) => {
      const { data } = res.data;
      let arr: object[] = [];
      for (let item in data) {
        switch (item) {
          case 'gt':
            arr.push({
              type: '个推(自有在线通道)',
              ...data.gt,
            });
            break;
          case 'apn':
            arr.push({
              type: 'APNs通道(ios通道)',
              ...data.apn,
            });
            break;
          case 'hw':
            arr.push({
              type: '华为',
              ...data.hw,
            });
            break;
          case 'ho':
            arr.push({
              type: '荣耀',
              ...data.ho,
            });
            break;
          case 'xm':
            arr.push({
              type: '小米',
              ...data.xm,
            });
            break;
            break;
          case 'vv':
            arr.push({
              type: 'vivo',
              ...data.vv,
            });
            break;
          case 'mz':
            arr.push({
              type: '魅族',
              ...data.mz,
            });
            break;
          case 'op':
            arr.push({
              type: 'oppo',
              ...data.op,
            });
            break;
        }
      }

      this.setState({
        detailData: { ...this.state.detailData, visible: true, data: arr, key: Date.now() },
      });
    });
  }, 500);
  filterChange = (key: any, value: any) => {
    this.setState({ filter: { ...this.state.filter, [key]: value } }, () => {
      this.getData({ current: 1 });
    });
  };

  handleKey = (e: any) => {
    if (e.which === 13) {
      this.filterChange('keyword', this.state.search);
    }
  };

  timeChange = (dates: any) => {
    if (dates.length === 0) {
      this.setState(
        {
          filter: { ...this.state.filter, start_date: null, end_date: null },
        },
        () => this.getData({ current: 1 })
      );
    } else {
      this.setState(
        {
          filter: { ...this.state.filter, start_date: dates[0], end_date: dates[1] },
        },
        () => this.getData({ current: 1 })
      );
    }
  };

  closeDrawer = () => {
    this.setState({
      drawer: { ...this.state.drawer, visible: false },
    });
  };

  onSubmitEnd = () => {
    this.setState({
      drawer: { ...this.state.drawer, visible: false },
    });
    if (searchToObject().channel_id && searchToObject().tmId) {
      this.props.history.push('/view/pushNotify');
    }
    this.getData();
  };

  createQuickPush = () => {
    this.setState({
      drawer: {
        visible: true,
        key: Date.now(),
      },
      formContent: {},
    });
  };

  createNotify = () => {
    this.setState({
      drawer: {
        visible: true,
        key: Date.now(),
      },
      formContent: {},
    });
  };

  getOperateLog = (record: any) => {
    if (this.props.session.permissions.indexOf(`admin_log:list`) === -1) {
      return;
    }
    api
      .getOperateLog({ target_id: record.id, type: 24 })
      .then((r: any) => {
        this.setState({
          operateLog: {
            visible: true,
            title: record.title,
            content: record.content,
            logs: r.data.admin_log_list,
            key: Date.now(),
          } as any,
        });
      })
      .catch();
  };

  hideOperateLog = () => {
    this.setState({
      operateLog: { ...this.state.operateLog, visible: false },
    });
  };

  getRemark = (remark: any) => {
    if (remark.length > 2) {
      return remark.includes('新增') ? '新建' : '发送';
    }
    return remark;
  };

  render() {
    const layout = {
      labelCol: { span: 6 },
      wrapperCol: { span: 16 },
    };
    const { filter, drawer, detail, detailData, operateLog } = this.state;

    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            {requirePerm(
              this,
              'push_notify:create'
            )(
              <Button onClick={this.createNotify}>
                <Icon type="plus-circle" />
                新建推送
              </Button>
            )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <Col span={12}>
              <DatePicker.RangePicker
                value={[filter.start_date, filter.end_date]}
                format="YYYY-MM-DD"
                onChange={this.timeChange}
              />
              <Select
                value={filter.status}
                onChange={(value: any) => this.filterChange('status', value)}
                style={{ width: 160, marginLeft: 8 }}
              >
                <Select.Option value="">推送状态</Select.Option>
                <Select.Option value="1">未推送</Select.Option>
                <Select.Option value="2">推送失败</Select.Option>
                <Select.Option value="3">已推送</Select.Option>
              </Select>
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <Select
                value={filter.searchType}
                onChange={(e: any) => this.setState({ filter: { searchType: e } })}
                style={{ width: 160, marginRight: 8 }}
              >
                <Select.Option value={1}>搜索内容</Select.Option>
                <Select.Option value={2}>搜索标题</Select.Option>
              </Select>
              <Input
                placeholder="输入推送内容搜索"
                style={{ width: 150 }}
                value={this.state.search}
                onChange={(e: any) => this.setState({ search: e.target.value })}
                onKeyPress={this.handleKey}
              />
              <Button
                style={{ marginLeft: 8, verticalAlign: 'top' }}
                onClick={() => this.handleKey({ which: 13 })}
              >
                <Icon type="search" />
                搜索
              </Button>
            </Col>
          </Row>
          <Table
            func="getPushNotifyList"
            index="push_notify_list"
            columns={this.getColumns()}
            filter={this.getFilter()}
            rowKey="id"
            pagination={true}
          />
          <Modal
            visible={detail.visible}
            key={detail.key}
            title="推送详情"
            onOk={() => this.setState({ detail: { ...detail, visible: false } })}
            onCancel={() => this.setState({ detail: { ...detail, visible: false } })}
          >
            <Form {...layout}>
              <Form.Item label="标题">{detail.title}</Form.Item>
              <Form.Item label="推送内容">{detail.content}</Form.Item>
              <Form.Item label="图片">
                {detail.pic_url ? (
                  <img style={{ maxWidth: 120, maxHeight: 120 }} src={detail.pic_url} />
                ) : null}
              </Form.Item>
              <Form.Item label="推送范围">
                {['全部', '指定手机号', '指定人群'][detail.receiver_type || 0]}
              </Form.Item>
              {detail.article_news?.length > 0 && (
                <Form.Item label="新闻列表">
                  {Boolean(detail.article_news) &&
                    detail.article_news.map((v: any) => (
                      <>
                        {detail.type === 4 ? (
                          <p key={v.article_id}>
                            {moment([v.article_id].toString().slice(0, 8)).format('YYYY-MM-DD')}
                            &nbsp;&nbsp;-&nbsp;&nbsp;
                            <a target="_blank" href={v.url || ''}>
                              {['', '早报', '晚报'][`${v.article_id}`.substr(-1) * 1]}
                            </a>
                          </p>
                        ) : (
                          <p key={v.article_id}>
                            {v.article_id}&nbsp;&nbsp;-&nbsp;&nbsp;
                            <a target="_blank" href={v.url || ''}>
                              {v.title}
                            </a>
                          </p>
                        )}
                      </>
                    ))}
                </Form.Item>
              )}
              {!!detail.url && (
                <Form.Item label="跳转链接">
                  <a href={detail.url} target="_blank">
                    {detail.url}
                  </a>
                </Form.Item>
              )}
              {Boolean(detail.to_users) && (
                <Form.Item label="发送用户">
                  <span style={{ wordBreak: 'break-all' }}>
                    {detail.to_users.indexOf('http') !== 0 ? (
                      detail.to_users
                    ) : (
                      <a href={detail.to_users} rel="noreferrer">
                        {detail.to_users}
                      </a>
                    )}
                  </span>
                </Form.Item>
              )}
            </Form>
          </Modal>
          <Drawer
            visible={drawer.visible}
            skey={drawer.key}
            title="新建推送"
            onClose={this.closeDrawer}
            onOk={() => this.formRef.doSubmit()}
          >
            <NotifyForm
              onEnd={this.onSubmitEnd}
              formContent={this.state.formContent}
              // eslint-disable-next-line no-return-assign
              wrappedComponentRef={(instance: any) => (this.formRef = instance)}
            />
          </Drawer>
          <Modal
            visible={detailData.visible}
            key={detailData.key}
            title="数据详情"
            onOk={() => this.setState({ detailData: { ...detailData, visible: false } })}
            onCancel={() => this.setState({ detailData: { ...detailData, visible: false } })}
            width={700}
            footer={false}
          >
            <div style={{ color: 'red', marginLeft: 8 }}>
              说明：以下数据为个推提供，仅供参考，实际点击数以列表上“打开数”为准。
            </div>
            <ATable
              columns={detailData.columns}
              dataSource={detailData.data}
              pagination={false}
              rowKey="type"
              style={{ width: 660 }}
            />
          </Modal>

          <Modal
            visible={operateLog.visible}
            title="操作日志"
            key={operateLog.key}
            cancelText={null}
            onCancel={this.hideOperateLog}
            onOk={this.hideOperateLog}
          >
            <p>标题：{operateLog.title}</p>
            <p>推送内容：{operateLog.content}</p>
            <br />
            <div>
              <Timeline>
                {operateLog.logs.map((v: any, i: number) => [
                  <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                    &nbsp;
                  </Timeline.Item>,
                  v.log_list.map((action: any, index: number) => (
                    <Timeline.Item
                      className="timeline-dot"
                      data-show={moment(action.created_at).format('HH:mm:ss')}
                      key={`time${i}-action${index}`}
                    >
                      {action.admin_name}&nbsp;{this.getRemark(action.remark)}
                    </Timeline.Item>
                  )),
                ])}
              </Timeline>
            </div>
          </Modal>
        </div>
      </>
    );
  }
}

export default PushNotifyList;
