import React, { useEffect, useState } from "react";
import { Row, Col, Button, Icon, Divider, Modal, message } from "antd";
import { getCrumb } from '@utils/utils';
import { setConfig } from '@action/config';
import { Table, OrderColumn } from '@components/common';
import { useHistory } from "react-router";
import { useDispatch, useSelector } from "react-redux";
import AddAdamantineDrawer from "@app/components/business/AddAdamantineDrawer";
import { getTableList } from "@app/action/tableList";
import { PermA, PermButton } from '@app/components/permItems';
import moment from 'moment';
import { opApi as api } from '@app/api';

export default function AdamantineMgr(props: any) {
  const history = useHistory()
  const dispatch = useDispatch();
  const { total, current, size, records = [] } = useSelector((state: any) => state.tableList)
  const getSeq = (i: number) => (current - 1) * size + i + 1;
  const columns: any = [] = [
    {
      title: '排序',
      key: 'order',
      render: (text: any, record: any, i: number) => {
        const pos = getSeq(i)
        return <OrderColumn
          pos={pos}
          start={1}
          end={total}
          perm="type_recommend:34:update_sort"
          onUp={() => exchangeOrder(record, 0)}
          onDown={() => exchangeOrder(record, 1)}
        />

      },
      width: 70,
    },
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      width: 70,
    },
    {
      title: '名称',
      key: 'title',
      dataIndex: 'title',
      width: 150,
    },
    {
      title: '图片',
      key: 'icon',
      dataIndex: 'pic_url',
      render: (text: string) => <img src={text} className="list-pic" alt="123" />,
      width: 120,
    },
    {
      title: '链接',
      key: 'url',
      dataIndex: 'url',
      // render: (text: string) =>
      // (
      //   <a href={text} target="_blank" rel="noreferrer">
      //     {text}
      //   </a>
      // )
    },
    {
      title: '修改时间',
      key: 'created_at',
      dataIndex: 'updated_at',
      render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
      width: 95,
    },
    {
      title: '操作',
      key: 'op',
      align: 'center',
      render(record: any) {
        return <span>
          <PermA perm="type_recommend:34:update" onClick={() => editRecord(record)}>编辑</PermA>
          <Divider type="vertical" />
          <PermA perm="type_recommend:34:delete" onClick={() => deleteRecord(record)}>删除</PermA>
        </span>
      },
      width: 100,
    },
  ]

  const [adamantineDrawerVisible, setAdamantineDrawerVisible] = useState(false)
  const [drawerEditRecord, setDrawerEditRecord] = useState(null)
  const exchangeOrder = (record: any, sort_flag: number) => {
    dispatch(setConfig({ loading: true }));
    api.sortProposal({ id: record.id, sort_flag })
      .then(() => {
        message.success('操作成功');
        getData();
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  }
  const handleCreateAdamantine = () => {
    setDrawerEditRecord(null)
    setAdamantineDrawerVisible(true)
  }
  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确定删除？`,
      content: '删除后无法恢复',
      onOk: () => {
        api.deleteProposal({ id: record.id }).then(() => {
          message.success('删除成功');
          getData()
        }).catch(() => { })
      },
    });
  }
  const editRecord = (record: any) => {
    setDrawerEditRecord(record)
    setAdamantineDrawerVisible(true)
  }
  const getData = (goToFirstPage = false) => {
    let cur = goToFirstPage ? 1 : current
    dispatch(getTableList('getProposalList', 'recommend_list', { current: cur, size, type: 34 }));
  }
  const submitEnd = () => {
    getData()
    setAdamantineDrawerVisible(false)
  }
  useEffect(() => {
    getData()
  }, [])
  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button style={{ marginRight: 8 }} onClick={() => history.goBack()}>
            <Icon type="left-circle" /> 返回
          </Button>
          <PermButton
            perm={'type_recommend:34:create'}
            onClick={handleCreateAdamantine}
          >
            <Icon type="plus-circle" /> 新建
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row >
      <div className="component-content">
        <Table
          func="getProposalList"
          index="recommend_list"
          pagination={false}
          rowKey="id"
          columns={columns}
          filter={{ type: 34 }}
        />
        <AddAdamantineDrawer record={drawerEditRecord} visible={adamantineDrawerVisible} onClose={() => setAdamantineDrawerVisible(false)} onEnd={submitEnd} />
      </div>
    </>
  )
}