import React, { useEffect, useState } from "react"
import { Row, Col, Button, Icon, Tag } from 'antd';
import { getCrumb, resolveNewsType } from '@utils/utils';
import { Table, PreviewMCN } from '@components/common';
import { useHistory, useRouteMatch } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import moment from 'moment';
import { getTableList } from '@app/action/tableList';

export default function TagContentList(props: any) {
  const history = useHistory()
  const dispatch = useDispatch()
  const { id, name: tagName } = useRouteMatch<any>().params
  const { current, size } = useSelector((state: any) => state.tableList)
  const [previewState, setPreviewState] = useState({
    visible: false,
    skey: Date.now() + 1,
    data: {}
  })
  const getSeq = (i: number) => (current - 1) * size + i + 1;
  const columns: any = [
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      width: 70,
    },
    {
      title: '潮新闻ID',
      key: 'scid',
      dataIndex: 'id',
      width: 100,
    },
    {
      title: '创作者平台ID',
      key: 'metadata_id',
      dataIndex: 'metadata_id',
      width: 115,
    },
    {
      title: '标题',
      key: 'list_title',
      dataIndex: 'list_title',
      render: (text: any, record: any) => (
        <>
          {Boolean(record.down) && (
            <Tag color="#D6D44a">沉底</Tag>
          )}
          <a className="list-title" onClick={() => preview(record)}>
            {text || '-'}
          </a>
        </>
      ),
    },
    {
      title: '类型',
      dataIndex: 'doc_type',
      render: (text: number) => resolveNewsType(text, 1),
      width: 80,
    },
    {
      title: '作者',
      key: 'account_nick_name',
      dataIndex: 'account_nick_name',
      width: 110,
    },
    {
      title: '小潮号',
      key: 'chao_id',
      dataIndex: 'chao_id',
      width: 120
    },
    {
      title: '发布时间',
      key: 'published_timestamp',
      dataIndex: 'published_timestamp',
      render: (text: any, record: any) => (
        <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>
      ),
      width: 180,
    },
  ]
  const preview = (record: any) => {
    setPreviewState({
      visible: true,
      skey: Date.now(),
      data: record
    })
  }
  const closePreview = () => {
    setPreviewState({
      ...previewState, visible: false
    })
  };
  const getData = () => {
    dispatch(getTableList('getUGCTagArticleList', 'article_list', { id, current, size }));
  }
  useEffect(() => {
    getData()
  }, [])
  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button onClick={() => history.push('/view/tagMgr')}><Icon type="left-circle" />标签管理</Button>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb([...props.breadCrumb, tagName])}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          func="getUGCTagArticleList"
          index="article_list"
          pagination={true}
          rowKey="id"
          columns={columns}
          filter={{ id }}
        />
        <PreviewMCN {...previewState} onClose={closePreview} />
      </div>
    </>
  )
} 