import React, { useEffect, useState } from "react";
import { useHistory } from "react-router-dom";
import { Row, Col, Button, message, Divider, Modal } from 'antd';
import { getCrumb } from '@utils/utils';
import { Table, OrderColumn, A } from '@components/common';
import AddTagModal from "@app/components/business/AddTagModal";
import { useDispatch, useSelector, useStore } from "react-redux";
import { getTableList } from '@app/action/tableList';
import { setConfig } from '@action/config';
import { opApi as api } from '@app/api';
import moment from 'moment';

export default function TagManager(props: any) {
  const dispatch = useDispatch();
  const history = useHistory()
  const { permissions } = useStore().getState().session;
  const { total, current, size, records = [] } = useSelector((state: any) => state.tableList)
  const [addModalVisible, setAddModalVisible] = useState(false)
  const [editRecord, setEditRecord] = useState(null)
  const getSeq = (i: number) => (current - 1) * size + i + 1;
  const canSaveTag = permissions.indexOf('ugc_tags:save') > 0;
  const canUpdateTagStatus = permissions.indexOf('ugc_tags:update_status') > 0;
  const canShowArticleList = permissions.indexOf('ugc_tags:article_list') > 0;
  const columns: any = [
    {
      title: '排序',
      key: 'order',
      render: (text: any, record: any, i: number) => {
        const pos = getSeq(i)
        return <OrderColumn
          pos={pos}
          start={1}
          end={total}
          perm="ugc_tags:update_sort"
          disableUp={record.status === 0 || pos === 0}
          // i === records.length - 1 
          disableDown={record.status === 0 || (i < records.length - 1 && records[i + 1].status === 0)}
          onUp={() => exchangeOrder(record, 0)}
          onDown={() => exchangeOrder(record, 1)}
        />
      },
      width: 70,
    },
    {
      title: '序号',
      dataIndex: 'seq',
      render: (a: any, b: any, c: number) => getSeq(c),
    },
    {
      title: '标签名称',
      dataIndex: 'name',
      render(text: string, record: any) {
        return canShowArticleList ? <A onClick={() => history.push(`/view/tagContentList/${record.id}/${record.name}`)}>{text}</A> : <span>{text}</span>
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      render(status: number) {
        return status === 1 ? '展示中' : '未展示'
      },
    },
    {
      title: '最后操作时间',
      dataIndex: 'updated_at',
      render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
    },
    {
      title: '操作',
      width: 140,
      render: (text: any, record: any) => {
        return <span>
          <A disabled={!canSaveTag} onClick={() => editTag(record)}>编辑</A>
          <Divider type="vertical" />
          <A disabled={!canUpdateTagStatus} onClick={() => toggleStatus(record)}>{record.status === 1 ? '下线' : '上线'}</A>
        </span>
      },
    }
  ]
  const handleCreateTag = () => {
    setEditRecord(null)
    setAddModalVisible(true)
  }

  const editTag = (record: any) => {
    setEditRecord(record)
    setAddModalVisible(true)
  }

  const submitEnd = (goToFirstPage: boolean) => {
    getData(goToFirstPage)
    setAddModalVisible(false)
  }

  const getData = (goToFirstPage = false) => {
    let cur = goToFirstPage ? 1 : current
    dispatch(getTableList('getUGCTagList', 'list', { current: cur, size }));
  }

  const exchangeOrder = (record: any, sort_flag: number) => {
    dispatch(setConfig({ loading: true }));
    api.sortUGCTag({ id: record.id, sort_flag })
      .then(() => {
        message.success('操作成功');
        getData();
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  }

  const toggleStatusRequest = (record: any) => {
    dispatch(setConfig({ loading: true }))
    api.updateUGCTag({ id: record.id, status: record.status === 1 ? 0 : 1 })
      .then(() => {
        message.success('操作成功')
        dispatch(setConfig({ loading: false }))
        getData()
      }).catch(() => {
        dispatch(setConfig({ loading: false }))
      })
  }

  const toggleStatus = (record: any) => {
    Modal.confirm({
      title: <p>确定{record.status === 1 ? '下' : '上'}线该标签？</p>,
      onOk: () => {
        toggleStatusRequest(record)
      },
    });
  }

  useEffect(() => {
    const { selectKeys, openKeys } = props
    dispatch(
      setConfig({ selectKeys, openKeys })
    );
    getData()
  }, [])
  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button onClick={handleCreateTag} disabled={!canSaveTag}>添加标签</Button>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          func="getUGCTagList"
          index="list"
          pagination={true}
          rowKey="id"
          columns={columns}
        />
        <AddTagModal visible={addModalVisible} record={editRecord} onCancel={() => setAddModalVisible(false)} onEnd={submitEnd} />
      </div>
    </>
  )
}