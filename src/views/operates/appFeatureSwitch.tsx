import { setConfig } from '@action/config';
import { opApi as api } from '@app/api';
import Form from '@components/business/appFeatureForm';
import { Drawer } from '@components/common';
import connect from '@utils/connectSession';
import { getCrumb, requirePerm } from '@utils/utils.tsx';
import {Button, Col, Divider, Icon, message, Modal, Row, Switch, Table} from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import {PermA} from "@components/permItems";

interface AppFeatureSwitchState {
  loading: boolean;
  records: any[];
  logs: any[];
  record: any;
  logVisible: boolean;
  logKey: string | number;
  formVisible: boolean;
  formKey: string | number;
}

@(withRouter as any)
@connect
class AppFeatureSwitch extends React.Component<any, AppFeatureSwitchState> {
  formRef: any;

  constructor(props: any) {
    super(props);
    this.state = {
      loading: false,
      records: [],
      record: null,
      logs: [],
      formVisible: false,
      formKey: Date.now(),
      logVisible: false,
      logKey: Date.now(),
    };
  }

  componentDidMount() {
    this.getData();
    this.props.dispatch(
      setConfig({
        selectKeys: this.props.selectKeys,
        openKeys: this.props.openKeys,
      })
    );
  }

  componentDidUpdate(prevProps: any) {
    // if(prevProps.location !== this.props.location) {
    //   this.getData();
    // }
  }

  getData = () => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .getFeatureList()
      .then((r: any) => {
        message.success('列表获取成功');
        this.props.dispatch(setConfig({ loading: false }));
        this.setState({ records: r.data.app_feature_switch_list });
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  getLogs = () => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .getFeatureLogs()
      .then((r: any) => {
        message.success('列表获取成功');
        this.props.dispatch(setConfig({ loading: false }));
        this.setState({
          logs: r.data.opt_log_list,
          logVisible: true,
          logKey: Date.now(),
        });
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  handleAdd = () => {
    this.setState({
      formVisible: true,
      formKey: Date.now(),
      record: null
    });
  };

  closeLog = () => {
    this.setState({ logVisible: false });
  };
  editRecord = (record:Object) => {
    this.setState({
      formVisible: true,
      formKey: Date.now(),
      record: record
    });
  };
  deleteRecord = (record:Object) => {
    Modal.confirm({
      title: '删除后无法恢复，请谨慎操作。',
      content: <div style={{color:'red'}}>务必和技术确认后，再删除！</div>,
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        api
            .deleteFeature({ id: record.id })
            .then(() => {
              message.success('删除成功');
              this.getData();
            })
            .catch(() => this.props.dispatch(setConfig({ loading: false })));
      },
    });
  }
  handleSwitch = (record: any) => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .updateFeatureStatus({ id: record.id, enabled: !record.enabled })
      .then(() => {
        message.success('操作成功');
        this.getData();
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  getFeatureColumns = () => {
    return [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 120,
      },
      {
        title: '系统',
        dataIndex: 'device_type',
        key: 'device_type',
        render: (text: any) => <span>{['iOS', 'Android', 'Web', '鸿蒙'][text - 1]}</span>,
        width: 120,
      },
      {
        title: '功能编码',
        dataIndex: 'feature',
        key: 'feature',
      },
      {
        title: '功能名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '操作人',
        dataIndex: 'updated_by',
        key: 'updated_by',
      },
      {
        title: '操作时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        render:((test:number,record:object)=> {
          return new Date(test).toLocaleString()
        })
      },
      {
        title: '开关',
        key: 'enabled',
        dataIndex: 'enabled',
        render: (text: any, record: any) =>
          requirePerm(
            this,
            'app_feature_switch:update'
          )(
            <Switch
              checkedChildren="on"
              unCheckedChildren="off"
              checked={text}
              onChange={() => this.handleSwitch(record)}
            />
          ),
      },
      {
        title: '操作',
        key: 'op',
        align: 'center',
        render:(record: any) => {
          return <span>
          <PermA perm="app_feature_switch:update" onClick={() =>this.editRecord(record)}>编辑</PermA>
          <Divider type="vertical" />
          <PermA perm="app_feature_switch:delete" onClick={() => this.deleteRecord(record)}>删除</PermA>
        </span>
        },
        width: 100,
      },
    ];
  };

  getLogColumns = () => {
    return [
      {
        title: '时间',
        dataIndex: 'created_at',
        key: 'created_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
      },
      {
        title: '操作人',
        dataIndex: 'admin_name',
        key: 'admin_name',
      },
      {
        title: '操作项',
        dataIndex: 'remark',
        key: 'remark',
      },
    ];
  };
  closeForm = () => {
    this.setState({ formVisible: false });
  };

  submitForm = () => {
    this.formRef.doSubmit();
  };

  handleSubmitEnd = () => {
    this.setState({ loading: false, formVisible: false });
    this.getData();
  };

  render() {
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            {requirePerm(
              this,
              'app_feature_switch:save'
            )(
              <Button onClick={this.handleAdd}>
                <Icon type="plus-circle" /> 添加功能
              </Button>
            )}
            <Button style={{ marginLeft: 8 }} onClick={this.getLogs}>
              操作日志
            </Button>
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Row>
            <Table
              dataSource={this.state.records}
              columns={this.getFeatureColumns()}
              rowKey="id"
              pagination={false}
            />
          </Row>
          <Drawer
            visible={this.state.logVisible}
            title="操作日志"
            skey={this.state.logKey}
            onClose={this.closeLog}
            onOk={this.closeLog}
          >
            <Row style={{ marginBottom: 16 }}>
              <Table
                dataSource={this.state.logs}
                columns={this.getLogColumns()}
                rowKey="id"
                pagination={false}
              />
            </Row>
          </Drawer>
          <Drawer
            visible={this.state.formVisible}
            title={`${this.state.record ? '编辑': '添加功能'}`}
            skey={this.state.formKey}
            onClose={this.closeForm}
            onOk={this.submitForm}
          >
            <Form
              onEnd={this.handleSubmitEnd}
              record={this.state.record}
              setLoading={(loading: boolean) => this.setState({ loading })}
              wrappedComponentRef={(instance: any) => (this.formRef = instance)}
            />
          </Drawer>
        </div>
      </>
    );
  }
}

export default AppFeatureSwitch;
