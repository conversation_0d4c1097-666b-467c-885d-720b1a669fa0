import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import {
  Button,
  Checkbox,
  Form,
  Icon,
  Input,
  InputNumber,
  Modal,
  Radio,
  Select,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import _ from 'lodash';
import { communityApi, opApi } from '@app/api';
import { FileUploader } from '@app/components/common';
import { IMReportType, iMReportTypeMap } from '@app/utils/utils';

const HandleIMViolationModal = (props: any, ref: any) => {
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator, getFieldValue } = props.form;

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true);
        const params: any = {
          ...values,
          id: props.record.id,
          status: 2,
        };

        opApi
          .updateIMComplainStatus(params)
          .then((res: any) => {
            message.success('操作成功');
            setLoading(false);
            props.onOk && props.onOk();
          })
          .catch(() => {
            // message.error('添加失败');
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
        setLoading(false);
      }
    });
  };

  return (
    <Modal
      width={500}
      visible={props.visible}
      title="私信违规"
      key={``}
      onCancel={() => {
        props.onCancel && props.onCancel();
      }}
      onOk={handleSubmit}
      maskClosable={false}
      destroyOnClose={true}
    >
      <Spin spinning={false}>
        <p>*标记违规后，系统将自动通知举报人及被举报人结果。</p>
        <Form {...formLayout} onSubmit={handleSubmit}>
          <Form.Item label="被举报人">
            {getFieldDecorator('deal_type', {
              initialValue: '1',
            })(
              <Radio.Group>
                <Radio value="1">发送警告</Radio>
                <Radio value="2">加入私信黑名单</Radio>
              </Radio.Group>
            )}
          </Form.Item>
          {getFieldValue('deal_type') === '2' && (
            <Form.Item label="拉黑时长">
              {getFieldDecorator('block_days', {
                initialValue: '1',
                rules: [{ required: true, message: '请选择添加方式' }],
              })(
                <Select>
                  <Select.Option value="1">1天</Select.Option>
                  <Select.Option value="3">3天</Select.Option>
                  <Select.Option value="7">7天</Select.Option>
                  <Select.Option value="30">30天</Select.Option>
                  <Select.Option value="-1">永久</Select.Option>
                </Select>
              )}
            </Form.Item>
          )}

          <Form.Item label={getFieldValue('deal_type') === '2' ? '拉黑理由' : '违规类型'}>
            {getFieldDecorator('reason_type', {
              initialValue: `${props.record?.reason_type}` || '1',
              rules: [{ required: true, message: '请选择添加方式' }],
            })(
              <Select>
                {Object.keys(iMReportTypeMap).map((key, index) => {
                  return <Select.Option value={key}>{iMReportTypeMap[key]}</Select.Option>;
                })}
              </Select>
            )}
          </Form.Item>

          {getFieldValue('deal_type') === '2' && (
            <Form.Item label="备注">
              {getFieldDecorator('remark', {
                initialValue: '',
              })(<Input.TextArea rows={4} placeholder="添加备注信息，仅后台可见" />)}
            </Form.Item>
          )}
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<any>({ name: 'HandleIMViolationModal' })(
  forwardRef<any, any>(HandleIMViolationModal)
);
