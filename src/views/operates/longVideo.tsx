import { getTableList } from '@action/tableList';
import { opApi as api } from '@app/api';
import { CommonObject } from '@app/types';
import { A, Table, OrderColumn, Drawer, PreviewMCN } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setLoading, setMenu, searchToObject } from '@utils/utils';
import Form from '@app/components/business/longVideoForm';

import { Button, Col, Divider, Icon, Input, message, DatePicker, Row, Select, Modal } from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import { RangePickerValue } from 'antd/es/date-picker/interface';
import LongVideoSearchUserDialog from './longVideoSearchUserDialog';

@(withRouter as any)
@connect
class longVideo extends React.Component<any, any> {
  formRef: any;
  constructor(props: any) {
    super(props);
    this.state = {
      cType: 1,
      cKeyword: '',
      listSelectedKeys: [],
      visible: false,
      searchUserVisible: false,
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    setMenu(this);
  }

  componentDidUpdate(prevProps: any) {
    if (this.props.tableList.timestamp !== prevProps.tableList.timestamp) {
      this.setState({ listSelectedKeys: [] });
    }
  }
  getData = (overlap: CommonObject = {}, filter = this.getFilter()) => {
    this.props.dispatch(
      getTableList('getLongVideoList', 'long_video_duration_list', { ...filter, ...overlap })
    );
  };
  // 列表请求参数
  getFilter = () => {
    const { current, size } = this.props.tableList;
    const filters: CommonObject = { current, size };
    return filters;
  };

  getColumns = () => {
    const { current, size, total } = this.props.tableList;
    const { channel_id } = this.state;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '权限分组ID',
        key: 'id',
        dataIndex: 'id',
        width: 100,
      },
      {
        title: '名称',
        key: 'name',
        dataIndex: 'name',
        width: 100,
        render: (text: any, record: any) => {
          return (
            <a
              onClick={() => {
                this.toDetail(record.id);
              }}
            >
              {text}
            </a>
          );
        },
      },
      {
        title: '时长(单位：秒)',
        key: 'duration',
        dataIndex: 'duration',
        width: 100,
      },
      {
        title: '	成员数量',
        key: 'account_num',
        dataIndex: 'account_num',
        width: 100,
      },
      {
        title: '更新时间',
        key: 'updated_at',
        dataIndex: 'updated_at',
        render: (text: number) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 95,
      },
      {
        title: '描述',
        key: 'describe',
        dataIndex: 'describe',
        width: 100,
      },
      {
        title: '操作',
        key: 'op',
        dataIndex: 'id',
        render: (text: any) => (
          <span>
            {requirePerm(
              this,
              `long_video_duration:account_list`
            )(<A onClick={this.toDetail.bind(this, text)}>添加用户</A>)}

            <Divider type="vertical" />
            {requirePerm(
              this,
              `long_video_duration:delete`
            )(<A onClick={this.deleteRecord.bind(this, text)}>删除</A>)}
          </span>
        ),
        width: 100,
      },
    ];
  };
  // 新建权限分组
  addNews = () => {
    this.setState({
      visible: true,
    });
  };
  searchUser = () => {
    this.setState({
      searchUserVisible: true,
    });
  };
  // 查看详细
  toDetail = (id: number) => {
    this.props.history.push(`/view/longVideoUser/${id}`);
  };
  // 删除权限分组
  deleteRecord = (id: number) => {
    Modal.confirm({
      title: '是否确定要删除该权限分组',
      onOk: () => {
        setLoading(this, true);
        api
          .deleteLongVideo({ id: id })
          .then(() => {
            message.success('操作成功');
            setLoading(this, false);
            this.getData();
          })
          .catch(() => setLoading(this, false));
      },
    });
  };
  // 关闭弹窗
  closeDrawer = () => {
    this.setState({
      visible: false,
    });
  };
  // 提交成功
  submitEnd = () => {
    this.closeDrawer();
    this.getData();
  };
  backToTopic = () => {
    this.props.history.go(-1);
  };
  submitForm = () => {
    this['formRef'].doSubmit();
  };
  setRef = (ref: 'formRef', instance: any) => {
    this[ref] = instance;
  };
  render() {
    const { channel_id } = this.state;
    return (
      <React.Fragment>
        <Row className="layout-infobar">
          <Col span={16}>
            {requirePerm(
              this,
              `long_video_duration:save`
            )(
              <Button onClick={this.addNews} style={{ marginRight: 8 }}>
                新建权限分组
              </Button>
            )}
            {requirePerm(
              this,
              `long_video_duration:save`
            )(
              <Button onClick={this.searchUser} style={{ marginRight: 8 }}>
                查找权限用户
              </Button>
            )}
          </Col>
          <Col span={8} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getLongVideoList"
            index="long_video_duration_list"
            filter={this.getFilter()}
            columns={this.getColumns()}
            pagination={true}
            rowKey="id"
          />
        </div>
        <Drawer
          visible={this.state.visible}
          skey={this.state.key}
          title="添加视频特权组 "
          onClose={this.closeDrawer}
          onOk={this.submitForm.bind('form')}
        >
          <Form
            id={channel_id}
            wrappedComponentRef={this.setRef.bind(this, 'formRef')}
            onEnd={this.submitEnd}
          />
        </Drawer>

        {this.state.searchUserVisible && (
          <LongVideoSearchUserDialog
            visible={this.state.searchUserVisible}
            onCancel={(id: any) => {
              this.setState({
                searchUserVisible: false,
              });
              if (id) {
                this.toDetail(id);
              }
            }}
          ></LongVideoSearchUserDialog>
        )}
      </React.Fragment>
    );
  }
}

export default longVideo;
