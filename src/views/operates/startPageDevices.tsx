import { setConfig } from '@action/config';
import { opApi as api } from '@app/api';
import { A, ImageUploader } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb } from '@utils/utils';
import { Button, Col, Divider, Form, Icon, message, Modal, Row, Select, Table } from 'antd';
import xor from 'lodash/xor';
import React from 'react';
import { withRouter } from 'react-router';

@(withRouter as any)
@connect
class StartPageDevices extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      deviceList: {
        default: {
          name: '默认尺寸（750*1134）',
          ratio: 750 / 1134,
          code: 7,
          picRatio: '750*1134',
        },
        iphone5s: {
          name: 'iPhone5S（640*964）',
          ratio: 640 / 964,
          code: 1,
          picRatio: '640*964',
        },
        iphone6: {
          name: 'iphone6（750*1134）',
          ratio: 750 / 1134,
          code: 2,
          picRatio: '750*1134',
        },
        iphone6plus: {
          name: 'iphone6plus（828*1250）',
          ratio: 414 / 625,
          code: 3,
          picRatio: '828*1250',
        },
        iphoneX: {
          name: 'iphoneX（750*1340）',
          ratio: 375 / 670,
          code: 4,
          picRatio: '750*1340',
        },
        'android(16:9)': {
          name: 'android-16:9（720*1089）',
          ratio: 720 / 1089,
          code: 5,
          picRatio: '720*1089',
        },
        'android(18:9)': {
          name: 'android-18:9（720*1224）',
          ratio: 720 / 1224,
          code: 6,
          picRatio: '720*1224',
        },
      },
      id: props.match.params.id,
      selections: [],
      detail: {},
      isEdit: props.isEdit || false,
      editRecord: {
        id: '',
        visible: false,
        title: '',
        key: Date.now(),
        url: '',
      },
    };
  }

  componentDidMount() {
    this.getData();
    this.props.dispatch(setConfig({ openKeys: this.props.openKeys }));
    this.props.dispatch(setConfig({ selectKeys: this.props.selectKeys }));
  }

  getData = (id = this.state.id) => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .getStartPageDetail({ app_start_page_id: id })
      .then((r: any) => {
        this.props.dispatch(setConfig({ loading: false }));
        const detail = r.data.app_start_page_detail;
        const sKeys = Object.keys(this.state.deviceList);
        const aKeys = detail.start_page_image_list.map((v: any) => v.phone_model);
        const selections = xor(sKeys, aKeys);
        this.setState({
          selections,
          detail,
        });
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  getFilter = () => {
    const { current, size } = this.props.tableList;
    return { current, size, type: 2 };
  };

  editRecord = (record: any) => {
    this.setState({
      editRecord: {
        id: record.id,
        visible: true,
        title: '编辑适配图',
        key: Date.now(),
        url: record.url,
        deviceType: record.phone_model,
      },
    });
  };

  deleteRecord = (record: any) => {
    const doDelete = () => {
      this.props.dispatch(setConfig({ loading: true }));
      api
        .deleteStartPageDevice({ id: record.id })
        .then(() => {
          message.success('操作成功');
          this.getData();
          this.props.dispatch(setConfig({ loading: false }));
        })
        .catch(() => this.props.dispatch(setConfig({ loading: false })));
    };
    Modal.confirm({
      title: <p>是否确认删除背景图{this.state.deviceList[record.phone_model].name}？</p>,
      onOk: () => doDelete(),
    });
  };

  getColumns = () => {
    return [
      {
        title: '图片',
        key: 'pic',
        dataIndex: 'url',
        render: (text: any) => <img style={{ maxWidth: 200, maxHeight: 120 }} src={text} />,
      },
      {
        title: '终端类型',
        key: 'status',
        dataIndex: 'phone_model',
        render: (text: string) => <span>{this.state.deviceList[text].name}</span>,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <span>
            <A
              disabled={record.phone_model === 'default' || !this.state.isEdit}
              onClick={() => this.editRecord(record)}
            >
              编辑
            </A>
            <Divider type="vertical" />
            <A
              disabled={record.phone_model === 'default' || !this.state.isEdit}
              onClick={() => this.deleteRecord(record)}
            >
              删除
            </A>
          </span>
        ),
        width: 90,
      },
    ];
  };

  handleAdd = () => {
    this.setState({
      editRecord: {
        id: '',
        visible: true,
        title: '添加适配图',
        key: Date.now(),
        url: '',
        deviceType: this.state.selections[0],
      },
    });
  };

  handleSubmitAdd = () => {
    if (!this.state.editRecord.url) {
      message.error('请上传图片');
      return;
    }
    this.setState({ loading: true });
    let func = 'addStartPageDevice';
    const body: {
      app_start_page_id?: string;
      url: string;
      id?: string | number;
      phone_model_code?: number;
    } = { url: this.state.editRecord.url };
    if (this.state.editRecord.id) {
      func = 'updateStartPageDevice';
      body.id = this.state.editRecord.id;
    } else {
      body.app_start_page_id = this.state.id;
      body.phone_model_code = this.state.deviceList[this.state.editRecord.deviceType].code;
    }
    api[func as keyof typeof api](body)
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setState({ editRecord: { ...this.state.editRecord, visible: false }, loading: false });
      })
      .catch(() => this.setState({ loading: false }));
  };

  picChange = (value: string) => {
    this.setState({ editRecord: { ...this.state.editRecord, url: value } });
  };

  deviceChange = (value: string) => {
    this.setState({ editRecord: { ...this.state.editRecord, deviceType: value } }, () => {
      (this.refs.uploader as any).triggerChange('');
    });
  };

  render() {
    const record = this.state.editRecord;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            <Button onClick={() => this.props.history.push('/view/startPage')}>
              <Icon type="left-circle" /> 返回启动页配置
            </Button>
            <Button
              onClick={this.handleAdd}
              style={{ marginLeft: 8 }}
              disabled={this.state.selections.length === 0 || !this.state.isEdit}
            >
              <Icon type="plus-circle" />
              添加适配图
            </Button>
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>主题：{this.state.detail.title}</Row>
          <Table
            columns={this.getColumns()}
            rowKey="id"
            pagination={false}
            dataSource={this.state.detail.start_page_image_list}
          />
          <Modal
            visible={record.visible}
            title={record.title}
            confirmLoading={this.state.loading}
            onOk={this.handleSubmitAdd}
            key={record.key}
            onCancel={() => this.setState({ editRecord: { ...record, visible: false } })}
          >
            <Form.Item
              extra={
                <span>
                  仅支持jpg、png、gif图片格式
                  <br />
                  图片上传尺寸为
                  {Boolean(record.deviceType) && this.state.deviceList[record.deviceType].picRatio}
                </span>
              }
            >
              <ImageUploader
                value={record.url}
                onChange={this.picChange}
                ratio={Boolean(record.deviceType) && this.state.deviceList[record.deviceType].ratio}
                accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
                ref="uploader"
              />
            </Form.Item>
            {!record.id && (
              <Form.Item>
                类型：
                <Select
                  value={record.deviceType}
                  onChange={this.deviceChange}
                  style={{ width: 300 }}
                >
                  {this.state.selections.map((v: any, i: number) => (
                    <Select.Option key={i} value={v}>
                      {this.state.deviceList[v].name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            )}
          </Modal>
        </div>
      </>
    );
  }
}

export default StartPageDevices;
