import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Di<PERSON>r,
  Form,
  Icon,
  Input,
  InputNumber,
  Modal,
  Pagination,
  Popconfirm,
  Radio,
  Row,
  Select,
  Spin,
  Switch,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { opApi } from '@app/api';
import { A, Drawer, ImageUploader } from '@app/components/common';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import uuid from 'uuid';
import './styles/imRecord.css';
import moment from 'moment';

const ImRecordDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const [tableList, setTableList] = useState({
    current: 1,
    size: 10,
    total: 0,
    showTotal: 0,
    records: [],
  });

  const onPageChange = (page: number) => {
    getList({
      size: 10,
      current: page,
    });
  };

  const onSizeChange = (page: number, pageSize: number) => {
    getList({
      size: pageSize,
      current: page,
    });
  };

  const getList = (params = {}) => {
    dispatch(setConfig({ mLoading: true }));
    opApi
      .getIMChatList({
        send_account_id: props.record.send_account_id,
        receive_account_id: props.record.receive_account_id,
        ...params,
      })
      .then((res: any) => {
        dispatch(setConfig({ mLoading: false }));

        setTableList({
          total: res.data.list.total,
          size: res.data.list.size,
          current: res.data.list.current,
          records: res.data.list.records,
          showTotal: res.data.list.records.length,
        });
      })
      .catch(() => {
        dispatch(setConfig({ mLoading: false }));
      });
  };

  useEffect(() => {
    if (props.visible) {
      getList({
        size: 10,
      });
    }
  }, [props.visible]);

  return (
    <Drawer
      title={props.title}
      visible={props.visible}
      skey={props.skey}
      onClose={props.onCancel}
      maskClosable={false}
      // width={500}
      footer={
        <Row style={{ marginTop: 16 }}>
          <Col span={6} style={{ verticalAlign: 'bottom', textAlign: 'left' }}>
            共{tableList.total}条数据
          </Col>
          <Col span={18} className="pagination-pages">
            <Pagination
              showSizeChanger={true}
              showQuickJumper={true}
              pageSize={tableList.size}
              current={tableList.current}
              pageSizeOptions={['10', '20', '50', '100']}
              onChange={onPageChange}
              onShowSizeChange={onSizeChange}
              total={tableList.total}
            />
          </Col>
        </Row>
      }
    >
      <div>
        {tableList.records.map((item: any) => {
          return (
            <div key={item.id} className="im-record-item">
              <div className="im-record-item-left-avatar">
                <img src={item.send_account_portrait} />
              </div>
              <div className="im-record-item-content">
                <div className="im-record-item-content-name">
                  {item.send_account_name}&nbsp;
                  {moment(item.created_at).format('YYYY-MM-DD HH:mm:ss')}
                </div>
                <div className="im-record-item-content-time">{item.content}</div>
              </div>
            </div>
          );
        })}
      </div>
    </Drawer>
  );
};

export default ImRecordDrawer;
