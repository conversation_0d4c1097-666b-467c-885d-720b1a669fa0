/* eslint-disable no-return-assign */
import { opApi as api } from '@app/api';
import { A, Table, BaseComponent, PreviewMCN, OrderColumn } from '@components/common';
import connect from '@utils/connectAll';
import {
  Button,
  Col,
  Icon,
  Row,
  Input,
  Select,
  DatePicker,
  Modal,
  InputNumber,
  message,
} from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import { getCrumb, requirePerm, setLoading, setMenu } from '@utils/utils';
import { getTableList } from '@app/action/tableList';

import '../news/index.scss';

@(withRouter as any)
@connect
export default class HotNewsList extends React.Component<any, any> {
  componentDidMount() {
    this.getData();
    setMenu(this);
  }

  getData = () => {
    this.props.dispatch(
      getTableList('getHotNewsList', 'recommend_articles', { type: 2, size: 100 })
    );
  };

  handleSort = (record: any, current: number, offset: 0 | 1) => {
    setLoading(this, true);
    api
      .exchangeHotNewsOrder({
        id: record.id,
        type: 2,
        current,
        offset,
      })
      .then(() => {
        message.success('操作成功');
        setLoading(this, false);
        this.getData();
      })
      .catch(() => {
        setLoading(this, false);
      });
  };

  getColumns = () => {
    const { current, size, total } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '排序',
        key: 'sort',
        render: (text: any, record: any, i: number) => (
          <OrderColumn
            perm="move_position:ugc_articles"
            pos={getSeq(i)}
            start={1}
            end={total}
            onUp={this.handleSort.bind(this, record, getSeq(i), 0)}
            onDown={this.handleSort.bind(this, record, getSeq(i), 1)}
          />
        ),
        width: 70,
      },
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '作者',
        dataIndex: 'creator',
        width: 110,
      },
      {
        title: '新闻标题',
        dataIndex: 'list_title',
      },
      {
        title: '点击量',
        dataIndex: 'fake_count',
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any, i: number) =>
          requirePerm(
            this,
            'move_position:ugc_articles'
          )(<A onClick={this.changeOrder.bind(this, record, getSeq(i))}>排序</A>),
        width: 70,
      },
    ];
  };

  changeOrder = (record: any, index: number) => {
    let position = index;
    const { total } = this.props.tableList;
    const posChange = (value: any) => (position = value || 1);
    Modal.confirm({
      title: `排序：${record.list_title}`,
      content: (
        <div>
          <Row>
            请输入位置：
            <InputNumber min={1} max={total} onChange={posChange} defaultValue={index} />
          </Row>
          <Row>此选项只对1~50位置有效</Row>
        </div>
      ),
      onOk: (closeFunc) => {
        if (!position) {
          message.error('请输入位置');
          return;
        }
        api
          .updateHotNewsSort({
            id: record.id,
            type: 2,
            position,
          })
          .then(() => {
            message.success('操作成功');
            this.getData();
            closeFunc();
          });
      },
    });
  };

  render() {
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12} />
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getHotNewsList"
            index="recommend_articles"
            columns={this.getColumns()}
            rowKey="id"
            filter={{ type: 2, size: 100 }}
          />
        </div>
      </>
    );
  }
}
