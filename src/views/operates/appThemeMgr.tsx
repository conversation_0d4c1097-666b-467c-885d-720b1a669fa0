import React, { useEffect, useState, useRef, } from 'react';
import { useDispatch, useSelector, useStore } from 'react-redux';
import moment from 'moment';
import { opApi, } from "@app/api";
import { getTableList } from '@action/tableList';
import { getCrumb, setMenuHook } from '@utils/utils';
import {
    Button,
    Col,
    Divider,
    Icon,
    Row,
    Select,
    message,
    Input, Form, Radio, Modal, Form as AForm, InputNumber, Tooltip
} from 'antd';
import { A, Table, Drawer, OrderColumn } from '@components/common';
import { PermA, PermButton } from "@components/permItems";
import AppThemeMgrForm from '@components/business/appThemeMgrForm';
import useXHR from "@utils/useXhr";
import { useHistory } from "react-router";
import _ from 'lodash'

const ReadPaper: React.FC<{ selectKeys: string; openKeys: string; breadCrumb: any }> = (
    props
) => {
    const dispatch = useDispatch();
    const history = useHistory()
    const { current, size } = useSelector((state: any) => state.tableList)
    const formRef = useRef({} as any);
    const store = useStore();
    const { loading, run } = useXHR();
    // 筛选条件
    const [param, setParam] = useState({
        current: 1,
        keyword: '',
        status: '',
        type: 0,
    });
    const [keyword, setKeyword] = useState('')

    const [drawer, setDrawer] = useState({
        visible: false,
        key: Date.now(),
        type: 'create',
    });
    const [formContent, setFormContent] = useState(null)
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    const handleSort = (record: any, flag: 0 | 1) => {
        run(opApi.updateThemeSort, { id: record.id, sort_flag: flag }, true).then(() => {
            message.success('操作成功');
            getData();
        });
    };
    const getColumns = () => {
        let columns = [
            {
                title: '排序',
                key: 'sort',
                render: (text: any, record: any, i: number) => (
                    <OrderColumn
                        perm="app:theme:update_sort"
                        pos={getSeq(i)}
                        start={1}
                        end={store.getState().tableList.total}
                        disableUp={!record.status || (param.keyword !== '' || param.status !=='')}
                        disableDown={!record.status || (param.keyword !== '' || param.status !=='')}
                        onUp={() => handleSort(record, 0)}
                        onDown={() => handleSort(record, 1)}
                    />
                ),
                width: 70,
            },
            {
                title: '序号',
                key: 'order',
                render(text: any, record: any, i: number) {
                    if (param.type) {
                        return getSeq(i);
                    } else {
                        if (!param.keyword && !param.status) {
                            return getSeq(i);
                        }
                    }
                },
                width: 60
            },
            {
                title: '主题ID',
                key: 'id',
                dataIndex: 'id',
                width: 60,
            },
            {
                title: '主题名称',
                key: 'name',
                dataIndex: 'name',
                width: 200,
            },
            {
                title: '封面图',
                key: 'word',
                dataIndex: 'cover_url',
                width: 100,
                render: (text: any, record: any) => (<div style={{ height: 60, textAlign: 'center', width: '100%' }}>
                    {/*<img src={record.cover_image} className='list-pic' style={{ height: '100%' }} onMouseEnter={() => showImagePreviewModal({ images: [record.cover_image] })}></img>*/}
                    <img src={record.cover_url} className='list-pic' style={{ height: '100%', marginLeft: -20 }}></img>
                </div>),
            },
            // {
            //     title: '内容数',
            //     dataIndex: 'content',
            //     render: (text: any, record: any) => {
            //         if (text && text !== 'null' && text.slice(0, 2) === "[{") {
            //             let n = 0
            //             const contentArray = text ? JSON.parse(text) : [];
            //             contentArray.map((item: any) => {
            //                 if (item.article_list) {
            //                     n += item.article_list.length
            //                 }
            //             })
            //             return <span>{n}</span>;
            //         } else {
            //             return 0
            //         }
            //     },
            // },
            {
                title: '状态',
                dataIndex: 'status',
                key: 'status',
                render: (text: any, record: any) => <>{['下架', '上架'][text]}</>,
                width: 90
            },
            {
                title: '最后修改时间',
                dataIndex: 'updated_at',
                key: 'updated_at',
                render: (text: number) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
                width: 155
            },
            {
                title: '最后操作人',
                key: 'updated_by',
                dataIndex: 'updated_by',
                width: 155
            },
            {
                title: '操作',
                dataIndex: 'date12',
                width: 200,
                render(text: any, record: any, i: number) {
                    return (
                        <>
                            <PermA perm="app:theme:edit"
                                onClick={() => {
                                    editTheme(record)
                                }}
                            >编辑</PermA>
                            <Divider type="vertical" />
                            <PermA perm="app:theme:change_status"
                                onClick={() => {
                                    changeStatus(record)
                                }}
                            >{['上架', '下架'][record.status]}</PermA>
                            <Divider type="vertical" />
                            {!param.type && <><PermA
                                perm='app:theme:update_sort'
                                disabled={!record.status}
                                onClick={exchangeInsertSort.bind(this, record, getSeq(i))}>排序</PermA>
                                <Divider type="vertical" />
                            </>
                            }
                            <PermA
                                perm="app:theme:delete"
                                onClick={() => {
                                    handleDelete(record)
                                }}
                            >
                                删除
                            </PermA>
                        </>
                    );
                },
            },
        ];
        // if(param.keyword !== '' || param.status !==''){
        //     delete columns[0]
        // }
        if (param.type === 1) {
            delete columns[4]
            delete columns[0]
        }
        return columns
    };


    const getData = (isCurrent) => {
        dispatch(getTableList('getThemeList', 'list', { ...param, current: isCurrent ? isCurrent : current, size }));
    };
    const handleDelete = (record: any) => {
        Modal.confirm({
            title: '确定删除该主题？',
            content: !param.type && '（使用该主题的用户将自动恢复初始默认样式）',
            onOk: (closeFunc: Function) => {
                opApi.deleteTheme({ id: record.id }).then(() => {
                    message.success('操作成功');
                    closeFunc()
                    getData();
                })
            }
        })
    };
    // 初始化
    useEffect(() => {
        setMenuHook(dispatch, props);
        getData(1);
    }, [param]);

    const formLayout = {
        labelCol: { span: 5 },
        wrapperCol: { span: 17 },
    };
    // 排序
    const exchangeInsertSort = (record: any, newSort: number | undefined) => {
        const posChange = (value: number | undefined) => {
            newSort = value;
        };
        if (param.keyword || param.status) {
            newSort = undefined
        }
        Modal.confirm({
            title: `调整排序`,
            icon: null,
            content: (
                <div>
                    <AForm {...formLayout}>
                        <AForm.Item
                            label={'排序值'}
                        // extra="客户端只呈现上架状态且视频数大于等于所填数字的话题。"
                        >
                            <InputNumber
                                placeholder="请输入修改序号"
                                min={1}
                                onChange={posChange}
                                defaultValue={newSort}
                                style={{ width: 200 }}
                                precision={0}
                            />
                            <br />
                            {/* <span style={{ color: 'red' }}>为保证客户端呈现，只能大于等于0</span> */}
                        </AForm.Item>
                    </AForm>
                </div>
            ),
            onOk: (closeFunc: Function) => {
                if (!newSort) {
                    message.error('请填写序号');
                    return;
                }
                opApi
                    .updateThemeSort({
                        id: record.id,
                        position: newSort,
                        sort_flag: 2,
                    })
                    .then(() => {
                        message.success('操作成功');
                        getData();
                        closeFunc();
                    });
            },
        });
    };
    const changeStatus = (record: any) => {
        const reqData = {
            status: record.status === 1 ? 0 : 1,
            id: record.id
        }
        Modal.confirm({
            title: record.status == 0 ? param.type == '1' ? '全局主题将对所有用户生效（优先级高于自定义主题或默认主题），确定上架？' : '确定上架该主题？' :
                param.type == '1' ? '确定下架该主题？（用户将自动恢复已设置的自定义主题或初始默认样式）' : '确定下架该主题？（使用该主题的用户将自动恢复初始默认样式；后续再上架时，用户需重新选用）？',
            content: (
                <>
                    {/*{record.status ?*/}
                    {/*    <div>（使用该主题的用户将自动恢复初始默认样式；后续再上架时，用户需重新选用）</div> : ''}*/}
                </>
            ),
            onOk: (closeFunc: Function) => {
                opApi.updateThemeStatus(reqData).then((res) => {
                    message.success('操作成功');
                    closeFunc()
                    getData()
                })
            },
        });
    }
    // 时间修改
    const editTheme = (record: any) => {
        opApi.detailTheme({ id: record.id }).then((res) => {
            const { detail } = res.data
            if (detail.android_version)
                detail.android_version = detail.android_version.split('-')

            if (detail.ios_version)
                detail.ios_version = detail.ios_version.split('-')


            setFormContent({ ...detail })
            setDrawer({
                visible: true,
                key: Date.now(),
                type: 'edit'
            })
        })
    };

    const getFilter = () => {
        return param;
    };
    const handleTypeChange = (e: any) => {
        setParam({
            current: 1,
            keyword: '',
            status: '',
            type: e.target.value,
        })
        // setKeyword('')
        // setType(e.target.value)
    }
    const keywordChange = (event: any) => {
        const value = event.target.value;
        setKeyword(value);
    };

    const keywordSearch = (e: any) => {
        setParam({
            ...param,
            keyword,
        })
    };
    const onSubmitEnd = () => {
        setDrawer({ ...drawer, visible: false });
    };
    return (
        <>
            <Row className="layout-infobar">
                <Col span={12}>
                    <Radio.Group
                        value={param.type}
                        style={{ marginRight: 8 }}
                        onChange={handleTypeChange}
                        buttonStyle="solid"
                    >
                        <Radio.Button value={0}>自选主题</Radio.Button>
                        <Radio.Button value={1}>全局主题</Radio.Button>
                    </Radio.Group>
                    <Tooltip title={<div>功能说明：<br />
                        1、用户自选主题，呈现在客户端的主题管理页面，用户自主选择使用；<br />
                        2、全局生效主题，不支持用户自主选择是否使用；后台开启则面向所有用户强制生效，优先级高于用户自选主题。<br />
                        3、两种主题样式，都针对6.0.0及以上版本生效。
                    </div>} placement="bottom">
                        <Icon style={{ marginRight: 8 }} type="question-circle" />
                    </Tooltip>
                    <PermButton
                        perm="app:theme:add"
                        onClick={() => {
                            setFormContent(null)
                            setDrawer({ ...drawer, visible: true, type: 'create' });
                        }}
                        style={{ marginRight: 8 }}
                    >
                        <Icon type="plus-circle" />添加主题
                    </PermButton>
                    <PermButton
                        perm="app:theme:remind:list"
                        onClick={() => {
                            history.push(`/view/homeRemind`);
                        }}
                    >
                        主题首页提醒
                    </PermButton>
                </Col>
                <Col span={12} className="layout-breadcrumb">
                    {getCrumb(props.breadCrumb)}
                </Col>
            </Row>
            <div className="component-content news-pages">
                <Row style={{ marginBottom: 16 }} type="flex" align="middle">
                    <Col span={14}>
                        <Select
                            style={{ width: 160, marginLeft: 8 }}
                            onChange={(e) => {
                                setParam({
                                    ...param,
                                    status: e
                                })
                            }}
                            placeholder="请选择"
                            value={param.status}
                        >
                            <Select.Option value="">全部状态</Select.Option>
                            <Select.Option value="1">上架</Select.Option>
                            <Select.Option value="0">下架</Select.Option>
                        </Select>
                    </Col>
                    <Col span={10} style={{ textAlign: 'right' }}>
                        <Input
                            placeholder="输入主题名称搜索"
                            style={{ width: 150 }}
                            value={keyword}
                            onChange={keywordChange}
                        />
                        <Button
                            style={{ marginLeft: 8, verticalAlign: 'top' }}
                            onClick={keywordSearch}
                        >
                            <Icon type="search" />
                            搜索
                        </Button>
                    </Col>
                </Row>
                <Table
                    func="getThemeList"
                    index="list"
                    pagination={true}
                    rowKey="id"
                    columns={getColumns()}
                    filter={getFilter()}
                />
                <Drawer
                    visible={drawer.visible}
                    skey={drawer.key}
                    title={drawer.type === 'create' ? `添加${['自选', '全局'][param.type]}主题` : '编辑主题'}
                    onClose={() => {
                        setDrawer({ ...drawer, visible: false })
                    }}
                    onOk={() => {
                        formRef.current.doSubmit()
                    }}
                >
                    <AppThemeMgrForm
                        type={param.type}
                        drawer={drawer}
                        onEnd={onSubmitEnd}
                        formContent={formContent}
                        wrappedComponentRef={(instance: any) => (formRef.current = instance)}
                        getData={() => {
                            getData()
                        }}
                    />
                </Drawer>
            </div>
        </>
    );
};

export default Form.create({})(ReadPaper);
