import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useStore, useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, OrderColumn, PreviewMCN, NewTable } from '@components/common';
import { CommonObject } from '@app/types';
import {
  getCrumb,
  objectToQueryString,
  requirePerm4Function,
  searchToObject,
  setMenuHook,
} from '@app/utils/utils';
import {
  Row,
  Col,
  Divider,
  Icon,
  Modal,
  message,
  InputNumber,
  Tooltip,
  Popconfirm,
  Button,
  Menu,
  Dropdown,
  Select,
  DatePicker,
  Timeline,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { opApi as api, communityApi, releaseListApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@components/permItems';
import moment from 'moment';
import AddCompilations from './addCompilations';
import AddConfigHeader from './addConfigHeader';
import { remove } from 'lodash';
export default function EditorChoiceness(props: any) {

  const [filter, setFilter] = useState<any>({
    online: "",
  });


  const dispatch = useDispatch();
  const store = useStore();
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const getList = useCallback(
    (first: boolean = false, overlap: CommonObject = {}) => {
      const { current, size = 10 } = store.getState().tableList;

      let params = {
        ...filter,
        current: first ? 1 : current,
        size,
        ...overlap,
      }

      if (filter.start_date) {
        params.start_date = moment(filter.start_date).format('YYYYMMDD');
      }

      if (filter.end_date) {
        params.end_date = moment(filter.end_date).format('YYYYMMDD');
      }

      dispatch(
        getTableList('getDrawerList', 'list', params)
      );
    },
    [filter]
  );


  const columns = [

    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
    },
    {
      title: '日期',
      key: 'date',
      dataIndex: 'date',
      render: (text: any, record: any) => {
        const dateStr = record.date.toString();
        if (!dateStr) return null;

        // 使用 substring 截取年、月、日部分
        const year = dateStr.substring(0, 4);
        const month = dateStr.substring(4, 6);
        const day = dateStr.substring(6, 8);

        // 重新组合成 YYYY-MM-DD 格式
        const formattedDate = `${year}-${month}-${day}`;
        return <span>{formattedDate}</span>;
      },
    },
    {
      title: '内容数',
      dataIndex: 'article_count',

      render: (text: any) => text || 0
    },
    {
      title: '状态',
      dataIndex: 'online',
      render: (text: any, record: any, index: number) => {


        if (record.online === true) {
          return '上架';  // Only explicitly true
        }
        if (record.online === false) {
          return '下架';  // Explicitly false
        }
        return '未知';
      }
    },
    {
      title: '操作人',
      dataIndex: 'updated_by',
    },
    {
      title: '操作时间',
      dataIndex: 'updated_at',
      render: (text: any, record: any) => {
        return (

          <a style={{ whiteSpace: 'normal' }} onClick={() => getOperateLog(record)}>
            <span>{moment(text).format('YYYY-MM-DD')}</span>
            <br></br>
            <span>{moment(text).format('HH:mm:ss')}</span>
          </a>

        );
      },
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any, i: number) => (
        <span>
          <PermA perm="drawer:online" onClick={() => soldOut(record)}>
            {record.online === false ? '上架' : '下架'}
          </PermA>
          <Divider type="vertical" />

          <PermA perm="drawer:edit" onClick={() => editCompilations(record)}>
            编辑
          </PermA>
          <Divider type="vertical" />
          <PermA perm="drawer:delete" onClick={() => deleteRecord(record)}>
            删除
          </PermA>
        </span>
      ),
    },
  ];



  useEffect(() => {
    setMenuHook(dispatch, props);
  }, []);

  useEffect(() => {
    getList(true);
  }, [filter]);

  const [compilationsDarwer, setCompilationsDarwer] = useState({
    visible: false,
    record: null,
    key: Date.now(),
  });

  //添加合集
  const addCompilations = (record: any) => {
    setCompilationsDarwer({
      visible: true,
      record: null,
      key: Date.now(),
    });
  };

  //编辑合集
  const editCompilations = (record: any) => {
    setCompilationsDarwer({
      ...compilationsDarwer,
      visible: true,
      record: record,
    });
  };

  const [configHeaderDarwer, setConfigHeaderDarwer] = useState({
    visible: false,
    key: Date.now(),
  });
  //配置头图
  const configHeader = (record: any) => {
    setConfigHeaderDarwer({
      ...configHeaderDarwer,
      visible: true,

    });
  };

  //下架
  const soldOut = (record: any) => {
    api
      .onlineDrawer({ id: record.id, online: record.online == 1 ? false : true })
      .then(() => {
        message.success('操作成功');
        // dispatch(setConfig({ loading: false }));
        getList();
      })
      .catch(() => {
        // dispatch(setConfig({ loading: false }));
      });
  };

  //删除
  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确定要删除吗？`,
      onOk: () => {
        run(api.deleteDrawer, { id: record.id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };
  // 时间选择
  const timeChange = (dates: any) => {
    if (dates.length === 0) {
      const { start_date, end_date, ...newFilter } = filter;
      setFilter({ ...newFilter });

    } else {

      setFilter({ ...filter, start_date: dates[0], end_date: dates[1] });
    }
  };
  // 状态修改
  const filterChange = (key: any, value: any) => {
    setFilter({ ...filter, [key]: value });
  };

  const [logs, setLogs] = useState<any>({
    visible: false,
    logs: [],
    title: '',
  });

  const { loading, run } = useXHR();

  const getOperateLog = (record: any) => {
    run(
      api.getOperateLog,
      { target_id: record.id, type: 200702 },
      true
    )
      .then((r: any) => {
        setLogs({
          visible: true,
          logs: r?.data?.admin_log_list || [],
          title: record.date,
          key: Date.now(),
        });
      })
      .catch();
  };

  // api.operationRecord({ type: 165, target_id: record.id }).then((res: any) => {
  //   this.setState({
  //     operationRecord: {
  //       visible: true,
  //       logs: res?.data?.admin_log_list || []
  //     }
  //   })
  // }).catch(() => {
  // })

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton perm='drawer:create' style={{ marginRight: 8 }} onClick={addCompilations}>
            <Icon type="plus-circle" /> 添加合集
          </PermButton>

          <PermButton perm='drawer:config' style={{ marginRight: 8 }} onClick={configHeader}>
            配置头图
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={12}>
            <DatePicker.RangePicker
              value={[filter.start_date, filter.end_date]}
              style={{ width: 260, marginRight: 8 }}
              format="YYYY-MM-DD"
              onChange={timeChange}
            />

            <Tooltip title='筛选日期' style={{ marginLeft: 3 }}
            >
              <Icon type="question-circle" />
            </Tooltip>

            <Select
              value={filter.online}
              onChange={(value: any) => filterChange('online', value)}
              style={{ width: 160, marginLeft: 12 }}
            >
              <Select.Option value="">全部状态</Select.Option>
              <Select.Option value="1">上架</Select.Option>
              <Select.Option value="0">下架</Select.Option>
            </Select>
          </Col>

        </Row>
        <Table
          func='getDrawerList'
          index="list"
          filter={filter}
          columns={columns}
          rowKey="date"
          pagination={true}
        />
        <AddCompilations
          {...compilationsDarwer}
          onClose={() => {
            setCompilationsDarwer({ ...compilationsDarwer, visible: false });
          }}
          onEnd={() => {
            setCompilationsDarwer({ ...compilationsDarwer, visible: false });
            getList();
          }}
        ></AddCompilations>


        <AddConfigHeader
          {...configHeaderDarwer}
          onClose={() => {
            setConfigHeaderDarwer({ ...configHeaderDarwer, visible: false });
          }}
          onOk={() => {
            setConfigHeaderDarwer({ ...configHeaderDarwer, visible: false });
            getList();
          }}
        ></AddConfigHeader>


        <Modal
          visible={logs.visible}
          title="操作日志"
          key={logs.key}
          cancelText={null}
          onCancel={() => setLogs({ visible: false })}
          onOk={() => setLogs({ visible: false })}
        >
          <div>
            <h3 className="line-max">
              编辑精选 {logs.title ?? ""}
            </h3>
            <Timeline style={{ marginTop: 20 }}>
              {logs.logs?.map((v: any, i: number) => [
                <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                  &nbsp;
                </Timeline.Item>,
                v.log_list?.map((action: any, index: number) => (
                  <Timeline.Item
                    className="timeline-dot"
                    data-show={moment(action.created_at).format('HH:mm:ss')}
                    key={`time${i}-action${index}`}
                  >
                    {action.admin_name}&emsp;{action.remark}
                    {/* {action.user}&emsp;&emsp;{action.action}&emsp;&emsp; */}
                  </Timeline.Item>
                )),
              ])}
            </Timeline>
          </div>
        </Modal>
      </div>
    </>
  );
}
