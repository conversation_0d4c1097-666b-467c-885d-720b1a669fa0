import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Dropdown,
  Form,
  Icon,
  Input,
  InputNumber,
  Menu,
  Modal,
  Radio,
  Row,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { opApi } from '@app/api';
import '@components/business/styles/business.scss';
import { setMLoading } from '@app/utils/utils';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { Drawer, ImageUploader, VideoUploader } from '@app/components/common';
import ImgUploadList from '@app/components/common/imgUploadList';
import { PermButton } from '@app/components/permItems';
import moment from 'moment';
import { initial } from 'lodash';

const circleBannerDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();

  const { title, pic_url, url } = props.record || {};

  const { getFieldDecorator, getFieldValue } = props.form;

  const doSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body: any = { ...values };
        let api;
        if (props.record) {
          body.id = props.record.id;
          api = opApi.updateCircleBanner;
        } else {
          api = opApi.createCircleBanner;
        }

        dispatch(setConfig({ mLoading: true }));
        api(body)
          .then(() => {
            dispatch(setConfig({ mLoading: false }));
            message.success('操作成功');
            props.onEnd(values.type);
          })
          .catch(() => {
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };
  // const menu = (
  //   <Menu onClick={props.onClose}>
  //     <Menu.Item key="1" style={{ textAlign: 'center' }}>
  //       仅保存修改
  //     </Menu.Item>
  //     <Menu.Item key="2" style={{ textAlign: 'center' }}>
  //       保存并通过
  //     </Menu.Item>
  //   </Menu>
  // );
  return (
    <Drawer
      title={!!props.record ? '编辑轮播图' : '添加轮播图'}
      visible={props.visible}
      skey={props.key}
      onClose={props.onClose}
      onOk={doSubmit}
      okText="确定"
    >
      <Form {...formLayout}>
        <Form.Item label="名称">
          {getFieldDecorator('title', {
            initialValue: title,
            rules: [
              {
                required: true,
                message: '请输入名称',
                whitespace: true,
              },
              {
                max: 10,
                message: '最多10个字',
              },
            ],
          })(<Input placeholder="请输入轮播图名称，仅在后台显示，最多10个字"></Input>)}
        </Form.Item>

        <Form.Item label="链接">
          {getFieldDecorator('url', {
            initialValue: url,
            rules: [
              {
                required: true,
                message: '请填写链接',
              },
              {
                validator: (rule: any, value: any, callback: any) => {
                  const regex = /^https?:\/\//;
                  if (value?.length > 0 && !regex.test(value)) {
                    callback('请正确填写关联链接');
                    return;
                  }
                  callback();
                },
              },
            ],
          })(<Input placeholder="请输入链接" />)}
        </Form.Item>
        <Form.Item label="图片" extra="支持扩展名：.jpg .jpeg .png，比例 3:4">
          {getFieldDecorator('pic_url', {
            initialValue: pic_url,
            rules: [
              {
                required: true,
                message: '请上传图片',
              },
            ],
          })(<ImageUploader ratio={3 / 4} />)}
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'circleBannerDrawer' })(
  forwardRef<any, any>(circleBannerDrawer)
);
