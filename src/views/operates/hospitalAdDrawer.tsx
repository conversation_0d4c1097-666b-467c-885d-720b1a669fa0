import { Button, Form, Icon, Input, Modal, Radio, Row, Switch, Tooltip, message } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import _, { set } from 'lodash';
import { opApi } from '@app/api';
import { A, Drawer, FileUploader, ImageUploader } from '@app/components/common';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import TMFormList, { FormListTitle } from '@app/components/common/TMFormList';
import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';

const HospitalAdDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const channel_id = props.channel_id ?? '';

  const platformListRef = useRef<any>(null);
  const [platformList, setPlatformList] = useState<any[]>([
    {
      name: '',
      logo_url: '',
      article_list: [],
    },
  ]);

  const { getFieldDecorator, getFieldValue, setFieldsValue } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  useEffect(() => {
    if (props.visible) {
      getDetail();
    }
  }, [props.visible]);

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = () => {
    // e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        dispatch(setConfig({ mLoading: true }));
        let parmas: any = {
          ...values,
          list: values.list.map((item: any) => {
            return {
              ...item,
              article_list: item.article_list.map((v: any) => {
                const result: any = {
                  id: v.id,
                };
                if (v.custom_list_pics) {
                  result.custom_list_pics = v.custom_list_pics;
                }
                return result;
              }),
            };
          }),
        };

        opApi
          .gptHospitalAdvertiseSave(parmas)
          .then((res: any) => {
            message.success('保存成功');
            dispatch(setConfig({ mLoading: false }));
            props.onEnd && props.onEnd();
          })
          .catch(() => {
            // message.error('添加失败');
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
        dispatch(setConfig({ mLoading: false }));
      }
    });
  };

  const [detailData, setDetailData] = useState<any>(null);

  const editPic = (url: string, index: number, listIndex: number) => {
    let pic: string = url;
    let modal: any;
    const picChange = (u: string) => {
      pic = u;
      modal.update({
        content: (
          <>
            <ImageUploader value={pic} onChange={picChange} ratio={160 / 80} />
            <p>比例160:80</p>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '编辑图片',
      content: (
        <>
          <ImageUploader value={pic} onChange={picChange} ratio={160 / 80} />
          <p>比例160:80</p>
        </>
      ),
      onOk: (destroy: Function) => {
        if (!pic) {
          message.error('请上传图片');
          return;
        }
        const hospitals = props.form.getFieldsValue().list;

        const current = hospitals[listIndex];
        const articles = [...current.article_list];
        articles[index].custom_list_pics = pic;
        current.article_list = articles;

        props.form.setFieldsValue({ list: hospitals });
        destroy();
      },
    });
  };

  const getDetail = _.debounce(() => {
    dispatch(setConfig({ mLoading: true }));

    opApi
      .gptHospitalAdvertiseDetail({})
      .then((res) => {
        dispatch(setConfig({ mLoading: false }));
        const detail: any = res.data;
        setDetailData(detail);
        setPlatformList(
          detail?.list || [
            {
              name: '',
              logo_url: '',
              article_list: [],
            },
          ]
        );
      })
      .catch(() => {
        dispatch(setConfig({ mLoading: false }));
      });
  }, 500);

  const getColumns = (listIndex: number) => {
    return [
      {
        title: '潮新闻ID',
        dataIndex: 'id',
        width: 80,
        render: (_: any, v: any) => v.uuid || v.id,
      },
      {
        title: '新闻频道',
        key: 'type',
        dataIndex: 'channel_name',
        width: 90,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
        width: 150,
      },
      {
        title: '列表图',
        align: 'center',
        // title: '列表图',
        dataIndex: 'custom_list_pics',
        width: 90,
        render: (text: string, record: any, index: number) => {
          return (
            <div style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
              {text && <img src={text} style={{ height: 40, maxWidth: 70 }} />}
              <A onClick={() => editPic(text, index, listIndex)}>{text ? '修改' : '上传'}</A>
            </div>
          );
        },
      },
    ];
  };

  return (
    <Drawer
      title={
        <>
          <span>医院广告位</span>
          <span
            style={{
              fontSize: '12px',
              color: 'gray',
            }}
          >
            &nbsp;最多可以配置10家医院
          </span>
        </>
      }
      visible={props.visible}
      skey={props.skey}
      onClose={props.onClose}
      maskClosable={false}
      // width={500}
      onOk={handleSubmit}
      okPerm=""
      okText="保存"
    >
      <Form {...formLayout}>
        <Form.Item label="展示">
          {getFieldDecorator('enabled', {
            initialValue: detailData?.enabled,
            valuePropName: 'checked',
            rules: [
              {
                required: true,
              },
            ],
          })(<Switch></Switch>)}
        </Form.Item>

        <TMFormList
          ref={platformListRef}
          dataList={platformList}
          form={props.form}
          fromItem={() => {
            return {
              name: '',
              logo_url: '',
              article_list: [],
            };
          }}
          filed="list"
        >
          {(item, index, length) => {
            return (
              <div key={`${item.name}-${index}`}>
                <FormListTitle
                  total={length}
                  i={index}
                  title="医院"
                  upMove={() => platformListRef.current?.upMove(index)}
                  downMove={() => platformListRef.current?.downMove(index)}
                  removeItem={() => platformListRef.current?.removeItem(index)}
                  min={1}
                />

                <Form.Item label="医院名称">
                  {getFieldDecorator(`list[${index}].name`, {
                    initialValue: item.name,
                    rules: [
                      {
                        required: getFieldValue('enabled'),
                        message: '请输入医院名称',
                        whitespace: true,
                      },
                      {
                        max: 30,
                        message: '最多可输入30字',
                      },
                    ],
                  })(<Input placeholder="最多可输入30字"></Input>)}
                </Form.Item>
                <Form.Item label="医院logo" extra="支持.jpg .jpeg .png图片格式，比例为1:1">
                  {getFieldDecorator(`list[${index}].logo_url`, {
                    initialValue: item.logo_url,
                    rules: [
                      {
                        required: getFieldValue('enabled'),
                        message: '请上传图片',
                      },
                    ],
                  })(<ImageUploader ratio={1} accept={['image/jpeg', 'image/png', 'image/jpg']} />)}
                </Form.Item>

                <Form.Item label="关联稿件">
                  {getFieldDecorator(`list[${index}].article_list`, {
                    initialValue: props.record?.article_list,
                    validateFirst: true,
                    rules: [
                      {
                        required: getFieldValue('enabled'),
                        message: '请关联新闻',
                        type: 'array',
                      },
                      {
                        max: 20,
                        message: '最多关联20条新闻',
                        type: 'array',
                      },
                      {
                        validator: (rule: any, val: any, callback: any) => {
                          if (!getFieldValue('enabled')) {
                            return callback();
                          }
                          if (!val) {
                            return callback('');
                            // } else if (val.filter((v) => !v.doc_title).length > 0) {
                            //   return callback('请填写自定义标题');
                          } else if (val.filter((v: any) => !v.custom_list_pics).length > 0) {
                            return callback('请上传列表图');
                          } else {
                            return callback();
                          }
                        },
                      },
                    ],
                  })(
                    <NewNewsSearchAndInput
                      // key={style}
                      max={20}
                      func="listArticleRecommendSearch"
                      columns={getColumns(index)}
                      placeholder="输入ID或标题关联稿件"
                      body={{ doc_types: '2,3,5' }}
                      order={true}
                      addOnTop={true}
                      map={(v: any) => {
                        return v;
                      }}
                      afix={
                        <Tooltip title="可关联新闻稿、专题稿、链接稿">
                          <Icon type="question-circle" />
                        </Tooltip>
                      }
                    />
                  )}
                </Form.Item>
              </div>
            );
          }}
        </TMFormList>

        <Row style={{ textAlign: 'center', marginBottom: 16 }} id="add-btn-line">
          <Button
            onClick={() => platformListRef.current?.addItem()}
            disabled={(platformListRef.current?.total ?? 0) >= 10}
          >
            添加医院
          </Button>
        </Row>
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'HospitalAdDrawer' })(
  forwardRef<any, any>(HospitalAdDrawer)
);
