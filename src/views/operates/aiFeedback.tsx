import { Col, Divider, Input, Modal, Row, message, InputNumber, Button, Radio, Select } from 'antd';
import { getCrumb, objectToQueryString, searchToObject, UserDetail } from '@app/utils/utils';
import ReactClipboard from 'react-clipboardjs-copy';
import { useHistory } from 'react-router';
import { useSelector, useDispatch, useStore } from 'react-redux';
import { getTableList } from '@app/action/tableList';
import { Table, OrderColumn } from '@components/common';
import React, { useEffect, useState } from 'react';
import { opApi, userApi } from '@app/api';
import { PermA, PermButton } from '@app/components/permItems';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import uuid from 'uuid';
import AiFeedbackModal from './aiFeedbackModal';
import useXHR from '@app/utils/useXhr';

export default function AiFeedback(props: any) {
  const history = useHistory();
  const dispatch = useDispatch();
  const { total, current, size, records = [] } = useSelector((state: any) => state.tableList);
  const { session } = useStore().getState();
  const [type, setType] = useState(parseInt(searchToObject().type ?? 0));
  const [filter, setFilter] = useState({
    types: type == 0 ? '100,101,102,103,104' : '1,2,3',
  });
  const { run, loading } = useXHR();
  // 1-智能标题、2-智能封面、3-智能配文
  // 100-潮奔奔，101-24小时会客厅  102-圈子数字代理人  103-公文搜索
  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const [aiFeedbackModal, setAiFeedbackModal] = useState<any>({
    visible: false,
    key: uuid(),
    detail: null,
  });

  const [userDetailModal, setUserDetailModal] = useState<any>({
    visible: false,
    key: uuid(),
    detail: null,
  });

  const showUserDetailModal = (record: any, visible: boolean) => {
    run(userApi.getUserDetail, { accountId: record.account_id }, true).then((r: any) => {
      setUserDetailModal({
        key: Date.now(),
        visible: true,
        detail: r.data.account,
      });
    });
  };

  const columns: any = [
    {
      title: '序号',
      key: 'id',
      render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
      width: 50,
    },
    {
      title: '反馈类型',
      dataIndex: 'favorite',
      width: 80,
      render: (text: number) => {
        return text == 0 ? '点踩' : '点赞';
      },
    },
    {
      title: type == 0 ? '智能体' : '功能场景',
      dataIndex: 'type',
      width: 100,
      render: (text: number) => {
        return type == 0
          ? ['潮奔奔', '潮小帮', '潮小拍', '潮小搜', '潮小康'][text - 100]
          : ['', '智能标题', '智能封面', '智能配文'][text];
      },
    },
    {
      title: '反馈原因',
      dataIndex: 'reason',
    },
    {
      title: '反馈人',
      dataIndex: 'nick_name',
      width: 160,
      render: (text: string, record: any) => {
        return <a onClick={() => showUserDetailModal(record, true)}>{text}</a>;
      },
    },
    {
      title: '反馈时间',
      key: 'created_at',
      dataIndex: 'created_at',
      width: 160,
      render: (text: number, record: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any, i: number) => (
        <span>
          <PermA perm="reporter:3:delete" onClick={() => showDetail(record)}>
            查看详情
          </PermA>
        </span>
      ),
      width: 180,
    },
  ];

  const changeFilter = (key: string, val: any, goToFirstPage = true) => {
    let newFilter = {
      ...filter,
      [key]: val,
    };
    setFilter(newFilter);
  };

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      const newFilter = {
        ...filter,
      };
      setFilter(newFilter);
    }
  };

  // 获取table
  const getData = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current;
    dispatch(getTableList('getAiFeedbackList', 'list', { current: cur, size, ...newFilter }));
  };

  const showDetail = (record: any) => {
    setAiFeedbackModal({
      visible: true,
      key: uuid(),
      detail: record,
    });
  };

  const onChangeType = (v: any, name: any) => {
    let { pathname: path, search } = history.location;
    const obj = searchToObject();
    obj[name] = v;
    path = `${path}?${objectToQueryString(obj)}`;
    history.replace(path);
    setType(v);
    setFilter({
      types: v == 0 ? '100,101,102,103,104' : '1,2,3',
    });
  };

  useEffect(() => {
    const { selectKeys, openKeys } = props;
    dispatch(setConfig({ selectKeys, openKeys }));
  }, []);

  useEffect(() => {
    getData(true);
  }, [filter]);

  const options =
    type == 0
      ? [
          { label: '全部智能体', value: '100,101,102,103,104' },
          { label: '潮奔奔', value: '100' },
          { label: '潮小帮', value: '101' },
          { label: '潮小拍', value: '102' },
          { label: '潮小搜', value: '103' },
          { label: '潮小康', value: '104' },
        ]
      : [
          { label: '全部功能场景', value: '1,2,3' },
          { label: '智能标题', value: '1' },
          { label: '智能封面', value: '2' },
          { label: '智能配文', value: '3' },
        ];

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Radio.Group
            defaultValue={type}
            buttonStyle="solid"
            onChange={(e) => onChangeType(e.target.value, 'type')}
          >
            <Radio.Button value={0}>智能助手</Radio.Button>
            <Radio.Button value={1}>智能创作</Radio.Button>
          </Radio.Group>

          <Select
            value={filter.types}
            style={{ width: 150, marginLeft: 8 }}
            onChange={(v) => changeFilter('types', v)}
          >
            {options.map((item) => {
              return <Select.Option value={item.value}>{item.label}</Select.Option>;
            })}
          </Select>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          func="getAiFeedbackList"
          index="list"
          pagination={true}
          rowKey="id"
          columns={columns}
          filter={filter}
        />

        {/* 用户详情弹窗 */}
        <Modal
          visible={userDetailModal.visible}
          key={userDetailModal.key}
          title="用户详情"
          width={800}
          onCancel={() => setUserDetailModal({ visible: false })}
          onOk={() => setUserDetailModal({ visible: false })}
        >
          {/*{user.visible && getUserDetail(user.detail)}*/}
          {userDetailModal.visible && <UserDetail detail={userDetailModal.detail} />}
        </Modal>

        <AiFeedbackModal
          type={type}
          {...aiFeedbackModal}
          onCancel={() => setAiFeedbackModal({ visible: false })}
        ></AiFeedbackModal>
      </div>
    </>
  );
}
