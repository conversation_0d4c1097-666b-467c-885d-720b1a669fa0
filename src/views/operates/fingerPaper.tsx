import React, { useEffect, useState, useRef } from 'react';
import { setConfig } from '@action/config';
import { useDispatch, useSelector } from 'react-redux';
import {
  Button,
  Col,
  DatePicker,
  Divider,
  Icon,
  Row,
  Select,
  Popconfirm,
  message,
  Tooltip,
  Modal,
  Timeline,
} from 'antd';
import { A, Table, Drawer } from '@components/common';
import { getTableList } from '@action/tableList';
import ImagePreview from '@components/common/imagePreview';
import { ShowModal, getCrumb } from '@utils/utils';
import FingerPaperForm from '@components/business/FingerPaperForm';
import { opApi as api } from '@app/api';
import moment from 'moment';
import { CommonObject, IOperationActionData } from '@app/types';
import FingerPapperPreview from '@components/business/FingerPapperPreview';
import showImagePreviewModal from '@app/components/common/imagePreviewModal';
import { PermA, PermButton } from '@components/permItems';
import uuid from 'uuid';
import ImagePreviewColumn from '@app/components/common/imagePreviewColumn';

const FingerPaper: React.FC<{ selectKeys: string; openKeys: string; breadCrumb: any }> = (
  props
) => {
  const dispatch = useDispatch();
  // 筛选条件
  const [param, setParam] = useState({
    current: 1,
    pub_date_start: '',
    pub_date_end: '',
    status: '',
  });
  // 图片预览
  const [imageUrl, setImageUrl] = useState('');
  // 抽屉
  const [drawer, setDrawer] = useState({
    visible: false,
    key: Date.now(),
    type: 'create',
  });
  const [formContent, setFormContent] = useState(null);
  const [operateLog, setOperateLog] = useState({
    visible: false,
    articleTitle: '',
    list: [],
    key: Date.now(),
  });
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const getOperateLog = (pub_date: number, mid: number, title: string, record: any) => {
    const isRecommend = record && record.doc_type === -1;
    let arr = [];
    api
      .fingerPagerLogs({ pub_date })
      .then((res) => {
        const data = res.data.logs;
        let keyArr = Object.keys(data);
        keyArr.map((item) => {
          arr.push({ date: item, list: data[item].reverse() });
        });
        const dateString = pub_date.toString();
        const formattedDate = moment(dateString, 'YYYYMMDD').format('YYYY-MM-DD');
        setOperateLog({
          ...operateLog,
          visible: true,
          articleTitle: formattedDate,
          list: arr.reverse(),
        });
      })
      .catch();
  };
  const getColumns = ({ current, size }: { current: number; size: number }) => {
    return [
      {
        title: '序号',
        key: 'order',
        render(text: any, record: any, i: number) {
          return getSeq(i);
        },
      },
      {
        title: '读报日期',
        dataIndex: 'pub_date',
        render: (text: number, record: any) => {
          if (!text) return;
          const dateString = text.toString();
          const formattedDate = moment(dateString, 'YYYYMMDD').format('YYYY-MM-DD');
          return <a onClick={() => editPush(record)}>{formattedDate}</a>;
        },
      },
      {
        title: '封面图',
        dataIndex: 'cover_image',
        width: 150,
        // render: (text: any, record: any) => (<div style={{ height: 60, textAlign: 'center' }}>
        //   {/*<img src={record.cover_image} className='list-pic' style={{ height: '100%' }} onMouseEnter={() => showImagePreviewModal({ images: [record.cover_image] })}></img>*/}
        //   <img src={record.cover_image} className='list-pic' style={{ height: '100%',marginLeft:-80 }}></img>
        // </div>)
        align: 'center',
        render: (text: any, record: any) => (
          <div style={{ height: 60, textAlign: 'center' }}>
            <ImagePreviewColumn
              text={record.cover_image}
              imgs={[record.cover_image]}
            ></ImagePreviewColumn>
          </div>
        ),
      },
      {
        title: '内容数',
        dataIndex: 'content',
        render: (text: any, record: any) => {
          if (text && text !== 'null' && text.slice(0, 2) === '[{') {
            const contentArray = text ? JSON.parse(text) : [];
            return <span>{contentArray ? contentArray.length : 0}</span>;
          } else {
            return 0;
          }
        },
      },
      {
        title: '显示状态',
        dataIndex: 'status',
        render: (text: any, record: any) => <>{['下架', '上架'][text]}</>,
      },
      {
        title: '操作人',
        dataIndex: 'updated_by',
      },
      {
        title: (
          <Tooltip title="点击时间查看操作日志" placement="top">
            操作时间
          </Tooltip>
        ),
        dataIndex: 'updated_at',
        render: (text: number, record) => (
          <a
            onClick={() => {
              getOperateLog(record.pub_date);
            }}
          >
            {moment(text).format('YYYY-MM-DD HH:mm:ss')}
          </a>
        ),
        width: 96,
      },
      {
        title: '操作',
        dataIndex: 'date12',
        render(text: any, record: any) {
          return (
            <>
              <PermA
                disabled={record.status === 3}
                perm="finger_paper:update"
                onClick={() => editPush(record)}
              >
                编辑
              </PermA>
              <Divider type="vertical" />
              <PermA perm="finger_paper:delete">
                <Popconfirm
                  placement="top"
                  title="确定要删除吗？"
                  okText="确定"
                  cancelText="取消"
                  icon={<Icon type="exclamation-circle" theme="twoTone" twoToneColor="red" />}
                  onConfirm={() => {
                    handleDelete(record);
                  }}
                >
                  删除
                </Popconfirm>
              </PermA>

              <Divider type="vertical" />
              <PermA
                perm="finger_paper:update_status"
                onClick={() => {
                  changeStatus(record);
                }}
              >
                {['上架', '下架'][record.status]}
              </PermA>
              {/*<Divider type="vertical" />*/}
              {/*<Popconfirm*/}
              {/*  placement="top"*/}
              {/*  title="确定发推送给已订阅用户吗？"*/}
              {/*  okText="确定"*/}
              {/*  cancelText="取消"*/}
              {/*  onConfirm={() => { pushFingerPager(record) }}*/}
              {/*>*/}
              {/*  <A>通知</A>*/}
              {/*</Popconfirm>*/}
              <Divider type="vertical" />
              <A
                onClick={() => {
                  showPreView(record);
                }}
              >
                预览
              </A>
            </>
          );
        },
      },
    ];
  };

  const showPreView = (record: any) => {
    ShowModal(
      {
        width: '500px',
        title: '预览',
        closable: true,
        footer: null,
        // centered: true,
        bodyStyle: { padding: '20px' },
      },
      <FingerPapperPreview record={record}></FingerPapperPreview>
    );
  };

  const editPush = (record: any) => {
    api.detailFingerPager({ pub_date: record.pub_date }).then((res) => {
      const { detail } = res.data;
      if (detail.content_list) {
        detail.content_list.forEach((item, index) => {
          item.id = uuid();
        });
      }

      setFormContent({ ...detail });
      setDrawer({
        visible: true,
        key: Date.now(),
        type: 'edit',
      });
    });
  };
  const getData = (isCurrent: boolean = false) => {
    dispatch(
      getTableList('getFingerPaperList', 'list', {
        ...param,
        current: isCurrent ? current : param.current,
        size,
      })
    );
  };
  const handleDelete = (record: any) => {
    // endpoint/finger_paper/delete
    api.delFingerPager({ pub_date: record.pub_date }).then(() => {
      message.success('操作成功');
      getData();
    });
  };
  // 初始化
  useEffect(() => {
    const { selectKeys, openKeys } = props;
    dispatch(setConfig({ selectKeys, openKeys }));
    getData();
  }, [param]);

  const changeStatus = (record: any) => {
    api
      .updateStatusFingerPager({ pub_date: record.pub_date, status: record.status === 1 ? 0 : 1 })
      .then((res) => {
        message.success('操作成功');
        getData();
      });
  };
  // 时间修改
  const dateChange = (_: any, dateString: string[]) => {
    setParam({
      ...param,
      pub_date_start: dateString[0].replaceAll('-', ''),
      pub_date_end: dateString[1].replaceAll('-', ''),
      current: 1,
    });
  };
  // 显示状态修改
  const statusChange = (value: string) => {
    setParam({
      ...param,
      status: value === undefined ? '' : value,
      current: 1,
    });
  };
  const getFilter = () => {
    const filter = param;
    const result: CommonObject = { current, size, ...param };
    // Object.keys(filter).forEach((k: string) => {
    //   if (k === 'pub_date_start' || k === 'pub_date_end') {
    //     if (filter[k]) {
    //       result[k] = filter[k].format('YYYY-MM-DD');
    //     }
    //   } else if (filter[k]) {
    //     result[k] = filter[k];
    //   }
    // });
    if (result.searchType === 2) {
      result.title = result.keyword;
      delete result.keyword;
    }
    console.log('result:::::::::::', result);
    return result;
  };
  const formRef = useRef<typeof FingerPaperForm>(null);
  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton
            perm="finger_paper:create"
            onClick={() => {
              setFormContent(null);
              setDrawer({ ...drawer, visible: true, type: 'create' });
            }}
          >
            <Icon type="plus-circle" />
            添加读报
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }} type="flex" align="middle">
          <div>读报日期：</div>
          <DatePicker.RangePicker onChange={dateChange} format="YYYY-MM-DD" />
          <div style={{ marginLeft: 16 }}>显示状态：</div>
          <Select
            style={{ width: 160, marginLeft: 8 }}
            onChange={statusChange}
            placeholder="请选择"
            allowClear
          >
            <Select.Option value="1">上架</Select.Option>
            <Select.Option value="0">下架</Select.Option>
          </Select>
        </Row>
        <Table
          func="getFingerPaperList"
          index="list"
          pagination={true}
          rowKey="pub_date"
          filter={getFilter()}
          columns={getColumns(param)}
        />
      </div>
      <ImagePreview
        src={imageUrl}
        closeByMask={true}
        onClose={() => {
          setImageUrl('');
        }}
      />
      <Drawer
        visible={drawer.visible}
        skey={drawer.key}
        title={drawer.type === 'create' ? '新建读报' : '编辑读报'}
        onClose={() => {
          setDrawer({ ...drawer, visible: false });
        }}
        // onOk={() => {
        //   formRef.current?.submit();
        // }}
      >
        <FingerPaperForm
          ref={formRef}
          drawer={drawer}
          onClose={() => {
            setDrawer({ ...drawer, visible: false });
          }}
          formContent={formContent}
          getData={() => {
            getData(drawer.type != 'create');
          }}
        />
      </Drawer>
      <Modal
        visible={operateLog.visible}
        title="操作日志"
        key={operateLog.articleTitle}
        onCancel={() => {
          setOperateLog({ visible: false });
        }}
        footer={null}
        destroyOnClose={false}
      >
        <p>指尖读报:&nbsp;{operateLog.articleTitle}</p>
        <br />
        <div>
          <Timeline>
            {operateLog.list?.map((v: any, i: number) => [
              <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                &nbsp;
              </Timeline.Item>,
              v.list.map((action: IOperationActionData, index: number) => (
                <Timeline.Item
                  className="timeline-dot"
                  data-show={action.time.slice(11, 26)}
                  key={`time${i}-action${index}`}
                >
                  {action.operate_user}&nbsp;{action.action}
                </Timeline.Item>
              )),
            ])}
          </Timeline>
        </div>
      </Modal>
    </>
  );
};

export default FingerPaper;
