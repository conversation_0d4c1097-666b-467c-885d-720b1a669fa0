import { opApi, opApi as api, userApi } from '@app/api';
import { ITableProps, IBaseProps, CommonObject } from '@app/types';
import { A, Table, BaseComponent, PreviewMCN, OrderColumn } from '@components/common';
import { connectTable as connect } from '@utils/connect';
import { setConfig } from '@app/action/config';
import {
  Button,
  Col,
  Icon,
  Row,
  Input,
  Select,
  DatePicker,
  Modal,
  InputNumber,
  message,
  Table as ATable,
  Tag,
  Tooltip,
} from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import { RangePickerValue } from 'antd/es/date-picker/interface';
import { requirePerm, resolveNewsType, showIDDetailModal, UserDetail } from '@utils/utils';
import { TopicArticleRecord, TopicArticleAllData } from './operates';
import showImagePreviewModal from '@app/components/common/imagePreviewModal';
import ImagePreviewColumn from '@app/components/common/imagePreviewColumn';

type State = {
  filter: {
    begin: string;
    end: string;
    search_type: 1 | 2 | 3 | 4 | 5;
    keyword: string;
    doc_type: number;
    down: string;
    retrieved: string;
  };
  cType: 1 | 2 | 3 | 4 | 5;
  cKeyword: string;
  id: string;
  name: string;
  type: string;
  preview: {
    visible: boolean;
    skey: number;
    data: CommonObject;
  };
  retrieveVisible: boolean;
  retrieveData: any[];
  user: {
    visible: boolean,
    detail: any,
    key: any,
  }
};

type Props = IBaseProps<
  ITableProps<TopicArticleRecord, TopicArticleAllData>,
  { id: string; name: string; type: string }
>;

const Retrieved = ['', '已取稿', '已取签'];

class TopicArticleList extends BaseComponent<
  ITableProps<TopicArticleRecord, TopicArticleAllData>,
  State,
  { id: string; name: string }
> {
  constructor(props: Props) {
    super(props);
    this.state = {
      filter: {
        begin: '',
        end: '',
        search_type: 1,
        keyword: '',
        doc_type: 0,
        down: '',
        retrieved: '',
      },
      cType: 1,
      cKeyword: '',
      id: props.match.params.id,
      name: decodeURIComponent(props.match.params.name),
      type: props.match.params.type,
      preview: {
        visible: false,
        skey: Date.now() + 1,
        data: {},
      },
      retrieveVisible: false,
      retrieveData: [],
      user: {
        key: null,
        visible: false,
        detail: {}
      },
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
  }

  getData = (overlap: CommonObject = {}, filters = this.getFilters()) => {
    this.dispatchTable('getTopicArticleList', 'list', { ...filters, ...overlap });
  };

  getFilters = () => {
    const { current, size } = this.props.tableList;
    const { filter, id } = this.state;
    const filters: CommonObject = { current, size, ...filter, topic_id: id };
    Object.keys(filter).map((k: string) => {
      if (!filters[k]) {
        delete filters[k];
      }
    });
    return filters;
  };

  preview = (record: any) => {
    this.setState({
      preview: {
        visible: true,
        skey: Date.now(),
        data: record,
      },
    });
  };

  getRetrieveColumns() {
    return [
      { title: '潮新闻id', dataIndex: 'id', width: 80 },
      { title: '媒立方id', dataIndex: 'metadata_id', width: 80 },
      { title: '所属频道', dataIndex: 'channel_name', width: 80 },
      { title: '稿件标题', dataIndex: 'list_title' },
      {
        title: '签发状态',
        dataIndex: 'status',
        width: 80,
        render(text: number) {
          return <div>{text === 4 ? '已签发' : '未签发'}</div>;
        },
      },
    ];
  }

  previewRetrieve = async (record: any) => {
    const data = await opApi.getTopicArticleRetrievedList({ id: record.id });
    console.log(data);
    this.setState({
      ...this.state,
      retrieveData: data.data.article_list,
      retrieveVisible: true,
    });
    console.log(record);
  };

  // 显示用户详情
  showUserDetailModal(record: any, visible: boolean) {
    this.props.dispatch(setConfig({ loading: true }));
    userApi
      .getUserDetail({ accountId: record.account_id })
      .then((r: any) => {
        this.setState({
          user: {
            key: Date.now(),
            visible,
            detail: r.data.account,
          }
        });
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));

  }

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const { begin, end, keyword } = this.state.filter;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 50,
      },
      {
        title: '潮新闻ID',
        key: 'scid',
        dataIndex: 'id',
        width: 90,
        render: (text: any, record: any) => (<a onClick={() => showIDDetailModal(record)}>{text}</a>)
      },
      // {
      //   title: '创作者平台ID',
      //   key: 'metadata_id',
      //   dataIndex: 'metadata_id',
      //   width: 115,
      // },
      {
        title: '标题',
        key: 'list_title',
        dataIndex: 'list_title',
        render: (text: any, record: any) => (
          <>
            {Boolean(record.down) && <Tag color="#D6D44a">沉底</Tag>}
            <a onClick={this.preview.bind(this, record)} className="list-title">
              {text?.substring(0, 30)}{text?.length > 30 ? '...' : ''}
            </a>
          </>
        ),
      },
      {
        title: '封面图/配图',
        key: 'pic_array',
        dataIndex: 'pic_array',
        width: 150,
        align: 'center',
        render: (text: any, record: any) => (<div style={{ height: 60, textAlign: 'center' }}>
          <ImagePreviewColumn text={text} imgs={record.pic_array}></ImagePreviewColumn>
          {/* <img src={text} className='list-pic' onMouseEnter={() => showImagePreviewModal({ images: record.pic_array })}></img> */}
        </div>)
      },
      {
        title: '内容类型',
        dataIndex: 'doc_type',
        render: (text: number) => resolveNewsType(text, 1),
        width: 80,
      },
      {
        title: '内容等级',
        key: 'content_level',
        dataIndex: 'content_level',
        render: (content_level: number) => ['通过', '全局沉底', '推荐', '圈外沉底'][content_level],
        width: 75,
      },
      {
        title: (
          <span>
            取签状态
            <Tooltip
              placement="top"
              title={
                <span>
                  已取稿——内容被取稿但未签发；
                  <br />
                  已取签——内容被取稿且被签发
                </span>
              }
            >
              <Icon style={{ paddingLeft: '5px' }} type="question-circle" />
            </Tooltip>
          </span>
        ),
        key: 'retrieved',
        dataIndex: 'retrieved',
        width: 100,
        render: (text: number, record: any) => (
          <A onClick={this.previewRetrieve.bind(this, record)}>{Retrieved[text]}</A>
        ),
      },
      {
        title: '关联圈子',
        key: 'circle_name',
        dataIndex: 'circle_name',
        width: 90,
        render: (circle_name: string, record: any) => circle_name ? `${circle_name}${record.circle_enable ? '' : '(已下线)'}` : ''
      },
      {
        title: '作者',
        key: 'account_nick_name',
        dataIndex: 'account_nick_name',
        width: 110,
        render: (text: any, record: any) => (<a onClick={() => this.showUserDetailModal(record, true)}>{text}</a>)
      },
      {
        title: '发布时间',
        key: 'published_timestamp',
        dataIndex: 'published_timestamp',
        render: (text: any, record: any) => (
          <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>
        ),
        width: 95,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) =>
          this.requirePerm('topic_label:remove_ugc_article')(
            <A onClick={this.removeArticle.bind(this, record)}>移除</A>
          ),
        width: 60,
      },
    ];
  };

  removeArticle = (record: any) => {
    Modal.confirm({
      title: '确定移除？',
      onOk: (closeFunc: Function) => {
        this.setLoading(true);
        api
          .removeArticleFromTopic({
            id: record.id,
            topic_label_id: this.state.id,
          })
          .then(() => {
            this.setLoading(false);
            this.getData();
            closeFunc();
          })
          .catch(() => this.setLoading(false));
      },
    });
  };

  handleDocTypeChange = (value: number) => {
    this.setState(
      {
        filter: { ...this.state.filter, doc_type: value },
      },
      () => this.getData()
    );
  };

  handleRetrievedChange = (value: string) => {
    this.setState(
      {
        filter: { ...this.state.filter, retrieved: value },
      },
      () => this.getData()
    );
  };

  handleDownChange = (value: string) => {
    this.setState(
      {
        filter: { ...this.state.filter, down: value },
      },
      () => this.getData()
    );
  };

  handleRangePickerChange = (dates: RangePickerValue) => {
    if (dates.length === 0) {
      this.setState(
        {
          filter: { ...this.state.filter, begin: '', end: '' },
        },
        () => this.getData({ current: 1 })
      );
    } else {
      this.setState(
        {
          filter: {
            ...this.state.filter,
            begin: dates[0] ? dates[0].format('YYYY-MM-DD') : '',
            end: dates[1] ? dates[1].format('YYYY-MM-DD') : '',
          },
        },
        () => this.getData({ current: 1 })
      );
    }
  };

  handleCTypeChange = (value: 1 | 2 | 3 | 4 | 5) => {
    this.setState({
      cType: value,
    });
  };

  handleKeywordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    this.setState({
      cKeyword: e.target.value,
    });
  };

  doSearch = () => {
    this.setState(
      {
        filter: {
          ...this.state.filter,
          search_type: this.state.cType,
          keyword: this.state.cKeyword,
        },
      },
      () => this.getData({ current: 1 })
    );
  };

  backToTopic = () => {
    this.props.history.goBack();
  };

  closePreview = () => {
    this.setState({
      preview: { ...this.state.preview, visible: false },
    });
  };

  toTopping = () => {
    this.props.history.push(`/view/TopManuscript/${this.state.id}/${this.state.type}`);
  };

  render() {
    const { filter, cType, cKeyword, preview, name, user } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            <Button onClick={this.backToTopic} style={{ marginRight: 8 }}>
              <Icon type="left-circle-o" />
              话题管理
            </Button>
            {requirePerm(
              this,
              `topic_label_article_top:list`
            )(<Button onClick={this.toTopping}>置顶稿件管理</Button>)}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {this.getCrumb(['运营管理', '话题管理', name])}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <Col span={12}>
              <DatePicker.RangePicker
                format="YYYY-MM-DD"
                onChange={this.handleRangePickerChange}
                value={
                  filter.begin
                    ? [moment(this.state.filter.begin), moment(this.state.filter.end)]
                    : []
                }
              />
              <Select
                value={filter.doc_type}
                onChange={this.handleDocTypeChange}
                style={{ width: 120, marginLeft: 8 }}
              >
                <Select.Option value={0}>全部类型</Select.Option>
                <Select.Option value={12}>短图文</Select.Option>
                <Select.Option value={10}>视频</Select.Option>
                <Select.Option value={13}>长文章</Select.Option>
              </Select>
              <Select
                value={filter.retrieved}
                onChange={this.handleRetrievedChange}
                style={{ width: 120, marginLeft: 8 }}
              >
                <Select.Option value="">取签状态</Select.Option>
                <Select.Option value="0">未取稿</Select.Option>
                <Select.Option value="1">已取稿</Select.Option>
                <Select.Option value="2">已取签</Select.Option>
              </Select>
              <Select
                value={filter.down}
                onChange={this.handleDownChange}
                style={{ width: 120, marginLeft: 8 }}
              >
                <Select.Option value="">沉底状态</Select.Option>
                <Select.Option value="0">未沉底</Select.Option>
                <Select.Option value="1">沉底</Select.Option>
              </Select>
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <Select
                value={cType}
                onChange={this.handleCTypeChange}
                style={{ marginRight: 8, width: 140 }}
              >
                <Select.Option value={1}>搜索潮新闻ID</Select.Option>
                <Select.Option value={2}>搜索创作者平台ID</Select.Option>
                <Select.Option value={7}>搜索标题</Select.Option>
                {/* <Select.Option value={6}>搜索正文</Select.Option> */}
                <Select.Option value={5}>搜索作者</Select.Option>
              </Select>
              <Input
                value={cKeyword}
                onChange={this.handleKeywordChange}
                style={{ width: 180, marginRight: 8 }}
                onKeyPress={this.handleKey}
                placeholder="请输入搜索内容"
              />
              <Button onClick={this.doSearch}>
                <Icon type="search" />
                搜索
              </Button>
            </Col>
          </Row>
          <Table
            func="getTopicArticleList"
            index="list"
            columns={this.getColumns()}
            pagination={true}
            rowKey="id"
            filter={this.getFilters()}
          />
          <PreviewMCN {...preview} onClose={this.closePreview} />
        </div>
        <Modal
          width="800px"
          title="取签详情"
          visible={this.state.retrieveVisible}
          footer={null}
          onCancel={() => {
            this.setState({
              ...this.state,
              retrieveVisible: false,
            });
          }}
        >
          <ATable
            pagination={false}
            columns={this.getRetrieveColumns()}
            dataSource={this.state.retrieveData}
          />
        </Modal>
        <Modal
          visible={user.visible}
          key={user.key}
          title="用户详情"
          width={800}
          onCancel={() => this.setState({ user: { ...user, visible: false } })}
          onOk={() => this.setState({ user: { ...user, visible: false } })}
        >
          {/*{user.visible && getUserDetail(user.detail)}*/}
          {user.visible && <UserDetail detail={user.detail} />}
        </Modal>
      </>
    );
  }
}

export default withRouter(connect<TopicArticleRecord, TopicArticleAllData>()(TopicArticleList));
