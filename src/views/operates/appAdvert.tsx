/* eslint-disable no-param-reassign */
import { setConfig } from '@action/config';
import { clearTableList, getTableList, setTableList } from '@app/action/tableList';
import { opApi as api, opApi } from '@app/api';
import { CommonObject } from '@app/types';
import AppAdvertForm from '@components/business/AppAdvertForm';
import { A, Drawer, Table } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, searchToObject } from '@utils/utils';
import {
  Button,
  Col,
  DatePicker,
  Form,
  Icon,
  Input,
  message,
  Modal,
  Row,
  Select,
  Divider,
  Tooltip,
  Radio,
} from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import BottomAdFormDrawer from './bottomAdFormDrawer';

@(withRouter as any)
@connect
class AppAdvert extends React.Component<any, any> {
  formRef: any;

  constructor(props: any) {
    super(props);
    this.state = {
      type: 0,
      filter: {
        start_date: null,
        end_date: null,
        title: ' ',
      },
      search: '',
      drawer: {
        visible: false,
        key: Date.now(),
        title: '添加广告',
      },
      bottomAdDrawer: {
        visible: false,
        skey: Date.now() + 1,
        formContent: null,
      },
      detail: {
        visible: false,
        key: Date.now(),
      },
      formContent: {},
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    this.props.dispatch(
      setConfig({
        selectKeys: this.props.selectKeys,
        openKeys: this.props.openKeys,
      })
    );
  }

  getData = (overlap: CommonObject = {}, filter = this.getFilter()) => {
    if (this.state.type == 0) {
      this.props.dispatch(
        getTableList('getAppadvertpageList', 'app_start_page_list', { ...filter, ...overlap })
      );
    } else {
      this.props.dispatch(
        getTableList('getBottomRecommendList', 'list', { ...overlap })
      );
    }
  };

  getFilter = () => {
    const { current, size } = this.props.tableList;
    const filter: CommonObject = { current, size, title: this.state.search };
    return filter;
  };

  getColumns = () => {
    if (this.state.type == 1) {
      return [
        {
          title: '名称',
          dataIndex: 'title',
          width: 120,
        }, {
          title: '图片',
          dataIndex: 'pic_url',
          key: 'pic_url',
          render: (text: any) => <img src={text} className="list-pic" />,
          width: 160,
        }, {
          title: '链接',
          dataIndex: 'url',
        }, {
          title: '状态',
          dataIndex: 'status',
          render: (text: any) => {
            return text == 0 ? '未展示' : '展示中'
          },
          width: 120,
        }, {
          title: '修改人',
          dataIndex: 'updated_by',
          width: 160,
        }, {
          title: '修改时间',
          dataIndex: 'updated_at',
          render: (text: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
          width: 160,
        }, {
          title: '操作',
          key: 'op',
          render: (text: any, record: any) => (
            <span>
              {requirePerm(
                this,
                'type_recommend:41:update'
              )(
                <A onClick={() => this.createAd(record)}>
                  编辑
                </A>
              )}
              <Divider type="vertical" />
              {requirePerm(
                this,
                'type_recommend:41:delete'
              )(
                <A onClick={() => this.deleteBottomAd(record)}>
                  删除
                </A>
              )}
            </span>
          ),
          width: 180,
        }
      ]
    }
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    const taskColumn = searchToObject().showtask
      ? [
        {
          title: 'taskId',
          dataIndex: 'gt_task_id',
        },
      ]
      : [];
    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '广告主题',
        dataIndex: 'title',
        key: 'title',
        width: 160,
      },
      {
        title: '图片',
        dataIndex: 'pic_url',
        key: 'pic_url',
        render: (text: any) => <img src={text} className="list-pic" />,
        width: 160,
      },
      {
        title: '广告地址',
        dataIndex: 'url',
        key: 'url',
      },
      {
        title: '显示位置',
        dataIndex: 'show_position',
        width: 120,
        render: (text: any) => text == 0 ? '中间' : '底部'
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (text: number) => <span>{text === 3 ? '展示中' : '未展示'}</span>,
        width: 80,
      },
      {
        title: '创建者',
        dataIndex: 'creator',
        key: 'creator',
        width: 100,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <span>
            {requirePerm(
              this,
              'app_advert_page:update'
            )(
              <A onClick={() => this.editPush(record)}>
                编辑
              </A>
            )}
            <Divider type="vertical" />
            {requirePerm(
              this,
              'app_advert_page:update_displayed'
            )(<A onClick={() => this.rePush(record)}>{record.status === 3 ? '下架' : '上架'}</A>)}
            <Divider type="vertical" />
            {requirePerm(
              this,
              'app_advert_page:delete'
            )(
              <A disabled={record.status === 3} onClick={() => this.deletePush(record)}>
                删除
              </A>
            )}
          </span>
        ),
        width: 180,
      },
      ...taskColumn,
    ];
  };

  editPush = (record: any) => {
    this.setState({
      drawer: {
        visible: true,
        key: Date.now(),
        title: '修改广告',
      },
      formContent: record,
    });
  };

  deleteBottomAd = (record: any) => {
    Modal.confirm({
      title: '确定要删除吗？',
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        api
          .bottomRecommendDelete({ id: record.id })
          .then(() => {
            message.success('操作成功');
            this.props.dispatch(setConfig({ loading: false }));
            this.getData({ size: 10 });
          })
          .catch(() => this.props.dispatch(setConfig({ loading: false })));
      },
    });
  }

  showDetail = (record: any) => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .getPushNotifyDetail({ id: record.id })
      .then((r: any) => {
        this.setState({
          detail: { visible: true, key: Date.now(), ...r.data.push_notify_list },
        });
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  rePush = (record: any) => {
    Modal.confirm({
      title: <p>是否确定要{record.status === 3 ? '下架' : '上架'}当前广告？</p>,
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        api
          .updatedisplayedAppadvertpage({ id: record.id, groundType: record.status !== 3 ? 1 : 0 })
          .then(() => {
            message.success('操作成功');
            this.props.dispatch(setConfig({ loading: false }));
            this.getData({ size: 10 });
          })
          .catch(() => this.props.dispatch(setConfig({ loading: false })));
      },
    });
  };

  deletePush = (record: any) => {
    Modal.confirm({
      title: <p>是否确定要删除当前广告？</p>,
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        api
          .deleteAppadvertpage({ id: record.id })
          .then(() => {
            message.success('操作成功');
            this.props.dispatch(setConfig({ loading: false }));
            this.getData();
          })
          .catch(() => this.props.dispatch(setConfig({ loading: false })));
      },
    });
  };

  filterChange = (key: any, value: any) => {
    this.setState({ filter: { ...this.state.filter, [key]: value } }, () => {
      this.getData({ current: 1, title: value });
    });
  };

  handleKey = (e: any) => {
    if (e.which === 13) {
      this.filterChange('title', this.state.search);
    }
  };

  // timeChange = (dates: any) => {
  //   if (dates.length === 0) {
  //     this.setState(
  //       {
  //         filter: { ...this.state.filter, start_date: null, end_date: null },
  //       },
  //       () => this.getData({ current: 1 })
  //     );
  //   } else {
  //     this.setState(
  //       {
  //         filter: { ...this.state.filter, start_date: dates[0], end_date: dates[1] },
  //       },
  //       () => this.getData({ current: 1 })
  //     );
  //   }
  // };

  closeDrawer = () => {
    this.setState({
      drawer: { ...this.state.drawer, visible: false },
    });
  };

  onSubmitEnd = () => {
    this.setState({
      drawer: { ...this.state.drawer, visible: false },
    });
    this.getData({ size: 10 });
  };

  createAd = (record: any) => {
    if (this.state.type == 1) {
      this.setState({
        bottomAdDrawer: {
          visible: true,
          skey: Date.now() + 1,
          formContent: record,
        },
      })
      return
    }
    this.setState({
      drawer: {
        visible: true,
        key: Date.now(),
        title: '添加广告',
      },
      formContent: {},
    });
  };

  render() {
    const layout = {
      labelCol: { span: 6 },
      wrapperCol: { span: 16 },
    };
    const { filter, drawer, detail } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            <Radio.Group defaultValue={this.state.type} buttonStyle="solid" onChange={(e) => {
              this.setState({
                type: e.target.value
              }, () => {
                this.props.dispatch(
                  clearTableList()
                );

                this.getData({ current: 1, size: 10 })
              })
            }}>
              <Radio.Button value={0}>首页弹窗</Radio.Button>
              <Radio.Button value={1}>稿件底部</Radio.Button>
            </Radio.Group>
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <Col span={12}>
              {/* <DatePicker.RangePicker
                value={[filter.start_date, filter.end_date]}
                format="YYYY-MM-DD"
                onChange={this.timeChange}
              /> */}
              {requirePerm(
                this,
                `${this.state.type == 0 ? 'app_advert_page:create' : 'type_recommend:41:create'}`
              )(
                <Button onClick={() => this.createAd(null)} disabled={this.state.type == 0 ? false : this.props.tableList.total == 1}>
                  <Icon type="plus-circle" />
                  添加广告
                </Button>
              )}
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              {this.state.type == 0 && <>
                <Input
                  placeholder="请输入广告标题搜索"
                  style={{ width: 150 }}
                  value={this.state.search}
                  onChange={(e: any) => this.setState({ search: e.target.value })}
                  onKeyPress={this.handleKey}
                />
                <Button
                  style={{ marginLeft: 8, verticalAlign: 'top' }}
                  onClick={() => this.handleKey({ which: 13 })}
                >
                  <Icon type="search" />
                  搜索
                </Button>
              </>}
            </Col>
          </Row>
          {this.state.type == 0 && <Table
            func='getAppadvertpageList'
            index='app_start_page_list'
            columns={this.getColumns()}
            filter={this.getFilter()}
            rowKey="id"
            pagination={true}
          />}
          {this.state.type == 1 && <Table
            func='getBottomRecommendList'
            index='list'
            columns={this.getColumns()}
            filter={this.getFilter()}
            rowKey="id"
            pagination={false}
          />}
          {/* <Modal
            visible={detail.visible}
            key={detail.key}
            title="推送详情"
            onOk={() => this.setState({ detail: { ...detail, visible: false } })}
            onCancel={() => this.setState({ detail: { ...detail, visible: false } })}
          >
            <Form {...layout}>
              <Form.Item label="标题">{detail.title}</Form.Item>
              <Form.Item label="推送内容">{detail.content}</Form.Item>
              <Form.Item label="推送范围">{detail.area_name ? detail.area_name : '全局'}</Form.Item>
              <Form.Item label="新闻列表">
                {Boolean(detail.article_news) &&
                  detail.article_news.map((v: any) => (
                    <p key={v.article_id}>
                      {v.article_id}&nbsp;&nbsp;-&nbsp;&nbsp;{v.title}
                    </p>
                  ))}
              </Form.Item>
              {Boolean(detail.to_users) && (
                <Form.Item label="发送用户">
                  <span style={{ wordBreak: 'break-all' }}>
                    {detail.to_users.indexOf('http') !== 0 ? (
                      detail.to_users
                    ) : (
                      <a href={detail.to_users} rel="noreferrer">
                        {detail.to_users}
                      </a>
                    )}
                  </span>
                </Form.Item>
              )}
            </Form>
          </Modal> */}
          <Drawer
            visible={drawer.visible}
            skey={drawer.key}
            title={drawer.title}
            onClose={this.closeDrawer}
            onOk={() => this.formRef.doSubmit()}
          >
            <AppAdvertForm
              onEnd={this.onSubmitEnd}
              formContent={this.state.formContent}
              // eslint-disable-next-line no-return-assign
              wrappedComponentRef={(instance: any) => (this.formRef = instance)}
            />
          </Drawer>

          <BottomAdFormDrawer
            {...this.state.bottomAdDrawer}
            onEnd={() => { this.setState({ bottomAdDrawer: { visible: false } }, () => { this.getData() }) }}
            onClose={() => { this.setState({ bottomAdDrawer: { visible: false } }) }}
          >
          </BottomAdFormDrawer>
        </div>
      </>
    );
  }
}

export default AppAdvert;
