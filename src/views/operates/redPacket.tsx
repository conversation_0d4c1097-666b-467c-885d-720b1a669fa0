/* eslint-disable react/no-string-refs */
import { setConfig } from '@action/config';
import { getTableList } from '@app/action/tableList';
import { opApi as api } from '@app/api';
import { CommonObject } from '@app/types';
import Form from '@components/business/redPacketInfoForm';
import { A, Drawer, Table, OrderColumn } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setLoading } from '@utils/utils';
import {
  Button,
  Col,
  DatePicker,
  Icon,
  Input,
  message,
  Modal,
  Row,
  Select,
  Divider,
  Tooltip,
  Switch,
  Form as AForm,
  Menu,
  Dropdown,
} from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';

@(withRouter as any)
@connect
class RedPacket extends React.Component<any, any> {
  entryRef: any;

  formRef: any;

  constructor(props: any) {
    super(props);
    this.state = {
      filter: {
        current: 1,
        size: 10,
        name: '',
        status: '',
      },
      entryForm: {
        visible: false,
        key: Date.now(),
      },
      form: {
        visible: false,
        key: Date.now() + 1,
      },
      switch: {
        visible: false,
        key: Date.now() + 2,
        switch: true,
        min_version: '',
        min_version_ios: '',
        cash_reward_switch: false,
        cash_account_switch: false,
      },
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    this.props.dispatch(
      setConfig({
        selectKeys: this.props.selectKeys,
        openKeys: this.props.openKeys,
      })
    );
  }

  componentDidUpdate(prevProps: any) {
    if (
      prevProps.tableList.timestamp !== this.props.tableList.timestamp &&
      Boolean(this.props.tableList.allData.red_packet_info_list)
    ) {
      this.setState({
        onCount: this.props.tableList.allData.online_count,
      });
    }
  }

  getData = (filter: CommonObject = this.getFilter()) => {
    const { current, size } = this.props.tableList;
    this.props.dispatch(getTableList('getRedPacketList', 'redPacketActivityList', filter));
  };
  // 列表参数
  getColumns = () => {
    const { current, size, total } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '活动ID',
        key: 'id',
        dataIndex: 'id',
      },
      {
        title: '活动名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '关联红包ID',
        dataIndex: 'qyRedPacketId',
        key: 'qyRedPacketId',
      },
      {
        title: '关联红包名称',
        dataIndex: 'qyRedPacketName',
        key: 'qyRedPacketName',
      },
      {
        title: '关联红包有效时间',
        key: 'times',
        render: (_: any, record: any) =>
          record.qyRedPacketPeriodType === 1 ? (
            '永久'
          ) : (
            <span>
              {moment(record.qyRedPacketStartTime).format('YYYY-MM-DD HH:mm:ss')}
              <br />
              {moment(record.qyRedPacketEndTime).format('YYYY-MM-DD HH:mm:ss')}
            </span>
          ),
        width: 170,
      },
      {
        title: '关联红包领取限制',
        key: 'qyRedPacketReceiveNum',
        render: (record: any) => (
          <span>{record.qyRedPacketReceiveDay + '天' + record.qyRedPacketReceiveNum + '次'}</span>
        ),
      },
      {
        title: '活动状态',
        dataIndex: 'status',
        key: 'status',
        render: (text: number) => <span>{['已下架', '已上架'][text]}</span>,
        width: 80,
      },
      {
        title: '创建人',
        dataIndex: 'creator',
        key: 'creator',
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        key: 'createTime',
      },

      {
        title: '操作',
        key: 'op',
        width: 140,
        render: (text: any, record: any) => {
          return (
            <span>
              {requirePerm(
                this,
                'redPacketActivity:updateStatus'
              )(
                <A onClick={() => this.changeStatus(record)}>
                  {record.status === 1 ? '下架' : '上架'}
                </A>
              )}
              <Divider type="vertical" />
              {requirePerm(
                this,
                'redPacketActivity:save'
              )(<A onClick={() => this.editRecord(record)}>编辑</A>)}
            </span>
          );
        },
      },
    ];
  };
  // 编辑红包
  editRecord = (record: any) => {
    if (record.id) {
      setLoading(this, true);

      api
        .getRedPacketDetail({
          id: record.id,
        })
        .then((res: any) => {
          if (res.data.redPacketActivity.rule === 'undefined') {
            res.data.redPacketActivity.rule = '';
          }
          const {
            name,
            description,
            url,
            img,
            rule,
            listShowStatus,
            ruleShowStatus,
            qyRedPacketId,
          } = res.data.redPacketActivity;
          api
            .getQYRedPacketDetail({
              qyRedPacketId: qyRedPacketId,
            })
            .then((msg: any) => {
              this.setState({
                form: {
                  name,
                  description,
                  url,
                  img,
                  rule,
                  listShowStatus,
                  ruleShowStatus,
                  qyRedPacketId,
                  visible: true,
                  key: Date.now(),
                  id: record.id,
                  redPackeShow: [msg.data.oneQyRedPacket],
                },
              });
              setLoading(this, false);
            });
        })
        .catch(() => {
          setLoading(this, false);
        });
    } else {
      this.setState({
        form: {
          visible: true,
          listShowStatus: 1,
          ruleShowStatus: 1,
          redPacketList: [],
          qyRedPacketId: '',
          url: '',
          img: '',
          rule: '',
          description: '',
          name: '',
          key: Date.now(),
        },
      });
    }
  };
  // 上下线状态
  changeStatus = (record: any) => {
    setLoading(this, true);
    api
      .updateRedPacketStatus({
        id: record.id,
        status: record.status === 0 ? 1 : 0,
      })
      .then(() => {
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  closeDrawer = () => {
    this.setState({
      form: { ...this.state.form, visible: false },
    });
  };

  onSubmitEnd = () => {
    this.closeDrawer();
    this.getData();
  };

  setRef = (ref: 'entryRef' | 'formRef', instance: any) => {
    this[ref] = instance;
  };

  submitForm = (ref: 'entryRef' | 'formRef') => {
    this[ref].doSubmit();
  };

  getFilter = () => {
    const { current, size } = this.props.tableList;
    const { filter } = this.state;
    const result: CommonObject = { current, size, name, status };
    Object.keys(filter).forEach((k: string) => {
      if (filter[k]) {
        result[k] = filter[k];
      }
    });
    return result;
  };
  // 功能开关
  showRedpacketSwitch = () => {
    setLoading(this, true);
    api
      .getRedPacketSwitch()
      .then((res: any) => {
        this.setState({
          switch: {
            ...res.data,
            visible: true,
            key: Date.now(),
          },
        });
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };
  // 选项切换
  filterChange = (key: any, value: any) => {
    this.setState({ filter: { ...this.state.filter, [key]: value } }, () => {
      this.getData();
    });
  };
  // 输入
  inputChange = (value: any) => {
    this.setState({ filter: { ...this.state.filter, name: value } });
  };
  handleKey = (e: any) => {
    if (e.which === 13) {
      this.getData();
    }
  };
  submitSwitch = () => {
    const minVersionIos = (this.refs.iosi as any).state.value;
    const minVersion = (this.refs.androidi as any).state.value;
    const maxVersionIos = (this.refs.iosmax as any).state.value;
    const maxVersion = (this.refs.androidmax as any).state.value;
    const { checked } = (this.refs.switchi as any).rcSwitch.state;
    const cash_reward_switch = (this.refs.switchi_reward as any).rcSwitch.state.checked;
    const cash_account_switch = (this.refs.switchi_account as any).rcSwitch.state.checked;
    const regex = /^\d+\.\d+\.\d+$/;
    if (
      !regex.test(minVersion) ||
      !regex.test(minVersionIos) ||
      !regex.test(maxVersion) ||
      !regex.test(maxVersionIos)
    ) {
      message.error('请正确填写最低版本号');
      return;
    }
    setLoading(this, true);
    api
      .updateRedPacketSwitch({
        min_version: minVersion,
        min_version_ios: minVersionIos,
        max_version: maxVersion,
        max_version_ios: maxVersionIos,
        status: checked,
        cash_reward_switch: cash_reward_switch,
        cash_account_switch: cash_account_switch,
      })
      .then(() => {
        message.success('操作成功');
        this.setState({
          switch: {
            ...this.state.switch,
            visible: false,
          },
        });
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  render() {
    const layout = {
      labelCol: { span: 9 },
      wrapperCol: { span: 10 },
    };
    const { entryForm, form, filter } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={18}>
            {requirePerm(
              this,
              'web_feature:red_packet_switch'
            )(
              <Button onClick={this.showRedpacketSwitch} style={{ marginRight: 8 }}>
                功能开关
              </Button>
            )}
            {requirePerm(
              this,
              'redPacketActivity:save'
            )(
              <Button onClick={this.editRecord.bind(this, {})} style={{ marginRight: 8 }}>
                <Icon type="plus-circle" />
                新建红包
              </Button>
            )}
          </Col>
          <Col span={6} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <Col span={12}>
              <Select
                value={filter.status}
                onChange={(value: any) => this.filterChange('status', value)}
                style={{ width: 160, marginLeft: 8 }}
              >
                <Select.Option value="">全部</Select.Option>
                <Select.Option value="1">上架</Select.Option>
                <Select.Option value="0">下架</Select.Option>
              </Select>
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <Input
                placeholder="输入活动名称搜索"
                style={{ width: 150 }}
                value={filter.name}
                onChange={(e: any) => this.inputChange(e.target.value)}
                onKeyPress={this.handleKey}
              />
              <Button
                style={{ marginLeft: 8, verticalAlign: 'top' }}
                onClick={() => this.handleKey({ which: 13 })}
              >
                <Icon type="search" />
                搜索
              </Button>
            </Col>
          </Row>
          <Table
            func="getRedPacketList"
            index="redPacketActivityList"
            columns={this.getColumns()}
            filter={this.getFilter()}
            rowKey="id"
            pagination={true}
          />

          <Drawer
            visible={form.visible}
            skey={form.key}
            onClose={this.closeDrawer}
            onOk={this.submitForm.bind(this, 'formRef')}
            title={`${form.id ? '编辑' : '新建'}红包`}
          >
            <Form
              formContent={form}
              wrappedComponentRef={this.setRef.bind(this, 'formRef')}
              onEnd={this.onSubmitEnd}
            />
          </Drawer>
          <Modal
            title="功能开关"
            visible={this.state.switch.visible}
            key={this.state.switch.key}
            width="600px"
            onCancel={() => this.setState({ switch: { ...this.state.switch, visible: false } })}
            onOk={this.submitSwitch}
          >
            <AForm {...layout}>
              <AForm.Item
                required={true}
                label="全局开关"
                labelCol={{ span: 4 }}
                wrapperCol={{ span: 20 }}
              >
                <Switch defaultChecked={this.state.switch.switch} ref="switchi" />{' '}
                （开启/关闭客户端内所有红包功能，包括现金活动页面入口、现金账户页面入口）
              </AForm.Item>
              <div>最低版本兼容：（即低于该客户端版本红包不生效）</div>
              <AForm.Item
                required={true}
                label="IOS版本"
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 18 }}
              >
                <Input
                  ref="iosi"
                  defaultValue={this.state.switch.min_version_ios}
                  style={{ width: '60%' }}
                />
                示例：5.0.0
              </AForm.Item>
              <AForm.Item
                required={true}
                label="Android版本"
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 18 }}
              >
                <Input
                  ref="androidi"
                  defaultValue={this.state.switch.min_version}
                  style={{ width: '60%' }}
                />{' '}
                示例：5.0.0
              </AForm.Item>
              <div>最高版本兼容：（即高于该客户端版本红包不生效）</div>
              <AForm.Item
                required={true}
                label="IOS版本"
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 18 }}
              >
                <Input
                  ref="iosmax"
                  defaultValue={this.state.switch.max_version_ios}
                  style={{ width: '60%' }}
                />
                示例：5.0.0
              </AForm.Item>
              <AForm.Item
                required={true}
                label="Android版本"
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 18 }}
              >
                <Input
                  ref="androidmax"
                  defaultValue={this.state.switch.max_version}
                  style={{ width: '60%' }}
                />
                示例：5.0.0
              </AForm.Item>
              <Divider />
              <div>局部开关</div>
              <AForm.Item required={true} label="个人中心-现金活动页面入口">
                <Switch
                  defaultChecked={this.state.switch.cash_reward_switch}
                  ref="switchi_reward"
                />
              </AForm.Item>
              <AForm.Item required={true} label="个人中心-现金账户页面入口">
                <Switch
                  defaultChecked={this.state.switch.cash_account_switch}
                  ref="switchi_account"
                />
              </AForm.Item>
            </AForm>
          </Modal>
        </div>
      </>
    );
  }
}

export default RedPacket;
