import { Modal, Table, Tabs } from 'antd';
import React, { useEffect, useState } from 'react';
import { opApi as api } from '@app/api';

export const PushDataModal = (props: any) => {
  const [list, setList] = useState<any>([]);
  const [coverList, setCoverList] = useState<any>([]);
  const [tab, setTab] = useState('1');
  const columns = [
    {
      title: '类别',
      dataIndex: 'type',
      key: 'type',
      render: (text: any) => <span>{text === -1 ? '-' : text}</span>,
    },
    {
      title: '成功下发数',
      dataIndex: 'target_num',
      key: 'target_num',
      render: (text: any) => <span>{text === -1 ? '-' : text}</span>,
    },
    {
      title: '到达数',
      dataIndex: 'receive_num',
      key: 'receive_num',
      render: (text: any) => <span>{text === -1 ? '-' : text}</span>,
    },
    {
      title: '展示数',
      dataIndex: 'display_num',
      key: 'display_num',
      render: (text: number) => <span>{text === -1 ? '-' : text}</span>,
    },
    {
      title: '点击数',
      dataIndex: 'click_num',
      key: 'click_num',
      render: (text: number) => <span>{text === -1 ? '-' : text}</span>,
    },
  ];

  useEffect(() => {
    if (props.visible) {
      api.getPushNotify({ task_id: props.record.gt_task_id }).then((res: any) => {
        const { data } = res.data;
        let arr: object[] = [];
        for (let item in data) {
          switch (item) {
            case 'gt':
              arr.push({
                type: '个推(自有在线通道)',
                ...data.gt,
              });
              break;
            case 'apn':
              arr.push({
                type: 'APNs通道(ios通道)',
                ...data.apn,
              });
              break;
            case 'hw':
              arr.push({
                type: '华为',
                ...data.hw,
              });
              break;
            case 'ho':
              arr.push({
                type: '荣耀',
                ...data.ho,
              });
              break;
            case 'xm':
              arr.push({
                type: '小米',
                ...data.xm,
              });
              break;
              break;
            case 'vv':
              arr.push({
                type: 'vivo',
                ...data.vv,
              });
              break;
            case 'mz':
              arr.push({
                type: '魅族',
                ...data.mz,
              });
              break;
            case 'op':
              arr.push({
                type: 'oppo',
                ...data.op,
              });
              break;
          }
        }

        setList(arr);
      });

      if (props.record.cover_task_id) {
        api.getPushNotify({ task_id: props.record.cover_task_id }).then((res: any) => {
          const { data } = res.data;
          let arr: object[] = [];
          for (let item in data) {
            switch (item) {
              case 'gt':
                arr.push({
                  type: '个推(自有在线通道)',
                  ...data.gt,
                });
                break;
              case 'apn':
                arr.push({
                  type: 'APNs通道(ios通道)',
                  ...data.apn,
                });
                break;
              case 'hw':
                arr.push({
                  type: '华为',
                  ...data.hw,
                });
                break;
              case 'ho':
                arr.push({
                  type: '荣耀',
                  ...data.ho,
                });
                break;
              case 'xm':
                arr.push({
                  type: '小米',
                  ...data.xm,
                });
                break;
                break;
              case 'vv':
                arr.push({
                  type: 'vivo',
                  ...data.vv,
                });
                break;
              case 'mz':
                arr.push({
                  type: '魅族',
                  ...data.mz,
                });
                break;
              case 'op':
                arr.push({
                  type: 'oppo',
                  ...data.op,
                });
                break;
            }
          }

          setCoverList(arr);
        });
      }
    }
  }, [props.visible]);

  return (
    <Modal
      visible={props.visible}
      title="数据详情"
      onOk={() => props.onOk()}
      onCancel={() => props.onCancel()}
      width={700}
      footer={false}
    >
      <div style={{ color: 'red' }}>
        说明：以下数据为个推提供，仅供参考，实际点击数以列表上“打开数”为准。
      </div>
      {props.record?.cover_task_id && (
        <Tabs defaultActiveKey="1" onChange={(key) => setTab(key)}>
          <Tabs.TabPane tab="原消息" key="1"></Tabs.TabPane>
          <Tabs.TabPane tab="覆盖消息" key="2"></Tabs.TabPane>
        </Tabs>
      )}
      {tab == '1' && (
        <div key={1}>
          <span>task id:{props.record?.gt_task_id}</span>
          <Table
            columns={columns}
            dataSource={list}
            pagination={false}
            rowKey="type"
            style={{ width: 660 }}
          />
        </div>
      )}
      {tab == '2' && (
        <div key={2}>
          <span>task id:{props.record?.cover_task_id}</span>
          <Table
            columns={columns}
            dataSource={coverList}
            pagination={false}
            rowKey="type"
            style={{ width: 660 }}
          />
        </div>
      )}
    </Modal>
  );
};

export default PushDataModal;
