import {
  Form,
  Icon,
  Input,
  Radio,
  Switch,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import _, { set } from 'lodash';
import { opApi } from '@app/api';
import { Drawer, FileUploader } from '@app/components/common';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
// 数字主理人气泡
const MasterBubbleDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const channel_id = props.channel_id ?? "";

  const { getFieldDecorator, getFieldValue, setFieldsValue } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  useEffect(() => {
    if (props.visible) {
      gptQuestionBubble('');
    }
  }, [props.visible]);


  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = () => {
    // e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        dispatch(setConfig({ mLoading: true }));
        let parmas: any = { channel_id: channel_id }

        if (values.status) {
          parmas.status = 1
        } else {
          parmas.status = 0
        }
        parmas.title = values.title

        opApi.gptQuestionBubbleSave(parmas)
          .then((res: any) => {
            message.success('保存成功');
            dispatch(setConfig({ mLoading: false }));
            props.onEnd && props.onEnd();
          })
          .catch(() => {
            // message.error('添加失败');
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
        dispatch(setConfig({ mLoading: false }));
      }
    });
  };

  const [detailData, setDetailData] = useState({
    id: 0,
    status: 0,
    title: ""
  });


  const gptQuestionBubble = _.debounce((val: any) => {
    opApi.gptQuestionBubbleDetail({ channel_id: channel_id })
      .then((res) => {
        const detail = res.data.detail;
        if (!!detail) {
          setDetailData(detail);
          setFieldsValue({ ...detail });
        }
      })
      .catch(() => { });
  }, 500);

  return (
    <Drawer
      title={
        <>
          <span>数字主理人提醒气泡</span>
        </>
      }
      visible={props.visible}
      skey={props.skey}
      onClose={props.onClose}
      maskClosable={false}
      // width={500}
      onOk={handleSubmit}
      okPerm='gpt_question:dp_bubble_save'
      okText="保存"
    >
      <Form {...formLayout}>
        <Form.Item label="功能开启">
          {getFieldDecorator('status', {
            initialValue: (detailData.status === 1),
            valuePropName: 'checked',
            rules: [
              {
                required: true,
              },
            ],
          })(<Switch></Switch>)}
        </Form.Item>


        <Form.Item label="气泡文字">
          {getFieldDecorator('title', {
            initialValue: detailData.title || '',
            rules: [
              {
                required: true,

                message: '请输入泡文字',
              }, {
                max: 20,
                message: '最多20字',
              }
            ],
          })(<Input placeholder="最多20字" maxLength={20} />)}

        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'MasterBubbleDrawer' })(
  forwardRef<any, any>(MasterBubbleDrawer)
);
