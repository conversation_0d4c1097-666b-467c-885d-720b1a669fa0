import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import { Button, Form, Icon, InputNumber, Modal, Spin, Tooltip, message } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { opApi as api } from '@app/api';
import { ImageUploader, SearchAndInput } from '@app/components/common';
import { useStore } from 'react-redux';

const AddConfigHeader = (props: any, ref: any) => {
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator, setFieldsValue } = props.form;
  const store = useStore();
  const { permissions } = store.getState().session;
  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };
  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        console.log("values == ", JSON.stringify(values));

        setLoading(true);

        const parmas = {
          ...values,
        };
        api
          .createDrawerSetConfig(parmas)
          .then((res: any) => {
            message.success('添加成功');
            setLoading(false);
            props.onOk && props.onOk();
          })
          .catch(() => {
            // message.error('添加失败');
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
        setLoading(false);
      }
    });
  };


  useEffect(() => {
    if (props.visible) {
      getConfigHeader('');
    }
  }, [props.visible]);


  const [detailData, setDetailData] = useState({
    banner_image: null
  });
  const getConfigHeader = _.debounce((val: any) => {

    // type: 1, current: 1, size: 50, biz_type: props.formContent?.biz_type,
    api
      .getDrawerConfig({})
      .then((res) => {

        const {
          data: { banner_image = '' },
        } = res;
        setDetailData({ banner_image });
        setFieldsValue({ banner_image: banner_image });


      })
      .catch(() => { });
  }, 500);

  return (
    <Modal
      width={500}
      visible={props.visible}
      title="配置头图"
      key={props.key}
      onCancel={props.onClose}
      onOk={handleSubmit}
      maskClosable={false}
      destroyOnClose={true}
      confirmLoading={loading}
      okButtonProps={{ disabled: !permissions.includes('audio_home:top_column_save') }}
      bodyStyle={{
        height: 500,
        maxHeight: 500,
        overflow: 'auto',
      }}
    >
      <Form {...formLayout}>
        <Form.Item
          label='头图'
          extra="支持扩展名：.jpg .png .jpeg，比例为 23:10"
        >
          {getFieldDecorator('banner_image', {
            initialValue: detailData.banner_image || '',
            rules: [
              {
                required: true,
                message: '请上传图片',
              },
            ],
          })(<ImageUploader ratio={23 / 10} />)}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create<any>({ name: 'AddConfigHeader' })(
  forwardRef<any, any>(AddConfigHeader)
);
