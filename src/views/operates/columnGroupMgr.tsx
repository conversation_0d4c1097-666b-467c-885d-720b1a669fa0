import { <PERSON><PERSON>, Col, Divider, Icon, Modal, Row, Table, Timeline, Tooltip, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router';
import { useDispatch, useStore } from 'react-redux';
import { getCrumb } from '@app/utils/utils';
import { OrderColumn } from '@components/common';
import { communityApi as api, opApi, releaseListApi } from '@app/api';
import { setConfig } from '@app/action/config';
import { PermA, PermButton } from '@app/components/permItems';
import moment from 'moment';
import AddColumnGroupModal from './AddColumnGroupModal';

export default function ColumnGroupMgr(props: any) {
  const dispatch = useDispatch();
  const history = useHistory();
  const { session } = useStore().getState();
  const { id: column_id, name } = useParams<any>();
  const [list, setList] = useState([]);
  const [circleBlockModalVisiable, setCircleBlockModalVisiable] = useState(false);
  const [editRecord, setEditRecord] = useState(null);
  const canSeeContent = session.permissions.indexOf('circle_article:list') > -1;
  const [operateLog, setOperateLog] = useState<any>({
    visible: false,
    // key: uuid(),
    logs: null,
  });
  const [groups, setGroups] = useState<any>(null);

  const columns: any = [
    {
      title: '排序',
      key: 'order',
      render: (text: any, record: any, i: number) => {
        const index = i + 1;
        return (
          <OrderColumn
            pos={index}
            start={1}
            end={list.length}
            perm="topic_label:0:group_order"
            onUp={() => exchangeOrder(record.id, index, 0)}
            onDown={() => exchangeOrder(record.id, index, 1)}
          />
        );
      },
      width: 70,
    },
    {
      title: '序号',
      key: 'id',
      render: (a: any, b: any, i: number) => <span>{i + 1}</span>,
      width: 90,
    },
    {
      title: '分组名称',
      key: 'name',
      dataIndex: 'name',
    },
    {
      title: (
        <div>
          内容数&nbsp;
          <Tooltip
            overlayStyle={{ maxWidth: 255 }}
            title="此处内容数包含了隐藏稿件，前台不展示隐藏稿件"
            placement="top"
          >
            <Icon type="question-circle" />
          </Tooltip>
        </div>
      ),
      key: 'group_content_num',
      dataIndex: 'group_content_num',
      render: (text: string, record: any) =>
        canSeeContent ? (
          <a
            onClick={() =>
              history.push(
                `/view/newsColumnArticleList/${column_id}/${name}?group_id=${record.id}`
              )
            }
          >
            {text}
          </a>
        ) : (
          text
        ),
    },
    {
      title: '创建人',
      dataIndex: 'created_by',
    },
    {
      title: '最后操作时间',
      dataIndex: 'updated_at',
      render: (text: any, record: any) => (
        <a onClick={() => getOperateLog(record)}>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</a>
      ),
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any) => (
        <span>
          <PermA perm="topic_label:0:group_edit" onClick={() => handleEditCircleBlock(record)}>
            编辑
          </PermA>
          <Divider type="vertical" />
          <PermA perm="topic_label:0:group_delete" onClick={() => handleDelete(record)}>
            删除
          </PermA>
        </span>
      ),
      width: 120,
    },
  ];

  const handleEditCircleBlock = (record: any) => {
    setEditRecord(record);
    setCircleBlockModalVisiable(true);
  };

  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '删除分组后，该分组下的内容只显示在栏目的全部列表下',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        api
          .deleteColumnGroup({ id: record.id })
          .then(() => {
            message.success('操作成功');
            dispatch(setConfig({ loading: false }));
            getData();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const exchangeOrder = (id: number, current: number, offset: number) => {
    dispatch(setConfig({ loading: true }));
    api
      .sortColumnGroup({ id, sort_flag: offset })
      .then(() => {
        message.success('操作成功');
        getData();
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  const getData = () => {
    api
      .getColumnGroupsList({ current: 1, size: 20, topic_label_id: column_id })
      .then(({ data }) => {
        message.success('列表获取成功');
        const { group_list } = data as any;
        setList(group_list);
      })
      .catch(() => {});
  };

  const submitEnd = () => {
    getData();
    setCircleBlockModalVisiable(false);
  };

  useEffect(() => {
    getData();
  }, []);

  const getOperateLog = (record: any) => {
    releaseListApi
      .getRecommendOperateLog({ id: record.id, type: 'TopicLabelGroup' })
      .then((r: any) => {
        setOperateLog({
          visible: true,
          logs: r.data.logs,
          // key: uuid(),
        });
      })
      .catch();
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button onClick={() => history.goBack()}>
            <Icon type="left-circle" />
            返回栏目管理
          </Button>
          <PermButton
            perm="topic_label:0:group_create"
            style={{ marginLeft: 8 }}
            onClick={() => handleEditCircleBlock(null)}
          >
            添加分组
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb([...props.breadCrumb, decodeURIComponent(name)] as any)}
        </Col>
      </Row>
      <div className="component-content">
        <Table columns={columns} rowKey="id" dataSource={list} pagination={false} />
        <AddColumnGroupModal
          columnId={column_id}
          record={editRecord}
          visible={circleBlockModalVisiable}
          onCancel={() => setCircleBlockModalVisiable(false)}
          onEnd={submitEnd}
        />
      </div>

      {/* 操作日志 */}
      <Modal
        visible={operateLog.visible}
        title="操作日志"
        key={operateLog.key}
        cancelText={null}
        onCancel={() => setOperateLog({ ...operateLog, visible: false })}
        onOk={() => setOperateLog({ ...operateLog, visible: false })}
      >
        <div>
          <Timeline>
            {operateLog.logs?.map((v: any, i: number) => [
              <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                &nbsp;
              </Timeline.Item>,
              v.actions?.map((action: any, index: number) => (
                <Timeline.Item
                  className="timeline-dot"
                  data-show={action.time}
                  key={`time${i}-action${index}`}
                >
                  {action.user}&emsp;&emsp;{action.action}&emsp;&emsp;
                  {/* {action.admin_name}&emsp;{action.remark} */}
                  {/* {action.user}&emsp;&emsp;{action.action}&emsp;&emsp; */}
                </Timeline.Item>
              )),
            ])}
          </Timeline>
        </div>
      </Modal>
    </>
  );
}
