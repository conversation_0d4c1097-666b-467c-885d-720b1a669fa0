import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import { Button, Form, Icon, InputNumber, Modal, Spin, Tooltip, message } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';

import { opApi } from '@app/api';

const AddArticleModal = (props: any, ref: any) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
      showModal,
    }),
    []
  );

  const showModal = () => {
    setVisible(true);
  };

  const column = [
    {
      title: '潮新闻ID',
      dataIndex: 'id',
      width: 80,
    },
    {
      title: '新闻频道',
      key: 'type',
      dataIndex: 'channel_name',
      width: 90,
    },
    {
      title: '新闻标题',
      key: 'list_title',
      dataIndex: 'list_title',
    },
  ];

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true);
        const parmas: any = {
          type: props.type,
          sort_index: values.sort_index,
          article_id: values.channelArticles[0].id,
        };
        if (props.type == 2) {
          parmas.article_type = values.channelArticles[0].doc_type == 9 ? 0 : 1;
        }
        opApi
          .addRankingArticle(parmas)
          .then((res: any) => {
            message.success('添加成功');
            setLoading(false);

            setVisible(false);
            props.onOk && props.onOk();
          })
          .catch(() => {
            // message.error('添加失败');
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
        setLoading(false);
      }
    });
  };

  return (
    <Modal
      width={800}
      visible={visible}
      title="添加稿件"
      key={``}
      onCancel={() => {
        setVisible(false);
      }}
      onOk={handleSubmit}
      maskClosable={false}
      destroyOnClose={true}
    >
      <Spin spinning={false}>
        <Form {...formLayout} onSubmit={handleSubmit}>
          <Form.Item
            label={
              <span style={{ position: 'relative' }}>
                <Tooltip
                  title={props.type == 2 ? '仅支持关联视频稿、小视频' : `仅支持关联媒立方稿件`}
                >
                  <Icon
                    type="question-circle"
                    style={{ position: 'absolute', left: -30, top: 0 }}
                  />
                </Tooltip>
                关联稿件
              </span>
            }
          >
            {getFieldDecorator('channelArticles', {
              initialValue: null,
              preserve: false,
              rules: [
                {
                  required: true,
                  message: '请关联新闻',
                  type: 'array',
                },
                {
                  max: 1,
                  message: '仅能添加1条稿件',
                  type: 'array',
                },
                {
                  min: 1,
                  message: '为保证客户端显示效果，关联新闻数不能少于1条！',
                  type: 'array',
                },
              ],
            })(
              <NewNewsSearchAndInput
                max={1}
                func="listArticleRecommendSearch"
                columns={column}
                placeholder="输入ID或标题关联稿件"
                body={{ doc_types: props.type == 2 ? '9,10' : '2,3,4,5,8,9' }}
                order={false}
                addOnTop={true}
              />
            )}
          </Form.Item>

          <Form.Item label="输入位置">
            {getFieldDecorator('sort_index', {
              // initialValue: 0,
              rules: [
                {
                  required: true,
                  message: '请填写位置',
                },
                {
                  min: 1,
                  message: '不能小于1',
                  type: 'number',
                },
                {
                  max: props.maxPosition,
                  message: `不能大于${props.maxPosition}`,
                  type: 'number',
                },
              ],
            })(
              <InputNumber
                max={props.maxPosition}
                style={{ width: 200 }}
                precision={0}
                placeholder="请输入位置序号"
                min={1}
              />
            )}
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<any>({ name: 'AddArticleModal' })(forwardRef<any, any>(AddArticleModal));
