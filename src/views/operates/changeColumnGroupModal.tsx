import { communityApi } from '@app/api';
import { Checkbox, Form, message, Modal, Spin } from 'antd';
import React, { useEffect, useState } from 'react';

export const ChangeColumnGroupModal = (props: any) => {
  const {
    form: { getFieldDecorator },
  } = props;
  const [loading, setLoading] = useState(false);
  const [groups, setGroups] = useState([]);
  const [values, setValues] = useState([]);
  useEffect(() => {
    if (props.visible) {
      setValues(props.record?.group_ids?.length > 0 ? props.record?.group_ids?.split(',') : [])
      getGroupsList();
    }
  }, [props.visible]);

  const getGroupsList = () => {
    communityApi
      .getColumnGroupsList({ topic_label_id: props.columnId })
      .then((res: any) => {
        setGroups(res.data?.group_list || []);
      })
      .catch((err: any) => {});
  };

  const handleOkClick = () => {
    if (values.length > 2) {
      message.error('最多关联2个分组');
      return;
    }

    setLoading(true);
    communityApi
      .changeColumnGroup({
        topic_label_id: props.columnId,
        article_id: props.record.id,
        group_ids: values?.join(',') || '',
      })
      .then(() => {
        message.success('修改成功');
        setLoading(false);
        props.onEnd();
      })
      .catch(() => {
        setLoading(false);
      });
  };

  return (
    <Modal
      visible={props.visible}
      key="AddColumnGroupModal"
      title={'修改分组'}
      confirmLoading={loading}
      width="500px"
      maskClosable={false}
      onCancel={props.onCancel}
      onOk={handleOkClick}
      destroyOnClose
    >
      <Spin tip="正在加载..." spinning={loading}>
        <p>「{props.columnName}」下的分组：</p>
        <Checkbox.Group value={values} onChange={(v: any) => setValues(v)}>
          {groups.map((v: any) => (
            <Checkbox key={v.id} value={v.id.toString()}>
              {v.name}
            </Checkbox>
          ))}
        </Checkbox.Group>
      </Spin>
    </Modal>
  );
};

export default Form.create<any>({ name: 'ChangeColumnGroupModal' })(ChangeColumnGroupModal);
