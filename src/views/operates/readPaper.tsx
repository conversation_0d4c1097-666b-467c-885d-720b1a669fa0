import React, { useEffect, useState, useRef, useCallback } from 'react';
import { setConfig } from '@action/config';
import { useDispatch, useSelector } from 'react-redux';
import {
  Button,
  Col,
  DatePicker,
  Divider,
  Icon,
  Row,
  Select,
  Popconfirm,
  message,
  Tooltip,
  Modal,
  Timeline,
  Input,
  Form,
} from 'antd';
import { A, Table, Drawer } from '@components/common';
import { getTableList } from '@action/tableList';
import ImagePreview from '@components/common/imagePreview';
import { ShowModal, getCrumb, requirePerm } from '@utils/utils';
import ReadPaperForm from '@components/business/ReadPaperForm';
import { opApi as api } from '@app/api';
import moment from 'moment';
import { CommonObject, IOperationActionData } from '@app/types';
import FingerPapperPreview from '@components/business/FingerPapperPreview';
import { PermA, PermButton } from '@components/permItems';
import ReactClipboard from 'react-clipboardjs-copy';
import showImagePreviewModal from '@components/common/imagePreviewModal';
import ImagePreviewColumn from '@app/components/common/imagePreviewColumn';
import SyntheticAudioDialog from './syntheticAudioDialog';

const ReadPaper: React.FC<{ selectKeys: string; openKeys: string; breadCrumb: any }> = (props) => {
  const dispatch = useDispatch();
  // 筛选条件
  const [param, setParam] = useState({
    current: 1,
    pub_date_start: '',
    pub_date_end: '',
    status: '',
  });
  const [updated_by, setUpdatedBy] = useState('');
  // 图片预览
  const [imageUrl, setImageUrl] = useState('');
  // 抽屉
  const [drawer, setDrawer] = useState({
    visible: false,
    key: Date.now(),
    type: 'create',
  });
  const [formContent, setFormContent] = useState(null);
  const [operateLog, setOperateLog] = useState({
    visible: false,
    articleTitle: '',
    list: [],
    key: Date.now(),
  });
  const [pushInfo, setPushInfo] = useState({
    visible: false,
    pub_date: '',
    time_type: '',
    title: '',
    content: '',
    key: Date.now(),
  });
  const [syntheticAudioDialog, setSyntheticAudioDialog] = useState({
    visible: false,
    key: Date.now(),
    record: null,
  });

  const { current, size } = useSelector((state: any) => state.tableList);
  const getSeq = (i: number) => (current - 1) * size + i + 1;
  const getOperateLog = (record: any) => {
    let arr = [];
    api
      .readPagerLogs({ pub_date: record.pub_date, time_type: record.time_type })
      .then((res) => {
        const data = res.data.logs;
        let keyArr = Object.keys(data);
        keyArr.map((item) => {
          arr.push({ date: item, list: data[item].reverse() });
        });
        const dateString = record.pub_date.toString();
        const formattedDate = moment(dateString, 'YYYYMMDD').format('YYYY-MM-DD');
        setOperateLog({
          ...operateLog,
          visible: true,
          articleTitle: formattedDate,
          list: arr.reverse(),
        });
      })
      .catch();
  };

  const mergeAudio = (record: any) => {
    setSyntheticAudioDialog({ key: Date.now(), visible: true, record });
  };

  const getColumns = ({ current, size }: { current: number; size: number }) => {
    return [
      {
        title: '序号',
        key: 'order',
        render(text: any, record: any, i: number) {
          return getSeq(i);
        },
        width: 60,
      },
      {
        title: '日期',
        dataIndex: 'pub_date',
        render: (text: number, record: any) => {
          if (!text) return;
          const dateString = text.toString();
          const formattedDate = moment(dateString, 'YYYYMMDD').format('YYYY-MM-DD');
          return <a onClick={() => editPush(record)}>{formattedDate}</a>;
        },
        width: 100,
      },
      {
        title: '类型',
        dataIndex: 'time_type',
        render: (text: number, record: any) => <span>{['', '早报', '晚报'][text]}</span>,
        width: 100,
      },
      {
        title: '列表图',
        key: 'word',
        dataIndex: 'pic_url',
        align: 'center',
        render: (text: any, record: any) => {
          let cover = record.cover_image;
          if (record.paper_list_type == 3) {
            cover =
              record.time_type == 1
                ? '/assets/readpaper_m_logo.png'
                : '/assets/readpaper_e_logo.png';
          }

          return (
            <div style={{ height: 60, textAlign: 'center' }}>
              <ImagePreviewColumn text={cover} imgs={[cover]}></ImagePreviewColumn>
            </div>
          );

          // return (
          //   <div style={{ height: 60, textAlign: 'center' }}>
          //     {/*<img src={record.cover_image} className='list-pic' style={{ height: '100%' }} onMouseEnter={() => showImagePreviewModal({ images: [record.cover_image] })}></img>*/}
          //     <img
          //       src={cover}
          //       className="list-pic"
          //       style={{ height: '100%', marginLeft: -80 }}
          //     ></img>
          //   </div>
          // );
        },
        width: 200,
      },
      {
        title: '列表标题',
        key: 'title',
        dataIndex: 'title',
      },
      {
        title: '页面样式',
        dataIndex: 'paper_page_style',
        width: 80,
        render: (text: any, record: any) => {
          return <span>{text == 0 ? '折叠翻页' : '平铺展示'}</span>;
        },
      },
      {
        title: '内容数',
        dataIndex: 'content',
        width: 80,
        render: (text: any, record: any) => {
          if (text && text !== 'null' && text.slice(0, 2) === '[{') {
            let n = 0;
            const contentArray = text ? JSON.parse(text) : [];
            contentArray.map((item) => {
              if (item.article_list) {
                n += item.article_list.length;
              }
            });
            return <span>{n}</span>;
          } else {
            return 0;
          }
        },
      },
      {
        title: '显示状态',
        dataIndex: 'status',
        width: 80,
        render: (text: any, record: any) => <>{['下架', '上架'][text]}</>,
      },
      {
        title: '操作人',
        width: 120,
        dataIndex: 'updated_by',
      },
      {
        title: (
          <Tooltip title="点击时间查看操作日志" placement="top">
            操作时间
          </Tooltip>
        ),
        dataIndex: 'updated_at',
        render: (text: number, record) => (
          <a
            onClick={() => {
              getOperateLog(record);
            }}
          >
            {moment(text).format('YYYY-MM-DD HH:mm:ss')}
          </a>
        ),
        width: 96,
      },
      {
        title: '操作',
        dataIndex: 'date12',
        width: 140,
        render(text: any, record: any) {
          return (
            <>
              <PermA perm="read_paper:update" onClick={() => editPush(record)}>
                编辑
              </PermA>
              <Divider type="vertical" />
              <PermA
                perm="read_paper:update_status"
                onClick={() => {
                  changeStatus(record);
                }}
              >
                {['上架', '下架'][record.status]}
              </PermA>
              <Divider type="vertical" />
              <PermA perm="read_paper:delete">
                <Popconfirm
                  placement="top"
                  title="确定要删除吗？"
                  okText="确定"
                  cancelText="取消"
                  icon={<Icon type="exclamation-circle" theme="twoTone" twoToneColor="red" />}
                  onConfirm={() => {
                    handleDelete(record);
                  }}
                >
                  删除
                </Popconfirm>
              </PermA>
              <PermA perm="read_paper:push">
                <>
                  {record.pushed == 0 ? (
                    <span
                      onClick={() => {
                        if (record.status !== 1) return;
                        setPushInfo({
                          ...pushInfo,
                          visible: true,
                          pub_date: record.pub_date,
                          time_type: record.time_type,
                          title: record.title,
                        });
                      }}
                      style={{ color: record.status !== 1 ? '#d9d9d9' : '' }}
                    >
                      推送
                    </span>
                  ) : (
                    <Tooltip title={'请在消息推送页面进行编辑或发送'} trigger="hover">
                      <span style={{ color: '#d9d9d9' }}>推送</span>
                    </Tooltip>
                  )}
                </>
              </PermA>
              <Divider type="vertical" />
              <A
                onClick={() => {
                  showPreView(record);
                }}
              >
                预览
              </A>
              <Divider type="vertical" />
              <A>
                <ReactClipboard
                  action="copy"
                  text={record.url}
                  onSuccess={() => message.success('链接复制成功')}
                  onError={() => message.error('链接复制失败')}
                >
                  <span style={{ cursor: 'pointer', whiteSpace: 'nowrap' }}> 复制链接</span>
                </ReactClipboard>
              </A>
              {record.paper_page_style == 1 && (
                <>
                  <Divider type="vertical" />
                  <PermA
                    perm="read_paper:merge_audios"
                    onClick={() => mergeAudio(record)}
                  >
                    合成音频
                  </PermA>
                </>
              )}
            </>
          );
        },
      },
    ];
  };
  const messagePush = () => {
    const { form } = props;
    form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const reqDate = {
          pub_date: pushInfo.pub_date,
          time_type: pushInfo.time_type,
          title: values.title,
          content: values.content,
        };
        api.pushStatusReadPaper(reqDate).then(() => {
          message.success('操作成功');
          getData();
          setPushInfo({ visible: false });
        });
      }
    });
  };
  const showPreView = (record: any) => {
    const reqData = {
      pub_date: record.pub_date,
      time_type: record.time_type,
    };
    api.detailReadPaper(reqData).then((res) => {
      const { detail } = res.data;
      ShowModal(
        {
          width: '500px',
          title: '预览',
          closable: true,
          footer: null,
          // centered: true,
          bodyStyle: { padding: '20px' },
        },
        <FingerPapperPreview
          record={detail}
          path={'morning_evening_post.html'}
        ></FingerPapperPreview>
      );
    });
  };

  const editPush = (record: any) => {
    const reqData = {
      pub_date: record.pub_date,
      time_type: record.time_type,
    };
    api.detailReadPaper(reqData).then((res) => {
      const { detail } = res.data;
      if (detail.content_list) {
        detail.content_list.forEach((item, index) => {
          item.id = index;
        });
      }

      setFormContent({ ...detail });
      setDrawer({
        visible: true,
        key: Date.now(),
        type: 'edit',
      });
    });
  };
  const getData = () => {
    dispatch(getTableList('getReadPaperList', 'list', { ...getFilter(), current, size }));
  };
  const handleDelete = (record: any) => {
    const reqDate = {
      pub_date: record.pub_date,
      time_type: record.time_type,
    };
    api.deleteReadPaper(reqDate).then(() => {
      message.success('操作成功');
      getData();
    });
  };
  // 初始化
  useEffect(() => {
    const { selectKeys, openKeys } = props;
    dispatch(setConfig({ selectKeys, openKeys }));
    getData();
  }, [param]);

  const changeStatus = (record: any) => {
    const reqData = {
      pub_date: record.pub_date,
      status: record.status === 1 ? 0 : 1,
      time_type: record.time_type,
    };
    api.updateStatusReadPaper(reqData).then((res) => {
      message.success('操作成功');
      getData();
    });
  };
  // 时间修改
  const dateChange = (_: any, dateString: string[]) => {
    setParam({
      ...param,
      pub_date_start: dateString[0].replaceAll('-', ''),
      pub_date_end: dateString[1].replaceAll('-', ''),
      current: 1,
    });
  };
  // 显示状态修改
  const statusChange = (value: string) => {
    setParam({
      ...param,
      status: value === undefined ? '' : value,
      current: 1,
    });
  };
  const updateByChange = useCallback((e: any) => {
    setUpdatedBy(e.target.value);
  }, []);
  const updatedBySearch = (e: any) => {
    setParam({
      ...param,
      updated_by,
    });
  };
  const getFilter = () => {
    return param;
  };

  const onSubmitEnd = () => {
    setDrawer({ ...drawer, visible: false });
  };
  const formRef = useRef({} as any);
  const { getFieldDecorator } = props.form;
  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton
            perm="read_paper:create"
            onClick={() => {
              setFormContent(null);
              setDrawer({ ...drawer, visible: true, type: 'create' });
            }}
          >
            <Icon type="plus-circle" />
            新建
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }} type="flex" align="middle">
          <Col span={8}>
            <span>日期：</span>
            <DatePicker.RangePicker onChange={dateChange} format="YYYY-MM-DD" />
          </Col>
          <Col span={6}>
            <span>显示状态：</span>
            <Select
              style={{ width: 160, marginLeft: 8 }}
              onChange={statusChange}
              placeholder="请选择"
              allowClear
            >
              <Select.Option value="1">上架</Select.Option>
              <Select.Option value="0">下架</Select.Option>
            </Select>
          </Col>
          <Col span={10} style={{ textAlign: 'right' }}>
            <span>操作人：</span>
            <Input placeholder="输入姓名搜索" style={{ width: 150 }} onChange={updateByChange} />
            <Button style={{ marginLeft: 8, verticalAlign: 'top' }} onClick={updatedBySearch}>
              <Icon type="search" />
              搜索
            </Button>
          </Col>
        </Row>
        <Table
          func="getReadPaperList"
          index="list"
          pagination={true}
          rowKey="auto_pk"
          columns={getColumns(param)}
          filter={getFilter()}
        />
      </div>
      <ImagePreview
        src={imageUrl}
        closeByMask={true}
        onClose={() => {
          setImageUrl('');
        }}
      />
      <Drawer
        visible={drawer.visible}
        skey={drawer.key}
        title={drawer.type === 'create' ? '新建读报' : '编辑读报'}
        onClose={() => {
          setDrawer({ ...drawer, visible: false });
        }}
        onOk={() => {
          formRef.current.doSubmit();
        }}
        width={1000}
      >
        <ReadPaperForm
          drawer={drawer}
          onEnd={onSubmitEnd}
          formContent={formContent}
          wrappedComponentRef={(instance: any) => (formRef.current = instance)}
          getData={() => {
            getData();
          }}
        />
      </Drawer>
      <Modal
        visible={operateLog.visible}
        title="操作日志"
        key={`${operateLog.articleTitle}早晚报`}
        onCancel={() => {
          setOperateLog({ visible: false });
        }}
        footer={null}
        destroyOnClose={true}
      >
        <p>早晚报:&nbsp;{operateLog.articleTitle}</p>
        <br />
        <div>
          <Timeline>
            {operateLog.list?.map((v: any, i: number) => [
              <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                &nbsp;
              </Timeline.Item>,
              v.list.map((action: IOperationActionData, index: number) => (
                <Timeline.Item
                  className="timeline-dot"
                  data-show={action.time.slice(11, 26)}
                  key={`time${i}-action${index}`}
                >
                  {action.operate_user}&nbsp;{action.action}
                </Timeline.Item>
              )),
            ])}
          </Timeline>
        </div>
      </Modal>

      {syntheticAudioDialog.visible && (
        <SyntheticAudioDialog
          {...syntheticAudioDialog}
          onOk={() => {
            setSyntheticAudioDialog({ ...syntheticAudioDialog, visible: false });
          }}
          onCancel={() => {
            setSyntheticAudioDialog({ ...syntheticAudioDialog, visible: false });
          }}
        ></SyntheticAudioDialog>
      )}
      {pushInfo.visible && (
        <Modal
          visible={pushInfo.visible}
          title="新建推送"
          key={operateLog.articleTitle}
          onCancel={() => {
            setPushInfo({ ...pushInfo, visible: false, key: Date.now() });
          }}
          footer={[
            <Button
              key="back"
              onClick={() => {
                setPushInfo({ ...pushInfo, visible: false, key: Date.now() });
              }}
            >
              取消
            </Button>,
            <Button
              key="submit"
              type="primary"
              onClick={() => {
                messagePush();
              }}
            >
              保存
            </Button>,
          ]}
        >
          <Form ref={formRef} labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
            <Form.Item label={'标题'}>
              {getFieldDecorator('title', {
                initialValue: pushInfo.title,
                rules: [
                  {
                    required: false,
                    message: '请填写标题',
                  },
                ],
              })(<Input.TextArea maxLength={30} placeholder="输入标题" />)}
              <span style={{ color: '#d9d9d9' }}>最多30字</span>
            </Form.Item>
            <Form.Item label={'推送内容'}>
              {getFieldDecorator('content', {
                initialValue: pushInfo.content,
                rules: [
                  {
                    required: true,
                    message: '请填推送内容',
                  },
                ],
              })(<Input.TextArea maxLength={100} placeholder="输入推送内容" />)}
              <span style={{ color: '#d9d9d9' }}>最多100字</span>
            </Form.Item>
            备注：推送将自动关联早晚报页面，无需手动添加
          </Form>
        </Modal>
      )}
    </>
  );
};

export default Form.create({})(ReadPaper);
