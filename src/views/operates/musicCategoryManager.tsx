import { setConfig } from '@action/config';
import { getTableList } from '@app/action/tableList';
import { opApi as api } from '@app/api';
import { CommonObject } from '@app/types';
import Form from '@components/business/musicCategoryForm';
import { A, Drawer, Table, OrderColumn } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setLoading } from '@utils/utils';
import { Button, Col, Icon, message, Modal, Row, Select, Divider, Tooltip } from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';

@(withRouter as any)
@connect
class RedPacket extends React.Component<any, any> {
  formRef: any;

  constructor(props: any) {
    super(props);
    this.state = {
      form: {
        visible: false,
        key: Date.now() + 1,
      },
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    this.props.dispatch(
      setConfig({
        selectKeys: this.props.selectKeys,
        openKeys: this.props.openKeys,
      })
    );
  }

  getData = (overlap: CommonObject = {}) => {
    const { current, size } = this.props.tableList;
    const { classId } = this.state;
    const body: any = { current, size, type: 1 };
    this.props.dispatch(
      getTableList('getMusicCategoryList', 'class_list', { ...body, ...overlap })
    );
  };

  getColumns = () => {
    const { current, size, total } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) => (
          <OrderColumn
            start={1}
            end={total}
            pos={getSeq(i)}
            perm="music:class_exchange_order"
            onUp={this.exchangeOrder.bind(this, record.id, getSeq(i), 0)}
            onDown={this.exchangeOrder.bind(this, record.id, getSeq(i), 1)}
          />
        ),
        width: 70,
      },
      {
        title: '序号',
        key: 'id',
        render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
        width: 110,
      },
      {
        title: '分类名称',
        dataIndex: 'class_name',
      },
      {
        title: (
          <span>
            歌曲数量{' '}
            <Tooltip title="一首歌曲，可属于多个分类">
              <Icon type="question-circle" />
            </Tooltip>
          </span>
        ),
        dataIndex: 'music_count',
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <span>
            {requirePerm(
              this,
              'music:class_update'
            )(<A onClick={() => this.editRecord(record)}>编辑</A>)}
            <Divider type="vertical" />
            {requirePerm(
              this,
              'music:class_delete'
            )(<A onClick={() => this.deleteRecord(record)}>删除</A>)}
          </span>
        ),
        width: 100,
      },
    ];
  };

  exchangeOrder = (id: any, pos: number, sortFlag: 0 | 1) => {
    setLoading(this, true);
    api
      .exchangeMusicCategoryOrder({
        id,
        current: pos,
        offset: sortFlag,
      })
      .then(() => {
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  editRecord = (record: any = {}) => {
    this.setState({
      form: {
        visible: true,
        key: Date.now(),
        id: record.id || '',
        class_name: record.class_name || '',
      },
    });
  };

  deleteRecord = (record: any) => {
    if (record.music_count > 0) {
      message.error('请先将该分类下的歌曲删除，再删除分类');
      return;
    }
    Modal.confirm({
      title: '确定删除吗？',
      onOk: () => {
        setLoading(this, true);
        api
          .deleteMusicCategory({
            id: record.id,
          })
          .then(() => {
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };

  classIdChange = (id: string) => {
    this.setState(
      {
        classId: id,
      },
      () => this.getData({ current: 1 })
    );
  };

  closeDrawer = () => {
    this.setState({
      form: { ...this.state.form, visible: false },
    });
  };

  toMusicList = () => {
    this.props.history.push('/view/musicMgr');
  };

  onSubmitEnd = () => {
    this.closeDrawer();
    this.getData();
  };

  setRef = (ref: 'formRef', instance: any) => {
    this[ref] = instance;
  };

  submitForm = (ref: 'formRef') => {
    this[ref].doSubmit();
  };

  render() {
    const { form } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={14}>
            {requirePerm(
              this,
              'music:list'
            )(
              <Button onClick={this.toMusicList} style={{ marginRight: 8 }}>
                <Icon type="left-circle" />
                返回
              </Button>
            )}
            {requirePerm(
              this,
              ''
            )(
              <Button onClick={this.editRecord} style={{ marginRight: 8 }}>
                <Icon type="plus-circle" />
                音乐分类管理
              </Button>
            )}
          </Col>
          <Col span={10} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getMusicCategoryList"
            index="class_list"
            columns={this.getColumns()}
            filter={{ type: 1 }}
            rowKey="id"
            pagination={true}
          />
          <Drawer
            visible={form.visible}
            skey={form.key}
            onClose={this.closeDrawer}
            onOk={this.submitForm.bind(this, 'formRef')}
            title={`${form.id ? '编辑' : '新建'}音乐分类`}
          >
            <Form
              formContent={form}
              wrappedComponentRef={this.setRef.bind(this, 'formRef')}
              onEnd={this.onSubmitEnd}
            />
          </Drawer>
        </div>
      </>
    );
  }
}

export default RedPacket;
