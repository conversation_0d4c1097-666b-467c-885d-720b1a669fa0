import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react'
import { Button, Col, Dropdown, Form, Icon, Input, Menu, Modal, Radio, Row, Select, Tooltip, message } from "antd";
import AppServiceForm from '@app/components/business/appServiceForm';
import { requirePerm, getCrumb, setMenu, requirePerm4Function, searchToObject } from '@app/utils/utils';
import { useDispatch, useSelector, useStore } from 'react-redux';
import { useHistory } from 'react-router';
import { setConfig } from '@app/action/config';
import { PermA, PermButton } from '@app/components/permItems';
import OperatePositionBannerContent from './component/operatePositionBannerContent';
import OperatePositionNoticeContent from './component/operatePositionNoticeContent';

export default function OperatePositionMgr(props: any) {
  const dispatch = useDispatch();
  const history = useHistory()
  const { session } = useStore().getState()
  const { current, size, total, records } = useSelector((state: any) => state.tableList)
  const { pType } = searchToObject()
  const [pageType, setPageType] = useState(pType || 0)
  const contentRef = useRef<any>()

  useEffect(() => {
    const { selectKeys, openKeys } = props
    dispatch(
      setConfig({ selectKeys, openKeys })
    );
  }, [])

  useEffect(() => {
    console.log('pageType', pageType)
    let { pathname: path, search } = history.location
    const index = search.lastIndexOf('&pType')
    if (index >= 0) {
      search = search.substring(0, index)
    }
    path = `${path}${search}&pType=${pageType}`
    history.replace(path)

  }, [pageType])

  const addRecord = (v: any) => {
    contentRef.current.addRecord(v)
  }

  return (
    <>
      <Row className="layout-infobar">
        <Col span={16}>
          <Radio.Group defaultValue={+pageType} buttonStyle="solid" style={{ marginRight: 8 }} onChange={(e) => {
            setPageType(e.target.value)
          }}>
            {requirePerm4Function(
              session,
              `type_recommend:38:view`
            )(<Radio.Button value={0}>首页banner</Radio.Button>)}

            {requirePerm4Function(
              session,
              `creator_notice:view`
            )(<Radio.Button value={1}>公告</Radio.Button>)}

          </Radio.Group>

          <>
            <PermButton perm={pageType == 0 ? "type_recommend:38:create" : "creator_notice:create"} onClick={() => addRecord(null)}>
              <Icon type="plus-circle" />添加
            </PermButton>&nbsp;
            <Tooltip title={pageType == 0 ? "如果设置了多条上线banner图，线上优先展示序号最前的5条" : "如果设置了多条上线公告，线上优先展示序号最前的1条"} placement="top">
              <Icon type="question-circle" />
            </Tooltip>
          </>

        </Col>
        <Col span={8} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        {pageType == 0 ? <OperatePositionBannerContent customRef={(r: any) => {
          contentRef.current = r
        }} /> : <OperatePositionNoticeContent customRef={(r: any) => {
          contentRef.current = r
        }} />}
      </div>
    </>
  );
}