import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react'
import { Button, Col, Divider, Dropdown, Form, Icon, Input, Menu, Modal, Radio, Row, Select, Tooltip, message } from "antd";
import AppServiceForm from '@app/components/business/appServiceForm';
import { requirePerm, getCrumb, setMenu, requirePerm4Function, searchToObject } from '@app/utils/utils';
import { useDispatch, useSelector, useStore } from 'react-redux';
import { useHistory } from 'react-router';
import { setConfig } from '@app/action/config';
import { PermA, PermButton } from '@app/components/permItems';
import ImageDetectionKeywordContent from './component/ImageDetectionKeywordContent';
import ImageDetectionSceneContent from './component/ImageDetectionSceneContent';
import { connectTable as connect } from '@utils/connect';
import moment from 'moment';

function ImageDetectionMgr(props: any) {
  const dispatch = useDispatch();
  const history = useHistory()
  const { session } = useStore().getState()
  const { current, size, total, records } = useSelector((state: any) => state.tableList)
  const { pType } = searchToObject()
  const [pageType, setPageType] = useState(pType || 1)
  const contentRef = useRef<any>()

  const [drawer, setDrawer] = useState<any>({ visible: false, key: Date.now(), record: null })

  const formRef = useRef<any>(null)

  useEffect(() => {
    const { selectKeys, openKeys } = props
    dispatch(
      setConfig({ selectKeys, openKeys })
    );
  }, [])

  useEffect(() => {
    console.log('pageType', pageType)
    let { pathname: path, search } = history.location
    const index = search.lastIndexOf('&pType')
    if (index >= 0) {
      search = search.substring(0, index)
    }
    path = `${path}${search}&pType=${pageType}`
    history.replace(path)

  }, [pageType])

  return (
    <>
      <Row className="layout-infobar">
        <Col span={16}>
          <Radio.Group defaultValue={+pageType} buttonStyle="solid" style={{ marginRight: 8 }} onChange={(e) => {
            setPageType(e.target.value)
          }}>
            {requirePerm4Function(
              session,
              ``
            )(<Radio.Button value={1}>发布内容-放行场景</Radio.Button>)}

            {requirePerm4Function(
              session,
              ``
            )(<Radio.Button value={2}>发布内容-放行关键词</Radio.Button>)}

            {requirePerm4Function(
              session,
              ``
            )(<Radio.Button value={3}>编辑资料-放行场景</Radio.Button>)}

            {requirePerm4Function(
              session,
              ``
            )(<Radio.Button value={4}>编辑资料-放行关键词</Radio.Button>)}

          </Radio.Group>

          <Tooltip title={<div>
            功能说明：<br />
            1、本页面用于配置在发布内容/编辑资料时，检测到图片命中哪些场景或关键词，不需要前置拦截，可正常提交到后台由人工审核（内容审核后台仍会提示「敏感信息」）；<br />
            2、关键词的权重高于场景类别，例如某张图命中违规场景、同时带有放行关键词，按放行处理。<br />
            3、当图片检测到多种违规场景/关键词，仅部分设置放行，最终仍会被前置拦截。
          </div>} placement="top">
            <Icon style={{ marginLeft: 8 }} type="question-circle" />
          </Tooltip>

        </Col>
        <Col span={8} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className='component-content'>
        {pageType % 2 != 0 ? <ImageDetectionSceneContent key={pageType} type={pageType} customRef={(r: any) => {
          contentRef.current = r
        }} /> : <ImageDetectionKeywordContent key={pageType} type={pageType} customRef={(r: any) => {
          contentRef.current = r
        }} />}
      </div>
    </>
  );
}

export default connect()(ImageDetectionMgr)