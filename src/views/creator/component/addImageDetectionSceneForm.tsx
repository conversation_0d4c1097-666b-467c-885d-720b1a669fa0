import React from 'react'
import { Form, Icon, Input, InputNumber, Tooltip, message } from "antd";
import _debounce from 'lodash/throttle';
import { setMLoading } from '@app/utils/utils';
import { ImageUploader } from '@app/components/common';
import list from 'antd/lib/list';
import { creatorApi as api } from '@app/api';
import connectAll from '@app/utils/connectAll';

@connectAll
@(Form.create({ name: 'AddImageDetectionSceneForm' }) as any)
class AddImageDetectionSceneForm extends React.Component<any, any> {
  doSubmit: any

  constructor(props: any) {
    super(props)

    const form = {
      name: '',
      code: '',
      score: ''
    };

    this.state = {
      ...form,
      ...props.formContent,
    };
    console.log(!this.props.formContent ? 'recommendBannerCreate' : 'recommendBannerUpdate')
  }

  componentDidMount() {
    this.doSubmit = _debounce(this.debDoSubmit, 800, {
      trailing: false,
    });
  }

  debDoSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        const body = { ...values, type: this.props.type };
        if (this.state.id) {
          body.id = this.state.id;
        }

        body.name = body.name.trim()

        // if (values.area_names.indexOf('全局') > -1 || values.area_names.length === 0) {
        //   delete body.area_names;
        // } else {
        //   body.area_names = body.area_names.join(',');
        // }
        console.log('555555', body);
        // api[!this.props.formContent ? 'recommendBannerCreate' : 'recommendBannerUpdate']
        api.reviewImageConfigCreate(body).then((res: any) => {
          message.success('操作成功');
          setMLoading(this, false);
          this.props.onEnd();
        }).catch(() => setMLoading(this, false));
      } else {
        console.log('111111', values);
        message.error('请检查表单内容');
      }
    });
  };

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  render() {
    const { getFieldDecorator } = this.props.form;

    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    };

    return (
      <Form
        {...formLayout}
        onSubmit={this.handleSubmit}
      >
        <Form.Item label="场景名称">
          {getFieldDecorator("name", {
            initialValue: this.state.name,
            rules: [
              {
                required: true,
                message: "请输入场景名称"
              }, {
                // max: 15,
                message: "最多10个字",
                validator: (rule: any, value: any, callback: any) => {
                  if (value.trim().length > 10) {
                    callback('最多10个字')
                  }
                  callback()
                }
              }
            ]
          })(<Input placeholder='请输入场景名称，最多10个字' style={{ width: '50%'}}/>)}
        </Form.Item>
        <Form.Item label="类型编码">
          {getFieldDecorator("code", {
            initialValue: this.state.code,
            rules: [
              {
                required: true,
                message: "请输入类型编码"
              }, {
                // max: 15,
                message: "最多50个字",
                validator: (rule: any, value: any, callback: any) => {
                  if (value.trim().length > 50) {
                    callback('最多50个字')
                  }
                  callback()
                }
              }
            ]
          })(<Input placeholder='请输入类型编码' style={{ width: '50%'}} />)}
          <Tooltip className='' title={'类型编码对应中台的检测字典，详见https://ones.commind.com/wiki/#/team/HrzW4aUq/space/BEN2zfmt/page/CLLXfFJq'} placement="topLeft">
            <Icon className='ant-tooltip_icon' type="question-circle-o" style={{ marginLeft: 8 }} />
          </Tooltip>
        </Form.Item>
        <Form.Item label="放行分数">
          {getFieldDecorator("score", {
            initialValue: this.state.score,
            rules: [
              {
                required: true,
                message: "请输入放行分数",
              }
            ]
          })(<InputNumber placeholder='请输入0～100的数字' precision={0} max={100} min={0} style={{ width: '50%'}} />)}
          <Tooltip className='' title={'分数指机器判断违规场景的确信度，满分100分。假设某类型需完全放行，分数可设为100'} placement="topLeft">
            <Icon className='ant-tooltip_icon' type="question-circle-o" style={{ marginLeft: 8 }} />
          </Tooltip>
        </Form.Item>
      </Form>
    );
  }
}

export default AddImageDetectionSceneForm;