import { Drawer, OrderColumn } from "@app/components/common"
import Table from "@app/components/common/table"
import connectTable from "@app/utils/connectTable"
import React, { forwardRef } from "react"
import { communityApi } from '@app/api';
import { Dropdown, Icon, Menu, Modal, message } from "antd";
import { getTableList } from "@app/action/tableList";
import moment from "moment";
import { requirePerm } from "@app/utils/utils";
import OperatePositionAddBannerForm from "./operatePositionAddBannerForm";

@connectTable
class OperatePositionBannerContent extends React.Component<any, any> {

  formRef: any

  constructor(props: any) {
    super(props)
    this.state = {
      drawer: {
        visible: false,
        key: Date.now(),
        formContent: null
      },
      filter: {
        type: 38
      }
    }
  }

  componentDidMount() {
    this.props.customRef(this)
    this.getData()
  }

  getData(parameters: any = this.state.filter) {
    this.props.dispatch(getTableList('recommendBannerList', 'recommend_list', { ...parameters, current: 1, size: 10 }));
  };


  getSeq(i: number) {
    return (this.props.tableList.current - 1) * this.props.tableList.size + i + 1
  }

  listSort(record: any, i: number) {
    let data = {
      id: record.id,
      sort_flag: i,
    }
    communityApi.recommendBannerSort(data).then((res: any) => {
      message.success('操作成功')
      this.getData()
    })
  }

  onSubmitEnd() {
    this.setState({
      drawer: {
        visible: false,
        key: Date.now(),
        formContent: null
      }
    })
    this.getData();
  }

  closeDrawer() {
    console.log(this)
    this.setState({
      drawer: {
        visible: false,
        key: Date.now(),
        formContent: null
      }
    })
  };

  addRecord(record: any) {
    this.setState({
      drawer: {
        visible: true,
        key: Date.now(),
        formContent: record
      }
    })
  }

  onMoveStatus(record: any, type: any) {
    // if (allData.on_show_count >= 5 && type == '上架') return message.error('最多展示5张轮播图')
    Modal.confirm({
      title: <p>确定设为{type}？</p>,
      onOk: () => {
        let data = {
          id: record.id,
          status: record.status ? '0' : '1'
        }
        communityApi.recommendBannerUpdateStatus(data).then((res: any) => {
          message.success('操作成功')
          this.getData()
        })
          .catch(() => console.log('error'))
      },
    });
  }

  onDeleteRecord = (id: any) => {
    Modal.confirm({
      title: <p>确认删除吗？</p>,
      onOk: () => {
        communityApi
          .recommendBannerDeleted({ id })
          .then(() => {
            message.success('操作成功');
            this.getData()
          })
          .catch(() => message.error('操作失败'))
      },
    });
  }

  getDropDown(record: any) {
    const menu = (
      <Menu>
        {requirePerm(this, 'type_recommend:38:update')(
          <Menu.Item
            // disabled={record.doc_type === DOC_TYPE.SUBJECT.code}
            onClick={() => this.addRecord(record)}
          >
            编辑
          </Menu.Item>
        )}
        {requirePerm(this, 'type_recommend:38:update_status')(
          <Menu.Item
            // disabled={record.doc_type === DOC_TYPE.SUBJECT.code}
            onClick={() => this.onMoveStatus(record, record.status ? '下线' : '上线')}
          >
            {record.status ? '下线' : '上线'}
          </Menu.Item>
        )}
        {requirePerm(this, 'type_recommend:38:delete')(
          <Menu.Item
            // disabled={record.doc_type === DOC_TYPE.SUBJECT.code}
            onClick={() => this.onDeleteRecord(record.id)}
          >
            删除
          </Menu.Item>
        )}
      </Menu>
    );
    return (
      <Dropdown overlay={menu}>
        <a className="ant-dropdown-link">
          操作 <Icon type="down" />
        </a>
      </Dropdown>
    );
  };


  columns: any = [
    {
      title: '排序',
      key: 'order',
      render: (text: any, record: any, i: number) => {
        const pos = this.getSeq(i)
        return <OrderColumn
          pos={pos}
          start={1}
          end={this.props.tableList.allData.on_show_count}
          perm="type_recommend:38:update_sort"
          disableUp={!record.status}
          disableDown={!record.status}
          onUp={() => this.listSort(record, 0)}
          onDown={() => this.listSort(record, 1)}
        />
      },
      width: 70,
    },
    {
      title: '序号',
      key: 'seq',
      width: 80,
      render: (text: any, record: any, i: number) => (<> <span style={{ marginRight: 8 }}>{this.getSeq(i)}</span></>)
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 90,
    },
    {
      title: 'banner名称',
      dataIndex: 'title',
      width: 90,
    },
    {
      title: '图片',
      dataIndex: 'pic_url',
      width: 150,
      render: (text: any) => (<img src={text} className="list-pic" />)
    },
    {
      title: '链接',
      dataIndex: 'url',
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 90,
      render: (text: any) => (<span>{text ? '已上线' : '已下线'}</span>)
    },
    {
      title: '创建人',
      dataIndex: 'created_by',
      width: 150,
    },
    {
      title: '最后操作时间',
      dataIndex: 'updated_at',
      render: (text: any) => (<span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>),
      width: 200
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any) => this.getDropDown(record),
      width: 140,
    }
  ]

  render() {
    const { drawer } = this.state
    return (
      <div>
        <Table
          filter={this.state.filter}
          index="recommend_list"
          func="recommendBannerList"
          columns={this.columns}
          rowKey="id"
          pagination={true}
        // tableProps={{ scroll: { x: 1500 } }}
        />
        <Drawer
          visible={drawer.visible}
          skey={drawer.key}
          title={`${!!drawer.formContent ? '编辑' : '添加'}首页banner`}
          onClose={this.closeDrawer.bind(this)}
          onOk={() => this.formRef.doSubmit()}
        >
          <OperatePositionAddBannerForm
            onEnd={this.onSubmitEnd.bind(this)}
            formContent={drawer.formContent}
            // eslint-disable-next-line no-return-assign
            wrappedComponentRef={(instance: any) => (this.formRef = instance)}
          />
        </Drawer>

      </div>
    )
  }

}

export default OperatePositionBannerContent