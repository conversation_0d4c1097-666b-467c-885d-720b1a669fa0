import React from 'react'
import { Form, Input, message } from "antd";
import _debounce from 'lodash/throttle';
import { setMLoading } from '@app/utils/utils';
import { ImageUploader } from '@app/components/common';
import list from 'antd/lib/list';
import { communityApi as api } from '@app/api';
import connectAll from '@app/utils/connectAll';

@connectAll
@(Form.create({ name: 'operatePositionAddBannerForm' }) as any)
class OperatePositionAddBannerForm extends React.Component<any, any> {
  doSubmit: any

  constructor(props: any) {
    super(props)

    const form = {
      title: '',
      pic_url: '',
      url: ''
    };

    this.state = {
      ...form,
      ...props.formContent,
    };
    console.log(!this.props.formContent ? 'recommendBannerCreate' : 'recommendBannerUpdate')
  }

  componentDidMount() {
    this.doSubmit = _debounce(this.debDoSubmit, 800, {
      trailing: false,
    });
  }

  debDoSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setMLoading(this, true);
        const body = { ...values, type: 38 };
        if (this.state.id) {
          body.id = this.state.id;
        }

        body.title = body.title.trim()

        // if (values.area_names.indexOf('全局') > -1 || values.area_names.length === 0) {
        //   delete body.area_names;
        // } else {
        //   body.area_names = body.area_names.join(',');
        // }
        api[!this.props.formContent ? 'recommendBannerCreate' : 'recommendBannerUpdate'](body).then((res: any) => {
          message.success('操作成功');
          setMLoading(this, false);
          this.props.onEnd();
        }).catch(() => setMLoading(this, false));
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };

  render() {
    const { getFieldDecorator } = this.props.form;

    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    };

    return (
      <Form
        {...formLayout}
        onSubmit={this.handleSubmit}
      >
        <Form.Item label="banner名称">
          {getFieldDecorator("title", {
            initialValue: this.state.title,
            rules: [
              {
                required: true,
                message: "请输入banner名称"
              }, {
                // max: 15,
                message: "最多15个字",
                validator: (rule: any, value: any, callback: any) => {
                  if (value.trim().length > 15) {
                    callback('最多15个字')
                  }
                  callback()
                }
              }
            ]
          })(<Input style={{ width: '40%' }} placeholder='请输入banner名称，仅后台可见' />)}
          {/* <Tooltip className='' title={getToolTip()} placement="topLeft">
            <Icon className='ant-tooltip_icon' type="question-circle-o" style={{ marginLeft: 8 }} />
          </Tooltip> */}
        </Form.Item>
        <Form.Item label="图片" extra="支持上传jpg,jpeg,png图片格式，比例为1500:319">
          {getFieldDecorator("pic_url", {
            initialValue: this.state.pic_url,
            rules: [
              {
                required: true,
                message: "请上传图片"
              },
            ]
          })(
            <ImageUploader ratio={1500 / 319} accept={['image/png', 'image/jpeg', 'image/jpg']} />
          )}

        </Form.Item>
        <Form.Item label="跳转链接">
          {getFieldDecorator("url", {
            initialValue: this.state.url,
            rules: [
              {
                pattern: /^https?:\/\//,
                message: "请输入正确的链接格式"
              }
            ]
          })(<Input style={{ width: '40%' }} placeholder='选填' />)}
        </Form.Item>
      </Form>
    );
  }
}

export default OperatePositionAddBannerForm;