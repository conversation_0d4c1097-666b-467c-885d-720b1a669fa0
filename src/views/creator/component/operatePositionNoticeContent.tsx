import { Drawer, OrderColumn } from "@app/components/common"
import Table from "@app/components/common/table"
import connectTable from "@app/utils/connectTable"
import React from "react"
import { creatorApi } from '@app/api';
import { Dropdown, Icon, Input, Menu, Modal, message } from "antd";
import { getTableList } from "@app/action/tableList";
import moment from "moment";
import { session } from "@app/reducer";
import { ShowModal, requirePerm, requirePerm4Function } from "@app/utils/utils";
import OperatePositionAddBannerForm from "./operatePositionAddBannerForm";

@connectTable
class OperatePositionNoticeContent extends React.Component<any, any> {

  formRef: any

  constructor(props: any) {
    super(props)
    this.state = {
      modal: {
        visible: false,
        key: Date.now(),
        record: null,
        loading: false,
        content: null
      }
    }
  }

  componentDidMount() {
    this.props.customRef(this)
    this.getData()
  }

  componentDidUpdate(prevProps: Readonly<any>, prevState: Readonly<any>, snapshot?: any): void {
    console.log(this.props)
  }

  getData() {
    this.props.dispatch(getTableList('creatorNoticeList', 'notice_list', { current: 1, size: 10 }));
  };


  getSeq(i: number) {
    return (this.props.tableList.current - 1) * this.props.tableList.size + i + 1
  }

  listSort(record: any, i: number) {
    let data = {
      id: record.id,
      sort_flag: i,
    }
    creatorApi.creatorNoticeUpdateSort(data).then((res: any) => {
      message.success('操作成功')
      this.getData()
    })
  }

  onSubmit() {
    const content = this.state.modal.content ?? ""
    if (!content.length) {
      message.error('请输入内容')
      return
    }

    this.setState({
      modal: {
        ...this.state.modal,
        loading: true
      }
    })

    const api = !this.state.modal.record ? creatorApi.creatorNoticeCreate : creatorApi.creatorNoticeUpdate
    const param: any = { content: content, title: content.substring(0, 20) }
    !!this.state.modal.record && (param['id'] = this.state.modal.record.id)
    api(param)
      .then((res: any) => {
        message.success('操作成功')
        this.setState({
          modal: {
            ...this.state.modal,
            visible: false
          }
        })
        this.getData()
      })
      .catch(() => {
        message.success('操作成功')
      });
  }

  closeModal() {
    this.setState({
      modal: {
        visible: false,
        key: Date.now(),
        record: null,
        content: null
      }
    })
  };

  addRecord(record: any) {
    this.setState({
      modal: {
        visible: true,
        key: Date.now(),
        record: record,
        loading: false,
        content: record?.content
      }
    })
  }

  onMoveStatus(record: any, type: any) {
    // if (allData.on_show_count >= 5 && type == '上架') return message.error('最多展示5张轮播图')
    Modal.confirm({
      title: <p>确定设为{type}？</p>,
      onOk: () => {
        let data = {
          id: record.id,
          enabled: !record.enabled
        }
        creatorApi.creatorNoticeUpdateEnabled(data).then((res: any) => {
          message.success('操作成功')
          this.getData()
        })
          .catch(() => console.log('操作失败'))
      },
    });
  }

  onDeleteRecord = (id: any) => {
    Modal.confirm({
      title: <p>确认删除吗？</p>,
      onOk: () => {
        creatorApi
          .creatorNoticeDelete({ id })
          .then(() => {
            message.success('操作成功');
            this.getData()
          })
          .catch(() => message.error('操作失败'))
      },
    });
  }

  getDropDown(record: any) {
    const menu = (
      <Menu>
        {requirePerm(this, 'creator_notice:update')(
          <Menu.Item
            // disabled={record.doc_type === DOC_TYPE.SUBJECT.code}
            onClick={() => this.addRecord(record)}
          >
            编辑
          </Menu.Item>
        )}
        {requirePerm(this, 'creator_notice:update_enabled')(
          <Menu.Item
            // disabled={record.doc_type === DOC_TYPE.SUBJECT.code}
            onClick={() => this.onMoveStatus(record, record.enabled ? '下线' : '上线')}
          >
            {record.enabled ? '下线' : '上线'}
          </Menu.Item>
        )}
        {requirePerm(this, 'creator_notice:delete')(
          <Menu.Item
            // disabled={record.doc_type === DOC_TYPE.SUBJECT.code}
            onClick={() => this.onDeleteRecord(record.id)}
          >
            删除
          </Menu.Item>
        )}
      </Menu>
    );
    return (
      <Dropdown overlay={menu}>
        <a className="ant-dropdown-link">
          操作 <Icon type="down" />
        </a>
      </Dropdown>
    );
  };


  columns: any = [
    {
      title: '排序',
      key: 'order',
      render: (text: any, record: any, i: number) => {
        const pos = this.getSeq(i)
        return <OrderColumn
          pos={pos}
          start={1}
          end={this.props.tableList.allData.on_show_count}
          perm="creator_notice:update_sort"
          disableUp={!record.enabled}
          disableDown={!record.enabled}
          onUp={() => this.listSort(record, 0)}
          onDown={() => this.listSort(record, 1)}
        />
      },
      width: 70,
    },
    {
      title: '序号',
      key: 'seq',
      width: 80,
      render: (text: any, record: any, i: number) => (<> <span style={{ marginRight: 8 }}>{this.getSeq(i)}</span></>)
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 90,
    },
    {
      title: '公告',
      dataIndex: 'content',
      // width: 90,
      render: (text: any) => (<span>{text?.substring(0, 50)}</span>)
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      width: 90,
      render: (text: any) => (<span>{text ? '已上线' : '已下线'}</span>)
    },
    {
      title: '创建人',
      dataIndex: 'created_by',
      width: 150,
    },
    {
      title: '最后操作时间',
      dataIndex: 'updated_at',
      render: (text: any) => (<span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>),
      width: 200
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any) => this.getDropDown(record),
      width: 140,
    }
  ]

  render() {
    const { modal } = this.state
    return (
      <div>
        <Table
          filter={this.state.filter}
          index="notice_list"
          func="creatorNoticeList"
          columns={this.columns}
          rowKey="id"
          pagination={true}
        // tableProps={{ scroll: { x: 1500 } }}
        />
        <Modal
          key={modal.key}
          title={!!modal.record ? "编辑公告" : "添加公告"}
          visible={modal.visible}
          onCancel={this.closeModal.bind(this)}
          onOk={this.onSubmit.bind(this)}
          confirmLoading={modal.loading}
          okButtonProps={{ disabled: !modal.content?.length }}
        >
          <Input.TextArea defaultValue={modal.content} style={{ width: '100%', height: '200px' }} onChange={(e: any) => this.setState({ modal: { ...modal, content: e.target.value } })} placeholder="请输入公告内容，支持换行，最多1000字" maxLength={1000}></Input.TextArea>
        </Modal>
      </div>
    )
  }

  // this.setState({ modal: { ...modal, record: { ...modal.record, content: e.target.value } } })
}

export default OperatePositionNoticeContent