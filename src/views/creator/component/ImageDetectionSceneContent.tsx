import { Drawer, OrderColumn } from "@app/components/common"
import Table from "@app/components/common/table"
import connectTable from "@app/utils/connectTable"
import React, { forwardRef } from "react"
import { creatorApi } from '@app/api';
import { <PERSON><PERSON>, Col, Divider, Dropdown, Icon, Menu, Modal, Row, Tooltip, message } from "antd";
import { getTableList } from "@app/action/tableList";
import moment from "moment";
import { requirePerm } from "@app/utils/utils";
import AddImageDetectionSceneForm from "./addImageDetectionSceneForm";

@connectTable
class ImageDetectionSceneContent extends React.Component<any, any> {

  formRef: any

  constructor(props: any) {
    super(props)
    this.state = {
      drawer: {
        visible: false,
        key: Date.now(),
        formContent: null
      },
      filter: {
        type: props.type
      }
    }
  }

  componentDidMount() {
    this.props.customRef(this)
    this.getData()
  }

  getData(parameters: any = this.state.filter) {
    this.props.dispatch(getTableList('reviewImageConfigList', 'list', { ...parameters, current: 1, size: 10 }));
  };


  getSeq(i: number) {
    return (this.props.tableList.current - 1) * this.props.tableList.size + i + 1
  }

  onSubmitEnd() {
    this.setState({
      drawer: {
        visible: false,
        key: Date.now(),
        formContent: null
      }
    })
    this.getData();
  }

  closeDrawer() {
    console.log(this)
    this.setState({
      drawer: {
        visible: false,
        key: Date.now(),
        formContent: null
      }
    })
  };

  addRecord(record: any) {
    this.setState({
      drawer: {
        visible: true,
        key: Date.now(),
        formContent: record
      }
    })
  }

  onDeleteRecord = (id: any) => {
    Modal.confirm({
      title: <p>确认删除吗？</p>,
      onOk: () => {
        creatorApi
          .reviewImageConfigDelete({ id })
          .then(() => {
            message.success('操作成功');
            this.getData()
          })
          .catch(() => message.error('操作失败'))
      },
    });
  }

  columns: any = [
    {
      title: '序号',
      key: 'seq',
      width: 80,
      render: (text: any, record: any, i: number) => (<> <span style={{ marginRight: 8 }}>{this.getSeq(i)}</span></>)
    },
    {
      title: '场景名称',
      dataIndex: 'name',
      width: 90,
    },
    {
      title: '类型编码',
      dataIndex: 'code',
      width: 90,
    },
    {
      title: '放行分数',
      dataIndex: 'score',
      width: 150,
    },
    {
      title: '最后操作人',
      dataIndex: 'updated_by',
      width: 150,
    },
    {
      title: '最后操作时间',
      dataIndex: 'updated_at',
      render: (text: any) => (<span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>),
      width: 200
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any) => {
        return (<div>
          {requirePerm(this, 'media:review:image:config:save')(<a onClick={() => { this.addRecord(record) }}>编辑</a>)}
          <Divider type="vertical" />
          {requirePerm(this, 'media:review:image:config:delete')(<a onClick={() => { this.onDeleteRecord(record.id) }}>删除</a>)}
        </div>)
      },
      width: 80,
    }
  ]

  render() {
    const { drawer } = this.state
    return (
      <div>
        <Row style={{ marginBottom: 16 }}>
          <Col span={12}>
            {requirePerm(this, 'media:review:image:config:save')(<Button
              type='primary'
              style={{ marginLeft: 8 }}
              onClick={() => { this.addRecord(null) }}
            >
              <Icon type="plus-circle-o" />
              添加场景
            </Button>)}
            <Tooltip title={<div>
              1、添加指定检测场景及放行分数后，命中且不超过放行分数的图片可正常提交到审核后台由人工判断。<br />2、命中的场景未配置或场景已配置但高于放行分数，会被前置拦截。
            </div>} placement="top">
              <Icon style={{ marginLeft: 8 }} type="question-circle" />
            </Tooltip>
          </Col>
        </Row>
        <Table
          filter={this.state.filter}
          index="list"
          func="reviewImageConfigList"
          columns={this.columns}
          rowKey="id"
          pagination={true}
        // tableProps={{ scroll: { x: 1500 } }}
        />
        <Drawer
          visible={drawer.visible}
          skey={drawer.key}
          title={`${!!drawer.formContent ? '编辑' : '添加'}场景`}
          onClose={this.closeDrawer.bind(this)}
          onOk={() => this.formRef.doSubmit()}
        >
          <AddImageDetectionSceneForm
            onEnd={this.onSubmitEnd.bind(this)}
            formContent={drawer.formContent}
            type={this.props.type}
            // eslint-disable-next-line no-return-assign
            wrappedComponentRef={(instance: any) => (this.formRef = instance)}
          />
        </Drawer>

      </div>
    )
  }

}

export default ImageDetectionSceneContent