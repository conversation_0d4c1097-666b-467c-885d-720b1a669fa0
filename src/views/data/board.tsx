import React, { useState, useEffect, useMemo, memo } from 'react';
import { Row, Col, Input, Divider, Button, Select, Icon, Modal, Form, message } from 'antd';
import { useDispatch } from 'react-redux';
import { setMenuHook } from '@app/utils/utils';
import { communityApi } from '@app/api';
export default function dataBoard(props: any) {
  const dispatch = useDispatch();
  const [token, setToken] = useState('');

  useEffect(() => {
    setMenuHook(dispatch, props);
    communityApi
      .getDataBoardToken({})
      .then((res: any) => {
        setToken(res.data.token);
      })
      .catch(() => message.error('操作失败'));
  }, []);

  return (
    <div className="operations" style={{ width: '100%', height: '100%' }}>
      {token && (
        <iframe
          src={`https://datahome.tmuyun.com:8443/dash/integration/65?rid=810&token=${token}`}
          style={{ width: '100%', height: '100%' }}
        ></iframe>
      )}
    </div>
  );
}
