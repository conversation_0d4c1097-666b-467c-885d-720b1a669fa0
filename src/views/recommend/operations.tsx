import React, { useState, useEffect, useMemo, memo } from 'react'
import { Row, Col, Input, Divider, Button, Select, Icon, Modal, Form, message } from 'antd';
import { PermA, PermButton } from '@components/permItems';
import { useHistory } from 'react-router-dom';
import { getCrumb, setMenuHook, searchToObject } from '@app/utils/utils';
import BannerDrawer from './components/bannerDrawer';
import ToppingManuscript from './components/toppingManuscript';
import { recommendApi } from '@app/api';
import { useDispatch } from 'react-redux';
import { setConfig } from '@action/config';
import './components/operationsPage.scss'
import { useParams } from "react-router-dom"

export default function Operations(props: any) {
  const history = useHistory()
  const [form, setForm] = useState({})
  const [visibleShow, setVisibleShow] = useState(false)
  const [toppingVisibleShow, setToppingVisibleShow] = useState(false)
  const [dataObj, setDataObj] = useState({})
  const [channelId, setChannelId] = useState('')
  const [bannerData, setBannerData] = useState({})
  const dispatch = useDispatch();
  useEffect(() => {
    const { openKeys } = props
    dispatch(
      setConfig({ selectKeys: [`/view/recommendOperate?channel_id=${searchToObject().channel_id}`], openKeys })
    );
    let params = searchToObject();
    setChannelId(params.channel_id)
  }, [])
  const changeVisible = (v: boolean) => {
    setVisibleShow(v)
  }
  const changeToppingVisible = (v: boolean) => {
    setToppingVisibleShow(v)
  }
  const openTopDrawer = () => {
    let data = { channel_id: channelId }
    recommendApi.getToppingManuscriptList(data).then((res: any) => {
      setDataObj(res.data)
      setToppingVisibleShow(true)

    }).catch(() => {
      message.error('请求错误，请稍等')
    })
  }
  const RotationManagement = () => {
    let data = { channel_id: channelId }
    recommendApi.getChannelFocusList(data).then((res: any) => {
      console.log(res.data.channel.focus_carousel,);
      setBannerData(res.data)
      setVisibleShow(true)
      // setDataObj(res.data)
    })
  }

  return (
    <div className='operations'>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton
            perm={`channel_article:${channelId}:list_focus`}
            style={{ marginRight: 8 }}
            onClick={() => RotationManagement()}
          >
            轮播图管理
          </PermButton>
          <PermButton
            onClick={() => openTopDrawer()}
            perm={`articles_top:${channelId}:view`}
          >
            置顶稿件管理
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <Row className='recommend_row'>
        <img className='recommend_img' src="/assets/recommendImg.jpg" />
      </Row>
      <BannerDrawer visibleShow={visibleShow} changeVisible={changeVisible} bannerData={bannerData} componentNames={'推荐页运营位'} />
      <ToppingManuscript visibleShow={toppingVisibleShow} changeVisible={changeToppingVisible} channelId={channelId} initialValues={dataObj} />
    </div>
  )
}
