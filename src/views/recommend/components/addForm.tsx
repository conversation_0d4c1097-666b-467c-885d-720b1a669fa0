import { opApi as api } from '@app/api';
import {
  Checkbox,
  Form,
  Input,
  message,
  Select,
  DatePicker,
  InputNumber,
  Radio,
  Row,
  Button,
  Divider,
  Col,
  Icon,
  Tooltip
} from 'antd';
import React from 'react';
import connectSession from '@utils/connectSession';
const { Option } = Select;
import './operationsPage.scss'
import scrollIntoView from 'dom-scroll-into-view';
import _, { times } from 'lodash'
import FromItem from './fromItem';

@connectSession
@(Form.create({ name: 'activityForm' }) as any)
class AddForm extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    const s = {
      json: props.bannerData.focus_list.records,
    };
    const objectedJson: any = {};
    const keys = s.json.map((v: any, i: number) => {
      objectedJson[i] = v;
      return i;
    });
    this.state = {
      v: '',
      ...s,
      json: objectedJson,
      keys,
      maxKey: keys[keys.length - 1],
      optionData: [],
      ref_ids: [],
      componentNames: props.componentNames
    };
  }
  handleSubmit = (e: any) => {
    e.preventDefault();
    this.doSubmit();
  };
  componentDidMount = (() => {
    const { getFieldsValue, setFieldsValue } = this.props.form;
    const values = getFieldsValue();
    let ref_id: any = []
    this.props.bannerData.focus_list.records.forEach((el: any, i: any) => {
      ref_id.push(el.channel_article_id)
      values.json[i].source_list_title = `${el.channel_article_id}-${el.source_list_title}`
    })
    setFieldsValue({ ...values })
    this.setState({
      ref_ids: ref_id,
      json: values.json
    })
  });
  //添加一图
  add = () => {
    const newKeys = [...this.state.keys, this.state.keys.length];
    const json = {
      ...this.state.json,
      [this.state.keys.length]: {
        ...this.props.setFormItem
      },
    };
    this.setState(
      {
        keys: newKeys,
        json: { ...json },
        data: []
      }, () => {
        console.log(document.getElementsByClassName('ant-drawer-wrapper-body')[0])
        scrollIntoView(
          document.getElementById('rolling-positioning'),
          document.getElementsByClassName('ant-drawer-wrapper-body')[0]
        );
      }
    );
  };
  //删除
  remove = (key: any) => {
    const { getFieldsValue, setFieldsValue } = this.props.form;
    const values = getFieldsValue();
    values['json'].splice(key, 1)
    setFieldsValue({
      ...values
    })
    let keys = []
    const newKeys = this.state.keys.filter((v: any) => v !== key);
    keys = newKeys.map((el: any, i: number) => i)
    this.setState({
      keys: keys,
      json: { ...values['json'] }
    }, () => {
      console.log(this.state.json, '回调');
    });
  };
  //校验提交表单
  doSubmit = () => {
    this.props.form.validateFieldsAndScroll((err: any, values: any) => {
      this.setState({
        ref_ids: []
      })
      if (!err) {
        const data = this.props.getUpdateDate(values, this.props.bannerData)
        const joggle: any = this.props.joggle
        joggle(data).then((res: any) => {
          message.success('操作成功');
          this.props.changeVisible(false)
          this.props.closeLoding(false)

        }).catch(() => {
          this.props.closeLoding(false)
        })
      } else {
        message.error('请检查表单内容');
        this.props.closeLoding(false)
      }
    });
  };
  //向上移动
  upMove = (v: any) => {
    const { getFieldsValue, setFieldsValue } = this.props.form;
    const value = getFieldsValue();
    const current = value.json[v]
    const next = value.json[v - 1]
    value.json[v] = next
    value.json[v - 1] = current
    setFieldsValue({
      ...value
    })
    this.setState({
      json: [...value.json]
    }, () => {
      this.props.form.resetFields()
      console.log(this.props.form.resetFields(), '2222222')
    })
  }
  //向下移动
  downMove = (v: any) => {
    const { getFieldsValue, setFieldsValue } = this.props.form;
    const value = getFieldsValue();
    const current = value.json[v]
    const next = value.json[v + 1]
    value.json[v] = next
    value.json[v + 1] = current
    setFieldsValue({
      ...value
    })
    this.setState({
      json: value.json
    }, () => {
      this.props.form.resetFields()
    })
  }
  changeRadio = (v: any, e: any) => {
    this.state.json[v].auto_title = e.target.value
  }
  getTooltipTitle = () => {
    return (
      <>
        <div>1、需显示在哪个位置就输入对应数字，仅限1~9</div>
        <div>2、多个运营模块设置相同位置的数字时，轮播图先显示</div>
      </>
    )
  }
  titleChange = (v: number, e: any) => {
    this.state.json[v].title = e.target.value
    this.setState({
      json: this.state.json
    })
  }

  changeInput = (v: any) => {
    this.props.form.resetFields()
    const { getFieldDecorator, getFieldsValue, setFieldsValue } = this.props.form;
    let val = getFieldsValue();
    val = v
    setFieldsValue({
      ...val
    })
    this.setState({
      json: { ...val.json }
    })
  }
  chooseSelectItem = (val: any) => {
    const { setFieldsValue } = this.props.form;
    setFieldsValue({
      ...val
    })
    this.setState({
      json: val.json
    }, () => {
      this.props.form.resetFields()
    })
  }
  render() {
    const { getFieldDecorator, setFieldsValue, getFieldsValue } = this.props.form;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <Form {...formLayout} onSubmit={this.handleSubmit} >
        <Form.Item label="显示位置" labelAlign={'left'} className='form_radioform_radioform_radio'>
          {getFieldDecorator('position', {
            initialValue: this.props.bannerData.position,
            rules: [
              {
                required: true,
                message: '请输入显示位置',
              },
              {
                pattern: /^[1-9]$/,
                message: '请输入1-9的数字',
              }
            ],
          })(<Input style={{ width: '50%' }} maxLength={30} placeholder="请输入显示位置" />)}
          <Tooltip className='' title={this.getTooltipTitle()}>
            <Icon className='ant-tooltip_icon' type="question-circle-o" />
          </Tooltip>
        </Form.Item>
        {this.state.keys.map((v: any, i: number) => (
          <Row key={v}>
            <Row>
              <Divider type="horizontal" />
              <Col span={6} style={{ textAlign: 'right' }}>
                {this.state.keys.length && i == 0 ? <Button onClick={this.upMove.bind(this, v)} className='btn_mar' disabled type="primary" icon="up" /> : <Button onClick={this.upMove.bind(this, v)} className='btn_mar' type="primary" icon="up" />}
                {this.state.keys.length && i != this.state.keys.length - 1 ? <Button onClick={this.downMove.bind(this, v)} className='btn_mar' type="primary" icon="down" /> : <Button onClick={this.downMove.bind(this, v)} className='btn_mar' disabled type="primary" icon="down" />}
                <span className='title'>图{i + 1}</span>
              </Col>
              <Col span={18} style={{ textAlign: 'right' }}>
                <Button
                  type="danger"
                  onClick={() => this.remove(v)}
                // disabled={this.state.keys.length <= 1}
                >
                  删除
                </Button>
              </Col>
            </Row>
            <Row key={v}>
              <Col>
                {
                  this.state.componentNames == '推荐页运营位' && <FromItem changeRadio={this.changeRadio} chooseSelectItem={this.chooseSelectItem} changeInput={this.changeInput} getFieldDecorator={getFieldDecorator} getFieldsValue={getFieldsValue} setFieldsValue={setFieldsValue} ref_ids={this.state.ref_ids} Form={Form} json={this.state.json} v={v} />
                }
              </Col>
            </Row>
          </Row>
        ))}
        <Row id='rolling-positioning' className='add_btn' style={{ display: `${this.state.keys.length > 4 ? 'none' : ''}` }}>
          <Button onClick={this.add}>添加一图</Button>
        </Row>
      </Form>
    );
  }
}

export default AddForm;
