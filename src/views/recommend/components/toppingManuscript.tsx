import React, { useEffect, useState, useCallback, memo, useReducer, createRef, forwardRef, useImperativeHandle } from 'react'
import Form, { FormComponentProps } from "antd/lib/form/Form";
import { <PERSON>er, <PERSON>ton, Col, Row, Input, Select, message } from 'antd';
const { Option } = Select;
import { recommendApi } from '@app/api';
import './operationsPage.scss';
import { DOC_TYPE } from '@app/utils/constants';
import SearchAndInput from '@components/common/newsSearchAndInput';
import { resolveNewsType } from '@utils/utils';
import { PermButton } from '@components/permItems';
import { searchToObject } from '@app/utils/utils';
import _ from 'lodash'

interface ToppingFormProps extends FormComponentProps {
  onSubmit: () => void;
  articlesChanges: (a: any) => void;
  list: () => [];
  position: () => String;
}
type Ref = FormComponentProps;
const ToppingManuscriptForm = forwardRef<Ref, ToppingFormProps>(
  ({ form, onSubmit, list, articlesChanges, position }: ToppingFormProps, ref) => {
    useImperativeHandle(ref, () => ({
      form
    }));
    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      onSubmit();
    };
    const articlesChange = (data: any) => {
      articlesChanges(data)
    }
    const getDoc_types = () => {
      return { doc_types: '2,3,4,5,8,9' }
    }
    return (
      <Form
        onSubmit={handleSubmit}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 19 }}
      >
        {/* <Form.Item label="显示位置">
          {form.getFieldDecorator("position", {
            initialValue: position,
            rules: [
              {
                required: true,
                message: "请输入显示位置"
              },
              {
                pattern: /^[1-9]$/,
                message: "请输入1-9的数字"
              }
            ]
          })(<Input style={{width:'50%'}} />)}
        </Form.Item> */}
        <Form.Item label="选择稿件">
          {form.getFieldDecorator("position_id", {
            initialValue: '',
          })(<Col span={24}>
            <SearchAndInput
              max={2}
              func="topListSearch"
              columns={[
                {
                  title: '频道',
                  key: 'channel_name',
                  dataIndex: 'channel_name',
                  width: '15%',
                },
                {
                  title: '新闻类型',
                  key: 'doc_type',
                  dataIndex: 'doc_type',
                  width: '25%',
                  render: (text: number) => <span>{resolveNewsType(text)}</span>,
                },
                {
                  title: '新闻标题',
                  key: 'list_title',
                  width: '35%',
                  dataIndex: 'list_title',
                },
              ]}
              order={true}
              placeholder="输入新闻（专题）ID或标题关联"
              initialValues={{ list }}
              triggerInitialValueChange={articlesChange}
              body={getDoc_types()}
            />
          </Col>)}
        </Form.Item>
      </Form>
    );
  }
);

const EnhancedForm = Form.create<ToppingFormProps>()(ToppingManuscriptForm);

function ToppingManuscript(props: any) {
  const formRef = createRef<FormComponentProps>();
  const [iconLoading, setconLoading] = useState(false)
  const [selectData, setSelectData] = useState([])
  const [channelId, setChannelId] = useState('')
  // const { getFieldDecorator } = props.form;
  useEffect(() => {
    let id: any = []
    if (props.initialValues?.recommend_detail?.articles) {
      props.initialValues?.recommend_detail?.articles.forEach((el: any) => {
        id.push(el.id)
      })
    }
    let params = searchToObject();
    setChannelId(params.channel_id)
    setSelectData(id)
  }, [props])

  const onClose = () => {
    props.changeVisible(false)
    setconLoading(false)
  }
  const onOk = () => {
    formRef.current?.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setconLoading(true)
        let data = {
          recommend_name: props.initialValues?.recommend_detail?.title,
          id: props.initialValues.position_id,
          ref_ids: selectData.join(),
          position: 1,//values.position,
          channel_id: channelId
        }
        recommendApi.saveTopList(data).then(res => {
          message.success('操作成功');
          props.changeVisible(false)
          setconLoading(false)

        }).catch(() => {
          setconLoading(false)
        })
      } else {
        message.error('请检查表单内容');
      }
      console.log(values, "values");
    });

  }
  const articlesChange = (data: any) => {
    setSelectData(data.channelArticleIds)
  };
  const getColumn = () => {
    return [
      {
        title: '新闻频道',
        key: 'channel',
        dataIndex: 'channel_name',
        width: 95,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
      },
    ];
  };
  const onCancel = () => {
    props.changeVisible(false)
    setconLoading(false)
  }

  return (
    <>
      <Drawer
        title="置顶稿件管理"
        maskClosable={false}
        width={'50%'}
        onClose={onClose}
        visible={props.visibleShow}
        bodyStyle={{ paddingBottom: 80 }}
        destroyOnClose
        className='operations'
      >
        <Row>
          <Col>
            <EnhancedForm onSubmit={onOk} articlesChanges={(data: any) => articlesChange(data)} wrappedComponentRef={formRef} list={props.initialValues?.recommend_detail?.articles} position={props.initialValues.position} />
          </Col>
        </Row>
        <div
          className='banner_drawer_btn'
        >
          <Button onClick={() => onCancel()} style={{ marginRight: 8 }}>
            取消
          </Button>
          <PermButton
            perm={`articles_top:${channelId}:update`}
            type="primary"
            loading={iconLoading}
            onClick={() => onOk()}
          >
            确定
          </PermButton>
          {/* <Button onClick={()=>onOk()} type="primary" loading={iconLoading}>
                确定
                </Button> */}
        </div>
      </Drawer>
    </>
  )
}
export default memo(ToppingManuscript)
