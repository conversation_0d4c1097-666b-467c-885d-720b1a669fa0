import React, { useState, useEffect } from 'react'
import {
  Input,
  DatePicker,
} from 'antd';
import TimeRangePicker from '@app/components/common/TimeRangePicker';

export default function CompetitionItem(props: any) {
  const getFieldDecorator = props.getFieldDecorator
  const v = props.v
  const Form = props.Form
  const [stateList, setStateList] = useState({ ...props.json })
  const itemStyle = { marginBottom: 15 }
  useEffect(() => {
    setStateList({ ...props.json })
  }, [props.json])

  return (
    <div>
      <Form.Item label="日程开始日期" style={itemStyle}>
        {getFieldDecorator(`json[${v}].date`, {
          initialValue: stateList[v]?.date,
          rules: [
            {
              required: true,
              message: '请选择日期',
            },
          ],
        })(<DatePicker placeholder='请选择日期' style={{ width: '80%' }} format="YYYY-MM-DD" />)}
      </Form.Item>
      <Form.Item label="日程时间段" style={itemStyle} required>
        {getFieldDecorator(`json[${v}].times`, {
          initialValue: stateList[v]?.times,
          rules: [
            {
              validator(rule: any, value: string, callback: Function) {
                if (!value[0] && !value[1]) {
                  callback('请填写日程时间段')
                  return
                }
                const [hours1, minute1] = value[0].split(':')
                const [hours2, minute2] = value[1].split(':')
                if (hours1 > hours2 || (hours1 === hours2 && minute1 >= minute2)) {
                  callback('结束时间点必须晚于起始时间点')
                  return
                }
                callback()
              }
            }
          ],
        })(<TimeRangePicker style={{ width: '80%' }} />)}
      </Form.Item>
      <Form.Item label="赛事名称" style={itemStyle} >
        {getFieldDecorator(`json[${v}].name`, {
          initialValue: stateList[v]?.name,
          rules: [
            {
              required: true,
              message: '请输入赛事名称',
            },
          ],
        })(<Input placeholder='请输入赛事名称' style={{ width: '80%' }} maxLength={15}/>)}
      </Form.Item>
      <Form.Item label="跳转链接" style={itemStyle}>
        {getFieldDecorator(`json[${v}].jump_url`, {
          initialValue: props.json[v]?.jump_url,
          rules: [
            {
              pattern: /^https?:\/\//,
              message: "请输入正确的链接格式"
            }
          ],
        })(
          <Input placeholder="输入跳转URL" maxLength={200} style={{ width: '80%' }} />
        )}
      </Form.Item>

      <Form.Item label="隐藏域" style={{ display: 'none' }} >
        {getFieldDecorator(`json[${v}].id`, {
          initialValue: stateList[v]?.id,
        })(<Input style={{ width: '80%' }} />)}
      </Form.Item>

    </div>
  )
}
