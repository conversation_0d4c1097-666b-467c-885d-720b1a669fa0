import React, { useEffect, useState, useRef } from 'react'
import { Drawer, Form, Button, Switch, message } from 'antd';
import AddForm from '@components/business/addForm';
// import AddForm from './addForm';
import { searchToObject, setLoading } from '@app/utils/utils';
import { recommendApi, releaseListApi } from '@app/api';
import { PermButton, PermSwitch } from '@components/permItems';
import './operationsPage.scss'

export default function bannerDrawer(props: any) {
  const [visible, setVisible] = useState(props.visibleShow)
  const [iconLoading, setIconLoading] = useState(false)
  const [voluntarilyChecked, setVoluntarilyChecked] = useState(false)
  const [channelId, setChannelId] = useState('')
  const formRef = useRef({} as any);
  const [bannerData, setBannerData] = useState({})
  const [checkedBool, setCheckedBool] = useState(true)
  useEffect(() => {
    //   getList()
    setCheckedBool(props.bannerData.channel?.focus_carousel)
    setBannerData(props.bannerData)
    setVoluntarilyChecked(props.bannerData.channel?.focus_carousel)
    let params = searchToObject();
    setChannelId(params.channel_id)
  }, [props])
  const getList = () => {
    let data = { channel_id: channelId }
    recommendApi.getChannelFocusList(data).then((res: any) => {
      console.log(res.data.channel.focus_carousel,);
      // setDataObj(res.data)
    })
  }
  const showDrawer = () => {
    props.changeVisible(true)
  }
  const onClose = () => {
    props.changeVisible(false)
    setIconLoading(false)
  }
  const onOk = (e: any) => {
    setIconLoading(true)
    // setconLoading(false)
    formRef.current.handleSubmit(e)
  }
  const onChange = (checked: boolean) => {
    releaseListApi
      .focusChangeCarousel({ id: channelId, focus_carousel: checked ? '1' : '0' })
      .then(() => {
        message.success('操作成功');
      })
      .catch(() => {
        message.error('操作失败');
      });
    setVoluntarilyChecked(checked)
  }
  const changeVisible = (v: boolean, type: any) => {
    props.changeVisible(v)
    getList()
  }
  const closeLoding = (v: boolean) => {
    setIconLoading(v)
  }
  //取消提交
  const cancleSubmit = () => {
    setIconLoading(false)
    props.changeVisible(false)
  }
  //提交参数整合
  const getUpdateDate = (values: any, bannerData: any = {}) => {
    let pic_urls: string[] = []
    let titles: string[] = []
    let ref_ids: any = []
    let id = ''
    if (JSON.stringify(values) !== '{}') {
      values.json && values?.json.forEach((el: any, i: number) => {
        id = el['source_list_title'].split('-')[0].trim()
        ref_ids.push(id)
        pic_urls.push(el['image_url'])
        if (!!!el['auto_title']) {
          titles.push(el['title'].trim())
        } else {
          titles.push('')
        }
      })
    }
    let data = {
      pic_urls,
      titles,
      ref_ids: ref_ids.toString(),
      recommend_name: bannerData.channel.name,
      id: bannerData.position_id,
      position: 2,
      channel_id: bannerData.channel.id,
    }
    return data
  }
  const setFormItem = () => {
    return {
      title: '',
      auto_title: 1,
      custom_title: '',
      image_url: '',
    }
  }
  return (
    <>
      <Drawer
        title="轮播图管理"
        width={'60%'}
        maskClosable={false}
        onClose={onClose}
        visible={props.visibleShow}
        bodyStyle={{ paddingBottom: 80 }}
        destroyOnClose
      >
        <div className='box'>
          <div className='play_switch' style={{ marginBottom: 20 }}>
            <span>自动轮播：</span>
            <PermSwitch perm={`channel:${channelId}:carousel`} checked={voluntarilyChecked} onChange={onChange} defaultChecked={checkedBool} />
          </div>
          <AddForm
            bannerData={bannerData} //复现数据
            changeVisible={changeVisible}
            closeLoding={closeLoding}
            wrappedComponentRef={(instance: any) => (formRef.current = instance)}
            getUpdateDate={getUpdateDate} //获取要提交的数据
            setFormItem={setFormItem()} //设置表单item
            joggle={recommendApi.saveTopList} // 接口
            componentNames={props.componentNames} //判断使用的组件
            addLabel={'添加一图'}
            headName={'图'}
            listLength={5}
            disabled={0}
          />
        </div>
        <div
          style={{
            position: 'absolute',
            right: 0,
            bottom: 0,
            width: '100%',
            borderTop: '1px solid #e9e9e9',
            padding: '10px 16px',
            background: '#fff',
            textAlign: 'right',
          }}
        >
          <Button onClick={() => cancleSubmit()} style={{ marginRight: 8 }}>
            取消
          </Button>
          <PermButton
            perm={`channel_focus:${channelId}:update`}
            type="primary" loading={iconLoading}
            onClick={(e) => onOk(e)}
          >
            确定
          </PermButton>
          {/* <Button onClick={(e)=>onOk(e)} type="primary" loading={iconLoading}>
                确定
                </Button> */}
        </div>
      </Drawer>
    </>
  )
}
