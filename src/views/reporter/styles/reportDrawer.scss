.drawer_detail_content {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: stretch;

  .report_content {
    width: 65%;
    border-right: 1px solid #e8e8e8;
    padding-right: 20px;
  }

  .report_info {
    width: 35%;
    padding-left: 20px;
    padding-right: 20px;
  }
}

.report_wrapper {
  overflow: auto;

  .content {
    white-space: pre-wrap;
  }

  p {
    overflow: hidden;
    width: 100%;
  }

  .row {
    text-align: center;
    margin-bottom: 10px;
  }

  .row img {
    vertical-align: middle;
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 370px;
  }

  .row video {
    // width: 100%;
    // height: auto;
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 530px;
  }
}

.report_info_wrapper {
  overflow: auto;


  >div {
    margin-bottom: 20px;
  }

  .report_info_header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    span {
      font-size: 16px;
      font-weight: 500;

      &::before {
        content: '';
        background-color: #1890ff;
        display: inline-block;
        width: 3px;
        height: 17px;
        border-radius: 1.5px;
        margin-right: 5px;
        vertical-align: text-top;
      }
    }
  }

  .contact_info {

    .ant-row {
      margin-bottom: 10px;
    }

  }

  .remark {
    line-height: 20px;
    font-size: 14px;

    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 5; // 控制多行的行数
    -webkit-box-orient: vertical;

  }
}

.comment_wrapper {
  margin-bottom: 20px;

  .comment_header {
    display: flex;
    flex-direction: row;
    align-items: center;

    img {
      width: 40px;
      height: 40px;
      border-radius: 20px;
      object-fit: cover;
      overflow: hidden;
      margin-right: 10px;
      flex: none;
    }

    .comment_user {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      span {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .comment_content {
    white-space: pre-wrap;
  }
}

.reporter-item {
  white-space: pre-wrap;
}

.ellipsis_text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2; // 控制多行的行数
  -webkit-box-orient: vertical;
  white-space: pre-wrap;
}

.table_comment_imgs {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 10px;
  gap: 10px;

  img {
    width: calc((100% - 20px) / 3);
    aspect-ratio: 1 / 1;
    object-fit: cover;
    vertical-align: middle;
    cursor: pointer;
  }

}