import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import {
  Button,
  Col,
  Dropdown,
  Form,
  Icon,
  Input,
  InputNumber,
  Menu,
  Modal,
  Radio,
  Row,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { reportApi } from '@app/api';
import '@components/business/styles/business.scss';
import { setMLoading } from '@app/utils/utils';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { Drawer, VideoUploader } from '@app/components/common';
import ImgUploadList from '@app/components/common/imgUploadList';
import { PermButton } from '@app/components/permItems';

const ReportForm = (props: any, ref: any) => {
  const dispatch = useDispatch();

  const {
    biz_type = 0,
    report_type = 0,
    title = '',
    content = '',
    medias = [],
    status = 0,
    remark = '',
    field_id = 0,
  } = props.formContent || {};

  const [imgs, setImgs] = useState([]);
  const [videoURL, setVideoURL] = useState<any>('');
  const { getFieldDecorator, getFieldValue, setFieldsValue } = props.form;

  const [fieldsList, setFieldsList] = useState([]);
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
      doSubmit,
    }),
    []
  );

  useEffect(() => {
    if (props.visible) {
      getFieldsList();
    }
  }, [props.visible]);

  const getFieldsList = () => {
    reportApi
      .getAllReportFieldList({})
      .then((res: any) => {
        setFieldsList(res.data.list.records || []);
      })
      .catch(() => {});
  };

  useEffect(() => {
    if (!!props.formContent) {
      let video: any = null;
      const imgs =
        props.formContent?.medias
          ?.filter((item: any) => {
            if (item.type == 0) {
              return true;
            }
            video = item;
            return false;
          })
          .map((v: any) => `${v.media_id},${v.url}`) || [];

      setVideoURL(!!video ? `${video.media_id},${video.url}` : '');
      setImgs(imgs);
    }
  }, [props.formContent]);

  const doSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body: any = { ...values, id: props.formContent?.id, submit_type: 0 };
        delete body.img_list;
        delete body.video_url;

        let medias = [];
        if (!!videoURL) {
          const arr = videoURL.split(',http');
          medias.push({
            id: arr[0],
            type: 1,
          });
        }

        if (!!values?.img_list?.length) {
          medias.push(
            ...values.img_list.map((v: any) => {
              const arr = v.split(',http');
              return {
                id: arr[0],
                type: 0,
              };
            })
          );
        }
        body.medias = medias;

        dispatch(setConfig({ mLoading: true }));
        reportApi
          .updateReport(body)
          .then(() => {
            dispatch(setConfig({ mLoading: false }));
            message.success('操作成功');
            props.onEnd(values.type);
          })
          .catch(() => {
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  // const menu = (
  //   <Menu onClick={props.onClose}>
  //     <Menu.Item key="1" style={{ textAlign: 'center' }}>
  //       仅保存修改
  //     </Menu.Item>
  //     <Menu.Item key="2" style={{ textAlign: 'center' }}>
  //       保存并通过
  //     </Menu.Item>
  //   </Menu>
  // );
  return (
    <Drawer
      title="编辑报料"
      visible={props.visible}
      skey={props.skey}
      onClose={props.onClose}
      onOk={doSubmit}
      okText="保存"
      // footer={((<><Button style={{ width: 100, marginRight: 8 }} onClick={props.onClose}>取消</Button>
      //   <Dropdown trigger={['hover']} overlay={menu} placement="topCenter">
      //     <PermButton style={{ width: 100 }} type="primary" perm='' onClick={props.onOk}>
      //       保存<Icon type="up" />
      //     </PermButton>
      //   </Dropdown>

      // </>))}
    >
      <Form {...formLayout}>
        {/* disabled={status != 0} */}
        <Form.Item label="业务类型">
          {getFieldDecorator('biz_type', {
            initialValue: biz_type,
            rules: [
              {
                required: true,
                message: '请选择业务类型',
              },
            ],
          })(
            <Radio.Group
              onChange={(e) => {
                if (e.target.value == 4) {
                  setFieldsValue({
                    report_type: 4,
                  });
                } else if (e.target.value == 5) {
                  setFieldsValue({
                    report_type: 7,
                  });
                } else {
                  setFieldsValue({
                    report_type: getFieldValue('report_type') > 3 ? 1 : getFieldValue('report_type'),
                  });
                }
              }}
            >
              <Radio value={1}>问政</Radio>
              <Radio value={2}>帮办</Radio>
              <Radio value={3}>帮帮团</Radio>
              <Radio value={4}>应急求助</Radio>
              <Radio value={5}>小店帮</Radio>
            </Radio.Group>
          )}
        </Form.Item>

        <Form.Item label="报料类型">
          {getFieldDecorator('report_type', {
            initialValue: report_type,
            rules: [
              {
                required: true,
                message: '请选择报料类型',
              },
            ],
          })(
            <Radio.Group>
              {getFieldValue('biz_type') == 4 ? (
                <>
                  <Radio value={4}>防汛应急</Radio>
                  <Radio value={5}>防台应急</Radio>
                  <Radio value={6}>其它应急</Radio>
                </>
              ) : getFieldValue('biz_type') == 5 ? (
                <>
                  <Radio value={7}>求助力</Radio>
                  <Radio value={8}>求表扬</Radio>
                  <Radio value={9}>求流量</Radio>
                </>
              ) : (
                <>
                  <Radio value={1}>投诉举报</Radio>
                  <Radio value={2}>咨询求助</Radio>
                  <Radio value={3}>建言献策</Radio>
                </>
              )}
            </Radio.Group>
          )}
        </Form.Item>

        <Form.Item label="关联领域">
          {getFieldDecorator('field_id', {
            initialValue: field_id,
            rules: [
              {
                required: true,
                message: '请选择关联领域',
              },
            ],
          })(
            <Radio.Group>
              {/* <Row> */}
              {/* <Col span={6} key={0}>
              <Radio style={{ lineHeight: '40px' }} value={0}>无领域</Radio>
            </Col> */}
              <Radio style={{ lineHeight: '40px' }} value={0}>
                无领域
              </Radio>
              {fieldsList.map((v: any) => {
                return (
                  <Radio style={{ lineHeight: '40px' }} key={v.id} value={v.id}>
                    {v.name}
                  </Radio>
                );
                // return <Col span={6} key={v.id}>
                //   <Radio style={{ lineHeight: '40px' }} value={v.id}>{v.name}</Radio>
                // </Col>
              })}
              {/* </Row> */}
            </Radio.Group>
          )}
        </Form.Item>

        <Form.Item label="报料标题">
          {getFieldDecorator('title', {
            initialValue: title,
            rules: [
              {
                required: true,
                message: '请输入标题',
                whitespace: true,
              },
              {
                max: 30,
                message: '最多30字',
              },
            ],
          })(<Input placeholder="最多30字"></Input>)}
        </Form.Item>

        <Form.Item label="报料内容">
          {getFieldDecorator('content', {
            initialValue: content,
            // rules: [],
          })(
            <Input.TextArea
              rows={10}
              maxLength={1000}
              placeholder="选填，最多1000字"
            ></Input.TextArea>
          )}
        </Form.Item>

        <Form.Item label="报料视频" extra={'最多可添加1个视频，视频100MB以内'}>
          {getFieldDecorator('video_url', {
            initialValue: videoURL,
            // rules: [
            //   {
            //     required: true,
            //     message: '请上传视频',
            //   }
            // ],
          })(
            <VideoUploader
              needMz={true}
              disable={imgs.length == 9 ? '只能添加9个附件（图片/视频）' : ''}
              size={100}
              onChange={(v: string) => setVideoURL(v)}
            />
          )}
        </Form.Item>

        <Form.Item label="报料图片">
          {getFieldDecorator('img_list', {
            initialValue: imgs,
            // rules: [
            //   {
            //     required: true,
            //     message: '请上传预览图',
            //   },
            // ],
          })(
            <ImgUploadList
              needMz={true}
              max={!!videoURL ? 8 : 9}
              isCutting={true}
              accept={['image/jpeg', 'image/png', 'image/jpg']}
              // tips={'用于让用户预览主题样式，可上传1～10张图片'}
              extra={'图片/视频加总最多可添加9个，图片10MB以内'}
              onChange={(v: any) => setImgs(v)}
              disable={
                Boolean(imgs.length == 9 || (imgs.length == 8 && !!videoURL))
                  ? '只能添加9个附件（图片/视频）'
                  : ''
              }
              customOp={true}
            />
          )}
        </Form.Item>

        <Form.Item label="备注" extra={'备注信息仅后台可见，不会向用户展示，最多500字'}>
          {getFieldDecorator('remark', {
            initialValue: remark,
            // rules: [
            //   max: 500,
            // ],
          })(
            <Input.TextArea rows={5} maxLength={500} placeholder="请输入备注信息"></Input.TextArea>
          )}
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'ReportForm' })(forwardRef<any, any>(ReportForm));
