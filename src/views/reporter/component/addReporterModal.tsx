import { Button, Form, Icon, InputNumber, Modal, Select, Spin, Tooltip, message } from 'antd'
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react'
import _, { set } from "lodash";
import { reportApi, communityApi } from '@app/api';
import "../styles/reportDrawer.scss"

const AddReporterModal = (props: any, ref: any) => {

  const [loading, setLoading] = useState(false)
  const [accountOptions, setAccountOptions] = useState([])
  const [reporter, setReporter] = useState(undefined)

  useEffect(() => {
    if (!!props.formContent && !!props.formContent.reporter_id) {
      setReporter(props.formContent.reporter_id)
      handleAccountSearch(props.formContent.reporter_id)
    } else {
      setReporter(undefined)
      handleAccountSearch('')
    }
  }, [props.formContent])


  const handleSubmit = (e: any) => {
    e.preventDefault();

    if (!!reporter) {
      setLoading(true)
      const parmas = {
        id: props.formContent?.id,
        biz_type: props.formContent?.biz_type,
        reporter_id: reporter
      }
      reportApi.updateReporter(parmas).then((res: any) => {
        message.success('添加成功');
        setLoading(false)

        props.onOk && props.onOk()
      }).catch(() => {
        // message.error('添加失败');
        setLoading(false)
      })
    } else {
      message.error('请选择记者');
    }
  }

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val) {
      setAccountOptions([])
      return
    }
    // type: 1, current: 1, size: 50, biz_type: props.formContent?.biz_type,
    communityApi.searchAdminUser({ keyword: val })
      .then(({ data }) => {
        const { user_list } = data as any
        setAccountOptions(user_list || [])
      })
      .catch(() => {
      })
  }, 500)

  const handleAccountChange = (val: any) => {
    setReporter(val)
  }

  return <Modal
    width={500}
    visible={props.visible}
    title="指派记者"
    key={props.skey}
    onCancel={() => { if (!loading) { props.onCancel && props.onCancel() } }}
    onOk={handleSubmit}
    maskClosable={false}
    destroyOnClose={true}
    confirmLoading={loading}
  >
    <Spin spinning={loading}>
      <Select style={{ width: '100%' }}
        // defaultValue={props.formContent?.reporter_id}
        value={reporter}
        placeholder="输入记者姓名或8531账号"
        onSearch={handleAccountSearch}
        showSearch
        onChange={handleAccountChange}
        filterOption={false}
      >
        {accountOptions.map((item: any) =>
          <Select.Option
            className={'reporter-item'}
            key={item.id}
            value={item.user_name}>
            {item.department?.length > 0 ? `${item.department}>` : ''}{item.name}（{item.user_name}）
          </Select.Option>)}
      </Select>
    </Spin>
  </Modal>

}

export default forwardRef<any, any>(AddReporterModal);
