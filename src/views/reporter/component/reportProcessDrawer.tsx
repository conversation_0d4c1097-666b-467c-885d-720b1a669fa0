import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react'

import { reportApi } from '@app/api';
import '@components/business/styles/business.scss'
import { setMLoading } from '@app/utils/utils';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { Drawer, NewTable } from '@app/components/common';
import '../styles/reportDrawer.scss'
import { TableList } from '@app/types';
import moment from 'moment';
import { PermA, PermButton } from '@app/components/permItems';
import { Button, Divider, Modal, message } from 'antd';
import useTable from '@app/utils/useTable';
import AddProcessModal from './addProcessModal';

const ReportProcessDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const [detail, setDetail] = useState<any>(null)
  const [addProcessModel, setAddProcessModel] = useState<any>({
    visible: false,
    skey: Date.now(),
    formContent: null,
  })

  const getFilter = () => {
    return {
      report_id: props.formContent?.id,
      biz_type: props.formContent?.biz_type
    }
  }

  const { tableList, loading, getTableList } = useTable({
    api: reportApi.reportProcessList,
    filter: getFilter,
    index: 'list'
  })
  // useImperativeHandle(
  //   ref,
  //   () => ({
  //     ...props.form,
  //     doSubmit
  //   }),
  //   []
  // );

  useEffect(() => {
    if (props.visible) {
      getTableList()
    }
  }, [props.visible])

  const getSeq = (i: number) => ((tableList?.current ?? 0) - 1) * (tableList?.size ?? 0) + i + 1;

  // const getDetail = () => {
  //   dispatch(setConfig({ mLoading: true }))
  //   reportApi.getOriginalReportDetail({ ...props.formContent }).then((data: any) => {
  //     dispatch(setConfig({ mLoading: false }))
  //     setDetail(data.detail || {})
  //   }).catch((e) => {
  //     dispatch(setConfig({ mLoading: false }))
  //   })
  // }

  const handleTableListChange = (v: TableList) => {
    // setTableList(v)
  }

  const editProcess = (record: any) => {
    setAddProcessModel({
      visible: true,
      skey: Date.now(),
      formContent: {
        biz_type: props.formContent?.biz_type,
        id: record.id,
      },
      record
    })
  }

  const deleteProcess = (record: any) => {
    Modal.confirm({
      title: '确定删除该进展？',
      onOk: () => {
        // setLoading(this, true);
        reportApi
          .deleteReportProcess({ id: record.id, biz_type: props.formContent?.biz_type })
          .then(() => {
            message.success('操作成功');
            // setLoading(this, false);
            // this.getData();
            getTableList()
            props.onReload && props.onReload()
          })
          .catch(() => {

          });
      },
    });
  }

  const addCustomProcess = () => {
    setAddProcessModel({
      visible: true,
      skey: Date.now(),
      formContent: {
        report_id: props.formContent?.id,
        biz_type: props.formContent?.biz_type
      },
      record: null
    })
  }

  const columns = [
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      width: 70,
    },
    {
      title: '进展时间',
      dataIndex: 'process_time',
      width: 100,
      render: (text: any, record: any) => {
        return <div style={{ whiteSpace: 'normal' }}>
          <div>{moment(text).format('YYYY-MM-DD')}</div>
          <div>{moment(text).format('HH:mm:ss')}</div>
        </div>
      }
    },
    {
      title: '进展说明',
      dataIndex: 'desc',
      // width: 90,
    },
    {
      title: '对应操作',
      key: 'type_name',
      dataIndex: 'type_name',
      width: 150,
      // render: (text: any, record: any) => (
      //   <span>{text}</span>
      //   // onClick={this.toMlf.bind(this, record.id, 'mlf_detail_url')}
      //   // <a onClick={() => titleClick(record)} className="list-title">
      //   //   {text}
      //   // </a>
      // ),
    },
    {
      title: '最后操作人',
      dataIndex: 'updated_by',
      width: 90,
    },
    {
      title: '最后操作时间',
      dataIndex: 'updated_at',
      width: 120,
      render: (text: any, record: any) => {
        return <div style={{ whiteSpace: 'normal' }}>
          <div>{moment(text).format('YYYY-MM-DD')}</div>
          <div>{moment(text).format('HH:mm:ss')}</div>
        </div>
      }
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any, i: number) => (
        <span>
          <PermA perm={`report:${props.formContent?.biz_type}:process_edit`} onClick={() => editProcess(record)}>
            编辑
          </PermA>
          {record.type == 0 && <Divider type="vertical" />}
          {record.type == 0 && <PermA perm={`report:${props.formContent?.biz_type}:process_edit`} onClick={() => deleteProcess(record)}>
            删除
          </PermA>}
        </span>
      ),
      width: 110,
    },
  ];

  return <Drawer
    title="进展管理"
    visible={props.visible}
    skey={props.skey}
    onClose={props.onClose}
    closeText='关闭'
    maskClosable={true}
  >
    {props.formContent?.status == 1 && <div style={{ marginBottom: 20 }}>
      <PermButton perm={`report:${props.formContent?.biz_type}:process_edit`} onClick={addCustomProcess}>添加自定义进展</PermButton>
    </div>}
    <NewTable
      columns={columns}
      rowKey="id"
      pagination={false}
      getTableList={getTableList}
      loading={loading}
      tableList={tableList}
    />

    <AddProcessModal
      {...addProcessModel}
      onCancel={() => setAddProcessModel({ visible: false })}
      onOk={() => {
        setAddProcessModel({ visible: false })
        getTableList()
        props.onReload && props.onReload()
      }}
    >
    </AddProcessModal>
  </Drawer>
}

export default forwardRef<any, any>(ReportProcessDrawer);
