import { Button, Form, Icon, InputNumber, Modal, Radio, Spin, Tooltip, message } from 'antd'
import React, { forwardRef, useImperativeHandle, useState } from 'react'

import { reportApi } from '@app/api';

const passAuditModal = (props: any, ref: any) => {

  const [loading, setLoading] = useState(false)
  const { getFieldDecorator } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const pass = (result: boolean) => {
    setLoading(true)
    const params: any = { id: props.formContent?.id, biz_type: props.formContent?.biz_type, status: 1 }
    if (result) {
      params['reporter_id'] = props.formContent?.journalist_user_uname
    }
    reportApi
      .changeAuditStatus(params)
      .then(() => {
        message.success('操作成功');
        setLoading(false)
        props.onOk && props.onOk()
      })
      .catch(() => {
        setLoading(false)
      });

  }

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true)
        let promise = null
        if (props.type == 0) {
          // 修改领域
          const parmas = {
            ...values,
            id: props.formContent.id,
            biz_type: props.formContent.biz_type
          }
          promise = reportApi.updateReportClassify(parmas)
        } else {
          const params = {
            ...values,
            id: props.formContent.id,
            biz_type: props.formContent.biz_type,
            status: 1
          }
          if (params.classify_id == -1) {
            delete params.classify_id
          }
          promise = reportApi.changeAuditStatus(params)
        }
        promise.then((res: any) => {
          message.success(props.type == 0 ? '修改成功' : '审核成功');
          setLoading(false)
          props.onOk && props.onOk(props.type)
        }).catch(() => {
          // message.error('添加失败');
          setLoading(false)
        })
      } else {
        message.error('请检查表单内容');
        setLoading(false)
      }
    });
  }

  return <Modal
    width={500}
    visible={props.visible}
    title='审核通过'
    key={props.skey}
    footer={<div>
      <Button onClick={() => { if (!loading) { props.onCancel && props.onCancel() } }}>取消</Button>
      <Button onClick={() => pass(false)}>仅审核通过</Button>
      <Button type='danger' onClick={() => pass(true)}>通过并指派</Button>
    </div>}
    onCancel={() => { if (!loading) { props.onCancel && props.onCancel() } }}
    // onOk={handleSubmit}
    maskClosable={false}
    destroyOnClose={true}
  >
    <div>该报料的对象是{`${['', '帮办记者', '帮帮团', '联盟记者'][props.formContent?.journalist_user_type]}-${props.formContent?.journalist_user_name}`}，是否审核通过并直接指派给ta？</div>
  </Modal>

}

export default Form.create<any>({ name: 'passAuditModal' })(forwardRef<any, any>(passAuditModal));
