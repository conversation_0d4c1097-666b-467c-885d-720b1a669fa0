import React, { useEffect, useLayoutEffect, useRef, useState } from 'react'
import '../styles/reportDrawer.scss'

const EllipsisText = (props: any) => {

  const {
    text = '',
    lineHeight = 21,
    maxLine = 2
  } = props

  const textRef = useRef<any>(null)
  // -1 不显示  0 收起状态  1  展开状态
  const [textState, setTextState] = useState(-1)

  useEffect(() => {
    const maxHeight = lineHeight * maxLine
    const expandAllEl = textRef.current
    if (expandAllEl && expandAllEl.offsetHeight > maxHeight) {
      setTextState(0)
    } else {
      setTextState(-1)
    }
  }, [text])

  return <div>
    <div className='ellipsis_text' ref={textRef} style={{
      WebkitLineClamp: textState == 0 ? maxLine : 'initial',
      lineHeight: `${lineHeight}px`,
    }}>{text || ''}</div>

    {textState >= 0 && <a onClick={() => {
      if (textState == 0) {
        setTextState(1)
      } else {
        setTextState(0)
      }
    }}>{textState == 0 ? '展开' : '收起'}</a>}
  </div>
}


export default EllipsisText;