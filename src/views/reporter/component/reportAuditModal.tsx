import { Form, Input, Modal, Select, Spin, message } from 'antd'
import React, { forwardRef, useImperativeHandle, useState } from 'react'

import { reportApi } from '@app/api';

const ReportAuditModal = (props: any, ref: any) => {

  const [loading, setLoading] = useState(false)
  const { getFieldDecorator } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true)
        const parmas = {
          ...values,
          id: props.formContent.id,
          biz_type: props.formContent.biz_type,
          status: props.type == 0 ? 2 : 10
        }
        reportApi.changeAuditStatus(parmas).then((res: any) => {
          message.success('操作成功');
          setLoading(false)

          props.onOk && props.onOk(props.type)
        }).catch(() => {
          // message.error('添加失败');
          setLoading(false)
        })
      } else {
        message.error('请检查表单内容');
        setLoading(false)
      }
    });
  }

  return <Modal
    width={500}
    visible={props.visible}
    title={props.type == 0 ? "审核不通过" : "删除"}
    key={props.skey}
    onCancel={() => { if (!loading) { props.onCancel && props.onCancel() } }}
    onOk={handleSubmit}
    maskClosable={false}
    destroyOnClose={true}
  >
    <Spin spinning={false}>
      <Form {...formLayout}>

        <Form.Item label="原因类型">
          {getFieldDecorator('reason', {
            // initialValue: 0,
            rules: [
              {
                required: true,
                message: '请选择原因',
              }
            ]
          })(
            <Select placeholder="请选择原因">
              <Select.Option value={0}>虚假报料</Select.Option>
              <Select.Option value={1}>违反相关法律规定</Select.Option>
            </Select>
          )}
        </Form.Item>

        <Form.Item label="备注说明">
          {getFieldDecorator('remark', {
            initialValue: "",
            rules: [
              {
                max: 50,
                message: '最多50字',
              },
            ],
          })(<Input.TextArea placeholder="选填，补充说明原因，最多50字" rows={3} />)}
        </Form.Item>

      </Form>
    </Spin>
  </Modal>

}

export default Form.create<any>({ name: 'ReportAuditModal' })(forwardRef<any, any>(ReportAuditModal));
