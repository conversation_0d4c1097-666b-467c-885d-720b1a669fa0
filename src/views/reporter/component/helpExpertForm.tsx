import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import {
  Button,
  Dropdown,
  Form,
  Icon,
  Input,
  InputNumber,
  Menu,
  Modal,
  Radio,
  Select,
  Spin,
  Switch,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { reportApi, communityApi } from '@app/api';
import '@components/business/styles/business.scss';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { Drawer, ImageUploader, VideoUploader } from '@app/components/common';
import ImgUploadList from '@app/components/common/imgUploadList';
import _, { set } from 'lodash';
import { ColorSelectButton } from '@app/components/business/ColorSelectModal';

function preview(colorData: any) {
  const { bottom_color } = colorData;
  return (
    <div className="report_expert_preview" style={{ background: bottom_color }}>
      标签文字
    </div>
  );
}

function previewBgColor(colorData: any) {
  const { bottom_color } = colorData;
  return (
    <div
      className="report_expert_bg_preview"
      style={{ background: `linear-gradient( 180deg, ${bottom_color} 0%, #FFFFFF 57%)` }}
    >
      <img src="/assets/user_default_avatar.png"></img>
      <div className="title">专家名称</div>
      <div className="desc">专家一句话介绍</div>
    </div>
  );
}

const HelpExpertForm = (props: any, ref: any) => {
  const dispatch = useDispatch();
  console.log('props.formContent', props.formContent);
  const {
    nick_name = undefined,
    information = '',
    head_img = '',
    classify_id = undefined,
    uid = '',
    short_desc = '',
    id = '',
    border_color = '',
    tag_color = '',
    tag_name = '',
    tagSwitch = tag_color ? true : false,
    colorSwitch = border_color ? true : false,
  } = props.formContent || {};
  console.log('xxx', tag_color, border_color);
  const [accountOptions, setAccountOptions] = useState([]);
  const { getFieldDecorator, getFieldValue } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
      doSubmit,
    }),
    []
  );

  // useEffect(() => {
  // if (!!uid) {
  // handleAccountSearch(uid)
  // }
  // }, [uid])

  useEffect(() => {
    if (props.visible) {
      setAccountOptions([]);
    }
  }, [props.visible]);

  const doSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body: any = { ...values, type: props.type };

        if (body.nick_name) {
          body.nick_name = body.nick_name?.trim();
        }

        if (body.short_desc) {
          body.short_desc = body.short_desc?.trim();
        }

        if (body.information) {
          body.information = body.information?.trim();
        }

        if (!!id) {
          body.id = id;
        }

        if (body.tag_color) {
          body.tag_color = body.tag_color.bottom_color;
        }

        if (body.border_color) {
          body.border_color = body.border_color.bottom_color;
        }

        dispatch(setConfig({ mLoading: true }));
        (!!props.formContent ? reportApi.updateReportUser : reportApi.createReportUser)(body)
          .then(() => {
            dispatch(setConfig({ mLoading: false }));
            message.success('操作成功');
            props.onEnd();
          })
          .catch(() => {
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val?.trim()) {
      setAccountOptions([]);
      return;
    }

    communityApi
      .recommendAccount_Search({ keyword: val })
      .then(({ data }) => {
        const { list } = data as any;
        setAccountOptions(list || []);
      })
      .catch(() => {});
  }, 500);

  return (
    <Drawer
      title={`${!props.formContent ? '添加' : '编辑'}${props.type == 2 ? '专家' : '联盟记者'}`}
      visible={props.visible}
      skey={props.skey}
      onClose={props.onClose}
      onOk={doSubmit}
      okText="保存"
      // footer={((<><Button style={{ width: 100, marginRight: 8 }} onClick={props.onClose}>取消</Button>
      //   <Dropdown trigger={['hover']} overlay={menu} placement="topCenter">
      //     <PermButton style={{ width: 100 }} type="primary" perm='' onClick={props.onOk}>
      //       保存<Icon type="up" />
      //     </PermButton>
      //   </Dropdown>

      // </>))}
    >
      <Form {...formLayout}>
        {/* <Form.Item label="用户昵称">
        {getFieldDecorator('account_id', {
          rules: [{
            required: true,
            message: '请输入用户昵称',
          }],
        })(<Select style={{ width: '100%' }}
          placeholder="请输入用户昵称或小潮号"
          onSearch={handleAccountSearch}
          showSearch
          disabled={!!props.formContent}
          filterOption={false}
        >

          {accountOptions.map((item: any) =>
            <Select.Option
              key={item.id}
              value={item.id}>
              {item.nick_name}-小潮号：{item.chao_id}
            </Select.Option>)}
        </Select>)}
      </Form.Item> */}

        <Form.Item label="名称">
          {getFieldDecorator('nick_name', {
            initialValue: nick_name,
            rules: [
              {
                required: true,
                whitespace: true,
                message: '请输入名称',
              },
              {
                max: 15,
                message: '最多15字',
              },
            ],
          })(<Input placeholder="请输入名称，最多15个字"></Input>)}
        </Form.Item>

        <Form.Item label="头像" extra={'支持jpg,jpeg,png图片格式，比例为1:1'}>
          {getFieldDecorator('head_img', {
            initialValue: head_img,
            rules: [
              {
                required: true,
                message: '请上传头像',
              },
            ],
          })(
            <ImageUploader
              ratio={1}
              // needMz={true}
              // isCutting={true}
              accept={['image/jpeg', 'image/png', 'image/jpg']}
            />
          )}
        </Form.Item>

        <Form.Item label="一句话介绍">
          {getFieldDecorator('short_desc', {
            initialValue: short_desc,
            rules: [
              {
                max: 20,
                message: '最多20字',
                whitespace: true,
              },
            ],
          })(<Input placeholder="介绍单位/职位等信息，最多20字，选填"></Input>)}
        </Form.Item>

        <Form.Item label="个人简介">
          {getFieldDecorator('information', {
            initialValue: information,
            rules: [
              {
                required: true,
                whitespace: true,
                message: '请输入个人简介',
              },
              {
                max: 1000,
                message: '最多1000字',
              },
            ],
          })(
            <Input.TextArea
              rows={5}
              placeholder="请输入个人简介，支持换行，最多1000字"
            ></Input.TextArea>
          )}
        </Form.Item>

        {props.type == 2 && (
          <>
            <Form.Item
              label={
                <span style={{ position: 'relative' }}>
                  <Tooltip
                    overlayStyle={{ maxWidth: 400 }}
                    title={
                      <img
                        src="/assets/report_expert_color_tips.png"
                        width={300}
                        height={157.6}
                      ></img>
                    }
                  >
                    <Icon
                      type="question-circle"
                      style={{ position: 'absolute', left: -35, top: 0 }}
                    />
                  </Tooltip>
                  自定义颜色
                </span>
              }
            >
              {getFieldDecorator('colorSwitch', {
                initialValue: colorSwitch,
                valuePropName: 'checked',
                rules: [
                  {
                    required: true,
                    message: '请选择',
                  },
                ],
              })(<Switch></Switch>)}
            </Form.Item>
            {getFieldValue('colorSwitch') && (
              <Form.Item label="卡片颜色">
                {getFieldDecorator('border_color', {
                  initialValue: { bottom_color: border_color },
                  rules: [
                    {
                      required: true,
                      message: '请选择颜色',
                      validator: (rule: any, value: any, callback: any) => {
                        if (!!value?.bottom_color) {
                          callback();
                          return
                        }
                        callback('请选择颜色');
                      },
                    },
                  ],
                })(
                  <ColorSelectButton
                    onlyManual={true}
                    picUrl={''}
                    preview={(colorData: any) => previewBgColor(colorData)}
                    colors={[
                      {
                        name: '橙色',
                        color: '#FFF4ED',
                      },
                      {
                        name: '蓝色',
                        color: '#E8F0FF',
                      },
                      {
                        name: '黄色',
                        color: '#FFF6E3',
                      },
                      {
                        name: '绿色',
                        color: '#F7FFEE',
                      },
                      {
                        name: '紫色',
                        color: '#F0EBFF',
                      },
                      {
                        name: '桃红色',
                        color: '#FFEFF5',
                      },
                      {
                        name: '青色',
                        color: '#EAF9F9',
                      },
                    ]}
                  />
                )}
              </Form.Item>
            )}

            <Form.Item
              label={
                <span style={{ position: 'relative' }}>
                  <Tooltip
                    overlayStyle={{ maxWidth: 400 }}
                    title={
                      <img
                        src="/assets/report_expert_label_tips.png"
                        width={300}
                        height={157.6}
                      ></img>
                    }
                  >
                    <Icon
                      type="question-circle"
                      style={{ position: 'absolute', left: -35, top: 0 }}
                    />
                  </Tooltip>
                  自定义标签
                </span>
              }
            >
              {getFieldDecorator('tagSwitch', {
                initialValue: tagSwitch,
                valuePropName: 'checked',
                rules: [
                  {
                    required: true,
                    message: '请选择',
                  },
                ],
              })(<Switch></Switch>)}
            </Form.Item>
            {getFieldValue('tagSwitch') && (
              <Form.Item label="标签文字">
                {getFieldDecorator('tag_name', {
                  initialValue: tag_name,
                  rules: [
                    {
                      required: true,
                      whitespace: true,
                      message: '请输入标签文字',
                    },
                    {
                      max: 6,
                      message: '最多6个字',
                    },
                  ],
                })(<Input placeholder="标签文字，最多6个字" />)}
              </Form.Item>
            )}
            {getFieldValue('tagSwitch') && (
              <Form.Item label="标签颜色">
                {getFieldDecorator('tag_color', {
                  initialValue: { bottom_color: tag_color },
                  rules: [
                    {
                      required: true,
                      message: '请选择颜色',
                      validator: (rule: any, value: any, callback: any) => {
                        if (!!value?.bottom_color) {
                          callback();
                          return
                        }
                        callback('请选择颜色');
                      },
                    },
                  ],
                })(
                  <ColorSelectButton
                    onlyManual={true}
                    picUrl={''}
                    preview={(colorData: any) => preview(colorData)}
                    colors={[
                      {
                        name: '橙色',
                        color: '#FF822C',
                      },
                      {
                        name: '蓝色',
                        color: '#3A7FFF',
                      },
                      {
                        name: '黄色',
                        color: '#FFB12C',
                      },
                      {
                        name: '绿色',
                        color: '#81C44A',
                      },
                      {
                        name: '紫色',
                        color: '#8157FF',
                      },
                      {
                        name: '桃红色',
                        color: '#FF2C7A',
                      },
                      {
                        name: '青色',
                        color: '#24B8B8',
                      },
                    ]}
                  />
                )}
              </Form.Item>
            )}
          </>
        )}
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'HelpExpertForm' })(forwardRef<any, any>(HelpExpertForm));
