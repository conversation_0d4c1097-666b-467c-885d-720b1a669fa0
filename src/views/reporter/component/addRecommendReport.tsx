import { Button, Form, Icon, InputNumber, Modal, Select, Spin, Tooltip, message } from 'antd'
import React, { forwardRef, useImperativeHandle, useState } from 'react'
import _, { set } from "lodash";
import { reportApi } from '@app/api';
import { reportTypeMap } from '@app/utils/utils';

const AddRecommendReport = (props: any, ref: any) => {

  const [loading, setLoading] = useState(false)
  const [accountOptions, setAccountOptions] = useState([])
  const [reporter, setReporter] = useState(null)

  const handleSubmit = (e: any) => {
    e.preventDefault();
    
    if (!!reporter) {
      setLoading(true)
      const parmas = {
        // id: props.formContent.id,
        biz_type: props.formContent?.biz_type,
        id: reporter
      }
      reportApi.addRecommendReport(parmas).then((res: any) => {
        message.success('添加成功');
        setLoading(false)

        props.onOk && props.onOk()
      }).catch(() => {
        // message.error('添加失败');
        setLoading(false)
      })
    } else {
      message.error('请选择报料');
    }
  }

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val) {
      setAccountOptions([])
      return
    }
    reportApi.searchRecommendReport({ keyword: val, biz_type: props.formContent?.biz_type, recommend: false })
      .then(({ data }) => {
        setAccountOptions(data?.list || [])
      })
      .catch(() => {
      })
  }, 500)

  const handleAccountChange = (val: any) => {
    setReporter(val)
  }

  return <Modal
    width={500}
    visible={props.visible}
    title="添加首页推荐"
    key={props.skey}
    onCancel={() => { if (!loading) { props.onCancel && props.onCancel() } }}
    onOk={handleSubmit}
    maskClosable={false}
    destroyOnClose={true}
    confirmLoading={loading}
  >
    <Spin spinning={loading}>
      <Select style={{ width: '100%' }}
        // defaultValue={props.formContent?.reporter_id}
        placeholder="输入标题或编号"
        onSearch={handleAccountSearch}
        showSearch
        onChange={handleAccountChange}
        filterOption={false}
      >
        {accountOptions.map((item: any) =>
          <Select.Option
            key={item.id}
            value={item.id}>
            {item.number}&nbsp;-（{reportTypeMap(item.report_type, '')}）|&nbsp;{item.title}
          </Select.Option>)}
      </Select>
    </Spin>
  </Modal>

}

export default forwardRef<any, any>(AddRecommendReport);
