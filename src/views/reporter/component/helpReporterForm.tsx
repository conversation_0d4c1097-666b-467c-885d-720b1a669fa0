import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import { Button, Dropdown, Form, Icon, Input, InputNumber, Menu, Modal, Radio, Select, Spin, Tooltip, message } from 'antd'
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react'

import { reportApi, communityApi } from '@app/api';
import '@components/business/styles/business.scss'
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { Drawer, ImageUploader, VideoUploader } from '@app/components/common';
import ImgUploadList from '@app/components/common/imgUploadList';
import _, { set } from "lodash";
import "../styles/reportDrawer.scss"

const classifyList = [
  { id: 1, name: '营商环境' },
  { id: 2, name: '医疗' },
  { id: 3, name: '教育' },
  { id: 4, name: '城建' },
  { id: 5, name: '交通' },
  { id: 6, name: '政务' },
  { id: 7, name: '金融' },
  { id: 8, name: '企业' },
  { id: 9, name: '就业' },
  { id: 10, name: '文娱' },
  { id: 11, name: '旅游' },
  { id: 12, name: '治安' },
  { id: 13, name: '环保' },
  { id: 14, name: '三农' }
]

const HelpReporterForm = (props: any, ref: any) => {
  const dispatch = useDispatch();

  const { real_name = undefined, nick_name = '', information = '', head_img = '', classify_id = undefined, uid = '' } = props.formContent || {}
  const [accountOptions, setAccountOptions] = useState([])

  const { getFieldDecorator } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
      doSubmit
    }),
    []
  );

  useEffect(() => {
    // if (!!uid) {
      handleAccountSearch(uid)
    // }
  }, [uid])

  useEffect(() => {
    if (props.visible) {

    }
  }, [props.visible])

  const doSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body: any = { ...values };
        delete body.real_name

        const arr = values.real_name?.split('（');
        body.real_name = arr?.[0]
        const last = arr?.[1]
        body.uid = last?.substring(0, last?.length - 1)
        body.type = 1
        
        if (!!props.formContent) {
          body.id = props.formContent.id
        }

        dispatch(setConfig({ mLoading: true }));
        (!!props.formContent ? reportApi.updateReportUser : reportApi.createReportUser)(body)
          .then(() => {
            dispatch(setConfig({ mLoading: false }))
            message.success('操作成功');
            props.onEnd();
          })
          .catch(() => {
            dispatch(setConfig({ mLoading: false }))
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  }

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val?.trim()) {
      setAccountOptions([])
      return
    }

    communityApi.searchAdminUser({ keyword: val })
      .then(({ data }) => {
        const { user_list } = data as any
        setAccountOptions(user_list || [])
      })
      .catch(() => {

      })
  }, 500)

  return <Drawer
    title={`${!!props.formContent ? '编辑' : '添加'}帮办记者`} 
    visible={props.visible}
    skey={props.skey}
    onClose={props.onClose}
    onOk={doSubmit}
    okText='保存'
  // footer={((<><Button style={{ width: 100, marginRight: 8 }} onClick={props.onClose}>取消</Button>
  //   <Dropdown trigger={['hover']} overlay={menu} placement="topCenter">
  //     <PermButton style={{ width: 100 }} type="primary" perm='' onClick={props.onOk}>
  //       保存<Icon type="up" />
  //     </PermButton>
  //   </Dropdown>

  // </>))}
  >
    <Form {...formLayout}>
      <Form.Item label="真实姓名">
        {getFieldDecorator('real_name', {
          initialValue: !!props.formContent ? `${real_name}（${uid}）` : undefined,
          rules: [{
            required: true,
            message: '请输入真实姓名',
          }],
        })(<Select style={{ width: '100%' }}
          // defaultValue={props.formContent?.reporter_id}
          placeholder="请输入真实姓名或8531账号"
          onSearch={handleAccountSearch}
          showSearch
          disabled={!!props.formContent}
          // onChange={handleAccountChange}
          filterOption={false}
        >
          {accountOptions.map((item: any) =>
            <Select.Option
              className='reporter-item'
              key={item.id}
              value={`${item.name}（${item.user_name}）`}>
              {item.department?.length > 0 ? `${item.department}>` : ''}{item.name}（{item.user_name}）
            </Select.Option>)}
        </Select>)}
      </Form.Item>

      <Form.Item label="记者名称">
        {getFieldDecorator('nick_name', {
          initialValue: nick_name,
          rules: [{
            required: true,
            message: '请输入记者名称',
            whitespace: true
          }, {
            max: 10,
            message: '最多10字'
          }
          ],
        })(<Input placeholder='请输入记者名称，最多10个字'></Input>)}
      </Form.Item>

      <Form.Item label="记者简介">
        {getFieldDecorator('information', {
          initialValue: information,
          rules: [{
            max: 50,
            message: '最多50字'
          }],
        })(<Input placeholder='最多50字，选填'></Input>)}
      </Form.Item>

      <Form.Item label="记者头像" extra={"支持jpg,jpeg,png图片格式，比例为1:1"}>
        {getFieldDecorator('head_img', {
          initialValue: head_img,
          rules: [
            {
              required: true,
              message: '请上传记者头像',
            },
          ],
        })(
          <ImageUploader
            ratio={1}
            // needMz={true}
            // isCutting={true}
            accept={['image/jpeg', 'image/png', 'image/jpg']}
          />
        )}
      </Form.Item>

      {/* <Form.Item label="领域分类">
        {getFieldDecorator('classify_id', {
          initialValue: classify_id,
          rules: [
            {
              required: true,
              message: '请选择领域'
            }
          ],
        })(<Radio.Group
          onChange={(e: any) => console.log('')}
          style={{ paddingLeft: 20 }}
        >
          {classifyList.map((v) => {
            return <Radio style={{ width: 90, height: 40, lineHeight: '40px' }} key={v.id} value={v.id}>{v.name}</Radio>
          })}

        </Radio.Group>)}
      </Form.Item> */}

    </Form>
  </Drawer>
}

export default Form.create<any>({ name: 'HelpReporterForm' })(forwardRef<any, any>(HelpReporterForm));
