import React, { forwardRef, useImperativeHandle, useState } from 'react'
import '../styles/reportDrawer.scss'
import { Row, Tag } from 'antd';
import { PhotoSlider } from 'react-photo-view';
import { bizTypeMap } from '@app/utils/utils';

const ReportDetail = (props: any, ref: any) => {

  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );
  const [imgPreviewVisible, setImgPreviewVisible] = useState(false)
  const [prevIndex, setPrevIndex] = useState(0)
  const { detail } = props

  let video: any = null
  const imgs = detail?.medias?.filter((item: any) => {
    if (item.type == 0) {
      return true
    }
    video = item
    return false
  })

  return <div className={`${props.className} report_wrapper`}>
    <h2>
      <Tag color="#1890ff" style={{ verticalAlign: 'text-top' }}>
        {bizTypeMap(detail?.biz_type ?? 0, '')}
      </Tag>
      {detail?.title}
    </h2>
    <p className='content' dangerouslySetInnerHTML={{ __html: detail?.content }}>
    </p>
    {video && <Row className='row' key={`${video}-`}>
      <video
        src={video.url}
        poster={video.cover_url}
        controls
        playsInline
        webkit-playsinline="true"
        x5-video-player-fullscreen="false"
      >
      </video>
    </Row>}

    {imgs?.map((v: any, i: any) => {
      return <Row className='row' key={`${v}-${i}`}>
        <img src={v.url} style={{ cursor: 'pointer' }} onClick={() => {
          setPrevIndex(i)
          setImgPreviewVisible(true)
        }}></img>
      </Row>
    })}
    {
      imgs?.length > 0 && <PhotoSlider maskOpacity={0.5}
        images={imgs?.map((v: any) => ({ src: v.url, key: v.url })) || []}
        visible={imgPreviewVisible}
        onClose={() => setImgPreviewVisible(false)}
        index={prevIndex}
        onIndexChange={(index) => setPrevIndex(index)}
      />
    }

  </div>
}

export default forwardRef<any, any>(ReportDetail);
