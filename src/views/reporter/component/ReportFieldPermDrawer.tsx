import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import { Button, Checkbox, Col, Dropdown, Form, Icon, Input, InputNumber, Menu, Modal, Radio, Row, Select, Spin, Tooltip, message } from 'antd'
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react'

import { reportApi, communityApi } from '@app/api';
import '@components/business/styles/business.scss'
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { Drawer, ImageUploader, VideoUploader } from '@app/components/common';
import ImgUploadList from '@app/components/common/imgUploadList';
import _, { set } from "lodash";
import "../styles/reportDrawer.scss"

const ReportFieldPermDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();

  const { user_name = undefined, field_list = [], uid = '' } = props.formContent || {}
  const scope = (field_list.length == 0 || field_list.filter((item: any) => item.id == -1).length > 0 ) ? 0 : 1
  const fieldIdsList = field_list.filter((item: any) => item.id != -1).map((item: any) => item.id)
  const [accountOptions, setAccountOptions] = useState([])
  const [fieldsList, setFieldsList] = useState([])

  const { getFieldDecorator, getFieldValue } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
      doSubmit
    }),
    []
  );

  useEffect(() => {
    // if (!!uid) {
    handleAccountSearch(user_name)
    // }
  }, [user_name])

  useEffect(() => {
    if (props.visible) {
      getFieldsList()
    }
  }, [props.visible])

  const getFieldsList = () => {
    reportApi.getAllReportFieldList({ }).then((res: any) => {
      setFieldsList(res.data.list.records || [])
    }).catch(() => {
    })
  }

  const doSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body: any = {};
        body.user_name = values.user_name
        if (values.scope == 0) {
          body.field_list = '-1'
        } else {
          body.field_list = values.fieldIdsList.join(',')
        }
        dispatch(setConfig({ mLoading: true }));
        (!!props.formContent ? reportApi.eidtReportFieldPerm : reportApi.createReportFieldPerm)(body)
          .then(() => {
            dispatch(setConfig({ mLoading: false }))
            message.success('操作成功');
            props.onEnd();
          })
          .catch(() => {
            dispatch(setConfig({ mLoading: false }))
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  }

  const formLayout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 21 },
  };

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val?.trim()) {
      setAccountOptions([])
      return
    }

    communityApi.searchAdminUser({ keyword: val })
      .then(({ data }) => {
        const { user_list } = data as any
        setAccountOptions(user_list || [])
      })
      .catch(() => {

      })
  }, 500)

  return <Drawer
    title={`${!!props.formContent ? '编辑' : '添加'}账号权限`}
    visible={props.visible}
    skey={props.skey}
    onClose={props.onClose}
    onOk={doSubmit}
    okText='保存'
  // footer={((<><Button style={{ width: 100, marginRight: 8 }} onClick={props.onClose}>取消</Button>
  //   <Dropdown trigger={['hover']} overlay={menu} placement="topCenter">
  //     <PermButton style={{ width: 100 }} type="primary" perm='' onClick={props.onOk}>
  //       保存<Icon type="up" />
  //     </PermButton>
  //   </Dropdown>

  // </>))}
  >
    <Form {...formLayout}>
      <Form.Item label="真实姓名">
        {getFieldDecorator('user_name', {
          initialValue: !!props.formContent ? `${user_name}` : undefined,
          rules: [{
            required: true,
            message: '请输入姓名',
          }],
        })(<Select style={{ width: '100%' }}
          // defaultValue={props.formContent?.reporter_id}
          placeholder="请输入姓名或8531账号"
          onSearch={handleAccountSearch}
          showSearch
          disabled={!!props.formContent}
          // onChange={handleAccountChange}
          filterOption={false}
        >
          {accountOptions.map((item: any) =>
            <Select.Option
              className='reporter-item'
              key={item.id}
              value={`${item.user_name}`}>
              {item.department?.length > 0 ? `${item.department}>` : ''}{item.name}（{item.user_name}）
            </Select.Option>)}
        </Select>)}
      </Form.Item>

      <Form.Item label="权限范围">
        {getFieldDecorator('scope', {
          initialValue: scope,
          rules: [
            {
              required: true,
              message: '请选择'
            }
          ],
        })(<Radio.Group
        // onChange={(e: any) => console.log('')}
        >
          <Radio key={0} value={0}>全部报料&nbsp;<Tooltip title='包括所有选择了领域分类或无领域的报料'>
            <Icon type="question-circle" />
          </Tooltip></Radio>
          <Radio key={1} value={1}>部分报料&nbsp;</Radio>
        </Radio.Group>)}
      </Form.Item>

      {getFieldValue('scope') == 1 && <Form.Item label="领域分类">
        {getFieldDecorator('fieldIdsList', {
          initialValue: fieldIdsList,
          rules: [
            {
              required: true,
              message: '请选择领域'
            }
          ],
        })(<Checkbox.Group
          style={{ width: '100%' }}
          onChange={(e: any) => console.log('')}
        >

          <Row>
            <Col span={6} key={0}>
              <Checkbox style={{ lineHeight: '40px' }} key={0} value={0}>无领域</Checkbox>
            </Col>
            {fieldsList.map((v: any) => {
              return <Col span={6} key={v.id}>
                <Checkbox style={{ lineHeight: '40px' }} key={v.id} value={v.id}>{v.name}</Checkbox>
              </Col>
            })}
          </Row>
        </Checkbox.Group>)}
      </Form.Item>}


    </Form>
  </Drawer>
}

export default Form.create<any>({ name: 'ReportFieldPermDrawer' })(forwardRef<any, any>(ReportFieldPermDrawer));
