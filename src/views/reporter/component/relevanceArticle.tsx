import { Button, Form, Icon, InputNumber, Modal, Select, Spin, Tooltip, message } from 'antd'
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react'
import _ from "lodash";
import { reportApi, recommendApi } from '@app/api';

const AddReporterModal = (props: any, ref: any) => {

  const [loading, setLoading] = useState(false)
  const [optionData, setOptionData] = useState<any>([])
  const [article, setArticle] = useState(undefined)

  useEffect(() => {
    if (!!props.formContent && !!props.formContent.related_article_id) {
      setArticle(props.formContent.related_article_id)
      handleAccountSearch(props.formContent.related_article_id)
    } else {
      setArticle(undefined)
      handleAccountSearch('')
    }
  }, [props.formContent])

  const handleSubmit = (e: any) => {
    e.preventDefault();

    if (!!article) {
      setLoading(true)
      const parmas: any = {
        id: props.formContent?.id,
        biz_type: props.formContent?.biz_type,
        related_article_id: article
      }
      
      reportApi.reportRelatedArticle(parmas).then((res: any) => {
        message.success('操作成功');
        setLoading(false)

        props.onOk && props.onOk()
      }).catch(() => {
        // message.error('添加失败');
        setLoading(false)
      })
    } else {
      message.error('请选择稿件');
    }
  }

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val) {
      setOptionData([])
      return
    }

    let data = { keyword: val.toString(), doc_types: '2,3,4,5,8,9' }
    recommendApi.searchManuscript(data)
      .then((res: any) => {
        let article_list: any = [...res.data.article_list]
        setOptionData(article_list)
      })
      .catch(() => {
      })
  }, 500)

  const handleAccountChange = (val: any) => {
    setArticle(val)
  }

  return <Modal
    width={500}
    visible={props.visible}
    title="关联稿件"
    key={props.skey}
    onCancel={() => { if (!loading) { props.onCancel && props.onCancel() } }}
    onOk={handleSubmit}
    maskClosable={false}
    destroyOnClose={true}
    confirmLoading={loading}
  >
    <Spin spinning={loading}>
      <Select style={{ width: '100%' }}
        value={article}
        placeholder="输入标题或ID关联稿件"
        onSearch={handleAccountSearch}
        showSearch
        onChange={handleAccountChange}
        filterOption={false}
        allowClear={true}
      >
        {optionData.map((d: any) =>
          <Select.Option
            key={d.id}
            value={`${d.id}`}
          >{`${d.id} - 【${d.channel_name}】- ${d.list_title}`}
          </Select.Option>)}
      </Select>
    </Spin>
  </Modal>

}

export default forwardRef<any, any>(AddReporterModal);
