import { Button, Form, Icon, Input, InputNumber, Modal, Radio, Spin, Tooltip, message } from 'antd'
import React, { forwardRef, useImperativeHandle, useState } from 'react'

import { reportApi } from '@app/api';

const AddProcessModal = (props: any, ref: any) => {

  const [loading, setLoading] = useState(false)
  const { getFieldDecorator } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true)
        let promise = null
        const parmas = {
          ...props.formContent,
          ...values,
        }        
        if (!props.record) {
          // 新增
          promise = reportApi.addReportProcess(parmas)
        } else {
          promise = reportApi.updateReportProcess(parmas)
        }
        promise.then((res: any) => {
          message.success(!props.record ? '新增成功' : '更新成功');
          setLoading(false)
          props.onOk && props.onOk()
        }).catch(() => {
          // message.error('添加失败');
          setLoading(false)
        })
      } else {
        message.error('请检查表单内容');
        setLoading(false)
      }
    });
  }

  return <Modal
    width={500}
    visible={props.visible}
    title={!props.record ? "添加自定义进展" : "编辑进展说明"}
    key={props.skey}
    onCancel={() => { if (!loading) { props.onCancel && props.onCancel() } }}
    onOk={handleSubmit}
    maskClosable={false}
    destroyOnClose={true}
  >
    <Spin spinning={false}>
      <Form {...formLayout}>

        <Form.Item label="进展说明">
          {getFieldDecorator('desc', {
            initialValue: props.record?.desc,
            rules: [
              {
                required: true,
                message: '请输入进展',
                whitespace: true
              },
              {
                max: 20,
                message: '最多20字'
              }
            ],
          })(<Input placeholder='最多20字'></Input>)}
        </Form.Item>
      </Form>
    </Spin>
  </Modal>

}

export default Form.create<any>({ name: 'AddProcessModal' })(forwardRef<any, any>(AddProcessModal));
