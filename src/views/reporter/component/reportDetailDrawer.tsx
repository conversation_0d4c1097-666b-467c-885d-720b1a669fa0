import { But<PERSON>, Modal, Spin, Tabs, Timeline, message } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { reportApi, opApi, userApi, releaseListApi } from '@app/api';
import '@components/business/styles/business.scss';
import { UserDetail, bizTypeMap, reportTypeMap, setMLoading } from '@app/utils/utils';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { Drawer } from '@app/components/common';
import ReportFormDrawer from './reportFormDrawer';
import '../styles/reportDrawer.scss';
import ReportDetail from './reportDetail';
import AddReporterModal from './addReporterModal';
import AddDomainModal from './addDomainModal';
import ReportAuditModal from './reportAuditModal';
import OriginalReportDetailDrawer from './originalReportDetailDrawer';
import ReportInfo from './reportInfo';
import RelevanceArticle from './relevanceArticle';
import ReportProcessDrawer from './reportProcessDrawer';
import moment from 'moment';
import uuid from 'uuid';
import ReportCommentDrawer from './reportCommentDrawer';
import { PermButton } from '@app/components/permItems';
import AddRemarkModal from './addRemarkModal';
import PassAuditModal from './passAuditModal';
import ChangeReportTimeModal from './changeReportTimeModal';

declare const BUILD_ENV: 'dev' | 'test' | 'prev' | 'prod' | 'testb';

const ReportDetailDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const [detail, setDetail] = useState<any>({});
  const [processList, setProcessList] = useState<any>([]);
  const [replyList, setReplyList] = useState<any>([]);
  const [reportFormDrawer, setReportFormDrawer] = useState<any>({
    visible: false,
    key: uuid(),
    formContent: null,
  });
  const [operateLog, setOperateLog] = useState<any>({
    visible: false,
    // key: uuid(),
    logs: null,
  });
  const [userDetail, setUserDetail] = useState<any>({
    visible: false,
    // key: uuid(),
    detail: null,
  });
  const [passWithReporter, setPassWithReporter] = useState<any>({
    visible: false,
    type: 0,
    formContent: null,
  });
  const [changeTimeModal, setChangeTimeModal] = useState<any>({
    visible: false,
    type: 0,
    formContent: null,
  });
  const [reportProcessDrawer, setReportProcessDrawer] = useState<any>({
    visible: false,
    // skey: uuid(),
    formContent: null,
  });
  const [reportCommentDrawer, setReportCommentDrawer] = useState<any>({
    visible: false,
    // skey: uuid(),
    formContent: null,
  });
  const [reporterModal, setReporterModal] = useState<any>({
    visible: false,
    // skey: uuid(),
    formContent: null,
  });
  const [remarkModal, setRemarkModal] = useState<any>({
    visible: false,
    // skey: uuid(),
    formContent: null,
  });

  const [audioInfoDrawer, setAudioInfoDrawer] = useState<any>({
    visible: false,
  });

  const [relevanceArticleModal, setRelevanceArticleModal] = useState<any>({
    visible: false,
    // skey: uuid(),
    formContent: null,
  });
  const [addDomainModal, setAddDomainModal] = useState<any>({
    visible: false,
    // skey: uuid(),
    formContent: null,
  });
  const [reporterAuditModal, setReporterAuditModal] = useState<any>({
    visible: false,
    // skey: uuid(),
    type: 0,
    formContent: null,
  });
  const [originalReporterDetail, setOriginalReporterDetail] = useState<any>({
    visible: false,
    // skey: uuid(),
    formContent: null,
  });

  useEffect(() => {
    if (props.visible) {
      getDetail();
    }
  }, [props.visible]);

  const getDetail = () => {
    let arr: any = [
      reportApi.getReportDetail({ id: props.record.id, biz_type: props.record.biz_type }),
    ];
    if (props.type == 1) {
      arr = arr.concat([
        reportApi.reportProcessList({
          report_id: props.record.id,
          biz_type: props.record.biz_type,
        }),
        reportApi.reportReplyList({
          report_id: props.record.id,
          biz_type: props.record.biz_type,
        }),
      ]);
    }

    dispatch(setConfig({ mLoading: true }));
    Promise.allSettled(arr)
      .then((values: any) => {
        dispatch(setConfig({ mLoading: false }));
        if (!!values?.[0]) {
          const obj = values?.[0];
          if (obj?.status == 'fulfilled') {
            setDetail(obj?.value?.data?.detail || {});
          } else if (obj?.reason?.code == 30201) {
            // 详情报错
            props.onReload && props.onReload();
            props.onClose && props.onClose();
            return;
          }
        }

        if (!!values?.[1]) {
          const obj = values?.[1];
          if (obj?.status == 'fulfilled') {
            setProcessList(obj?.value?.data?.list || []);
          }
        }

        if (!!values?.[2]) {
          const obj = values?.[2];
          if (obj?.status == 'fulfilled') {
            setReplyList(obj?.value?.data?.list || []);
          }
        }
      })
      .catch((e: any) => {
        dispatch(setConfig({ mLoading: false }));
      });
  };

  // useImperativeHandle(
  //   ref,
  //   () => ({
  //     ...props.form,
  //     doSubmit
  //   }),
  //   []
  // );

  // 跳转评论系统
  const toCommentSystem = () => {
    const host =
      {
        dev: 'https://testcomment.tianmunews.com',
        test: 'https://testcomment.tianmunews.com',
        prev: 'https://precomment.tianmunews.com',
        prod: 'https://comment.tianmunews.com',
        testb: 'https://testcomment.tianmunews.com',
      }[BUILD_ENV] || 'https://comment.tianmunews.com';

    const w = window.open();
    if (w) {
      w.location = `${host}/#/auditManagement/regularAudit?id=report_${props.record.id}`;
    }

    // releaseListApi
    //   .getCommentSystemUrl({ article_id: props.record.id })
    //   .then((r: any) => {
    //     if (w) {
    //       w.location = r.data.url;
    //     } else {
    //       message.error('浏览器可能拦截了新页面');
    //     }
    //   })
    //   .catch(() => {
    //     w && w.close();
    //   });
  };

  const handleAudit = (type: any) => {
    // setReporterAuditModal({
    //   visible: true,
    //   // skey: uuid(),
    //   type: type,
    //   formContent: props.record
    // })
  };

  const handleDelete = () => {
    Modal.confirm({
      title: '确定删除该报料？',
      onOk: () => {
        // setLoading(this, true);
        reportApi
          .changeAuditStatus({ id: props.record.id, status: 10, biz_type: props.record.biz_type })
          .then(() => {
            message.success('操作成功');
            // setLoading(this, false);
            // this.getData();
            // getTableList()
            getDetail();
            props.onReload && props.onReload();
            props.onClose && props.onClose();
          })
          .catch(() => {});
      },
    });
  };

  const handlePass = () => {
    if (!!detail.journalist_user_name && detail.journalist_user_type == 1 && !detail.reporter_id) {
      setPassWithReporter({
        visible: true,
        type: detail.biz_type,
        formContent: detail,
      });
      return;
    }

    Modal.confirm({
      title: '确定通过该报料？',
      onOk: () => {
        // setLoading(this, true);
        reportApi
          .changeAuditStatus({ id: props.record.id, status: 1, biz_type: props.record.biz_type })
          .then(() => {
            message.success('操作成功');
            // setLoading(this, false);
            // this.getData();
            // getTableList()
            getDetail();
            props.onReload && props.onReload();
            props.onClose && props.onClose();
          })
          .catch(() => {});
      },
    });
  };

  const handleNoPass = () => {
    Modal.confirm({
      title: '确定不通过该报料？',
      onOk: () => {
        // setLoading(this, true);
        reportApi
          .changeAuditStatus({ id: props.record.id, status: 2, biz_type: props.record.biz_type })
          .then(() => {
            message.success('操作成功');
            // setLoading(this, false);
            // this.getData();
            // getTableList()
            getDetail();
            props.onReload && props.onReload();
            props.onClose && props.onClose();
          })
          .catch(() => {});
      },
    });
  };

  const handleReview = () => {
    Modal.confirm({
      title: '确定将该报料重新设为【待审核】状态？',
      onOk: () => {
        // setLoading(this, true);
        reportApi
          .changeAuditStatus({ id: props.record.id, status: 0, biz_type: props.record.biz_type })
          .then(() => {
            message.success('操作成功');
            // setLoading(this, false);
            // this.getData();
            // getTableList()
            getDetail();
            props.onReload && props.onReload();
            props.onClose && props.onClose();
          })
          .catch(() => {});
      },
    });
  };

  const handleAddReporter = () => {
    setReporterModal({
      visible: true,
      // skey: uuid(),
      formContent: detail,
    });
  };

  const handleAddArticle = () => {
    setRelevanceArticleModal({
      visible: true,
      // skey: uuid(),
      formContent: detail,
    });
  };

  const changeReportFocusStatus = (type: any) => {
    Modal.confirm({
      title: `确定将该报料设为${type == 0 ? '持续关注' : '跟进中'}？`,
      onOk: () => {
        // setLoading(this, true);
        const request = type == 0 ? reportApi.setReportFocus : reportApi.setReportProcess;
        request({ id: detail.id, biz_type: props.record.biz_type })
          .then(() => {
            message.success('操作成功');
            // setLoading(this, false);
            // this.getData();
            // getTableList()
            getDetail();

            props.onReload && props.onReload();
          })
          .catch(() => {});
      },
    });
  };

  const showOriginalDetail = () => {
    setOriginalReporterDetail({
      visible: true,
      // skey: uuid(),
      formContent: props.record,
    });
  };

  const handleAddDomain = (type: any) => {
    setAddDomainModal({
      visible: true,
      // skey: uuid(),
      type: type,
      formContent: props.record,
    });
  };

  const editReport = () => {
    setReportFormDrawer({
      visible: true,
      key: uuid(),
      formContent: detail,
    });
  };

  const handleAuditResult = (type: any) => {
    setReporterAuditModal({ visible: false });
    if (type == 0) {
      // 审核不通过
    } else {
      // 删除
    }
    getDetail();

    props.onReload && props.onReload();
  };

  const handleAddDomainResult = (type: any) => {
    setAddDomainModal({ visible: false });
    if (type == 0) {
      // 修改领域
    } else {
      // 审核通过
    }
    getDetail();

    props.onReload && props.onReload();
  };

  // 操作日志
  const getOperateLog = () => {
    // if (
    //   this.props.session.permissions.indexOf(
    //     `admin_log:list`
    //   ) === -1
    // ) {
    //   return;
    // }
    opApi
      .getOperateLog({ target_id: props.record.id, type: 162 })
      .then((r: any) => {
        setOperateLog({
          visible: true,
          logs: r.data.admin_log_list,
          key: uuid(),
        });
      })
      .catch();
  };

  // 显示进展管理
  const showProcessDrawer = () => {
    setReportProcessDrawer({
      visible: true,
      // skey: uuid(),
      formContent: props.record,
    });
  };

  const showRemarkModal = () => {
    setRemarkModal({
      visible: true,
      formContent: detail,
    });
  };

  const showCommentDrawer = () => {
    setReportCommentDrawer({
      visible: true,
      // skey: uuid(),
      formContent: props.record,
    });
  };

  // 显示用户详情
  const showUserDetailModal = () => {
    dispatch(setConfig({ mLoading: true }));
    userApi
      .getUserDetail({ accountId: detail.account_id })
      .then((r: any) => {
        dispatch(setConfig({ mLoading: false }));
        setUserDetail({
          key: uuid(),
          visible: true,
          detail: r.data.account,
        });
      })
      .catch(() => dispatch(setConfig({ mLoading: false })));
  };

  // 设为办结
  const handleFinish = () => {
    Modal.confirm({
      title: '确定将该报料设为办结？',
      onOk: () => {
        // setLoading(this, true);
        reportApi
          .setReportFinish({ id: props.record.id, biz_type: props.record.biz_type })
          .then(() => {
            message.success('操作成功');
            // setLoading(this, false);
            // this.getData();
            // getTableList()
            getDetail();
            props.onReload && props.onReload();
          })
          .catch(() => {});
      },
    });
  };

  const changeTime = () => {
    setChangeTimeModal({
      visible: true,
      key: uuid(),
      formContent: detail,
    });
  };

  return (
    <Drawer
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ marginRight: 8 }}>报料详情</div>
          {detail?.has_copy && (
            <Button style={{ width: 100, marginRight: 8 }} onClick={showOriginalDetail}>
              原始报料
            </Button>
          )}
          {!!detail?.audio_info?.content && (
            <Button
              style={{ width: 100, marginRight: 8 }}
              onClick={() => {
                setAudioInfoDrawer({ visible: true, audio_info: detail?.audio_info });
              }}
            >
              语音报料
            </Button>
          )}
          <Button style={{ width: 100, marginRight: 8 }} onClick={getOperateLog}>
            操作日志
          </Button>
          {/* {props.type == 1 && (
            <Button style={{ width: 100, marginRight: 8 }} onClick={toCommentSystem}>
              评论管理
            </Button>
          )} */}
        </div>
      }
      width={1000}
      maskClosable={true}
      visible={props.visible}
      skey={props.skey}
      onClose={props.onClose}
      footer={
        <div
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'space-between',
          }}
        >
          {Boolean(
            (props.type == 0 && [0, 1].includes(detail?.audit_status ?? -1)) || props.type == 1
          ) && (
            <PermButton
              perm={props.type == 0 ? 'report:update' : `report:${props.record?.biz_type}:update`}
              style={{ width: 100 }}
              onClick={editReport}
            >
              编辑报料
            </PermButton>
          )}

          <div></div>

          {/* 审核报料 */}
          {props.type == 0 && (
            <div>
              {[0, 1, 2].includes(detail?.audit_status ?? -1) && (
                <PermButton
                  perm="report:doc:change_audit_status"
                  style={{ marginRight: 8, color: 'red' }}
                  onClick={handleDelete}
                >
                  删除
                </PermButton>
              )}
              {[11, 12].includes(detail?.audit_status ?? -1) && (
                <PermButton
                  perm="report:doc:change_audit_status"
                  style={{ marginRight: 8, width: 100, color: 'red' }}
                  onClick={handleDelete}
                >
                  彻底删除
                </PermButton>
              )}
              {[1, 2].includes(detail?.audit_status ?? -1) && (
                <PermButton
                  perm="report:doc:change_audit_status"
                  style={{ width: 100, marginRight: 8 }}
                  onClick={() => handleReview()}
                >
                  重新审核
                </PermButton>
              )}
              {[0, 1].includes(detail?.audit_status ?? -1) && (
                <PermButton
                  perm="report:doc:change_audit_status"
                  style={{ width: 100, marginRight: 8 }}
                  onClick={handleNoPass}
                >
                  不通过
                </PermButton>
              )}
              {[0, 2].includes(detail?.audit_status ?? -1) && (
                <PermButton
                  perm="report:doc:change_audit_status"
                  style={{ width: 100 }}
                  type="primary"
                  onClick={handlePass}
                >
                  通过
                </PermButton>
              )}
            </div>
          )}

          {/* 已分配报料 */}
          {props.type == 1 && (
            <div>
              {[0, 1, 3].includes(detail?.status ?? -1) && (
                <PermButton
                  perm={`report:${props.record.biz_type}:update_reporter`}
                  style={{ width: 100, marginRight: 8 }}
                  type="primary"
                  onClick={() => handleAddReporter()}
                >
                  指派记者
                </PermButton>
              )}
              {[1, 2, 3].includes(detail?.status ?? -1) && (
                <PermButton
                  perm={`report:${props.record.biz_type}:update_article`}
                  style={{ width: 100, marginRight: 8 }}
                  type="primary"
                  onClick={() => handleAddArticle()}
                >
                  关联稿件
                </PermButton>
              )}
              {detail?.status == 1 && (
                <PermButton
                  perm={`report:${props.record.biz_type}:focus`}
                  style={{ marginRight: 8 }}
                  type="primary"
                  onClick={() => changeReportFocusStatus(0)}
                >
                  设为持续关注
                </PermButton>
              )}
              {detail?.status == 3 && (
                <PermButton
                  perm={`report:${props.record.biz_type}:process`}
                  style={{ width: 100, marginRight: 8 }}
                  type="primary"
                  onClick={() => changeReportFocusStatus(1)}
                >
                  设为跟进中
                </PermButton>
              )}
              {(detail?.status == 1 || detail?.status == 3) && (
                <PermButton
                  perm={`report:${props.record.biz_type}:finish`}
                  style={{ width: 100, marginRight: 8 }}
                  type="primary"
                  onClick={() => handleFinish()}
                >
                  设为办结
                </PermButton>
              )}
            </div>
          )}
        </div>
      }
    >
      <div className="drawer_detail_content">
        <ReportDetail className="report_content" detail={detail}></ReportDetail>
        <ReportInfo
          className="report_info"
          type={props.type}
          detail={detail}
          process_list={processList}
          reply_list={replyList}
          showUserDetailModal={showUserDetailModal}
          showProcessDrawer={showProcessDrawer}
          showCommentDrawer={showCommentDrawer}
          showRemarkModal={showRemarkModal}
          changeTime={changeTime}
        ></ReportInfo>
      </div>
      {/* 编辑爆料 */}
      <ReportFormDrawer
        {...reportFormDrawer}
        onClose={() => setReportFormDrawer({ visible: false })}
        onEnd={() => {
          setReportFormDrawer({ visible: false });
          getDetail();
          props.onReload && props.onReload();
        }}
      ></ReportFormDrawer>

      {/* 指派记者 */}
      <AddReporterModal
        {...reporterModal}
        onCancel={() => setReporterModal({ visible: false })}
        onOk={() => {
          setReporterModal({ visible: false });
          getDetail();
          props.onReload && props.onReload();
        }}
      ></AddReporterModal>

      <AddRemarkModal
        {...remarkModal}
        onCancel={() => setRemarkModal({ visible: false })}
        onOk={() => {
          setRemarkModal({ visible: false });
          getDetail();
          props.onReload && props.onReload();
        }}
      ></AddRemarkModal>

      {/* 关联稿件 */}
      <RelevanceArticle
        {...relevanceArticleModal}
        onCancel={() => setRelevanceArticleModal({ visible: false })}
        onOk={() => {
          setRelevanceArticleModal({ visible: false });
          getDetail();
          props.onReload && props.onReload();
        }}
      ></RelevanceArticle>

      {/* 指派领域 审核通过 */}
      <AddDomainModal
        {...addDomainModal}
        onCancel={() => setAddDomainModal({ visible: false })}
        onOk={handleAddDomainResult}
      ></AddDomainModal>

      {/* 审核不通过 删除 */}
      <ReportAuditModal
        {...reporterAuditModal}
        onCancel={() => setReporterAuditModal({ visible: false })}
        onOk={handleAuditResult}
      ></ReportAuditModal>

      {/* 原始报料 */}
      <OriginalReportDetailDrawer
        {...originalReporterDetail}
        onClose={() => setOriginalReporterDetail({ visible: false })}
      ></OriginalReportDetailDrawer>

      {/* 进展管理 */}
      <ReportProcessDrawer
        {...reportProcessDrawer}
        onClose={() => setReportProcessDrawer({ visible: false })}
        onReload={getDetail}
      ></ReportProcessDrawer>

      {/* 回复管理 */}
      <ReportCommentDrawer
        {...reportCommentDrawer}
        onClose={() => setReportCommentDrawer({ visible: false })}
        onReload={getDetail}
      ></ReportCommentDrawer>

      {/* 操作日志 */}
      <Modal
        visible={operateLog.visible}
        title="操作日志"
        key={operateLog.key}
        cancelText={null}
        onCancel={() => setOperateLog({ visible: false })}
        onOk={() => setOperateLog({ visible: false })}
      >
        <div>
          <Timeline>
            {operateLog.logs?.map((v: any, i: number) => [
              <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                &nbsp;
              </Timeline.Item>,
              v.log_list?.map((action: any, index: number) => (
                <Timeline.Item
                  className="timeline-dot"
                  data-show={moment(action.created_at).format('HH:mm:ss')}
                  key={`time${i}-action${index}`}
                >
                  {action.admin_name}&emsp;{action.remark}
                  {/* {action.user}&emsp;&emsp;{action.action}&emsp;&emsp; */}
                </Timeline.Item>
              )),
            ])}
          </Timeline>
        </div>
      </Modal>

      <PassAuditModal
        {...passWithReporter}
        onCancel={() => setPassWithReporter({ visible: false })}
        onOk={() => {
          setPassWithReporter({ visible: false });
          getDetail();
          props.onReload && props.onReload();
        }}
      ></PassAuditModal>

      <ChangeReportTimeModal
        {...changeTimeModal}
        onCancel={() => {
          setChangeTimeModal({ visible: false });
        }}
        onOk={() => {
          setChangeTimeModal({ visible: false });
          getDetail();
          props.onReload && props.onReload();
        }}
      ></ChangeReportTimeModal>

      {/* 用户详情弹窗 */}
      <Modal
        visible={userDetail.visible}
        key={userDetail.key}
        title="用户详情"
        width={800}
        onCancel={() => setUserDetail({ visible: false })}
        onOk={() => setUserDetail({ visible: false })}
      >
        {/*{user.visible && getUserDetail(user.detail)}*/}
        {userDetail.visible && <UserDetail detail={userDetail.detail} />}
      </Modal>

      <Drawer
        title={'语音报料'}
        visible={audioInfoDrawer.visible}
        onClose={() => setAudioInfoDrawer({ visible: false })}
        skey={''}
        maskClosable={true}
      >
        <Tabs defaultActiveKey="1">
          <Tabs.TabPane tab="语音转文字" key="1">
            {audioInfoDrawer.audio_info?.content}
          </Tabs.TabPane>

          <Tabs.TabPane tab="智能提炼" key="2">
            <h2>
              {bizTypeMap(audioInfoDrawer.audio_info?.ai_biz_type || 1, '')}&nbsp;|&nbsp;
              {reportTypeMap(audioInfoDrawer.audio_info?.ai_report_type || 1, '')}&nbsp;|&nbsp;
              {audioInfoDrawer.audio_info?.ai_title}
            </h2>
            {audioInfoDrawer.audio_info?.ai_content}
          </Tabs.TabPane>
        </Tabs>
      </Drawer>
    </Drawer>
  );
};

export default forwardRef<any, any>(ReportDetailDrawer);

// 爆料类型，1-投诉举报，2-咨询求助，3-建言献策
// 业务类型，1-问政，2-帮办，3-帮帮团：
// "id":'1234',
//             	                "biz_type":1,
//             	                "report_type":2,
// 				"number":"1343", //编号
//                                 "title":"xxxx",
//                                 "url":"xxxx",//跳转链接
//                                 "account_id":1234,
//                                 "nick_name":'xxx',//爆料人
//                                 "operator":"bob",
//                                 "reporter_id":1234, //跟进记者id
//                                 "reporter_name":'', //记者名称
//                                 "expert_id":1234, //跟进专家id
//                                 "expert_name":'', //专家名称
//                                 "finish_time":************,//审核时间
//                                 "created_at":**************, //爆料时间
//                                 "medias":[{
//                                         "id":"1234",
//                                         "type":0,
//                                         "url":"https://xxx.jpg"
//                                  }
//                                 ]
