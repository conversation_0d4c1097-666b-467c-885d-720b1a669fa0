import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react'

import { reportApi } from '@app/api';
import '@components/business/styles/business.scss'
import { setMLoading } from '@app/utils/utils';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { Drawer, NewTable } from '@app/components/common';
import '../styles/reportDrawer.scss'
import { TableList } from '@app/types';
import moment from 'moment';
import { PermA, PermButton } from '@app/components/permItems';
import { Button, Divider, Modal, message } from 'antd';
import useTable from '@app/utils/useTable';
import AddCommentModal from './addCommentModal';
import EllipsisText from './EllipsisText'
import { PhotoSlider } from 'react-photo-view';

const ReportCommentDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const [detail, setDetail] = useState<any>(null)
  const [addCommentModal, setAddCommentModel] = useState<any>({
    visible: false,
    skey: Date.now(),
    formContent: null,
  })

  const [imagePreview, setImagePreview] = useState<any>({
    visible: false,
    imgs: [],
    index: 0
  })

  const getFilter = () => {
    return {
      report_id: props.formContent?.id,
      biz_type: props.formContent?.biz_type
    }
  }

  const { tableList, loading, getTableList } = useTable({
    api: reportApi.reportReplyList,
    filter: getFilter,
    index: 'list'
  })
  // useImperativeHandle(
  //   ref,
  //   () => ({
  //     ...props.form,
  //     doSubmit
  //   }),
  //   []
  // );

  useEffect(() => {
    if (props.visible) {
      getTableList()
    }
  }, [props.visible])

  const getSeq = (i: number) => ((tableList?.current ?? 0) - 1) * (tableList?.size ?? 0) + i + 1;

  // const getDetail = () => {
  //   dispatch(setConfig({ mLoading: true }))
  //   reportApi.getOriginalReportDetail({ ...props.formContent }).then((data: any) => {
  //     dispatch(setConfig({ mLoading: false }))
  //     setDetail(data.detail || {})
  //   }).catch((e) => {
  //     dispatch(setConfig({ mLoading: false }))
  //   })
  // }

  const handleTableListChange = (v: TableList) => {
    // setTableList(v)
  }

  const editProcess = (record: any) => {
    setAddCommentModel({
      visible: true,
      skey: Date.now(),
      formContent: {
        id: record.id,
        biz_type: props.formContent?.biz_type,
      },
      record
    })
  }

  const deleteProcess = (record: any) => {
    Modal.confirm({
      title: '确定删除该回复？',
      onOk: () => {
        // setLoading(this, true);
        reportApi
          .deleteReportComment({ id: record.id, biz_type: props.formContent?.biz_type })
          .then(() => {
            message.success('操作成功');
            // setLoading(this, false);
            // this.getData();
            getTableList()
            props.onReload && props.onReload()
          })
          .catch(() => {

          });
      },
    });
  }

  const addComment = () => {
    setAddCommentModel({
      visible: true,
      skey: Date.now(),
      formContent: {
        report_id: props.formContent?.id,
        biz_type: props.formContent?.biz_type
      },
      record: null
    })
  }

  const columns = [
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      width: 70,
    },
    {
      title: '回复时间',
      dataIndex: 'created_at',
      width: 100,
      render: (text: any, record: any) => {
        return <div style={{ whiteSpace: 'normal' }}>
          <div>{moment(text).format('YYYY-MM-DD')}</div>
          <div>{moment(text).format('HH:mm:ss')}</div>
        </div>
      }
    },
    {
      title: '回复人',
      dataIndex: 'name',
      width: 90,
    },
    {
      title: '头像',
      dataIndex: 'head_img',
      width: 90,
      render: (text: any, record: any) => {
        return <img src={text} style={{ width: 40, height: 40 }}></img>
      }
    },
    {
      title: '回复内容',
      dataIndex: 'content',
      key: 'content',
      // width: 90,
      render: (text: any, record: any) => {
        return <div>
          <EllipsisText key={text} text={text} maxLine={5} lineHeight={21}></EllipsisText>
          {record?.pic_urls?.length > 0 &&
            <div className='table_comment_imgs'>
              
              {record.pic_urls?.split(',')?.map((v: any, i: number) => {
                return <img onClick={() => {
                  setImagePreview({
                    visible: true,
                    imgs: record?.pic_urls?.split(','),
                    index: i
                  })
                }} src={v} key={v}></img>
              })}
            </div>
          }
        </div>
      }
    },
    {
      title: '最后操作人',
      dataIndex: 'updated_by',
      width: 90,
    },
    {
      title: '最后操作时间',
      dataIndex: 'updated_at',
      width: 120,
      render: (text: any, record: any) => {
        return <div style={{ whiteSpace: 'normal' }}>
          <div>{moment(text).format('YYYY-MM-DD')}</div>
          <div>{moment(text).format('HH:mm:ss')}</div>
        </div>
      }
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any, i: number) => (
        <span>
          <PermA perm={`report:${props.formContent?.biz_type}:reply_edit`} onClick={() => editProcess(record)}>
            编辑
          </PermA>
          <Divider type="vertical" />
          <PermA perm={`report:${props.formContent?.biz_type}:reply_edit`} onClick={() => deleteProcess(record)}>
            删除
          </PermA>
        </span>
      ),
      width: 110,
    },
  ];

  return <Drawer
    title="回复管理"
    visible={props.visible}
    skey={props.skey}
    onClose={props.onClose}
    closeText='关闭'
    maskClosable={true}
    width={1000}
  >
    <div style={{ marginBottom: 20 }}>
      <PermButton perm={`report:${props.formContent?.biz_type}:reply_edit`} onClick={addComment}>添加回复</PermButton>
    </div>
    <NewTable
      columns={columns}
      rowKey="id"
      pagination={false}
      getTableList={getTableList}
      loading={loading}
      tableList={tableList}
    />

    <AddCommentModal
      {...addCommentModal}
      onClose={() => setAddCommentModel({ visible: false })}
      onEnd={() => {
        setAddCommentModel({ visible: false })
        getTableList()
        props.onReload && props.onReload()
      }}
    >
    </AddCommentModal>

    <PhotoSlider maskOpacity={0.5}
      images={imagePreview.imgs?.map((v: any) => ({ src: v, key: v })) || []}
      visible={imagePreview.visible}
      onClose={() => setImagePreview({ visible: false })}
      index={imagePreview.index}
      onIndexChange={(index) => setImagePreview({ ...imagePreview, index })}
    />
  </Drawer>
}

export default forwardRef<any, any>(ReportCommentDrawer);
