import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import {
  Button,
  Col,
  Dropdown,
  Form,
  Icon,
  Input,
  InputNumber,
  Menu,
  Modal,
  Radio,
  Row,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { reportApi } from '@app/api';
import '@components/business/styles/business.scss';
import { setMLoading } from '@app/utils/utils';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { Drawer, ImageUploader, VideoUploader } from '@app/components/common';
import ImgUploadList from '@app/components/common/imgUploadList';
import { PermButton } from '@app/components/permItems';

const PicConfigForm = (props: any, ref: any) => {
  const dispatch = useDispatch();

  const {
    pic_url = '',
    url = '',

    kjrk_pic_url = '',
    kjrk_text = '',
    rk_url = '',
    khd_detail_pic_url = '',
    khd_button_pic_url = '',
    khd_button_url = '',
    h5_detail_pic_url = '',
    h5_button_pic_url = '',
    h5_button_url = '',
  } = props.formContent || {};

  const { getFieldDecorator } = props.form;

  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
      doSubmit,
    }),
    []
  );

  const doSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body: any = { ...values };
        body.type = props.type
        dispatch(setConfig({ mLoading: true }));
        reportApi
          .updatePicConfig(body)
          .then(() => {
            dispatch(setConfig({ mLoading: false }));
            message.success('操作成功');
            props.onEnd(values.type);
          })
          .catch(() => {
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  // const menu = (
  //   <Menu onClick={props.onClose}>
  //     <Menu.Item key="1" style={{ textAlign: 'center' }}>
  //       仅保存修改
  //     </Menu.Item>
  //     <Menu.Item key="2" style={{ textAlign: 'center' }}>
  //       保存并通过
  //     </Menu.Item>
  //   </Menu>
  // );
  return (
    <Drawer
      title="功能图片配置"
      visible={props.visible}
      skey={props.skey}
      onClose={props.onClose}
      onOk={doSubmit}
      okText="保存"
      // footer={((<><Button style={{ width: 100, marginRight: 8 }} onClick={props.onClose}>取消</Button>
      //   <Dropdown trigger={['hover']} overlay={menu} placement="topCenter">
      //     <PermButton style={{ width: 100 }} type="primary" perm='' onClick={props.onOk}>
      //       保存<Icon type="up" />
      //     </PermButton>
      //   </Dropdown>

      // </>))}
    >
      <Form {...formLayout}>
        {props.type == 50 && (
          <>
            <Form.Item label="图片" extra="支持上传jpg，jpeg，png，gif图片格式，比例为345:54">
              {getFieldDecorator('pic_url', {
                initialValue: pic_url,
                // rules: [
                //   {
                //     required: true,
                //     message: '请上传图片',
                //   },
                // ],
              })(<ImageUploader ratio={345/54} />)}
            </Form.Item>
            <Form.Item label="跳转链接">
              {getFieldDecorator('url', {
                initialValue: url,
                rules: [
                  // {
                  //   required: true,
                  //   message: '请输入链接',
                  //   whitespace: true,
                  // },
                  {
                    pattern: /^https?:\/\//,
                    message: '请填写正确的URL地址',
                  },
                ],
              })(<Input placeholder="请输入点击跳转的页面地址"></Input>)}
            </Form.Item>
          </>
        )}
        {props.type == 45 && (
          <>
            {/* disabled={status != 0} */}
            <Form.Item label="快捷入口图标" extra="支持上传jpg，jpeg，png，gif图片格式，比例为1:1">
              {getFieldDecorator('kjrk_pic_url', {
                initialValue: kjrk_pic_url,
                // rules: [
                //   {
                //     required: true,
                //     message: '请上传图片',
                //   },
                // ],
              })(<ImageUploader ratio={1} />)}
            </Form.Item>

            <Form.Item label="快捷入口文字">
              {getFieldDecorator('kjrk_text', {
                initialValue: kjrk_text,
                rules: [
                  // {
                  //   required: true,
                  //   message: '请输入文字',
                  //   whitespace: true,
                  // },
                  {
                    max: 4,
                    message: '最多4字',
                  },
                ],
              })(<Input placeholder="最多4字,比如应急互助"></Input>)}
            </Form.Item>

            <Form.Item label="入口跳转链接">
              {getFieldDecorator('rk_url', {
                initialValue: rk_url,
                rules: [
                  // {
                  //   required: true,
                  //   message: '请输入链接',
                  //   whitespace: true,
                  // },
                  {
                    pattern: /^https?:\/\//,
                    message: '请填写正确的URL地址',
                  },
                ],
              })(<Input placeholder="请输入点击跳转的页面地址"></Input>)}
            </Form.Item>

            <Form.Item
              label="客户端详情头图"
              extra="支持上传jpg，jpeg，png，gif图片格式，比例为375:128"
            >
              {getFieldDecorator('khd_detail_pic_url', {
                initialValue: khd_detail_pic_url,
                rules: [
                  // {
                  //   required: true,
                  //   message: '请上传图片',
                  // },
                ],
              })(<ImageUploader ratio={375 / 128} />)}
            </Form.Item>

            <Form.Item
              label="客户端悬浮按钮"
              extra="支持上传jpg，jpeg，png，gif图片格式,无比例限制，前台按固定高度等比例显示"
            >
              {getFieldDecorator('khd_button_pic_url', {
                initialValue: khd_button_pic_url,
                rules: [
                  // {
                  //   required: true,
                  //   message: '请上传图片',
                  // },
                ],
              })(<ImageUploader />)}
            </Form.Item>

            <Form.Item label="客户端按钮跳转">
              {getFieldDecorator('khd_button_url', {
                initialValue: khd_button_url,
                rules: [
                  // {
                  //   required: true,
                  //   message: '请输入链接',
                  //   whitespace: true,
                  // },
                  {
                    pattern: /^https?:\/\//,
                    message: '请填写正确的URL地址',
                  },
                ],
              })(<Input placeholder="请输入点击跳转的页面地址"></Input>)}
            </Form.Item>

            <Form.Item
              label="h5详情头图"
              extra="支持上传jpg，jpeg，png，gif图片格式，比例为375:100"
            >
              {getFieldDecorator('h5_detail_pic_url', {
                initialValue: h5_detail_pic_url,
                rules: [
                  // {
                  //   required: true,
                  //   message: '请上传图片',
                  // },
                ],
              })(<ImageUploader ratio={375 / 100} />)}
            </Form.Item>

            <Form.Item
              label="h5悬浮按钮"
              extra="支持上传jpg，jpeg，png，gif图片格式,无比例限制，前台按固定高度等比例显示"
            >
              {getFieldDecorator('h5_button_pic_url', {
                initialValue: h5_button_pic_url,
                rules: [
                  // {
                  //   required: true,
                  //   message: '请上传图片',
                  // },
                ],
              })(<ImageUploader />)}
            </Form.Item>

            <Form.Item label="h5按钮跳转">
              {getFieldDecorator('h5_button_url', {
                initialValue: h5_button_url,
                rules: [
                  // {
                  //   required: true,
                  //   message: '请输入链接',
                  //   whitespace: true,
                  // },
                  {
                    pattern: /^https?:\/\//,
                    message: '请填写正确的URL地址',
                  },
                ],
              })(<Input placeholder="请输入点击跳转的页面地址"></Input>)}
            </Form.Item>
          </>
        )}
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'PicConfigForm' })(forwardRef<any, any>(PicConfigForm));
