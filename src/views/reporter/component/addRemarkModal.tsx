import { Button, Form, Icon, Input, InputNumber, Modal, Select, Spin, Tooltip, message } from 'antd'
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react'
import _, { set } from "lodash";
import { reportApi, communityApi } from '@app/api';
import "../styles/reportDrawer.scss"

const AddRemarkModal = (props: any, ref: any) => {

  const [loading, setLoading] = useState(false)
  const [remark, setRemark] = useState(undefined)

  useEffect(() => {
    if (!!props.formContent && !!props.formContent?.remark) {
      setRemark(props.formContent.remark)
    } else {
      setRemark(undefined)
    }
  }, [props.formContent])


  const handleSubmit = (e: any) => {
    e.preventDefault();

    // if (!!remark) {
    setLoading(true)
    const parmas = {
      id: props.formContent?.id,
      biz_type: props.formContent?.biz_type,
      remark
    }
    reportApi.updateReportRemark(parmas).then((res: any) => {
      message.success('更新成功');
      setLoading(false)

      props.onOk && props.onOk()
    }).catch(() => {
      // message.error('添加失败');
      setLoading(false)
    })
    // } else {
    //   message.error('请输入备注信息');
    // }
  }

  const handleChange = (val: any) => {
    setRemark(val.target.value)
  }

  return <Modal
    width={500}
    visible={props.visible}
    title="编辑备注信息"
    key={props.skey}
    onCancel={() => { if (!loading) { props.onCancel && props.onCancel() } }}
    onOk={handleSubmit}
    maskClosable={false}
    destroyOnClose={true}
    confirmLoading={loading}
  >
    <Spin spinning={loading}>
      <Input.TextArea value={remark} rows={5} maxLength={500} placeholder='请输入备注信息' onChange={handleChange}></Input.TextArea>
    </Spin>
  </Modal>

}

export default forwardRef<any, any>(AddRemarkModal);
