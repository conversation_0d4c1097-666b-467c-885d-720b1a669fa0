import { Button, Form, Icon, InputNumber, Modal, Radio, Spin, Tooltip, message } from 'antd'
import React, { forwardRef, useImperativeHandle, useState } from 'react'

import { reportApi } from '@app/api';

const classifyList = [
  { id: 1, name: '营商环境' },
  { id: 2, name: '医疗' },
  { id: 3, name: '教育' },
  { id: 4, name: '城建' },
  { id: 5, name: '交通' },
  { id: 6, name: '政务' },
  { id: 7, name: '金融' },
  { id: 8, name: '企业' },
  { id: 9, name: '就业' },
  { id: 10, name: '文娱' },
  { id: 11, name: '旅游' },
  { id: 12, name: '治安' },
  { id: 13, name: '环保' },
  { id: 14, name: '三农' }
]

const AddDomainModal = (props: any, ref: any) => {

  const [loading, setLoading] = useState(false)
  const { getFieldDecorator } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        setLoading(true)
        let promise = null
        if (props.type == 0) {
          // 修改领域
          const parmas = {
            ...values,
            id: props.formContent.id,
            biz_type: props.formContent.biz_type
          }
          promise = reportApi.updateReportClassify(parmas)
        } else {
          const params = {
            ...values,
            id: props.formContent.id,
            biz_type: props.formContent.biz_type,
            status: 1
          }
          if (params.classify_id == -1) {
            delete params.classify_id
          }
          promise = reportApi.changeAuditStatus(params)
        }
        promise.then((res: any) => {
          message.success(props.type == 0 ? '修改成功' : '审核成功');
          setLoading(false)
          props.onOk && props.onOk(props.type)
        }).catch(() => {
          // message.error('添加失败');
          setLoading(false)
        })
      } else {
        message.error('请检查表单内容');
        setLoading(false)
      }
    });
  }

  return <Modal
    width={500}
    visible={props.visible}
    title={props.type == 0 ? "修改领域" : "审核通过"}
    key={props.skey}
    onCancel={() => { if (!loading) { props.onCancel && props.onCancel() } }}
    onOk={handleSubmit}
    maskClosable={false}
    destroyOnClose={true}
  >
    <Spin spinning={false}>
      <Form {...formLayout}>

        <Form.Item label="领域分类">
          {getFieldDecorator('classify_id', {
            initialValue: props.formContent?.classify_id || null,
            rules: [
              {
                required: true,
                message: '请选择领域'
              }
            ],
          })(<Radio.Group
            onChange={(e: any) => console.log('')}
            style={{ paddingLeft: 20 }}
          >
            {classifyList.map((v) => {
              return <Radio style={{ width: 90, height: 40, lineHeight: '40px' }} key={v.id} value={v.id}>{v.name}</Radio>
            })}
            {props.type != 0 && <Radio style={{ width: 90, height: 40, lineHeight: '40px' }} key={-1} value={-1}>无分类</Radio>}
          </Radio.Group>)}
        </Form.Item>
      </Form>
    </Spin>
  </Modal>

}

export default Form.create<any>({ name: 'AddDomainModal' })(forwardRef<any, any>(AddDomainModal));
