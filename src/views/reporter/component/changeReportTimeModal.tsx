import {
  But<PERSON>,
  DatePicker,
  Form,
  Icon,
  Input,
  InputNumber,
  Modal,
  Radio,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';

import { reportApi } from '@app/api';
import moment from 'moment';

const ChangeReportTimeModal = (props: any, ref: any) => {
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  const formLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const params = {
          id: props.formContent.id,
          report_time: moment(values.report_time).format('YYYY-MM-DD HH:mm:ss'),
        };
        setLoading(true);
        reportApi
          .updateReportTime(params)
          .then((res: any) => {
            message.success('修改成功');
            setLoading(false);
            props.onOk && props.onOk(props.type);
          })
          .catch(() => {
            // message.error('添加失败');
            setLoading(false);
          });
      } else {
        message.error('请检查表单内容');
        setLoading(false);
      }
    });
  };

  const oldReportTime = props.formContent?.old_created_at
    ? props.formContent?.old_created_at
    : props.formContent?.created_at;

  console.log('oldReportTime', oldReportTime, props.formContent?.old_created_at);
  return (
    <Modal
      width={500}
      visible={props.visible}
      title="报料时间"
      key={props.skey}
      onCancel={() => {
        if (!loading) {
          props.onCancel && props.onCancel();
        }
      }}
      onOk={handleSubmit}
      maskClosable={false}
      destroyOnClose={true}
    >
      <Form {...formLayout}>
        {console.log(props.formContent)}
        <Form.Item label="原始报料时间">
          <div>{moment(oldReportTime).format('YYYY-MM-DD HH:mm:ss')}</div>
        </Form.Item>

        <Form.Item label="自定义报料时间">
          {getFieldDecorator(`report_time`, {
            initialValue: props.formContent?.old_created_at
              ? moment(props.formContent?.created_at)
              : undefined,
          })(
            <DatePicker
              showTime
              placeholder="请选择日期"
              format="YYYY-MM-DD HH:mm:ss"
              disabledDate={(current) => {
                return current?.isAfter(moment(), 'day') ?? false;
              }}
            />
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create<any>({ name: 'ChangeReportTimeModal' })(
  forwardRef<any, any>(ChangeReportTimeModal)
);
