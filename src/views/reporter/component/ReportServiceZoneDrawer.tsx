import { Button, Form, Icon, Input, InputNumber, Radio, Row, Switch, Tooltip, message } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { reportApi, communityApi } from '@app/api';
import '@components/business/styles/business.scss';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { Drawer, ImageUploader, VideoUploader } from '@app/components/common';
import _, { set } from 'lodash';
import '../styles/reportDrawer.scss';
import TMFormList, { FormListTitle, TMFormListRef } from '@app/components/common/TMFormList';

const ReportServiceZoneDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const bigPicFormListRef = useRef<TMFormListRef>(null);
  const smallPicFormListRef = useRef<TMFormListRef>(null);
  const [bigPicList, setBigPicList] = useState<any[]>([
    {
      pic_url: '',
      link_url: '',
    },
  ]);
  const [smallPicList, setSmallPicList] = useState<any[]>([
    {
      pic_url: '',
      link_url: '',
    },
  ]);

  useEffect(() => {
    if (!!props.formContent) {
      const ref_extensions = JSON.parse(props.formContent.ref_extensions || '{}');
      setBigPicList(
        ref_extensions.big_images || [
          {
            pic_url: '',
            link_url: '',
          },
        ]
      );
      setSmallPicList(
        ref_extensions.small_images || [
          {
            pic_url: '',
            link_url: '',
          },
        ]
      );
    }
  }, [props.visible]);

  const { getFieldDecorator, getFieldValue } = props.form;

  const doSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body: any = {};
        body.big_images = values.big_images;
        body.small_images = values.small_images;
        dispatch(setConfig({ mLoading: true }));
        reportApi
          .saveReportServiceZone({
            ref_extensions: JSON.stringify(body),
            big_cycle_carousel: values.big_cycle_carousel,
          })
          .then(() => {
            dispatch(setConfig({ mLoading: false }));
            message.success('操作成功');
            props.onEnd();
          })
          .catch(() => {
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  const formLayout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 21 },
  };

  return (
    <Drawer
      title={'服务专区配置'}
      visible={props.visible}
      skey={props.skey}
      onClose={props.onClose}
      onOk={doSubmit}
      okText="保存"
      // footer={((<><Button style={{ width: 100, marginRight: 8 }} onClick={props.onClose}>取消</Button>
      //   <Dropdown trigger={['hover']} overlay={menu} placement="topCenter">
      //     <PermButton style={{ width: 100 }} type="primary" perm='' onClick={props.onOk}>
      //       保存<Icon type="up" />
      //     </PermButton>
      //   </Dropdown>

      // </>))}
    >
      <Form {...formLayout}>
        <h3>
          大图区域&nbsp;
          <Tooltip
            title={
              <img src="../../../assets/report_zone_tips_big.png" width={200} height={132}></img>
            }
          >
            <Icon type="question-circle" />
          </Tooltip>
        </h3>
        <Form.Item label="循环轮播">
          {getFieldDecorator('big_cycle_carousel', {
            initialValue: props.formContent?.big_cycle_carousel ?? true,
            valuePropName: 'checked',
            rules: [
              {
                required: true,
                message: '请选择是否循环轮播',
              },
            ],
          })(<Switch />)}
        </Form.Item>

        <p>最多可添加5张图片</p>

        <TMFormList
          ref={bigPicFormListRef}
          dataList={bigPicList}
          form={props.form}
          fromItem={() => {
            return {
              label: '图片',
            };
          }}
          filed="big_images"
        >
          {(item: any, i: any, total: number) => (
            <Row key={i}>
              <FormListTitle
                total={total}
                i={i}
                title="图片"
                upMove={() => bigPicFormListRef.current?.upMove(i)}
                downMove={() => bigPicFormListRef.current?.downMove(i)}
                removeItem={() => bigPicFormListRef.current?.removeItem(i)}
              ></FormListTitle>
              <Row>
                <Form.Item label="图片" extra="支持jpg,jpeg,png图片格式，比例待定">
                  {getFieldDecorator(`big_images[${i}].pic_url`, {
                    initialValue: item.pic_url,
                    rules: [
                      {
                        required: true,
                        message: '请上传图片',
                      },
                    ],
                  })(<ImageUploader ratioType={1} />)}
                </Form.Item>
                <Form.Item label="跳转链接">
                  {getFieldDecorator(`big_images[${i}].link_url`, {
                    initialValue: item.link_url,
                    rules: [
                      {
                        required: true,
                        message: '请输入跳转链接',
                      },
                      {
                        pattern: /^https?:\/\//,
                        message: '请输入正确的URL',
                      },
                    ],
                  })(<Input placeholder="请输入跳转链接" />)}
                </Form.Item>
              </Row>
            </Row>
          )}
        </TMFormList>

        <Row style={{ textAlign: 'center', marginBottom: 16 }} id="add-btn-line">
          <Button
            onClick={() => bigPicFormListRef.current?.addItem()}
            disabled={(bigPicFormListRef.current?.total ?? 0) >= 5}
          >
            添加图片
          </Button>
        </Row>

        <h3>
          小图区域&nbsp;
          <Tooltip
            title={
              <img src="../../../assets/report_zone_tips_small.png" width={200} height={132}></img>
            }
          >
            <Icon type="question-circle" />
          </Tooltip>
        </h3>
        <p>最多可添加20张图片</p>

        <TMFormList
          ref={smallPicFormListRef}
          dataList={smallPicList}
          form={props.form}
          fromItem={() => {
            return {
              label: '图片',
            };
          }}
          filed="small_images"
        >
          {(item: any, i: any, total: number) => (
            <Row key={i}>
              <FormListTitle
                total={total}
                i={i}
                title="图片"
                upMove={() => smallPicFormListRef.current?.upMove(i)}
                downMove={() => smallPicFormListRef.current?.downMove(i)}
                removeItem={() => smallPicFormListRef.current?.removeItem(i)}
              ></FormListTitle>
              <Row>
                <Form.Item label="图片" extra="支持jpg,jpeg,png图片格式，比例待定">
                  {getFieldDecorator(`small_images[${i}].pic_url`, {
                    initialValue: item.pic_url,
                    rules: [
                      {
                        required: true,
                        message: '请上传图片',
                      },
                    ],
                  })(<ImageUploader ratioType={1} />)}
                </Form.Item>
                <Form.Item label="跳转链接">
                  {getFieldDecorator(`small_images[${i}].link_url`, {
                    initialValue: item.link_url,
                    rules: [
                      {
                        required: true,
                        message: '请输入跳转链接',
                      },
                      {
                        pattern: /^https?:\/\//,
                        message: '请输入正确的URL',
                      },
                    ],
                  })(<Input placeholder="请输入跳转链接" />)}
                </Form.Item>
              </Row>
            </Row>
          )}
        </TMFormList>

        <Row style={{ textAlign: 'center', marginBottom: 16 }} id="add-btn-line">
          <Button
            onClick={() => smallPicFormListRef.current?.addItem()}
            disabled={(smallPicFormListRef.current?.total ?? 0) >= 20}
          >
            添加图片
          </Button>
        </Row>
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'ReportServiceZoneDrawer' })(
  forwardRef<any, any>(ReportServiceZoneDrawer)
);
