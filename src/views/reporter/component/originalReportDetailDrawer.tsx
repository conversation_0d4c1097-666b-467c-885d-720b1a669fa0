import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react'

import { reportApi } from '@app/api';
import '@components/business/styles/business.scss'
import { setMLoading } from '@app/utils/utils';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { Drawer } from '@app/components/common';
import '../styles/reportDrawer.scss'
import ReportDetail from './reportDetail';

const OriginalReportDetailDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const [detail, setDetail] = useState<any>(null)
  // useImperativeHandle(
  //   ref,
  //   () => ({
  //     ...props.form,
  //     doSubmit
  //   }),
  //   []
  // );

  useEffect(() => {
    if (props.visible) {
      getDetail()
    }
  }, [props.visible])

  const getDetail = () => {
    dispatch(setConfig({ mLoading: true }))
    reportApi.getOriginalReportDetail({ id: props.formContent.id, biz_type: props.formContent.biz_type }).then((data: any) => {
      dispatch(setConfig({ mLoading: false }))
      setDetail(data?.data.detail || {})
    }).catch((e) => {
      dispatch(setConfig({ mLoading: false }))
    })
  }

  return <Drawer
    title="原始报料"
    visible={props.visible}
    skey={props.skey}
    onClose={props.onClose}
    closeText='关闭'
    maskClosable={true}
  >
    <ReportDetail className="report_content" detail={detail}></ReportDetail>
  </Drawer>
}

export default forwardRef<any, any>(OriginalReportDetailDrawer);
