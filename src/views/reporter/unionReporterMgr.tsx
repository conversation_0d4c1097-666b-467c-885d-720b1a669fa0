import {
  Button,
  Col,
  Divider,
  Icon,
  Input,
  Modal,
  Row,
  Select,
  Tooltip,
  message,
  Radio,
  Menu,
  Dropdown,
  DatePicker,
  Checkbox,
  InputNumber,
} from 'antd';
import {
  geBoostDetail,
  getCrumb,
  resolveNewsType,
  showIDDetailModal,
  UserDetail,
  requirePerm4Function,
} from '@app/utils/utils';
import ReactClipboard from 'react-clipboardjs-copy';
import { useHistory } from 'react-router';
import { useSelector, useDispatch, useStore } from 'react-redux';
import { getTableList } from '@app/action/tableList';
import { Table, OrderColumn } from '@components/common';
import React, { useEffect, useState } from 'react';
import { reportApi as api, sysApi, userApi } from '@app/api';
import { PermA, PermButton } from '@app/components/permItems';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import HelpExpertForm from './component/helpExpertForm';
import uuid from 'uuid';

export default function unionReporterMgr(props: any) {
  const history = useHistory();
  const dispatch = useDispatch();
  const { total, current, size, records = [] } = useSelector((state: any) => state.tableList);
  const { TextArea } = Input;
  const { session } = useStore().getState();
  const [filter, setFilter] = useState({
    type: 3,
  });

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const [reporterFormDrawer, setReporterFormDrawer] = useState<any>({
    visible: false,
    formContent: null,
  })

  const [userDetailModal, setUserDetailModal] = useState<any>({
    visible: false,
    key: uuid(),
    detail: null,
  })

  const columns: any = [
    {
      title: '排序',
      key: 'order',
      render: (text: any, record: any, i: number) => {
        const pos = getSeq(i);
        return (
          <OrderColumn
            pos={pos}
            start={1}
            end={total}
            perm="reporter:3:sort"
            // disableUp={!record.enabled || (i > 0 && !records[i - 1].enabled)}
            // disableDown={!record.enabled || (i < records.length - 1 && !records[i + 1].enabled)}
            onUp={() => exchangeOrder(record.id, getSeq(i), 1)}
            onDown={() => exchangeOrder(record.id, getSeq(i), -1)}
          />
        );
      },
      width: 70,
    },
    {
      title: '序号',
      key: 'id',
      render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
      width: 50,
    },
    // {
    //   title: '小潮号',
    //   dataIndex: 'chao_id',
    // },
    // {
    //   title: '用户昵称',
    //   dataIndex: 'nick_name',
    //   render: (text: any, record: any, i: number) => <a onClick={() => showUserDetailModal(record)}>{text}</a>
    // },
    {
      title: '头像',
      dataIndex: 'head_img',
      render: (text: any, record: any, i: number) => {
        return (<div style={{ height: 60 }}>
          <img src={text} className='list-pic'></img>
        </div>)
      },
      width: 80
    },
    {
      title: '名称',
      dataIndex: 'nick_name',
      width: 160
    },
    {
      title: '一句话介绍',
      dataIndex: 'short_desc'
    },
    {
      title: '最后操作人',
      dataIndex: 'operator_name',
      width: 160
    },
    // {
    //   title: '最后操作人',
    //   dataIndex: 'operator_name',
    // },
    {
      title: '最后操作时间',
      key: 'updated_at',
      dataIndex: 'updated_at',
      render: (text: number, record: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any, i: number) => (
        <span>
          <PermA perm="reporter:3:update" onClick={() => handleEdit(record)}>
            编辑
          </PermA>
          <Divider type="vertical" />
          <PermA perm="reporter:3:sort" onClick={() => handelSort(record, getSeq(i))}>
            排序
          </PermA>
          <Divider type="vertical" />
          <PermA perm="reporter:3:delete" onClick={() => handelDel(record)}>
            删除
          </PermA>
        </span>
      ),
      width: 180,
    },
  ];
  // 领域分类转义
  const resolveType = (val: any) => {
    let name = '未知';
    switch (val) {
      case 1:
        name = '营商环境';
        break;
      case 2:
        name = '医疗';
        break;
      case 3:
        name = '教育';
        break;
      case 4:
        name = '城建';
        break;
      case 5:
        name = '交通';
        break;
      case 6:
        name = '政务';
        break;
      case 7:
        name = '金融';
        break;
      case 8:
        name = '企业';
        break;
      case 9:
        name = '文娱';
        break;
      case 10:
        name = '旅游';
        break;
      case 11:
        name = '治安';
        break;
      case 12:
        name = '环保';
        break;
      case 13:
        name = '三农';
        break;
    }
    return name;
  };
  // 排序
  const exchangeOrder = (id: number, current: number, offset: number) => {
    dispatch(setConfig({ loading: true }));
    api
      .sortReportUser({ id, current, offset, type: 3 })
      .then(() => {
        message.success('操作成功');
        getData();
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };
  // 编辑
  const handleEdit = (val: any) => {
    setReporterFormDrawer({
      visible: true,
      formContent: val,
    })
  };
  // 排序
  // 排序
  const handelSort = (record: any, pos: any) => {

    const posChange = (value: number | undefined) => {
      pos = value;
    };
    // if (param.keyword || param.status) {
    //   newSort = undefined
    // }
    Modal.confirm({
      title: `调整排序`,
      icon: null,
      content: (
        <div>
          <InputNumber
            placeholder="请输入修改序号"
            min={1}
            max={total}
            onChange={posChange}
            defaultValue={pos}
            style={{ width: '100%' }}
            precision={0}
          />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!pos) {
          message.error('请填写序号');
          return;
        }
        api
          .moveReportUserPosition({
            id: record.id,
            position: pos,
            type: 3,
          })
          .then(() => {
            message.success('操作成功');
            getData();
            closeFunc();
          });
      },
    });
  };

  // 删除
  const handelDel = (val: any) => {
    Modal.confirm({
      title: '确定删除？',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        api
          .delReportUser({ id: val.id, type: 3 })
          .then(() => {
            message.success('操作成功');
            dispatch(setConfig({ loading: false }));
            getData();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const changeFilter = (key: string, val: any, goToFirstPage = true) => {
    let newFilter = {
      ...filter,
      [key]: val,
    };
    setFilter(newFilter);
    getData(goToFirstPage, newFilter);
  };

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      const newFilter = {
        ...filter,
      };
      setFilter(newFilter);
      getData(true, newFilter);
    }
  };

  // 报料标题点击
  const showDetailModal = (record: any, visible: boolean) => { };
  // 爆料人/审核人点击
  const showUserDetailModal = (record: any) => {
    dispatch(setConfig({ loading: true }))
    userApi
      .getUserDetail({ accountId: record.account_id })
      .then((r: any) => {
        dispatch(setConfig({ loading: false }))
        setUserDetailModal({
          key: uuid(),
          visible: true,
          detail: r.data.account,
        })
      })
      .catch(() => dispatch(setConfig({ loading: false })));
  };
  // 获取table
  const getData = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current;
    dispatch(getTableList('getReportUserList', 'list', { current: cur, size, ...newFilter }));
  };
  // 添加
  const add = () => {
    setReporterFormDrawer({
      visible: true,
      formContent: null,
    })
  };

  useEffect(() => {
    const { selectKeys, openKeys } = props;
    dispatch(setConfig({ selectKeys, openKeys }));
    getData(true);
  }, []);

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton
            perm="reporter:3:create"
            onClick={() => {
              add();
            }}
            style={{ marginLeft: 8 }}
          >
            添加联盟记者
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <div style={{ marginBottom: 20 }}>注：记者帮专区首页的【联盟】模块，默认显示前15个联盟记者；在联盟专区列表显示全部联盟记者</div>
        <Table
          func="getReportUserList"
          index="list"
          pagination={true}
          rowKey="id"
          columns={columns}
          filter={filter}
        />

        {/* 编辑爆料 */}
        <HelpExpertForm
          {...reporterFormDrawer}
          type={3}
          onClose={() => setReporterFormDrawer({ visible: false })}
          onEnd={() => {
            setReporterFormDrawer({ visible: false })
            getData()
          }}
        >
        </HelpExpertForm>

        {/* 用户详情弹窗 */}
        <Modal
          visible={userDetailModal.visible}
          key={userDetailModal.key}
          title="用户详情"
          width={800}
          onCancel={() => setUserDetailModal({ visible: false })}
          onOk={() => setUserDetailModal({ visible: false })}
        >
          {/*{user.visible && getUserDetail(user.detail)}*/}
          {userDetailModal.visible && <UserDetail detail={userDetailModal.detail} />}
        </Modal>

      </div>
    </>
  );
}
