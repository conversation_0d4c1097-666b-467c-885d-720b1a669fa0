import {
  Form,
  Icon,
  Input,
  Radio,
  Switch,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { opApi } from '@app/api';
import { Drawer, ImageUploader } from '@app/components/common';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';

const ConfigSaloonbStyleDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();

  const { getFieldDecorator, getFieldValue } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const [feature, setFeature] = useState({
    status: false,
    pic_url: "",
  });

  // 功能开关
  const getGpt24hmeetSwitch = () => {
    dispatch(setConfig({ mLoading: true }));
    opApi
      .getGpt24hmeetSwitch()
      .then((res: any) => {
        console.log("----------------------------------------------------------------------------------------------------");
        console.log(res);
        setFeature({
          status: res.data.web_feature.enabled ?? false,
          pic_url: res.data.web_feature.custom_icon_url ?? "",
        });
        dispatch(setConfig({ mLoading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ mLoading: false }));
      });
  };

  const handleSubmit = () => {
    // e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        dispatch(setConfig({ mLoading: true }));

        let parmas: any = {}
        if (values.status) {
          parmas.status = 1
        } else {
          parmas.status = 0
        }
        parmas.custom_icon_url = values.pic_url


        opApi.updateGpt24hmeetSwitch(parmas)
          .then((res: any) => {
            message.success('保存成功');
            dispatch(setConfig({ mLoading: false }));
            props.onEnd && props.onEnd();
          })
          .catch(() => {
            // message.error('添加失败');
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
        dispatch(setConfig({ mLoading: false }));
      }
    });
  };

  useEffect(() => {
    getGpt24hmeetSwitch();
  }, []);

  return (
    <Drawer
      title={
        <>
          <span>24H会客厅功能入口</span>

        </>
      }
      visible={props.visible}
      skey={props.skey}
      onClose={props.onClose}
      maskClosable={false}
      // width={500}
      onOk={handleSubmit}
      okPerm='web_feature:gpt_24hmeet_switch'
      okText="保存"
    >
      <Form {...formLayout}>
        <Form.Item label="功能入口">
          {getFieldDecorator('status', {
            initialValue: feature.status,
            valuePropName: 'checked',
            rules: [
              {
                required: true,
              },
            ],
          })(<Switch></Switch>)}
        </Form.Item>

        <>
          <Form.Item
            label="图片资源"
            extra={
              <span>
                支持jpg,jpeg,png,gif图片格式，比例为1:1，未自定义时将显示默认图片
              </span>
            }
          >
            {getFieldDecorator('pic_url', {
              initialValue: feature.pic_url,
              rules: [
                {
                  required: false,
                  message: '请上传图片资源',
                },
              ],
            })(<ImageUploader ratio={1 / 1} />)}
          </Form.Item>

        </>
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'ConfigSaloonbStyleDrawer' })(
  forwardRef<any, any>(ConfigSaloonbStyleDrawer)
);
