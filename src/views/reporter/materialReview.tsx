import {
  Button,
  Col,
  Divider,
  Icon,
  Input,
  Modal,
  Row,
  Select,
  Tooltip,
  message,
  Radio,
} from 'antd';
import {
  UserDetail,
  bizTypeMap,
  getCrumb,
  objectToQueryString,
  reportTypeMap,
  searchToObject,
  showReportIDDetailModal
} from '@app/utils/utils';
import { useHistory } from 'react-router';
import { useSelector, useDispatch } from 'react-redux';
import { getTableList } from '@app/action/tableList';
import { A, PreviewMCN, Table } from '@components/common';
import React, { useEffect, useState } from 'react';
import { reportApi as api, userApi } from '@app/api';
import { PermA, PermButton } from '@app/components/permItems';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import ReportDetailDrawer from './component/reportDetailDrawer';
import AddDomainModal from './component/addDomainModal';
import ReportAuditModal from './component/reportAuditModal';
import uuid from 'uuid';
import PassAuditModal from './component/passAuditModal';

export default function materialReview(props: any) {
  const dispatch = useDispatch();
  const history = useHistory();
  const { total, current, size, records = [] } = useSelector((state: any) => state.tableList);
  const [fieldsList, setFieldsList] = useState([])
  const [filter, setFilter] = useState({
    biz_type: '',
    report_type: '',
    audit_status: parseInt(searchToObject().audit_status ?? 0),
    search_type: 1,
    deleted: false,
    keyword: '',
    field_list: searchToObject().field_list || '',
    from_source: '',
  });
  const [searchState, setSearchState] = useState({
    search_type: 1,
    keyword: '',
  });

  // 通过弹窗
  const [pass, setPass] = useState<any>({
    visible: false,
    type: 0,
    formContent: null,
  })

  // 删除 不通过
  const [passWithReporter, setPassWithReporter] = useState<any>({
    visible: false,
    type: 0,
    formContent: null,
  })

  // 删除 不通过
  const [audit, setAudit] = useState<any>({
    visible: false,
    type: 0,
    formContent: null,
  })

  // 详情
  const [reportDetailDrawer, setReportDetailDrawer] = useState<any>({
    visible: false,
    record: null,
    skey: Date.now()
  })

  const [userDetailModal, setUserDetailModal] = useState<any>({
    visible: false,
    key: uuid(),
    detail: null,
  })

  const getFilter = () => {
    const body: any = {
      ...filter
    }
    if (body.deleted) {
      delete body.audit_status
    } else {
      delete body.deleted
    }
    if (!body.keyword) {
      delete body.keyword
      delete body.search_type
    }

    return body
  }

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const getColumns = () => {
    const columns: any = [
      {
        title: '序号',
        key: 'id',
        render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
        width: 50,
      },
      {
        title: '编号',
        dataIndex: 'number',
        width: 120,
        render: (text: any, record: any) => (<a onClick={() => showReportIDDetailModal(record)}>{text}</a>)
      },
      {
        title: '业务类型',
        dataIndex: 'biz_type',
        render: (biz_type: any) => bizTypeMap(biz_type),
        width: 80,
      },
      {
        title: '报料类型',
        dataIndex: 'report_type',
        render: (report_type: any) => reportTypeMap(report_type),
        width: 80,
      },
      {
        title: '关联领域',
        dataIndex: 'field_name',
        width: 120,
      },
      {
        title: '报料标题',
        key: 'title',
        dataIndex: 'title',
        render: (title: any, record: any) => (
          <a onClick={() => showDetailModal(record, true)}> {title}</a>
        ),
      },
      {
        title: '报料人',
        key: 'nick_name',
        dataIndex: 'nick_name',
        width: 110,
        render: (text: any, record: any) => {
          return <a onClick={() => showUserDetailModal(record)}>
            {text}
          </a>
        },
      },
      {
        title: '审核人',
        key: 'audit_person',
        dataIndex: 'audit_person',
        width: 110,
      },
      {
        title: filter.audit_status === 0 ? '报料时间' : '审核时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: number, record: any) => {
          const date = filter.audit_status === 0
            ? moment(record.created_at)
            : moment(record.audit_time)
          return <div>
            <div>{date.format('YYYY-MM-DD')}</div>
            <div>{date.format('HH:mm:ss')}</div>
          </div>
        },
        width: 100,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <span>
            {filter.audit_status === 0 ? (
              <>
                <PermA perm="report:doc:change_audit_status" onClick={() => handlePass(record)}>
                  通过
                </PermA>
                <Divider type="vertical" />
                <PermA perm="report:doc:change_audit_status" onClick={() => handleNoPass(record)}>
                  不通过
                </PermA>
                <Divider type="vertical" />
                <PermA perm="report:doc:change_audit_status" onClick={() => handleDel(record)}>
                  删除
                </PermA>
              </>
            ) : (
              ''
            )}
            {filter.audit_status === 1 ? (
              <>
                <PermA perm="report:doc:change_audit_status" onClick={() => handleReAudit(record)}>
                  重新审核
                </PermA>
                <Divider type="vertical" />
                <PermA perm="report:doc:change_audit_status" onClick={() => handleNoPass(record)}>
                  不通过
                </PermA>
                <Divider type="vertical" />
                <PermA perm="report:doc:change_audit_status" onClick={() => handleDel(record)}>
                  删除
                </PermA>
              </>
            ) : (
              ''
            )}
            {filter.audit_status === 2 ? (
              <>
                <PermA perm="report:doc:change_audit_status" onClick={() => handleReAudit(record)}>
                  重新审核
                </PermA>
                <Divider type="vertical" />
                <PermA perm="report:doc:change_audit_status" onClick={() => handlePass(record)}>
                  通过
                </PermA>
                <Divider type="vertical" />
                <PermA perm="report:doc:change_audit_status" onClick={() => handleDel(record)}>
                  删除
                </PermA>
              </>
            ) : (
              ''
            )}
          </span>
        ),
        width: 180,
      },
    ];

    if (filter.audit_status == 0) {
      columns.splice(7, 1)
    }

    return columns
  }

  const columns2: any = [
    {
      title: '序号',
      key: 'id',
      render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
      width: 50,
    },
    {
      title: '编号',
      dataIndex: 'number',
      width: 120,
      render: (text: any, record: any) => (<a onClick={() => showReportIDDetailModal(record)}>{text}</a>)
    },
    {
      title: '业务类型',
      dataIndex: 'biz_type',
      render: (biz_type: any) => bizTypeMap(biz_type),
      width: 80,
    },
    {
      title: '报料类型',
      dataIndex: 'report_type',
      render: (report_type: any) => reportTypeMap(report_type),
      width: 80,
    },
    {
      title: '关联领域',
      dataIndex: 'field_name',
      width: 160,
    },
    {
      title: '报料标题',
      key: 'title',
      dataIndex: 'title',
      render: (title: any, record: any) => (
        <a onClick={() => showDetailModal(record, true)}> {title}</a>
      ),
    },
    {
      title: '报料人',
      key: 'nick_name',
      dataIndex: 'nick_name',
      width: 110,
      render: (nick_name: any, record: any) => (
        <a onClick={() => showUserDetailModal(record)}>{nick_name}</a>
      ),
    },
    {
      title: '删除类型',
      dataIndex: 'audit_status',
      render: (audit_status: any) => resolveType3(audit_status),
      width: 110,
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any) => {
        if (record.audit_status !== 10) {
          return (
            <span>
              <PermA perm="report:doc:change_audit_status" onClick={() => handleDel(record)}>
                后台删除
              </PermA>
            </span>
          )
        }

        return <span>-</span>
      },
      width: 180,
    },
  ];

  // 删除类型转义
  const resolveType3 = (val: any) => {
    let name = '未知';
    switch (val) {
      case 10:
        name = '后台删除';
        break;
      case 11:
        name = '作者删除';
        break;
      case 12:
        name = '注销删除';
        break;
    }
    return name;
  };
  const changeFilter = (key: string, val: any, goToFirstPage = true) => {
    let newFilter = {
      ...filter,
      [key]: val,
    };
    if (key === 'audit_status') {
      newFilter.audit_status = val;
      if (val === 3) { }
      newFilter.deleted = val == 3

      let { pathname: path, search } = history.location
      const obj = searchToObject()
      obj[key] = val
      path = `${path}?${objectToQueryString(obj)}`
      history.replace(path)
    }

    setFilter(newFilter);
    // getData(goToFirstPage, newFilter);
  };

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      const newFilter = {
        ...filter,
        ...searchState,
      };
      setFilter(newFilter);
      // getData(true, newFilter);
    }
  };

  // 报料标题点击
  const showDetailModal = (record: any, visible: boolean) => {
    setReportDetailDrawer({
      record,
      visible,
      skey: Date.now()
    })
  };

  // 获取table
  const getData = (goToFirstPage = false, newFilter = getFilter()) => {
    let cur = goToFirstPage ? 1 : current;
    dispatch(getTableList('getMaterialsList', 'list', { current: cur, size, ...newFilter }));
  };
  // 通过
  const handlePass = (val: any) => {
    if (!!val.journalist_user_name && val.journalist_user_type == 1 && !val.reporter_id) {
      setPassWithReporter({
        visible: true,
        type: val.biz_type,
        formContent: val
      })
      return
    }

    Modal.confirm({
      title: '确定通过该报料？',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        api
          .changeAuditStatus({ id: val.id, biz_type: val.biz_type, status: 1 })
          .then(() => {
            message.success('操作成功');
            dispatch(setConfig({ loading: false }));
            getData();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
    // setPass({
    //   visible: true,
    //   type: 1,
    //   formContent: val
    // });
  };
  // 不通过
  const handleNoPass = (val: any) => {
    Modal.confirm({
      title: '确定不通过该报料？',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        api
          .changeAuditStatus({ id: val.id, biz_type: val.biz_type, status: 2 })
          .then(() => {
            message.success('操作成功');
            dispatch(setConfig({ loading: false }));
            getData();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });

    // setAudit({
    //   visible: true,
    //   type: 0,
    //   formContent: val
    // })
  };
  // 删除
  const handleDel = (val: any) => {
    Modal.confirm({
      title: '确定删除该报料？',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        api
          .changeAuditStatus({ id: val.id, biz_type: val.biz_type, status: 10 })
          .then(() => {
            message.success('操作成功');
            dispatch(setConfig({ loading: false }));
            getData();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });


    // setAudit({
    //   visible: true,
    //   type: 1,
    //   formContent: val
    // })
  };
  // 重新审核
  const handleReAudit = (val: any) => {
    Modal.confirm({
      title: '确定将该报料重新设为【待审核】状态？',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        api
          .changeAuditStatus({ id: val.id, biz_type: val.biz_type, status: 0 })
          .then(() => {
            message.success('操作成功');
            dispatch(setConfig({ loading: false }));
            getData();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };
  // 彻底删除
  const handleComDel = (val: any) => {
    setAudit({
      visible: true,
      type: 1,
      formContent: val
    })
  };
  // 通过确认
  const handleAdoptOk = () => {
    setPass({ visible: false })

    getData()
  };
  // 不通过确认
  const handleAuditOk = () => {
    setAudit({ visible: false })

    getData()
  };

  // 显示用户详情
  const showUserDetailModal = (record: any) => {
    dispatch(setConfig({ loading: true }))
    userApi
      .getUserDetail({ accountId: record.account_id })
      .then((r: any) => {
        dispatch(setConfig({ loading: false }))
        setUserDetailModal({
          key: uuid(),
          visible: true,
          detail: r.data.account,
        })
      })
      .catch(() => dispatch(setConfig({ loading: false })));
  }

  const getFieldsList = () => {
    api.getSimpleReportFieldList({}).then((res: any) => {
      setFieldsList(res.data.list || [])
    }).catch(() => {
    })
  }

  useEffect(() => {
    const { selectKeys, openKeys } = props;
    dispatch(setConfig({ selectKeys, openKeys }));

    getFieldsList()
  }, []);

  useEffect(() => {
    getData(true);
  }, [filter])

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Radio.Group
            defaultValue={filter.audit_status}
            buttonStyle="solid"
            onChange={(val: any) => changeFilter('audit_status', val.target.value)}
          >
            <Radio.Button value={0}>待审核</Radio.Button>
            <Radio.Button value={1}>已通过</Radio.Button>
            <Radio.Button value={2}>不通过</Radio.Button>
            <Radio.Button value={3}>已删除</Radio.Button>
          </Radio.Group>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={14}>
            <Select
              style={{ width: 110 }}
              value={filter.biz_type}
              onChange={(val: any) => changeFilter('biz_type', val)}
            >
              <Select.Option value="">业务类型</Select.Option>
              <Select.Option value={1}>问政</Select.Option>
              <Select.Option value={2}>帮办</Select.Option>
              <Select.Option value={3}>帮帮团</Select.Option>
              <Select.Option value={4}>应急求助</Select.Option>
              <Select.Option value={5}>小店帮</Select.Option>
            </Select>

            <Select
              style={{ width: 110, marginLeft: 8 }}
              value={filter.report_type}
              onChange={(val: any) => changeFilter('report_type', val)}
            >
              <Select.Option value="">报料类型</Select.Option>
              <Select.Option value={1}>投诉举报</Select.Option>
              <Select.Option value={2}>咨询求助</Select.Option>
              <Select.Option value={3}>建言献策</Select.Option>
              <Select.Option value={4}>防汛应急</Select.Option>
              <Select.Option value={5}>防台应急</Select.Option>
              <Select.Option value={6}>其它应急</Select.Option>
              <Select.Option value={7}>求助力</Select.Option>
              <Select.Option value={8}>求表扬</Select.Option>
              <Select.Option value={9}>求流量</Select.Option>
            </Select>
            <Select
              style={{ width: 110, marginLeft: 8 }}
              value={filter.field_list}
              onChange={(val: any) => changeFilter('field_list', val)}
            >
              <Select.Option key={-2} value="">关联领域</Select.Option>
              {/* <Select.Option key={'0'} value="0">无领域</Select.Option> */}
              {fieldsList?.map((item: any) => {
                return <Select.Option key={`${item.id}`} value={`${item.id}`}>{item.name}</Select.Option>
              })}
            </Select>
            <Select
              style={{ width: 110, marginLeft: 8 }}
              value={filter.from_source}
              onChange={(val: any) => changeFilter('from_source', val)}
            >
              <Select.Option key={-2} value="">报料渠道</Select.Option>
              <Select.Option key={'app'} value="app">客户端</Select.Option>
              <Select.Option key={'h5'} value="h5">端外h5</Select.Option>
            </Select>
          </Col>
          <Col span={10} style={{ textAlign: 'right' }}>
            <Select
              value={searchState.search_type}
              style={{ width: 110, marginRight: 8 }}
              onChange={(search_type: any) => setSearchState({ ...searchState, search_type })}
            >
              <Select.Option value={1}>标题</Select.Option>
              <Select.Option value={2}>编号</Select.Option>
              <Select.Option value={5}>报料ID</Select.Option>
              <Select.Option value={3}>昵称</Select.Option>
              <Select.Option value={4}>小潮号</Select.Option>
            </Select>
            <Input
              value={searchState.keyword}
              style={{ marginRight: 8, width: 160 }}
              onChange={(e: any) => setSearchState({ ...searchState, keyword: e.target.value })}
              onKeyPress={handleKey}
              placeholder="输入搜索内容"
            />
            <Button onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" /> 搜索
            </Button>
          </Col>
        </Row>
        <Table
          func="getMaterialsList"
          index="list"
          pagination={true}
          rowKey="id"
          filter={getFilter()}
          columns={filter.audit_status === 3 ? columns2 : getColumns()}
        />

        {/* 详情 */}
        <ReportDetailDrawer
          record={reportDetailDrawer.record}
          key={reportDetailDrawer.skey}
          visible={reportDetailDrawer.visible}
          onClose={() => setReportDetailDrawer({ visible: false })}
          type={0}
          onReload={() => getData(false)}
        >
        </ReportDetailDrawer>

        {/* 指派领域 审核通过 */}
        <AddDomainModal
          {...pass}
          onCancel={() => setPass({ visible: false })}
          onOk={handleAdoptOk}
        ></AddDomainModal>

        {/* 审核不通过 删除 */}
        <ReportAuditModal
          {...audit}
          onCancel={() => setAudit({ visible: false })}
          onOk={handleAuditOk}
        ></ReportAuditModal>

        {/* 用户详情弹窗 */}
        <Modal
          visible={userDetailModal.visible}
          key={userDetailModal.key}
          title="用户详情"
          width={800}
          onCancel={() => setUserDetailModal({ visible: false })}
          onOk={() => setUserDetailModal({ visible: false })}
        >
          {/*{user.visible && getUserDetail(user.detail)}*/}
          {userDetailModal.visible && <UserDetail detail={userDetailModal.detail} />}
        </Modal>

        <PassAuditModal
          {...passWithReporter}
          onCancel={() => setPassWithReporter({ visible: false })}
          onOk={() => {
            setPassWithReporter({ visible: false })
            getData()
          }}
        ></PassAuditModal>
      </div>
    </>
  );
}
