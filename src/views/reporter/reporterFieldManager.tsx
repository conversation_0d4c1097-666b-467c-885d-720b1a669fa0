import {
  Col,
  Divider,
  Input,
  Modal,
  Row,
  message,
  InputNumber,
  Radio,
  Tooltip,
  Icon,
  Select,
  Button,
} from 'antd';
import {
  getCrumb,
  objectToQueryString,
  searchToObject,
  UserDetail,
} from '@app/utils/utils';
import { useHistory } from 'react-router';
import { useSelector, useDispatch, useStore } from 'react-redux';
import { getTableList } from '@app/action/tableList';
import { Table, OrderColumn } from '@components/common';
import React, { useEffect, useState } from 'react';
import { reportApi as api, sysApi, userApi } from '@app/api';
import { PermA, PermButton } from '@app/components/permItems';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import ReportFieldPermDrawer from './component/ReportFieldPermDrawer';

export default function reporterFieldManager(props: any) {
  const history = useHistory();
  const dispatch = useDispatch();
  const { total, current, size, records = [] } = useSelector((state: any) => state.tableList);
  const { session } = useStore().getState();
  const [type, setType] = useState(parseInt(searchToObject().type ?? 0))
  const [fieldsList, setFieldsList] = useState([])
  const [filter, setFilter] = useState({
    field_id: '',
    search_type: 0,
    keyword: '',
  });

  const [searchState, setSearchState] = useState({
    search_type: 0,
    keyword: '',
  });

  const [permDrawer, setPermDrawer] = useState<any>({
    visible: false,
    formContent: null,
  })

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const getFieldsList = () => {
    api.getAllReportFieldList({ }).then((res: any) => {
      setFieldsList(res.data.list.records || [])
    }).catch(() => {
    })
  }

  const exchangeOrder = (id: number, sort_flag: number) => {
    dispatch(setConfig({ loading: true }));
    api.sortReportField({ id, sort_flag })
      .then(() => {
        dispatch(setConfig({ loading: false }));
        message.success('操作成功');
        getData();
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  }

  const columns: any = [
    {
      title: '排序',
      key: 'order',
      render: (text: any, record: any, i: number) => {
        return <OrderColumn
          pos={getSeq(i)}
          start={1}
          end={total}
          perm="reporter:field:category:mgr"
          // disableUp={hasFilter || !record.enabled || (i > 0 && !records[i - 1].enabled)}
          // disableDown={hasFilter || !record.enabled || (i < records.length - 1 && !records[i + 1].enabled)}
          onUp={() => exchangeOrder(record.id, 0)}
          onDown={() => exchangeOrder(record.id, 1)}
        />
      },
      width: 70,
    },
    {
      title: '序号',
      render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
      width: 50,
    },
    {
      title: '领域',
      dataIndex: 'name',
    },
    {
      title: '权限用户',
      dataIndex: 'user_count',
      width: 160,
      render: (text: any, record: any) => <a onClick={() => {
        setFilter({
          ...filter,
          field_id: record.id
        })
        onChangeType(1)
      }}>{text}</a>
    },
    {
      title: '已通过报料',
      dataIndex: 'report_count',
      width: 160,
      render: (text: any, record: any) => <a onClick={() => {
        history.push(`/view/reportMgr?audit_status=1&field_list=${record.id}`)
      }}>{text}</a>
    },
    {
      title: '最后操作人',
      dataIndex: 'updated_by',
      width: 160
    },
    {
      title: '最后操作时间',
      key: 'updated_at',
      dataIndex: 'updated_at',
      render: (text: number, record: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 170
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any, i: number) => (
        <>
          <PermA perm="reporter:field:category:mgr" style={{ marginRight: 10 }} onClick={() => editField(record)}>
            编辑
          </PermA>
          <PermA perm="reporter:field:category:mgr" onClick={() => deleteField(record)}>
            删除
          </PermA>
        </>
      ),
      width: 100,
    },
  ];

  const permColumns: any = [
    {
      title: '序号',
      render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
      width: 50,
    },
    {
      title: '8531账号',
      dataIndex: 'user_name',
      width: 160,
    },
    {
      title: '真实姓名',
      dataIndex: 'name',
      width: 160,
    },
    {
      title: '权限范围',
      dataIndex: 'field_list',
      // width: 160,
      render: (text: any, record: any) => {
        return text?.map((item: any) => {
          return item.name
        }).join(',')
      }
    },
    {
      title: '最后操作人',
      dataIndex: 'updated_by',
      width: 160
    },
    {
      title: '最后操作时间',
      key: 'updated_at',
      dataIndex: 'updated_at',
      render: (text: number, record: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 170
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any, i: number) => (
        <>
          <PermA perm="reporter:field:permission:mgr" style={{ marginRight: 10 }} onClick={() => {
            setPermDrawer({
              visible: true,
              formContent: record
            })
          }}>
            编辑
          </PermA>
          <PermA perm="reporter:field:permission:mgr" onClick={() => deleteFieldPerm(record)}>
            删除
          </PermA>
        </>
      ),
      width: 100,
    },
  ];

  // 最新报道模块显示的稿件数量
  const changeRecommendReportCount = (val: any) => {
    let pos = val
    const valueChange = (v: any) => {
      // msg = v.replace(/\r\n/g, '\n').slice(0, 40)
      pos = v
      modal.update({
        content: (<>
          <div>最新报道模块显示的稿件数量：</div>
          <InputNumber
            placeholder="请输入1～10的数字"
            min={1}
            max={10}
            onChange={(e) => valueChange(e)}
            value={pos}
            style={{ width: '100%' }}
            precision={0}
          />
        </>)
      })
    }

    var modal = Modal.confirm({
      title: '最新报道',
      icon: null,
      content: (<>
        <div>最新报道模块显示的稿件数量：</div>
        <InputNumber
          placeholder="请输入1～10的数字"
          min={1}
          max={10}
          onChange={(e) => valueChange(e)}
          defaultValue={pos}
          style={{ width: '100%' }}
          precision={0}
        />
      </>),
      onOk: (destroy: Function) => {
        // const { permissions } = session;
        // const disabled = permissions.indexOf('report:articlenum_save') === -1;
        // if (disabled) {
        //   message.error('没有权限');
        //   destroy()
        //   return
        // }

        if (pos == null) {
          message.error('请输入1～10的数字');
          return
        }

        api.saveReportHomeConfig({ id: 'latest', value: pos || 1 }).then((data: any) => {
          message.success('操作成功');
          getData()
        }).catch((e) => {
        })

        // run(api.gptQuestionWelcomeSave, { welcome: msg }, true).then(() => {
        //   message.success('操作成功');
        // });
        destroy()
      }
    })
  }

  const changeReportFinishRate = (val: any) => {
    let pos = val
    const valueChange = (v: any) => {
      // msg = v.replace(/\r\n/g, '\n').slice(0, 40)
      pos = v
      modal.update({
        okButtonProps: { disabled: typeof v != 'number' },
        content: (<>
          <div>请填写对外展示的办结率：</div>
          <Row>
            <Col span={22}>
              <InputNumber
                placeholder="精确到小数点后1位"
                min={0}
                max={100}
                onChange={(e) => valueChange(e)}
                value={pos}
                style={{ width: '100%' }}
                precision={1}
              />
            </Col>
            <Col span={2}><div style={{ height: '30px', lineHeight: '30px', textAlign: 'center' }}>%</div></Col>
          </Row>
        </>)
      })
    }

    var modal = Modal.confirm({
      title: '办结率',
      icon: null,
      okButtonProps: { disabled: !pos },
      content: (<>
        <div>请填写对外展示的办结率：</div>
        <Row>
          <Col span={22}>
            <InputNumber
              placeholder="精确到小数点后1位"
              min={0}
              max={100}
              onChange={(e) => valueChange(e)}
              defaultValue={pos}
              style={{ width: '100%' }}
              precision={1}
            />
          </Col>
          <Col span={2}><div style={{ height: '30px', lineHeight: '30px', textAlign: 'center' }}>%</div></Col>
        </Row>
      </>),
      onOk: (destroy: Function) => {
        // const { permissions } = session;
        // const disabled = permissions.indexOf('report:articlenum_save') === -1;
        // if (disabled) {
        //   message.error('没有权限');
        //   destroy()
        //   return
        // }

        // if (pos == null) {
        //   message.error('请输入1～10的数字');
        //   return
        // }

        api.saveReportHomeConfig({ id: 'rate', value: pos || 0 }).then((data: any) => {
          message.success('操作成功');
          getData()
        }).catch((e) => {
        })

        // run(api.gptQuestionWelcomeSave, { welcome: msg }, true).then(() => {
        //   message.success('操作成功');
        // });
        destroy()
      }
    })
  }

  const changeTotalCount = (val: any, sysVal: any) => {
    let pos = val
    const valueChange = (v: any) => {
      // msg = v.replace(/\r\n/g, '\n').slice(0, 40)
      pos = v
      modal.update({
        okButtonProps: { disabled: pos === null || pos === '' },
        content: (<>
          <Row style={{ height: '30px' }}>
            <Col span={8}><div style={{ textAlign: 'right', lineHeight: '30px' }}>实际报料数量：</div></Col>
            <Col span={16} style={{ lineHeight: '30px' }}>{sysVal}</Col>
          </Row>
          <Row style={{ height: '30px' }}>
            <Col span={8}><div style={{ textAlign: 'right', lineHeight: '30px' }}>额外增量：</div></Col>
            <Col span={16}>
              <InputNumber
                placeholder="请填写对外显示时要增加的数量"
                min={0}
                max={99999999}
                onChange={(e) => valueChange(e)}
                value={pos}
                style={{ width: '100%' }}
                precision={0}
              />
            </Col>
          </Row>
          <Row style={{ height: '30px' }}>
            <Col span={8}><div style={{ textAlign: 'right', lineHeight: '30px' }}>最终呈现：</div></Col>
            <Col span={16} style={{ lineHeight: '30px' }}>{parseInt(sysVal || 0) + parseInt(pos || 0)}</Col>
          </Row>
        </>)
      })
    }

    var modal = Modal.confirm({
      title: '累计报料数量',
      icon: null,
      okButtonProps: { disabled: pos === null || pos === '' },
      content: (<>
        <Row style={{ height: '30px' }}>
          <Col span={8}><div style={{ textAlign: 'right', lineHeight: '30px' }}>实际报料数量：</div></Col>
          <Col span={16} style={{ lineHeight: '30px' }}>{sysVal}</Col>
        </Row>
        <Row style={{ height: '30px' }}>
          <Col span={8}><div style={{ textAlign: 'right', lineHeight: '30px' }}>额外增量：</div></Col>
          <Col span={16}>
            <InputNumber
              placeholder="请填写对外显示时要增加的数量"
              min={0}
              max={99999999}
              onChange={(e) => valueChange(e)}
              value={pos}
              style={{ width: '100%' }}
              precision={0}
            />
          </Col>
        </Row>
        <Row style={{ height: '30px' }}>
          <Col span={8}><div style={{ textAlign: 'right', lineHeight: '30px' }}>最终呈现：</div></Col>
          <Col span={16} style={{ lineHeight: '30px' }}>{parseInt(sysVal || 0) + parseInt(val || 0)}</Col>
        </Row>
      </>),
      onOk: (destroy: Function) => {
        // const { permissions } = session;
        // const disabled = permissions.indexOf('report:articlenum_save') === -1;
        // if (disabled) {
        //   message.error('没有权限');
        //   destroy()
        //   return
        // }

        // if (pos == null) {
        //   message.error('请输入1～10的数字');
        //   return
        // }
        api.saveReportHomeConfig({ id: 'total', value: parseInt(pos || 0) }).then((data: any) => {
          message.success('操作成功');
          destroy()
          getData()
        }).catch((e) => {
        })

        // run(api.gptQuestionWelcomeSave, { welcome: msg }, true).then(() => {
        //   message.success('操作成功');
        // });
      }
    })
  }

  const deleteFieldPerm = (record: any) => {
    Modal.confirm({
      title: '确定删除该账号及对应权限？',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        api.deleteReportFieldPerm({ user_name: record.user_name }).then((data: any) => {
          dispatch(setConfig({ loading: false }));
          message.success('操作成功');
          getData()
        }).catch((e) => {
          dispatch(setConfig({ loading: false }));
        })
      },
    })
  }

  const deleteField = (record: any) => {
    Modal.confirm({
      title: '删除后，该领域下的报料将变为“无领域”',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        api.deleteReportField({ id: record.id }).then((data: any) => {
          dispatch(setConfig({ loading: false }));
          message.success('操作成功');
          getData()
        }).catch((e) => {
          dispatch(setConfig({ loading: false }));
        })
      },
    })
  }

  const editField = (record: any) => {
    const func: keyof (typeof api) = !!record ? 'eidtReportField' : 'createReportField'
    let pos = record?.name || ''
    const valueChange = (v: any) => {
      pos = v
      modal.update({
        okButtonProps: { disabled: !pos?.trim() },
        content: (<>
          <Input
            maxLength={10}
            placeholder="请输入名称，最多10个字"
            onChange={(e) => valueChange(e.target.value)}
            defaultValue={pos}
            style={{ width: '100%' }}
          />
        </>)
      })
    }

    var modal = Modal.confirm({
      title: '添加领域分类',
      icon: null,
      okButtonProps: { disabled: !pos?.trim() },
      content: (<>
        <Input
          maxLength={10}
          placeholder="请输入名称，最多10个字"
          onChange={(e) => valueChange(e.target.value)}
          defaultValue={pos}
          style={{ width: '100%' }}
        />
      </>),
      onOk: (destroy: Function) => {
        const params: any = { name: pos?.trim() }
        if (!!record) {
          params.id = record.id
        }
        api[func](params).then((data: any) => {
          message.success('操作成功');
          destroy()
          getData()
        }).catch((e) => {
        })

        // run(api.gptQuestionWelcomeSave, { welcome: msg }, true).then(() => {
        //   message.success('操作成功');
        // });
      }
    })
  }

  // 排序
  const handelSort = (record: any, pos: any) => {

    const posChange = (value: number | undefined) => {
      pos = value;
    };
    // if (param.keyword || param.status) {
    //   newSort = undefined
    // }
    Modal.confirm({
      title: `调整排序`,
      icon: null,
      content: (
        <div>
          <InputNumber
            placeholder="请输入修改序号"
            min={1}
            max={total}
            onChange={posChange}
            defaultValue={pos}
            style={{ width: '100%' }}
            precision={0}
          />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!pos) {
          message.error('请填写序号');
          return;
        }
        api
          .moveReportUserPosition({
            id: record.id,
            position: pos,
            type: 3,
          })
          .then(() => {
            message.success('操作成功');
            getData();
            closeFunc();
          });
      },
    });
  };

  const changeFilter = (key: string, val: any, goToFirstPage = true) => {
    let newFilter = {
      ...filter,
      [key]: val,
    };
    setFilter(newFilter);
    getData(goToFirstPage, newFilter);
  };

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      const newFilter = {
        ...filter,
        ...searchState,
      };
      setFilter(newFilter);
      getData(true, newFilter);
    }
  };

  // 获取table
  const getData = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current;
    // ...newFilter
    const func: any = type == 0 ? 'getReportFieldList' : 'getReportFieldPermList'
    let params: any
    if (type == 0) {
      params = { current: cur, size }
    } else {
      params = { current: cur, size, ...newFilter }
      if (!params.field_id) {
        delete params.field_id
      }
      if (!params.keyword) {
        delete params.keyword
        delete params.search_type
      }
    }
    dispatch(getTableList(func, 'list', params));
  };

  useEffect(() => {
    const { selectKeys, openKeys } = props;
    dispatch(setConfig({ selectKeys, openKeys }));
  }, []);

  useEffect(() => {
    getData(true);

    if (type == 1) {
      getFieldsList()
    }

  }, [type]);

  const onChangeType = (v: any) => {
    let { pathname: path, search } = history.location
    const obj = searchToObject()
    obj.type = v
    path = `${path}?${objectToQueryString(obj)}`
    history.replace(path)
    setType(v)
  }

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Radio.Group
            value={type}
            buttonStyle="solid"
            onChange={(val: any) => onChangeType(val.target.value)}
          >
            <Radio.Button value={0} disabled={session.permissions.indexOf('reporter:field:category:list') === -1}>领域分类</Radio.Button>
            <Radio.Button value={1} disabled={session.permissions.indexOf('reporter:field:permission:list') === -1}>领域权限</Radio.Button>
          </Radio.Group>
          <PermButton
            perm={type == 0 ? `reporter:field:category:mgr` : 'reporter:field:permission:mgr'}
            onClick={() => {
              if (type == 0) {
                editField(null)
              } else {
                setPermDrawer({
                  visible: true
                })
              }
            }}
            style={{ marginLeft: 8, marginRight: 8 }}
          >
            {type == 0 ? '添加领域分类' : '添加账号权限'}
          </PermButton>
          {type == 1 && <Tooltip title={'8531账号需同时有「报料审核/处理」的页面及功能权限，以及指定领域的数据权限，才能在后台看到相应的报料数据并进行操作'}>
            <Icon type="question-circle" />
          </Tooltip>}
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        {type == 1 && <Row style={{ marginBottom: 16 }}>
          <Col span={14}>
            <Select
              style={{ width: 110 }}
              value={filter.field_id}
              onChange={(val: any) => changeFilter('field_id', val)}
            >
              <Select.Option key={-2} value="">权限范围</Select.Option>
              <Select.Option key={-1} value="-1">全部报料</Select.Option>
              <Select.Option key={0} value="0">无领域</Select.Option>
              {fieldsList?.map((item: any) => {
                return <Select.Option key={item.id} value={item.id}>{item.name}</Select.Option>
              })}
            </Select>
          </Col>
          <Col span={10} style={{ textAlign: 'right' }}>
            <Select
              value={searchState.search_type}
              style={{ width: 110, marginRight: 8 }}
              onChange={(search_type: any) => setSearchState({ ...searchState, search_type })}
            >
              <Select.Option value={0}>真实姓名</Select.Option>
              <Select.Option value={1}>8531账号</Select.Option>
            </Select>
            <Input
              value={searchState.keyword}
              style={{ marginRight: 8, width: 160 }}
              onChange={(e: any) => setSearchState({ ...searchState, keyword: e.target.value })}
              onKeyPress={handleKey}
              placeholder="输入搜索内容"
            />
            <Button onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" /> 搜索
            </Button>
          </Col>
        </Row>}
        <Table
          func={type == 0 ? 'getReportFieldList' : 'getReportFieldPermList'}
          index="list"
          pagination={true}
          rowKey={type == 0 ? 'id' : 'user_name'}
          columns={type == 0 ? columns : permColumns}
          filter={type == 0 ? {} : filter}
        />

        <ReportFieldPermDrawer
          {...permDrawer}
          onClose={() => setPermDrawer({ visible: false })}
          onEnd={() => {
            setPermDrawer({ visible: false })
            getData()
          }}
        >

        </ReportFieldPermDrawer>
      </div>
    </>
  );
}
