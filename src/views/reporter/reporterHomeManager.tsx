import { Col, Divider, Input, Modal, Row, message, InputNumber, Radio } from 'antd';
import { getCrumb, objectToQueryString, searchToObject, UserDetail } from '@app/utils/utils';
import { useHistory } from 'react-router';
import { useSelector, useDispatch, useStore } from 'react-redux';
import { clearTableList, getTableList } from '@app/action/tableList';
import { Table, OrderColumn } from '@components/common';
import React, { useEffect, useState } from 'react';
import { reportApi as api, sysApi, userApi } from '@app/api';
import { PermA, PermButton } from '@app/components/permItems';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import HelpExpertForm from './component/helpExpertForm';
import uuid from 'uuid';
import { isNumber } from 'lodash';
import ReportDataRecommendDrawer from './component/ReportDataRecommendDrawer';
import ReportServiceZoneDrawer from './component/ReportServiceZoneDrawer';

export default function reporterHomeManager(props: any) {
  const history = useHistory();
  const dispatch = useDispatch();
  const {
    total,
    current,
    size,
    records = [],
    allData,
  } = useSelector((state: any) => state.tableList);
  const { session } = useStore().getState();
  const [type, setType] = useState(parseInt(searchToObject().type ?? 1));
  const [filter, setFilter] = useState({});

  const [dataRecommend, setDataRecommend] = useState<any>({
    visible: false,
    formContent: null,
  });

  const [dataServiceZone, setDataServiceZone] = useState<any>({
    visible: false,
    formContent: null,
  });

  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const handleSort = (record: any, flag: any) => {
    dispatch(setConfig({ loading: true }));
    api
      .sortReportRecommendData({ id: record.id, sort_flag: flag })
      .then(() => {
        message.success('操作成功');
        getData();
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  const columns: any = [
    {
      title: '序号',
      key: 'id',
      render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
      width: 50,
    },
    {
      title: '功能',
      dataIndex: 'name',
      width: 160,
    },
    {
      title: '配置项',
      dataIndex: 'value',
      width: 160,
      render: (text: any, record: any) =>
        record.id == 'total'
          ? parseInt(record.system_value || '0') + parseInt(record.value || '0')
          : text,
    },
    {
      title: '最后操作人',
      dataIndex: 'updated_by',
      width: 160,
    },
    {
      title: '最后操作时间',
      key: 'updated_at',
      dataIndex: 'updated_at',
      render: (text: number, record: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 170,
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any, i: number) => (
        <PermA perm="reporter:home_mgr:edit" onClick={() => handleEdit(record)}>
          编辑
        </PermA>
      ),
      width: 100,
    },
  ];

  const recommendColumns: any = [
    {
      title: '排序',
      key: 'sort',
      render: (text: any, record: any, i: number) => (
        <OrderColumn
          perm="jzbdata:sort"
          pos={i}
          start={0}
          end={allData.on_show_count - 1}
          disable={record.status == 0}
          onUp={() => handleSort(record, 0)}
          onDown={() => handleSort(record, 1)}
        />
      ),
      width: 70,
    },
    {
      title: '序号',
      key: 'id',
      render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
      width: 50,
    },
    {
      title: '推荐位标题',
      dataIndex: 'title',
      width: 160,
      render: (title: any, record: any) => (
        <a onClick={() => handleEditRecommendData(record)}>{title}</a>
      ),
    },
    {
      title: '显示位置',
      dataIndex: 'position',
      width: 160,
      render: (text: any, record: any) =>
        [
          '',
          '最新报道前面',
          '问政模块前面',
          '帮办模块前面',
          '帮帮团模块前面',
          '小店帮模块前面',
          '联盟模块前面',
          // '联动帮模块前面',
        ][text],
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 160,
      render: (text: any, record: any) => ['未展示', '展示中'][text],
    },
    {
      title: '最后操作人',
      dataIndex: 'updated_by',
      width: 160,
    },
    {
      title: '最后操作时间',
      key: 'updated_at',
      dataIndex: 'updated_at',
      render: (text: number, record: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 170,
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any, i: number) => (
        <span>
          <PermA perm="jzbdata:update" onClick={() => handleEditRecommendData(record)}>
            编辑
          </PermA>
          <Divider type="vertical" />
          <PermA perm="jzbdata:update_status" onClick={() => handleChangeStatus(record)}>
            {record.status == 0 ? '上架' : '下架'}
          </PermA>
          <Divider type="vertical" />
          <PermA perm="jzbdata:delete" onClick={() => handleDeleteRecommendData(record)}>
            删除
          </PermA>
        </span>
      ),
      width: 100,
    },
  ];

  const handleEdit = (record: any) => {
    if (record.id == 'latest') {
      changeRecommendReportCount(record.value);
    } else if (record.id == 'total') {
      changeTotalCount(record.value, record.system_value);
    } else if (record.id == 'rate') {
      changeReportFinishRate(record.value);
    } else if (record.id == 'week') {
      changeWeekURL(record.value);
    }
  };

  const handleDeleteRecommendData = (record: any) => {
    Modal.confirm({
      title: `确认删除推荐位《${record.title}》?`,
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        api
          .deleteReportRecommendData({ id: record.id })
          .then(() => {
            message.success('操作成功');
            getData();
            dispatch(setConfig({ loading: false }));
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const handleEditRecommendData = (record: any) => {
    dispatch(setConfig({ loading: true }));
    api
      .getReportRecommendDataDetail({ id: record.id })
      .then((res: any) => {
        dispatch(setConfig({ loading: false }));
        setDataRecommend({
          visible: true,
          formContent: res.data?.recommend,
        });
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  const handleChangeStatus = (record: any) => {
    dispatch(setConfig({ loading: true }));
    api
      .changeReportRecommendDataStatus({ id: record.id, status: record.status == 0 ? 1 : 0 })
      .then(() => {
        message.success('操作成功');
        getData();
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  // 最新报道模块显示的稿件数量
  const changeRecommendReportCount = (val: any) => {
    let pos = val;
    const valueChange = (v: any) => {
      // msg = v.replace(/\r\n/g, '\n').slice(0, 40)
      pos = v;
      modal.update({
        content: (
          <>
            <div>最新报道模块显示的稿件数量：</div>
            <InputNumber
              placeholder="请输入1～10的数字"
              min={1}
              max={10}
              onChange={(e) => valueChange(e)}
              value={pos}
              style={{ width: '100%' }}
              precision={0}
            />
          </>
        ),
      });
    };

    var modal = Modal.confirm({
      title: '最新报道',
      icon: null,
      content: (
        <>
          <div>最新报道模块显示的稿件数量：</div>
          <InputNumber
            placeholder="请输入1～10的数字"
            min={1}
            max={10}
            onChange={(e) => valueChange(e)}
            defaultValue={pos}
            style={{ width: '100%' }}
            precision={0}
          />
        </>
      ),
      onOk: (destroy: Function) => {
        // const { permissions } = session;
        // const disabled = permissions.indexOf('report:articlenum_save') === -1;
        // if (disabled) {
        //   message.error('没有权限');
        //   destroy()
        //   return
        // }

        if (pos == null) {
          message.error('请输入1～10的数字');
          return;
        }

        api
          .saveReportHomeConfig({ id: 'latest', value: pos || 1 })
          .then((data: any) => {
            message.success('操作成功');
            getData();
          })
          .catch((e) => {});

        // run(api.gptQuestionWelcomeSave, { welcome: msg }, true).then(() => {
        //   message.success('操作成功');
        // });
        destroy();
      },
    });
  };

  const changeReportFinishRate = (val: any) => {
    let pos = val;
    const valueChange = (v: any) => {
      // msg = v.replace(/\r\n/g, '\n').slice(0, 40)
      pos = v;
      modal.update({
        okButtonProps: { disabled: typeof v != 'number' },
        content: (
          <>
            <div>请填写对外展示的办结率：</div>
            <Row>
              <Col span={22}>
                <InputNumber
                  placeholder="精确到小数点后1位"
                  min={0}
                  max={100}
                  onChange={(e) => valueChange(e)}
                  value={pos}
                  style={{ width: '100%' }}
                  precision={1}
                />
              </Col>
              <Col span={2}>
                <div style={{ height: '30px', lineHeight: '30px', textAlign: 'center' }}>%</div>
              </Col>
            </Row>
          </>
        ),
      });
    };

    var modal = Modal.confirm({
      title: '办结率',
      icon: null,
      okButtonProps: { disabled: !pos },
      content: (
        <>
          <div>请填写对外展示的办结率：</div>
          <Row>
            <Col span={22}>
              <InputNumber
                placeholder="精确到小数点后1位"
                min={0}
                max={100}
                onChange={(e) => valueChange(e)}
                defaultValue={pos}
                style={{ width: '100%' }}
                precision={1}
              />
            </Col>
            <Col span={2}>
              <div style={{ height: '30px', lineHeight: '30px', textAlign: 'center' }}>%</div>
            </Col>
          </Row>
        </>
      ),
      onOk: (destroy: Function) => {
        // const { permissions } = session;
        // const disabled = permissions.indexOf('report:articlenum_save') === -1;
        // if (disabled) {
        //   message.error('没有权限');
        //   destroy()
        //   return
        // }

        // if (pos == null) {
        //   message.error('请输入1～10的数字');
        //   return
        // }

        api
          .saveReportHomeConfig({ id: 'rate', value: pos || 0 })
          .then((data: any) => {
            message.success('操作成功');
            getData();
          })
          .catch((e) => {});

        // run(api.gptQuestionWelcomeSave, { welcome: msg }, true).then(() => {
        //   message.success('操作成功');
        // });
        destroy();
      },
    });
  };

  const changeTotalCount = (val: any, sysVal: any) => {
    let pos = val;
    const valueChange = (v: any) => {
      // msg = v.replace(/\r\n/g, '\n').slice(0, 40)
      pos = v;
      modal.update({
        okButtonProps: { disabled: pos === null || pos === '' },
        content: (
          <>
            <Row style={{ height: '30px' }}>
              <Col span={8}>
                <div style={{ textAlign: 'right', lineHeight: '30px' }}>实际报料数量：</div>
              </Col>
              <Col span={16} style={{ lineHeight: '30px' }}>
                {sysVal}
              </Col>
            </Row>
            <Row style={{ height: '30px' }}>
              <Col span={8}>
                <div style={{ textAlign: 'right', lineHeight: '30px' }}>额外增量：</div>
              </Col>
              <Col span={16}>
                <InputNumber
                  placeholder="请填写对外显示时要增加的数量"
                  min={0}
                  max={99999999}
                  onChange={(e) => valueChange(e)}
                  value={pos}
                  style={{ width: '100%' }}
                  precision={0}
                />
              </Col>
            </Row>
            <Row style={{ height: '30px' }}>
              <Col span={8}>
                <div style={{ textAlign: 'right', lineHeight: '30px' }}>最终呈现：</div>
              </Col>
              <Col span={16} style={{ lineHeight: '30px' }}>
                {parseInt(sysVal || 0) + parseInt(pos || 0)}
              </Col>
            </Row>
          </>
        ),
      });
    };

    var modal = Modal.confirm({
      title: '累计报料数量',
      icon: null,
      okButtonProps: { disabled: pos === null || pos === '' },
      content: (
        <>
          <Row style={{ height: '30px' }}>
            <Col span={8}>
              <div style={{ textAlign: 'right', lineHeight: '30px' }}>实际报料数量：</div>
            </Col>
            <Col span={16} style={{ lineHeight: '30px' }}>
              {sysVal}
            </Col>
          </Row>
          <Row style={{ height: '30px' }}>
            <Col span={8}>
              <div style={{ textAlign: 'right', lineHeight: '30px' }}>额外增量：</div>
            </Col>
            <Col span={16}>
              <InputNumber
                placeholder="请填写对外显示时要增加的数量"
                min={0}
                max={99999999}
                onChange={(e) => valueChange(e)}
                value={pos}
                style={{ width: '100%' }}
                precision={0}
              />
            </Col>
          </Row>
          <Row style={{ height: '30px' }}>
            <Col span={8}>
              <div style={{ textAlign: 'right', lineHeight: '30px' }}>最终呈现：</div>
            </Col>
            <Col span={16} style={{ lineHeight: '30px' }}>
              {parseInt(sysVal || 0) + parseInt(val || 0)}
            </Col>
          </Row>
        </>
      ),
      onOk: (destroy: Function) => {
        // const { permissions } = session;
        // const disabled = permissions.indexOf('report:articlenum_save') === -1;
        // if (disabled) {
        //   message.error('没有权限');
        //   destroy()
        //   return
        // }

        // if (pos == null) {
        //   message.error('请输入1～10的数字');
        //   return
        // }
        api
          .saveReportHomeConfig({ id: 'total', value: parseInt(pos || 0) })
          .then((data: any) => {
            message.success('操作成功');
            destroy();
            getData();
          })
          .catch((e) => {});

        // run(api.gptQuestionWelcomeSave, { welcome: msg }, true).then(() => {
        //   message.success('操作成功');
        // });
      },
    });
  };

  const changeWeekURL = (val: any) => {
    let pos = val;
    const valueChange = (v: any) => {
      // msg = v.replace(/\r\n/g, '\n').slice(0, 40)
      pos = v;
      modal.update({
        content: (
          <>
            <div>跳转链接：</div>
            <Input
              placeholder="请输入链接"
              onChange={(e) => valueChange(e.target.value)}
              defaultValue={pos}
              style={{ width: '100%' }}
            />
            <div
              style={{
                color: '#999',
                marginTop: 10,
              }}
            >
              功能说明：链接为空时，客户端自动隐藏该功能入口
            </div>
          </>
        ),
      });
    };

    var modal = Modal.confirm({
      title: '一周重点',
      icon: null,
      content: (
        <>
          <div>跳转链接：</div>
          <Input
            placeholder="请输入链接"
            onChange={(e) => valueChange(e.target.value)}
            defaultValue={pos}
            style={{ width: '100%' }}
          />
          <div
            style={{
              color: '#999',
              marginTop: 10,
            }}
          >
            功能说明：链接为空时，客户端自动隐藏该功能入口
          </div>
        </>
      ),
      onOk: (destroy: Function) => {
        // const { permissions } = session;
        // const disabled = permissions.indexOf('report:articlenum_save') === -1;
        // if (disabled) {
        //   message.error('没有权限');
        //   destroy()
        //   return
        // }
        const regex = /^https?:\/\//;
        if (!!pos && !(pos.startsWith('http://') || pos.startsWith('https://'))) {
          message.error('请输入正确格式的链接');
          return;
        }
        // if (pos == null) {
        //   message.error('请输入1～10的数字');
        //   return
        // }
        api
          .saveReportHomeConfig({ id: 'week', value: pos })
          .then((data: any) => {
            message.success('操作成功');
            destroy();
            getData();
          })
          .catch((e) => {});

        // run(api.gptQuestionWelcomeSave, { welcome: msg }, true).then(() => {
        //   message.success('操作成功');
        // });
      },
    });
  };

  // 排序
  const handelSort = (record: any, pos: any) => {
    const posChange = (value: number | undefined) => {
      pos = value;
    };
    // if (param.keyword || param.status) {
    //   newSort = undefined
    // }
    Modal.confirm({
      title: `调整排序`,
      icon: null,
      content: (
        <div>
          <InputNumber
            placeholder="请输入修改序号"
            min={1}
            max={total}
            onChange={posChange}
            defaultValue={pos}
            style={{ width: '100%' }}
            precision={0}
          />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!pos) {
          message.error('请填写序号');
          return;
        }
        api
          .moveReportUserPosition({
            id: record.id,
            position: pos,
            type: 3,
          })
          .then(() => {
            message.success('操作成功');
            getData();
            closeFunc();
          });
      },
    });
  };

  const changeFilter = (key: string, val: any, goToFirstPage = true) => {
    let newFilter = {
      ...filter,
      [key]: val,
    };
    setFilter(newFilter);
    getData(goToFirstPage, newFilter);
  };

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      const newFilter = {
        ...filter,
      };
      setFilter(newFilter);
      getData(true, newFilter);
    }
  };

  // 获取table
  const getData = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current;
    // ...newFilter
    dispatch(
      getTableList(type == 0 ? 'getReportHomeConfigList' : 'getReportRecommendDataList', 'list', {
        current: cur,
        size,
      })
    );
  };

  useEffect(() => {
    const { selectKeys, openKeys } = props;
    dispatch(setConfig({ selectKeys, openKeys }));
  }, []);

  useEffect(() => {
    getData(true);
  }, [type]);

  const onChangeType = (v: any, name: any) => {
    dispatch(clearTableList());

    let { pathname: path, search } = history.location;
    const obj = searchToObject();
    obj[name] = v;
    path = `${path}?${objectToQueryString(obj)}`;
    history.replace(path);
    setType(v);
  };

  const addDataRecommend = () => {
    setDataRecommend({
      visible: true,
      formContent: null,
    });
  };

  const addDataServiceZone = () => {
    dispatch(setConfig({ loading: true }));
    api
      .getReportServiceZoneDetail({})
      .then((data: any) => {
        dispatch(setConfig({ loading: false }));
        setDataServiceZone({
          visible: true,
          formContent: data.data.recommend,
        });
      })
      .catch((e) => {
        dispatch(setConfig({ loading: false }));
      });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Radio.Group
            defaultValue={type}
            buttonStyle="solid"
            onChange={(e) => onChangeType(e.target.value, 'type')}
            style={{ marginRight: 8 }}
          >
            <Radio.Button value={1}>推荐位</Radio.Button>
            <Radio.Button value={0}>数据配置</Radio.Button>
          </Radio.Group>
          <PermButton
            perm="jzbdata:create"
            onClick={() => addDataRecommend()}
            style={{ marginRight: 8 }}
          >
            添加数据推荐位
          </PermButton>
          {/* <PermButton
            perm=" jzbzone:save"
            onClick={() => addDataServiceZone()}
            style={{ marginRight: 8 }}
          >
            服务专区配置
          </PermButton> */}
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          func={type == 0 ? 'getReportHomeConfigList' : 'getReportRecommendDataList'}
          index="list"
          pagination={type != 0}
          rowKey="id"
          columns={type == 0 ? columns : recommendColumns}
          // filter={filter}
        />

        <ReportDataRecommendDrawer
          {...dataRecommend}
          onClose={() => setDataRecommend({ visible: false })}
          onEnd={() => {
            setDataRecommend({ visible: false });
            getData();
          }}
        ></ReportDataRecommendDrawer>

        <ReportServiceZoneDrawer
          {...dataServiceZone}
          onClose={() => setDataServiceZone({ visible: false })}
          onEnd={() => {
            setDataServiceZone({ visible: false });
            getData();
          }}
        ></ReportServiceZoneDrawer>
      </div>
    </>
  );
}
