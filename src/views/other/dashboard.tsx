import { ICommonProps } from '@app/types';
import { getCrumb, setMenu } from '@app/utils/utils';
import { connectSession as connect } from '@utils/connect';
import { Col, Row, Divider } from 'antd';
import React from 'react';

import './index.scss';
import { withRouter, RouteComponentProps } from 'react-router';
import { Link } from 'react-router-dom';

class DashBoard extends React.Component<RouteComponentProps & ICommonProps, {}> {
  componentDidMount() {
    setMenu(this);
  }

  render() {
    return (
      <>
        <Row className="layout-infobar">
          <Col span={24} className="layout-breadcrumb">
            {getCrumb(['欢迎页'])}
          </Col>
        </Row>
        <div className="component-content dashboard">
          <img src="/assets/welcome.png" className="mainpic" />
        </div>
      </>
    );
  }
}

export default connect(withRouter(DashBoard));
