import SearchAndInput from '@components/common/newNewsSearchAndInput';
import {
  Button,
  Checkbox,
  Dropdown,
  Form,
  Icon,
  Input,
  InputNumber,
  Menu,
  Modal,
  Radio,
  Row,
  Select,
  Spin,
  Switch,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { communityApi, serviceApi, userApi } from '@app/api';
import '@components/business/styles/business.scss';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { A, Drawer, ImageUploader, VideoUploader } from '@app/components/common';
import ImgUploadList from '@app/components/common/imgUploadList';
import _, { set } from 'lodash';
import PermsCheckbox from '@app/components/common/PermsCheckbox';
import { render } from 'react-dom';
import { getImageRatio } from '@app/utils/utils';
import TMFormList, { FormListTitle, TMFormListRef } from '@app/components/common/TMFormList';

const ServiceHeaderDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const [accountOptions, setAccountOptions] = useState([]);
  const [platformList, setPlatformList] = useState([]);
  const platformListRef = useRef<TMFormListRef>(null);
  const { getFieldDecorator, getFieldValue, getFieldsValue, setFieldsValue } = props.form;

  useEffect(() => {
    if (props.visible) {
      setPlatformList(
        props.record?.head_list?.length > 0
          ? props.record?.head_list
          : [
              {
                name: '',
                url: '',
                logo: '',
              },
              {
                name: '',
                url: '',
                logo: '',
              },
              {
                name: '',
                url: '',
                logo: '',
              },
            ]
      );
    }
  }, [props.visible]);

  const doSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body = {
          head_list: values.head_list,
          head_enabled: values.head_enabled,
          activity_enabled: values.activity_enabled,
          activity: {
            logo: values.activity_pic_url,
            url: values.activity_url,
          },
        };
        dispatch(setConfig({ mLoading: true }));
        serviceApi
          .saveServiceHeader(body)
          .then(() => {
            dispatch(setConfig({ mLoading: false }));
            message.success('操作成功');
            props.onOk();
          })
          .catch(() => {
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val?.trim()) {
      setAccountOptions([]);
      return;
    }

    communityApi
      .recommendAccount_Search({ keyword: val })
      .then(({ data }) => {
        const { list } = data as any;
        setAccountOptions(list || []);
      })
      .catch(() => {});
  }, 500);

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  return (
    <Drawer
      title={'服务头部管理'}
      visible={props.visible}
      skey={props.key}
      onClose={props.onClose}
      onOk={doSubmit}
      okText="保存"
      okPerm="web_link:head_save"
    >
      <Form {...formLayout}>
        <h3>
          重点服务
          <Tooltip title={<img src={`/assets/sevice_header_1.png`} width={230} height={69} />}>
            <Icon type="question-circle" />
          </Tooltip>
        </h3>
        <Form.Item label="展示">
          {getFieldDecorator(`head_enabled`, {
            initialValue: props.record?.head_enabled ?? true,
            valuePropName: 'checked',
          })(<Switch />)}
        </Form.Item>
        <TMFormList
          ref={platformListRef}
          dataList={platformList}
          form={props.form}
          fromItem={() => {
            return {
              name: '',
              url: '',
              logo: '',
            };
          }}
          filed="head_list"
        >
          {(item, index, length) => {
            return (
              <div key={`${item.name}-${index}`}>
                <FormListTitle
                  total={length}
                  i={index}
                  title="服务"
                  upMove={() => platformListRef.current?.upMove(index)}
                  downMove={() => platformListRef.current?.downMove(index)}
                  removeItem={() => platformListRef.current?.removeItem(index)}
                  min={3}
                />

                <Form.Item label="名称">
                  {getFieldDecorator(`head_list[${index}].name`, {
                    initialValue: item.name,
                    rules: [
                      {
                        required: getFieldValue('head_enabled'),
                        message: '请输入名称',
                        whitespace: true,
                      },
                      {
                        max: 6,
                        message: '最多可输入6字',
                      },
                    ],
                  })(<Input placeholder="最多可输入6字"></Input>)}
                </Form.Item>
                <Form.Item label="图片" extra="支持.jpg .jpeg .png图片格式，比例为1:1">
                  {getFieldDecorator(`head_list[${index}].logo`, {
                    initialValue: item.logo,
                    rules: [
                      {
                        required: getFieldValue('head_enabled'),
                        message: '请上传图片',
                      },
                    ],
                  })(<ImageUploader ratio={1} accept={['image/jpeg', 'image/png', 'image/jpg']} />)}
                </Form.Item>
                <Form.Item label="跳转链接">
                  {getFieldDecorator(`head_list[${index}].url`, {
                    initialValue: item.url,
                    rules: [
                      {
                        required: getFieldValue('head_enabled'),
                        message: '请输入跳转链接',
                      },
                      {
                        validator: (rule: any, value: any, callback: any) => {
                          const regex = /^https?:\/\//;
                          if (!value) {
                            return callback();
                          } else if (!regex.test(value)) {
                            return callback('请正确填写链接格式');
                          } else {
                            return callback();
                          }
                        },
                      },
                    ],
                  })(<Input placeholder="请输入跳转链接"></Input>)}
                </Form.Item>
              </div>
            );
          }}
        </TMFormList>

        <Row style={{ textAlign: 'center', marginBottom: 16 }} id="add-btn-line">
          <Button
            onClick={() => platformListRef.current?.addItem()}
            disabled={(platformListRef.current?.total ?? 0) >= 4}
          >
            添加服务
          </Button>
        </Row>

        <h3>
          活动
          <Tooltip title={<img src={`/assets/sevice_header_2.png`} width={230} height={180} />}>
            <Icon type="question-circle" />
          </Tooltip>
        </h3>
        <Form.Item label="展示">
          {getFieldDecorator(`activity_enabled`, {
            initialValue: props.record?.activity_enabled ?? true,
            valuePropName: 'checked',
          })(<Switch />)}
        </Form.Item>

        <Form.Item label="图片" extra="支持.jpg .jpeg .png图片格式，比例为375:305">
          {getFieldDecorator(`activity_pic_url`, {
            initialValue: props.record?.activity?.logo,
            rules: [
              {
                required: getFieldValue('activity_enabled'),
                message: '请上传图片',
              },
            ],
          })(<ImageUploader isCutting accept={['image/jpeg', 'image/png', 'image/jpg']} />)}
        </Form.Item>
        <Form.Item label="跳转链接">
          {getFieldDecorator(`activity_url`, {
            initialValue: props.record?.activity?.url,
            rules: [
              {
                required: getFieldValue('activity_enabled'),
                message: '请输入跳转链接',
              },
              {
                validator: (rule: any, value: any, callback: any) => {
                  const regex = /^https?:\/\//;
                  if (!value) {
                    return callback();
                  } else if (!regex.test(value)) {
                    return callback('请正确填写链接格式');
                  } else {
                    return callback();
                  }
                },
              },
            ],
          })(<Input placeholder="请输入跳转链接"></Input>)}
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'ServiceHeaderDrawer' })(
  forwardRef<any, any>(ServiceHeaderDrawer)
);
