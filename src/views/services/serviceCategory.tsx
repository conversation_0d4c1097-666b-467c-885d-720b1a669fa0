/* eslint-disable react/no-unused-state */
import { getTableList } from '@app/action/tableList';
import { serviceApi as api } from '@app/api';
import { A, Table } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setLoading, setMenu } from '@utils/utils.tsx';
import { Button, Col, Divider, Form, Icon, Input, message, Modal, Row } from 'antd';
import React from 'react';
import { withRouter } from 'react-router';

type Api = 'createServiceCategory' | 'updateServiceCategory';

@(withRouter as any)
@connect
class ServiceCategory extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      name: '',
      mtitle: '',
      loading: false,
      visible: false,
      key: Date.now(),
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    setMenu(this);
  }

  getData = (overlap: any = {}) => {
    const { current, size } = this.props.tableList;
    this.props.dispatch(
      getTableList('getServiceCategoryList', 'category_list', { current, size, ...overlap })
    );
  };

  getColumns = () => {
    const { current, size, total } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    return [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) => (
          <span>
            {requirePerm(
              this,
              'web_link_category:order'
            )(
              <A
                disabled={getSeq(i) === 1}
                className="sort-up"
                onClick={() => this.order(record, getSeq(i), -1)}
              >
                <Icon type="up-circle" theme="filled" />
              </A>
            )}{' '}
            {requirePerm(
              this,
              'web_link_category:order'
            )(
              <A
                disabled={getSeq(i) === total}
                className="sort-down"
                onClick={() => this.order(record, getSeq(i), 1)}
              >
                <Icon type="down-circle" theme="filled" />
              </A>
            )}
          </span>
        ),
        width: 80,
      },
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      },
      {
        title: '分类名称',
        key: 'name',
        dataIndex: 'name',
      },
      {
        title: '上架服务数量',
        key: 'service_count',
        dataIndex: 'service_count',
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <span>
            {requirePerm(
              this,
              'web_link_category:update'
            )(<A onClick={() => this.editRecord(record)}>编辑</A>)}
            <Divider type="vertical" />
            {requirePerm(
              this,
              'web_link_category:delete'
            )(<A onClick={() => this.deleteRecord(record)}>删除</A>)}
          </span>
        ),
        width: 120,
      },
    ];
  };

  order = (record: any, current: number, offset: number) => {
    setLoading(this, true);
    api
      .sortServiceCategory({ current, offset, id: record.id })
      .then(() => {
        message.success('操作成功');
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  deleteRecord = (record: any) => {
    Modal.confirm({
      title: <p>确认删除分类“{record.name}”吗？</p>,
      content: '所有关联服务将做同步删除处理',
      onOk: () => {
        setLoading(this, true);
        api
          .deleteServiceCategory({ id: record.id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };

  editRecord = (record: any = {}) => {
    const { total } = this.props.tableList;
    // if (!record.id && total >= 10) {
    //   message.error('分类数量已达上限10个，请先删除再添加');
    //   return;
    // }
    this.setState({
      visible: true,
      key: Date.now(),
      id: record.id || '',
      name: record.name || '',
      mtitle: record.id ? '编辑分类' : '添加分类',
    });
  };

  handleSubmit = () => {
    const { id, name } = this.state;
    if (name === '') {
      message.error('请填写分类名称');
      return;
    }
    if (name.length > 10) {
      message.error('分类名称不能多于10个字');
      return;
    }
    let func = 'createServiceCategory';
    const body: any = { name };
    if (id) {
      body.id = id;
      func = 'updateServiceCategory';
    }
    this.setState({ loading: true });
    api[func as Api](body)
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setState({ loading: false, visible: false });
      })
      .catch(() => this.setState({ loading: false }));
  };

  render() {
    const { name, visible, mtitle, loading } = this.state;
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            {requirePerm(
              this,
              'web_link_category:create'
            )(
              <Button onClick={() => this.editRecord()}>
                <Icon type="plus-circle" /> 添加分类
              </Button>
            )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getServiceCategoryList"
            index="category_list"
            rowKey="id"
            filter={{}}
            columns={this.getColumns()}
            pagination={true}
          />

          <Modal
            title={mtitle}
            visible={visible}
            confirmLoading={loading}
            onCancel={() => this.setState({ visible: false })}
            onOk={this.handleSubmit}
          >
            <Form {...formLayout}>
              <Form.Item required={true} label="名称">
                <Input
                  value={name}
                  placeholder="请填写名称"
                  onChange={(e: any) => this.setState({ name: e.target.value })}
                />
              </Form.Item>
            </Form>
          </Modal>
        </div>
      </>
    );
  }
}

export default ServiceCategory;
