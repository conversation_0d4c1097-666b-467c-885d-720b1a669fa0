import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useStore, useDispatch } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, A } from '@components/common';
import { CommonObject } from '@app/types';
import { getCrumb, setMenuHook } from '@app/utils/utils';
import { Row, Col, Input, Divider, Button, Select, Icon, Modal, Form, message } from 'antd';
import { getTableList } from '@app/action/tableList';
import { serviceApi as api } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@components/permItems';

export default function ServiceKeywords(props: any) {
  const [filter, setFilter] = useState(() => ({
    keyword: '',
    classId: '',
  }));
  const [form, setForm] = useState(
    () =>
      ({
        visible: false,
        key: Date.now(),
      } as any)
  );
  const [classList, setClassList] = useState(() => []);
  const [keyword, setKeyword] = useState('');
  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const { loading, run } = useXHR();

  const updateKeyword = useCallback((e: any) => {
    setKeyword(e.target.value);
  }, []);

  const f = useMemo(() => {
    const x: CommonObject = {};
    if (filter.keyword) {
      x.keyword = filter.keyword;
    }
    if (filter.classId !== '') {
      x.class_id = filter.classId;
    }
    return x;
  }, [filter]);

  const getList = useCallback(
    (overlap: CommonObject = {}) => {
      const { current, size = 10 } = store.getState().tableList;
      dispatch(getTableList('getSearchKeywordList', 'list', { ...f, current, size, ...overlap }));
    },
    [f]
  );

  const editRecord = (record: any) => {
    setForm({
      visible: true,
      key: Date.now(),
      id: record.id,
      name: record.name,
      class_id: record.class_id,
    });
  };
  const deleteRecord = (record: any) => {
    Modal.confirm({
      title: `确认删除关键词“${record.name}”？`,
      onOk: () => {
        run(api.deleteSearchKeyword, { id: record.id }, true).then(() => {
          message.success('操作成功');
          getList();
        });
      },
    });
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
    },
    {
      title: '关联词',
      dataIndex: 'name',
    },
    {
      title: '分类',
      dataIndex: 'class_name',
    },
    {
      title: '操作',
      key: 'op',
      render: (record: any) => (
        <span>
          <PermA perm="web_link_relevance:update" onClick={() => editRecord(record)}>
            编辑
          </PermA>
          <Divider type="vertical" />
          <PermA perm="web_link_relevance:delete" onClick={() => deleteRecord(record)}>
            删除
          </PermA>
        </span>
      ),
      width: 110,
    },
  ];

  useEffect(() => {
    // getList({ current: 1, size: 10 });
    api.getSearchKeywordCategoryList({}).then((res: any) => {
      setClassList(res.data.list);
    });
    setMenuHook(dispatch, props);
  }, []);

  useEffect(() => {
    getList({ current: 1 });
  }, [f]);

  const classIdChange = useCallback((classId: any) => {
    setFilter((oFilter) => {
      return {
        ...oFilter,
        classId,
      };
    });
  }, []);

  const doSearch = () => {
    setFilter((oFilter) => {
      return {
        ...oFilter,
        keyword,
      };
    });
  };

  const submitKeyword = () => {
    if (form.class_id === '') {
      message.error('请选择分类');
      return;
    }
    if (!form.name) {
      message.error('请填写关键词');
      return;
    }
    if (form.name.length > 6) {
      message.error('关联词不能超过6个字');
      return;
    }
    const body = form.id
      ? { name: form.name, id: form.id, class_id: form.class_id }
      : { name: form.name, class_id: form.class_id };
    run(form.id ? api.updateSearchKeyword : api.createSearchKeyword, body).then(() => {
      message.success('操作成功');
      setForm((s: any) => ({ ...s, visible: false }));
      getList();
    });
  };

  const formChange = (v: any) => {
    if (typeof v === 'object') {
      setForm({
        ...form,
        name: v.target.value,
      });
    } else {
      setForm({
        ...form,
        class_id: v,
      });
    }
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton
            perm="web_link_relevance:create"
            style={{ marginRight: 8 }}
            onClick={() => setForm({ visible: true, key: Date.now(), name: '', class_id: '' })}
          >
            <Icon type="plus-circle" /> 新建关键词
          </PermButton>
          <PermButton
            onClick={() => history.push('/view/serviceKeywordsCategory')}
            perm="web_link_relevance_class:view"
          >
            <Icon type="right-circle" /> 分类管理
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={12}>
            <Select value={filter.classId} onChange={classIdChange} style={{ width: 140 }}>
              <Select.Option value="">全部分类</Select.Option>
              {classList.map((v: any) => (
                <Select.Option value={v.id}>{v.name}</Select.Option>
              ))}
            </Select>
          </Col>
          <Col span={12} style={{ textAlign: 'right' }}>
            <Input
              value={keyword}
              onChange={updateKeyword}
              placeholder="搜索ID、关联词"
              style={{ width: 200, marginRight: 8 }}
              onKeyPress={(e) => e.which === 13 && doSearch()}
            />
            <Button type="primary" onClick={doSearch} style={{ verticalAlign: 'top' }}>
              搜索
            </Button>
          </Col>
        </Row>
        <Table
          func="getSearchKeywordList"
          index="list"
          filter={f}
          columns={columns}
          rowKey="id"
          pagination
        />
        <Modal
          visible={form.visible}
          title={form.id ? '编辑关键词' : '新建关键词'}
          onCancel={() => setForm((s: any) => ({ key: s.key, visible: false }))}
          onOk={submitKeyword}
          confirmLoading={loading}
        >
          <Form labelCol={{ span: 4 }} wrapperCol={{ span: 19 }}>
            <Form.Item label="分类" required>
              <Select value={form.class_id} onChange={formChange}>
                <Select.Option value="" disabled>
                  请选择分类
                </Select.Option>
                {classList.map((v: any) => (
                  <Select.Option value={v.id}>{v.name}</Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item label="关键词" required>
              <Input
                value={form.name}
                onChange={formChange}
                placeholder="请输入6个字以内的关键词"
              />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </>
  );
}
