import { getTableList } from '@app/action/tableList';
import { serviceApi as api } from '@app/api';
import { CommonObject } from '@app/types';
import { A, ImageUploader, Table } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setLoading, setMenu } from '@utils/utils';
import { Button, Col, Divider, Form, Icon, Input, message, Modal, Row } from 'antd';
import React from 'react';
import { withRouter } from 'react-router';

type Api = 'createServiceFocus' | 'updateServiceFocus';

@(withRouter as any)
@connect
class ServiceFocus extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      loading: false,
      key: Date.now(),
      visible: false,
      mtitle: '',
      onCount: 0,
      id: '',
      pic_url: '',
      service_url: '',
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    setMenu(this);
  }

  getData = (overlap: CommonObject = {}) => {
    const { current, size } = this.props.tableList;
    this.props.dispatch(
      getTableList('getServiceFocusList', 'top_list', { current, size, ...overlap })
    );
    api.getServiceFocusOnCount().then((r: any) => {
      this.setState({
        onCount: r.data.count,
      });
    });
  };

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;

    return [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) => (
          <span>
            {requirePerm(
              this,
              'web_link_top:order'
            )(
              <A
                disabled={getSeq(i) === 1 || !record.enabled}
                className="sort-up"
                onClick={() => this.exchangeOrder(record, getSeq(i), -1)}
              >
                <Icon type="up-circle" theme="filled" />
              </A>
            )}{' '}
            {requirePerm(
              this,
              'web_link_top:order'
            )(
              <A
                disabled={getSeq(i) === this.state.onCount || !record.enabled}
                className="sort-down"
                onClick={() => this.exchangeOrder(record, getSeq(i), 1)}
              >
                <Icon type="down-circle" theme="filled" />
              </A>
            )}
          </span>
        ),
        width: 70,
      },
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '头图',
        key: 'icon',
        dataIndex: 'pic_url',
        render: (text: any) => <img src={text} className="list-pic" />,
        width: 150,
      },
      {
        title: '链接',
        key: 'link',
        dataIndex: 'service_url',
        render: (text: any) => (
          <a href={text} target="_blank" rel="noreferrer">
            {text}
          </a>
        ),
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'enabled',
        render: (text: any) => <span>{text ? '展示中' : '待展示'}</span>,
        width: 70,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => (
          <span>
            {requirePerm(
              this,
              record.enabled ? 'web_link_top:off' : 'web_link_top:on'
            )(<A onClick={() => this.changeEnabled(record)}>{record.enabled ? '下架' : '上架'}</A>)}
            <Divider type="vertical" />
            {requirePerm(
              this,
              'web_link_top:update'
            )(<A onClick={() => this.editRecord(record)}>编辑</A>)}
            <Divider type="vertical" />
            {requirePerm(
              this,
              'web_link_top:delete'
            )(<A onClick={() => this.deleteRecord(record)}>删除</A>)}
          </span>
        ),
        width: 170,
      },
    ];
  };

  exchangeOrder = (record: any, current: number, offset: number) => {
    setLoading(this, true);
    api
      .sortServiceFocus({ current, offset, id: record.id })
      .then(() => {
        message.success('操作成功');
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  changeEnabled = (record: any) => {
    // if (record.enabled && this.state.onCount <= 1) {
    //   message.error('请保持至少1条上架头图数据');
    //   return;
    // }
    if (!record.enabled && this.state.onCount >= 5) {
      message.error('头图轮播已达上限5个，请先下架再上架');
      return;
    }
    setLoading(this, true);
    api
      .updateServiceFocusStatus(record.enabled ? 'off' : 'on', { id: record.id })
      .then(() => {
        message.success('操作成功');
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  deleteRecord = (record: any) => {
    // if (record.enabled && this.state.onCount <= 1) {
    //   message.error('请保持至少1条上架头图数据');
    //   return;
    // }
    Modal.confirm({
      title: <p>确认删除吗？</p>,
      onOk: () => {
        setLoading(this, true);
        api
          .deleteServiceFocus({ id: record.id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };

  editRecord = (record: any = {}) => {
    if (!record.id && this.state.onCount >= 5) {
      message.error('上架头图已达上限5个，请先下架再添加');
      return;
    }
    this.setState({
      visible: true,
      key: Date.now(),
      mtitle: record.id ? '编辑头图' : '添加头图',
      id: record.id || '',
      pic_url: record.pic_url || '',
      service_url: record.service_url || '',
    });
  };

  handleSubmit = () => {
    const { id, pic_url, service_url } = this.state;
    if (pic_url === '' || service_url === '') {
      message.error('请检查必填项');
      return;
    }
    const regex = /^https?:\/\//;
    if (!regex.test(service_url)) {
      message.error('服务连接填写格式不正确');
      return;
    }
    let func = 'createServiceFocus';
    const body: any = { service_url, url: pic_url };
    if (id) {
      func = 'updateServiceFocus';
      body.id = id;
    }
    this.setState({ loading: true });
    api[func as Api](body)
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setState({ loading: false, visible: false });
      })
      .catch(() => this.setState({ loading: false }));
  };

  render() {
    const formLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
    const { loading, visible, key, mtitle, pic_url, service_url } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            {requirePerm(
              this,
              'web_link_top:create'
            )(
              <Button onClick={() => this.editRecord()}>
                <Icon type="plus-circle" /> 添加头图
              </Button>
            )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getServiceFocusList"
            index="top_list"
            filter={{}}
            columns={this.getColumns()}
            rowKey="id"
            pagination={true}
          />
          <Modal
            visible={visible}
            key={key}
            title={mtitle}
            confirmLoading={loading}
            width="600px"
            onCancel={() => this.setState({ visible: false })}
            onOk={this.handleSubmit}
          >
            <Form {...formLayout}>
              <Form.Item required={true} label="图标">
                <ImageUploader
                  value={pic_url}
                  ratio={3 / 1}
                  onChange={(v: any) => this.setState({ pic_url: v })}
                />
              </Form.Item>
              <Form.Item required={true} label="链接">
                <Input
                  value={service_url}
                  placeholder="请输入链接"
                  onChange={(e: any) => this.setState({ service_url: e.target.value })}
                />
              </Form.Item>
            </Form>
          </Modal>
        </div>
      </>
    );
  }
}

export default ServiceFocus;
