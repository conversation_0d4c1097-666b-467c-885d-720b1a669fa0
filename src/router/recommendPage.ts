import { lazy } from 'react';

const Operations = lazy(() => import('@views/recommend/operations'));
const dataBoard = lazy(() => import('@app/views/data/board'));

// const ServiceFocus = lazy(() => import('@views/services/serviceFocus'));
// const ServiceKeywords = lazy(() => import('@views/services/serviceKeywords'));
// const ServiceKeywordsCategory = lazy(() => import('@views/services/serviceKeywordsCategory'));
// const RecommendedServices = lazy(() => import('@views/services/RecommendedServices'));
export default [
  {
    path: '/recommendOperate',
    component: Operations,
    routeProps: {
      breadCrumb: ['推荐页管理', '推荐页运营位'],
      selectKeys: ['/view/recommendOperate'],
      openKeys: ['/view/recommendPages'],
    },
    permission: '',
  },
  {
    path: '/datahome',
    component: dataBoard,
    routeProps: {
      breadCrumb: ['数据管理', '数据看板'],
      selectKeys: ['/view/datahome'],
      openKeys: ['/view/recommendPages'],
    },
    permission: '',
  },

];
