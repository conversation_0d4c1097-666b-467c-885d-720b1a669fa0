import { connectSession as connect } from '@app/utils/connect';
import React from 'react';
import { Route } from 'react-router-dom';
import { withRouter, RouteComponentProps } from 'react-router';
import { ISessionProps } from '@app/types';

interface PrivateRouteProps {
  routeProps: any;
  perm: string;
  path: string;
  component: any;
}

class PrivateRoute extends React.Component<
  PrivateRouteProps & RouteComponentProps & ISessionProps,
  {}
> {
  constructor(props: any) {
    super(props);
    this.state = {};
  }

  renderNoPerm = () => {
    return <div>无权限</div>;
  };

  renderComponent = (COMPONENT: any, props: any) => {
    return <COMPONENT {...props} />;
  };

  render() {
    const { permissions } = this.props.session;
    if (permissions.indexOf(this.props.perm) === -1 && this.props.perm !== '') {
      return (
        <Route
          path={`/view${this.props.path}`}
          exact={true}
          {...this.props.routeProps}
          render={this.renderNoPerm}
        />
      );
    }
    return (
      <Route
        path={`/view${this.props.path}`}
        exact={true}
        render={this.renderComponent.bind(this, this.props.component, this.props.routeProps)}
      />
    );
  }
}

export default withRouter(connect(PrivateRoute));
