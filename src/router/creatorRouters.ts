import { lazy } from 'react';

const OperatePositionMgr = lazy(() => import('@app/views/creator/operatePositionMgr'));
const ImageDetectionMgr = lazy(() => import('@app/views/creator/imageDetectionMgr'));
export default [
  {
    path: '/operatePosition',
    component: OperatePositionMgr,
    routeProps: {
      breadCrumb: ['创作者平台管理', '运营位管理'],
      selectKeys: ['/view/operatePosition'],
      openKeys: ['/view/creator'],
    },
    permission: '',
  },
  {
    path: '/reviewImageConfigMgr',
    component: ImageDetectionMgr,
    routeProps: {
      breadCrumb: ['创作者平台管理', '图片检测配置'],
      selectKeys: ['/view/reviewImageConfigMgr'],
      openKeys: ['/view/creator'],
    },
    permission: '',
  },
]