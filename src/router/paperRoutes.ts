import { lazy } from 'react';

const FingerPaper = lazy(() => import('@views/operates/fingerPaper'));
const readPaper = lazy(() => import('@views/operates/readPaper'));

export default [
  {
    path: '/fingerPaper',
    component: FingerPaper,
    routeProps: {
      breadCrumb: ['读报管理', '指尖读报'],
      selectKeys: ['/view/fingerPaper'],
      openKeys: ['/view/paper'],
    },
    permission: '',
  },
  {
    path: '/readPaper',
    component: readPaper,
    routeProps: {
      breadCrumb: ['读报管理', '早晚报'],
      selectKeys: ['/view/readPaper'],
      openKeys: ['/view/paper'],
    },
    permission: '',
  },
];
