import { lazy } from 'react';

const BatchRevoke = lazy(() => import('@app/views/devops/batchRevoke'));
const GlobalSearch = lazy(() => import('@app/views/devops/globalSearch'));
const AudioManager = lazy(() => import('@app/views/devops/audioManage'));
const UgcContentAuditMgr = lazy(() => import('@app/views/devops/ugcContentAuditMgr'));

export default [
  {
    path: '/cancelReleaseMgr',
    component: BatchRevoke,
    routeProps: {
      breadCrumb: ['运维管理', '撤稿管理'],
      selectKeys: ['/view/cancelReleaseMgr'],
      openKeys: ['/view/ywMgr'],
    },
    permission: '',
  },
  {
    path: '/globalSearchManage',
    component: GlobalSearch,
    routeProps: {
      breadCrumb: ['运维管理', '全局搜索管理'],
      selectKeys: ['/view/globalSearchManage'],
      openKeys: ['/view/ywMgr'],
    },
    permission: '',
  },
  {
    path: '/globalAudioManage',
    component: AudioManager,
    routeProps: {
      breadCrumb: ['运维管理', '音频管理'],
      selectKeys: ['/view/globalAudioManage'],
      openKeys: ['/view/ywMgr'],
    },
    permission: '',
  },
  {
    path: '/ugcContentAuditMgr',
    component: UgcContentAuditMgr,
    routeProps: {
      breadCrumb: ['运维管理', '用户内容审核'],
      selectKeys: ['/view/ugcContentAuditMgr'],
      openKeys: ['/view/ywMgr'],
    },
    permission: '',
  },
];
