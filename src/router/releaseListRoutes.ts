import { lazy } from 'react';

const FocusList = lazy(() => import('@views/news/focusList'));
const ReleaseList = lazy(() => import('@views/news/releaseList'));
const LiveList = lazy(() => import('@views/news/live'));
const SlowLiveList = lazy(() => import('@views/news/slowlive'));
const UserDeleted = lazy(() => import('@views/news/userDeletedList'));
const userComplaintList = lazy(() => import('@views/news/userComplaintList'));
const TopArticleManager = lazy(() => import('@views/news/articleTopManager'));
const importantArticleManager = lazy(() => import('@views/news/importantArticleManager'));
const commentRecommend = lazy(() => import('@views/news/commentRecommend'));
const tmhTopping = lazy(() => import('@views/news/tmhTopping'));
const ProposalManager = lazy(() => import('@views/operates/proposalManager'));


export default [
  {
    path: '/releaselist',
    component: ReleaseList,
    routeProps: {
      openKeys: ['/view/releasePages'],
    },
    permission: '',
  },
  {
    path: '/live',
    component: LiveList,
    routeProps: {
      openKeys: ['/view/releasePages'],
    },
    permission: '',
  },
  {
    path: '/slowlive/:id',
    component: SlowLiveList,
    routeProps: {
      openKeys: ['/view/releasePages'],
      breadCrumb: ['签发内容管理', '慢直播管理'],
    },
    permission: '',
  },
  {
    path: '/focuslist/:id/:from',
    component: FocusList,
    routeProps: {
      openKeys: ['/view/releasePages'],
    },
    permission: '',
  },
  {
    path: '/floatWindow/:id/:from',
    component: ProposalManager,
    routeProps: {
      openKeys: ['/view/releasePages'],
      breadCrumb: ['签发内容管理', '浮窗管理'],
    },
    permission: '',
  },
  {
    path: '/userDeletedContents/:id/:name',
    component: UserDeleted,
    routeProps: {
      // openKeys: ['/view/releasePages'],
      // breadCrumb: ['签发内容管理', '潮客', '用户发起的删除内容'],
    },
    permission: '',
  },
  {
    path: '/userComplaintContents/:id/:name',
    component: userComplaintList,
    routeProps: {
      // openKeys: ['/view/releasePages'],
      // breadCrumb: ['签发内容管理', '潮客', '举报内容'],
    },
    permission: '',
  },
  {
    path: '/articleTopMgr/:id',
    component: TopArticleManager,
    routeProps: {
      openKeys: ['/view/releasePages'],
    },
    permission: '',
  },
  {
    path: '/importantArticleMgr/:id',
    component: importantArticleManager,
    routeProps: {
      openKeys: ['/view/releasePages'],
    },
    permission: '',
  },
  {
    path: '/commentRecommend/:id/:name/:articleid?',
    component: commentRecommend,
    routeProps: {
      openKeys: ['/view/releasePages'],
    },
    permission: '',
  },
  {
    path: '/tmhTopping/:id',
    component: tmhTopping,
    routeProps: {
      // openKeys: ['/view/releasePages'],
      breadCrumb: ['签发内容管理', '潮鸣号', '置顶稿件管理'],
    },
    permission: '',
  },
];
