import { lazy } from 'react';

const MaterialReview = lazy(() => import('@app/views/reporter/materialReview'));
const Deputy = lazy(() => import('@app/views/reporter/deputyMgr'));
const Recommend = lazy(() => import('@app/views/reporter/recommendMat'));
const HelpReporterMgr = lazy(() => import('@app/views/reporter/helpReporterMgr'));
const ExpertMgr = lazy(() => import('@app/views/reporter/expertMgr'));
const UnionReporterMgr = lazy(() => import('@app/views/reporter/unionReporterMgr'));
const ReporterHomeManager = lazy(() => import('@app/views/reporter/reporterHomeManager'));
const ReporterFieldManager = lazy(() => import('@app/views/reporter/reporterFieldManager'));
const AssistantManager = lazy(() => import('@app/views/operates/assistantManager'));

export default [
  {
    path: '/24hHktRoom',
    component: AssistantManager,
    routeProps: {
      breadCrumb: ['记者帮管理', '24H会客厅'],
      selectKeys: ['/view/24hHktRoom'],
      openKeys: ['/view/report_manage'],
      category: 1
    },
    permission: '',
  },
  {
    path: '/reportMgr',
    component: MaterialReview,
    routeProps: {
      breadCrumb: ['记者帮管理', '报料审核'],
      selectKeys: ['/view/reportMgr'],
      openKeys: ['/view/report_manage'],
    },
    permission: '',
  },
  {
    path: '/reportHelpMgr',
    component: Deputy,
    routeProps: {
      breadCrumb: ['记者帮管理', '帮办管理'],
      selectKeys: ['/view/reportHelpMgr'],
      openKeys: ['/view/report_manage'],
      biz_type: 2,
    },
    permission: '',
  },
  {
    path: '/reportAskGov',
    component: Deputy,
    routeProps: {
      breadCrumb: ['记者帮管理', '问政管理'],
      selectKeys: ['/view/reportAskGov'],
      openKeys: ['/view/report_manage'],
      biz_type: 1,
    },
    permission: '',
  },
  {
    path: '/reportAskExpert',
    component: Deputy,
    routeProps: {
      breadCrumb: ['记者帮管理', '帮帮团管理'],
      selectKeys: ['/view/reportAskExpert'],
      openKeys: ['/view/report_manage'],
      biz_type: 3,
    },
    permission: '',
  },
  {
    path: '/yjqzMgr',
    component: Deputy,
    routeProps: {
      breadCrumb: ['记者帮管理', '应急求助管理'],
      selectKeys: ['/view/yjqzMgr'],
      openKeys: ['/view/report_manage'],
      biz_type: 4,
    },
    permission: '',
  },
  {
    path: '/xdbMgr',
    component: Deputy,
    routeProps: {
      breadCrumb: ['记者帮管理', '小店帮'],
      selectKeys: ['/view/xdbMgr'],
      openKeys: ['/view/report_manage'],
      biz_type: 5,
    },
    permission: '',
  },
  {
    path: '/reportHelpMgr/recommend',
    component: Recommend,
    routeProps: {
      breadCrumb: ['记者帮管理', '推荐报料'],
      selectKeys: ['/reportHelpMgr/recommend'],
      openKeys: ['/view/report_manage'],
    },
    permission: '',
  },
  {
    path: '/8531_journalist',
    component: HelpReporterMgr,
    routeProps: {
      breadCrumb: ['记者帮管理', '帮办记者管理'],
      selectKeys: ['/view/8531_journalist'],
      openKeys: ['/view/report_manage'],
    },
    permission: '',
  },
  {
    path: '/expert',
    component: ExpertMgr,
    routeProps: {
      breadCrumb: ['记者帮管理', '专家管理'],
      selectKeys: ['/view/expert'],
      openKeys: ['/view/report_manage'],
    },
    permission: '',
  },
  {
    path: '/journalist',
    component: UnionReporterMgr,
    routeProps: {
      breadCrumb: ['记者帮管理', '联盟记者管理'],
      selectKeys: ['/view/journalist'],
      openKeys: ['/view/report_manage'],
    },
    permission: '',
  },
  {
    path: '/reporterHomeManager',
    component: ReporterHomeManager,
    routeProps: {
      breadCrumb: ['记者帮管理', '专区首页管理'],
      selectKeys: ['/view/reporterHomeManager'],
      openKeys: ['/view/report_manage'],
    },
    permission: '',
  },
  {
    path: '/reporterFieldManager',
    component: ReporterFieldManager,
    routeProps: {
      breadCrumb: ['记者帮管理', '领域管理'],
      selectKeys: ['/view/reporterFieldManager'],
      openKeys: ['/view/report_manage'],
    },
    permission: '',
  },
];
