import { lazy } from 'react';

const Rated = lazy(() => import('@app/views/creativeRevenue/feeRating/rated'));
const FirstAdopt = lazy(() => import('@app/views/creativeRevenue/feeRating/firstAdopt'));
const SecondAdopt = lazy(() => import('@app/views/creativeRevenue/feeRating/secondAdopt'));
const Passed = lazy(() => import('@app/views/creativeRevenue/feeRating/passed'));
const RevenueQuery = lazy(() => import('@app/views/creativeRevenue/feeRating/revenueQuery'));
const AccountDetail = lazy(() => import('@app/views/creativeRevenue/feeRating/accountDetail'));
const WithdrawalApplicationMgr = lazy(() => import('@app/views/creativeRevenue/withdrawalApplication/withdrawalApplicationMgr'))
const WithdrawalApplicationFirstAdoptMgr = lazy(() => import('@app/views/creativeRevenue/withdrawalApplication/firstAdoptMgr'))
const WithdrawalApplicationSecondAdoptMgr = lazy(() => import('@app/views/creativeRevenue/withdrawalApplication/secondAdoptMgr'))
const WithdrawalApplicationPassedMgr = lazy(() => import('@app/views/creativeRevenue/withdrawalApplication/passedMgr'))
const WithdrawalApplicationNotPassedMgr = lazy(() => import('@app/views/creativeRevenue/withdrawalApplication/notPassedMgr'))

export default [
  {
    path: '/rated',
    component: Rated,
    routeProps: {
      breadCrumb: ['创作收益管理', '稿费评级', '待评级'],
      selectKeys: ['/view/rated'],
      openKeys: ['/view/creativeRevenue'],
    },
    permission: '',
  },
  {
    path: '/firstAdopt',
    component: FirstAdopt,
    routeProps: {
      breadCrumb: ['创作收益管理', '稿费评级', '一审稿费'],
      selectKeys: ['/view/firstAdopt'],
      openKeys: ['/view/creativeRevenue'],
    },
    permission: '',
  },
  {
    path: '/secondAdopt',
    component: SecondAdopt,
    routeProps: {
      breadCrumb: ['创作收益管理', '稿费评级', '二审稿费'],
      selectKeys: ['/view/secondAdopt'],
      openKeys: ['/view/creativeRevenue'],
    },
    permission: '',
  },
  {
    path: '/passed',  
    component: Passed,
    routeProps: {
      breadCrumb: ['创作收益管理', '稿费评级', '已通过'],
      selectKeys: ['/view/passed'],
      openKeys: ['/view/creativeRevenue'],
    },
    permission: '',
  },
  {
    path: '/revenueQuery',
    component: RevenueQuery,
    routeProps: {
      breadCrumb: ['创作收益管理', '收益查询'],
      selectKeys: ['/view/revenueQuery'],
      openKeys: ['/view/creativeRevenue'],
    },
    permission: '',
  },
  {
    path: '/feeRating/accountDetail/:accountId',
    component: AccountDetail,
    routeProps: {
      breadCrumb: ['创作收益管理', '收益查询', '账户详情'],
      selectKeys: ['/view/revenueQuery'],
      openKeys: ['/view/creativeRevenue'],
    },
    permission: '',
  },
  {
    path: '/withdrawalApplication',
    component: WithdrawalApplicationMgr,
    routeProps: {
      breadCrumb: ['创作收益管理', '提现申请'],
      selectKeys: ['/view/withdrawalApplication'],
      openKeys: ['/view/creativeRevenue'],
    },
    permission: '',
  },
  {
    path: '/withdrawalApplication/firstAdopt',
    component: WithdrawalApplicationFirstAdoptMgr,
    routeProps: {
      breadCrumb: ['创作收益管理', '提现申请', '待审核（一审）'],
      selectKeys: ['/view/withdrawalApplication/firstAdopt'],
      openKeys: ['/view/creativeRevenue'],
    },
    permission: '',
  },
  {
    path: '/withdrawalApplication/secondAdopt',
    component: WithdrawalApplicationSecondAdoptMgr,
    routeProps: {
      breadCrumb: ['创作收益管理', '提现申请', '待审核（二审）'],
      selectKeys: ['/view/withdrawalApplication/secondAdopt'],
      openKeys: ['/view/creativeRevenue'],
    },
    permission: '',
  },
  {
    path: '/withdrawalApplication/passed',
    component: WithdrawalApplicationPassedMgr,
    routeProps: {
      breadCrumb: ['创作收益管理', '提现申请', '已通过'],
      selectKeys: ['/view/withdrawalApplication/passed'],
      openKeys: ['/view/creativeRevenue'],
    },
    permission: '',
  },
  {
    path: '/withdrawalApplication/notPassed',
    component: WithdrawalApplicationNotPassedMgr,
    routeProps: {
      breadCrumb: ['创作收益管理', '提现申请', '不通过'],
      selectKeys: ['/view/withdrawalApplication/notPassed'],
      openKeys: ['/view/creativeRevenue'],
    },
    permission: '',
  },
];
