import { BASE_NAME } from '@app/utils/constants';
import React, { Suspense } from 'react';
import { BrowserRouter, Route } from 'react-router-dom';

/* 基础组件 */
import Layout from '@components/common/layout';
import Login from '@views/other/login';
import Logout from '@views/other/logout';
import debugLogin from '@views/other/debugLogin';
import PrivateRoute from './privateRoute';
import routes from './routes';

class Routes extends React.Component {
  renderRoutes = () => {
    return (
      <Layout>
        <Suspense fallback={<div>loading</div>}>
          {routes.map((route, i) => (
            <PrivateRoute
              component={route.component}
              path={route.path}
              key={route.path}
              routeProps={route.routeProps}
              perm={route.permission}
            />
          ))}
        </Suspense>
      </Layout>
    );
  };

  render() {
    return (
      <BrowserRouter basename={BASE_NAME}>
        <div className="router-div">
          <Route exact={true} path="/" component={Login} />
          <Route exact={true} path="/login" component={Login} />
          <Route exact={true} path="/debugLogin" component={debugLogin} />
          <Route exact={true} path="/logout" component={Logout} />
          <Route path="/view/" render={this.renderRoutes} />
        </div>
      </BrowserRouter>
    );
  }
}

export default Routes;
