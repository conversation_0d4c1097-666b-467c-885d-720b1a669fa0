import { lazy } from 'react';

const AppServiceList = lazy(() => import('@views/services/appServiceList'));
const ServiceCategory = lazy(() => import('@views/services/serviceCategory'));
const ServiceFocus = lazy(() => import('@views/services/serviceFocus'));
const ServiceKeywords = lazy(() => import('@views/services/serviceKeywords'));
const ServiceKeywordsCategory = lazy(() => import('@views/services/serviceKeywordsCategory'));
const RecommendedServices = lazy(() => import('@views/services/RecommendedServices'));
export default [
  {
    path: '/serviceFocus',
    component: ServiceFocus,
    routeProps: {
      breadCrumb: ['服务管理', '服务头图'],
      selectKeys: ['/view/serviceFocus'],
      openKeys: ['/view/servicePages'],
    },
    permission: '',
  },
  {
    path: '/serviceCategory',
    component: ServiceCategory,
    routeProps: {
      breadCrumb: ['服务管理', '分类管理'],
      selectKeys: ['/view/serviceCategory'],
      openKeys: ['/view/servicePages'],
    },
    permission: '',
  },
  {
    path: '/appService',
    component: AppServiceList,
    routeProps: {
      breadCrumb: ['服务管理', '全局服务管理'],
      selectKeys: ['/view/appService'],
      openKeys: ['/view/servicePages'],
    },
    permission: '',
  },
  {
    path: '/recommendedServices',
    component: RecommendedServices,
    routeProps: {
      breadCrumb: ['服务管理', '全局服务管理', '推荐服务配置'],
      selectKeys: ['/view/appService'],
      openKeys: ['/view/servicePages'],
    },
    permission: '',
  },
  {
    path: '/serviceKeywords',
    component: ServiceKeywords,
    routeProps: {
      breadCrumb: ['服务管理', '搜索关键词'],
      selectKeys: ['/view/serviceKeywords'],
      openKeys: ['/view/servicePages'],
    },
    permission: '',
  },
  {
    path: '/serviceKeywordsCategory',
    component: ServiceKeywordsCategory,
    routeProps: {
      breadCrumb: ['服务管理', '搜索关键词', '关键词分类管理'],
      selectKeys: ['/view/serviceKeywords'],
      openKeys: ['/view/servicePages'],
    },
    permission: '',
  },
];
