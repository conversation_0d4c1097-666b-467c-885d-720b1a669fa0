/* eslint-disable import/extensions */
import { RouteComponentProps } from 'react-router';
import { CommonObject, ISessionProps, ICommonProps } from './commonTypes';

export interface MenuItem {
  children: Array<MenuItem>;
  id: number;
  name: string;
  remark: string;
  url: string;
  location_type: number;
  sort_number?: number;
  is_tou_tiao: boolean;
}

export interface IAdmin {
  id: string;
  mlf_id: number;
  name: string;
  state: number;
  user_name: string;
}

export interface Session {
  admin: IAdmin;
  menus: Array<MenuItem>;
  permissions: Array<string>;
}

export interface TableList<T = CommonObject, TALL = CommonObject> {
  records: Array<T>;
  total: number;
  current: number;
  size: number;
  allData: TALL;
  requestId?: string;
  loading?: boolean;
  timestamp?: number;
}
export interface TableCache<T = CommonObject, TALL = CommonObject> {
  records: Array<T>;
  total: number;
  current: number;
  size: number;
  allData: TALL;
  requestId?: string;
  loading?: boolean;
  timestamp?: number;
  beforeRoute: string;
  filters?: any;
}

export interface TableFetchStatus {
  requestId?: string;
  loading: boolean;
}

export type IBaseProps<T = {}, TMatchProps = {}> = RouteComponentProps<TMatchProps> &
  ICommonProps &
  ISessionProps &
  T;
