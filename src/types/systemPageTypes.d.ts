import { ISysChannel, ICommonTableList } from './responseBodyTypes';

export type TSysPermissionGroupRecord = {
  id: number;
  name: string;
  remark: string;
};

export type TSysPermissionGroupData = {
  role_list: TSysPermissionGroupRecord[];
};

export interface TSysPermissionElementRecord {
  checked: boolean;
  id: number;
  is_last_level_menu: boolean;
  children: TSysPermissionElementRecord[];
  name: string;
  type: number;
}

export type TSysPermissionElementData = {
  elements: TSysPermissionElementRecord[];
};

export type TSysAdminRecord = {
  id: string;
  mlf_id: number;
  name: string;
  state: number;
  user_name: string;
  roles: TSysPermissionGroupRecord[];
};

export type TSysAdminData = {
  admin_list: TSysAdminRecord[];
};

export type TSysAdminRoleData = {
  selected: TSysPermissionGroupRecord[];
  unselected: TSysPermissionGroupRecord[];
};

export type TSysChannelCategoryRecord = {
  id: number;
  name: string;
};

export type TSysChannelCategoryData = {
  channel_category_list: TSysChannelCategoryRecord[];
};

export type TSysChannelData = {
  channel_list: ISysChannel[];
};

export type TSysNavRecord = {
  id: number;
  name: string;
  creator: string;
  enabled: boolean;
  require_permission_id: number;
  created_at: number;
  uri_scheme?: string;
  url?: string;
};

export type TSysNavData = {
  area_list?: TSysNavRecord[];
  app_nav_list: ICommonTableList<TSysNavRecord>;
  enabled_count?: number;
};
