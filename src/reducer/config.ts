/* eslint-disable no-case-declarations */
/* eslint-disable import/prefer-default-export */
import * as actionTypes from '@app/action/config';
import { CommonObject } from '@app/types';

const storedState = localStorage.getItem('keys');

const keys = storedState ? JSON.parse(storedState) : {};

const initialState: CommonObject = {
  selectKeys: keys.selectKeys || [],
  openKeys: keys.openKeys || [],
  breadCrumb: [],
  loading: false,
  mLoading: false,
  leftCol: [],
};

export function config(state: CommonObject = initialState, action: CommonObject): CommonObject {
  switch (action.type) {
    case actionTypes.SET_CONFIG:
      const newState = { ...state, ...action.data };
      localStorage.setItem(
        'keys',
        JSON.stringify({
          selectKeys: newState.selectKeys,
          openKeys: newState.openKeys,
        })
      );
      return { ...state, ...action.data };
    default:
      return state;
  }
}
