/* eslint-disable import/prefer-default-export */
import * as actionTypes from '@app/action/session';
import { Session, SessionAction } from '@app/types';

const storedState = localStorage.getItem('session');

const initialState: Session = storedState
  ? JSON.parse(storedState)
  : {
      admin: {},
      menus: [],
      permissions: [],
    };

export function session(state: Session = initialState, action: SessionAction): Session {
  switch (action.type) {
    case actionTypes.SET_SESSION:
      localStorage.setItem('session', JSON.stringify(action.data));
      return { ...state, ...action.data };
    case actionTypes.CLEAR_SESSION:
      localStorage.removeItem('session');
      return {
        admin: {},
        menus: [],
        permissions: [],
      };
    default:
      return state;
  }
}
