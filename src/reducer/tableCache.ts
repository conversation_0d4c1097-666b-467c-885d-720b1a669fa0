/* eslint-disable import/prefer-default-export */
import * as actionTypes from '@app/action/tableCache';
import { TableCache, TableAction } from '@app/types';

const initialState: TableCache = {
    records: [],
    total: 0,
    current: 1,
    size: 10,
    requestId: '',
    loading: false,
    allData: {},
    timestamp: Date.now(),
    beforeRoute: '',
};

export function tableCache(state = initialState, action: TableAction) {
    switch (action.type) {
        case actionTypes.SET_TABLE_CACHE:
            return { ...state, ...action.data, timestamp: Date.now() };
        default:
            return state;
    }
}
