/* eslint-disable import/prefer-default-export */
import * as actionTypes from '@app/action/tableList';
import { TableList, TableAction } from '@app/types';

const initialState: TableList = {
  records: [],
  total: 0,
  current: 1,
  size: 10,
  requestId: '',
  loading: false,
  allData: {},
  timestamp: Date.now(),
};

export function tableList(state = initialState, action: TableAction) {
  switch (action.type) {
    case actionTypes.SET_TABLE:
      return { ...state, ...action.data, timestamp: Date.now() };
    case actionTypes.SET_FETCH_STATUS:
      return { ...state, ...action.data };
    case actionTypes.CLEAR_TABLE:
      return initialState;
    default:
      return state;
  }
}
